<?php
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/session.php';

// Giriş kontrolü
if (!isLoggedIn()) {
    header('Location: auth/login.php?required=1');
    exit;
}

// Test için session timeout sü<PERSON><PERSON> kısaltalım (sadece test için)
if (isset($_GET['test']) && $_GET['test'] === 'timeout') {
    $_SESSION['last_activity'] = time() - 3700; // 1 saat 1 dakika önce
}

$page_title = 'Session Timeout Test';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bakıcı Platform</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="index.php">
                <i class="bi bi-heart-fill me-2"></i>Bakıcı Platform
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.php">Dashboard</a>
                <a class="nav-link" href="auth/logout.php">Çıkış</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="bi bi-clock me-2"></i>Session Timeout Test</h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="bi bi-info-circle me-2"></i>Session Bilgileri</h6>
                            <ul class="mb-0">
                                <li><strong>Kullanıcı ID:</strong> <?php echo $_SESSION['user_id']; ?></li>
                                <li><strong>Kullanıcı Adı:</strong> <?php echo $_SESSION['full_name']; ?></li>
                                <li><strong>Giriş Zamanı:</strong> <?php echo isset($_SESSION['login_time']) ? date('Y-m-d H:i:s', $_SESSION['login_time']) : 'Bilinmiyor'; ?></li>
                                <li><strong>Son Aktivite:</strong> <?php echo isset($_SESSION['last_activity']) ? date('Y-m-d H:i:s', $_SESSION['last_activity']) : 'Bilinmiyor'; ?></li>
                                <li><strong>Session ID:</strong> <?php echo substr(session_id(), 0, 16) . '...'; ?></li>
                                <li><strong>Kalan Süre:</strong> <span id="remainingTime"><?php echo 3600 - (time() - ($_SESSION['last_activity'] ?? time())); ?></span> saniye</li>
                            </ul>
                        </div>

                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">Test Butonları</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="d-grid gap-2">
                                            <button class="btn btn-primary" onclick="refreshSession()">
                                                <i class="bi bi-arrow-clockwise me-2"></i>Session Yenile
                                            </button>
                                            <a href="?test=timeout" class="btn btn-warning">
                                                <i class="bi bi-clock me-2"></i>Timeout Simüle Et
                                            </a>
                                            <button class="btn btn-info" onclick="checkSessionStatus()">
                                                <i class="bi bi-info-circle me-2"></i>Session Durumu Kontrol Et
                                            </button>
                                            <a href="dashboard.php" class="btn btn-success">
                                                <i class="bi bi-house me-2"></i>Dashboard'a Git
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">Session Timer</h6>
                                    </div>
                                    <div class="card-body text-center">
                                        <div class="display-6" id="timerDisplay">--:--</div>
                                        <small class="text-muted">Kalan süre</small>
                                        <div class="progress mt-3">
                                            <div class="progress-bar" id="timerProgress" role="progressbar" style="width: 100%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4">
                            <h6>Test Senaryoları:</h6>
                            <ol>
                                <li><strong>Normal Kullanım:</strong> Sayfa aktivitesi session'ı otomatik yeniler</li>
                                <li><strong>5 Dakika Uyarısı:</strong> Session süresi 5 dakika kala uyarı gösterir</li>
                                <li><strong>Timeout:</strong> 1 saat sonra otomatik çıkış yapar</li>
                                <li><strong>Manual Refresh:</strong> "Session Yenile" butonu ile manuel yenileme</li>
                                <li><strong>Timeout Simülasyonu:</strong> "Timeout Simüle Et" ile anında test</li>
                            </ol>
                        </div>

                        <div class="alert alert-warning mt-3">
                            <h6><i class="bi bi-exclamation-triangle me-2"></i>Önemli Notlar</h6>
                            <ul class="mb-0">
                                <li>Session timeout süresi: <strong>1 saat (3600 saniye)</strong></li>
                                <li>Uyarı süresi: <strong>5 dakika (300 saniye)</strong> kala</li>
                                <li>Aktivite algılama: Mouse, keyboard, click, scroll</li>
                                <li>Otomatik yenileme: 30 dakika aktivitesizlik sonrası</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Session refresh fonksiyonu
        function refreshSession() {
            fetch('includes/refresh-session.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    alert('Session başarıyla yenilendi!');
                    location.reload();
                } else {
                    alert('Session yenileme hatası: ' + data.message);
                }
            })
            .catch(error => {
                alert('Hata: ' + error.message);
            });
        }

        // Session durumu kontrol et
        function checkSessionStatus() {
            fetch('includes/refresh-session.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                alert('Session Durumu: ' + data.status + '\nMesaj: ' + data.message);
            })
            .catch(error => {
                alert('Session kontrol hatası: ' + error.message);
            });
        }

        // Timer güncelleme
        let remainingSeconds = <?php echo 3600 - (time() - ($_SESSION['last_activity'] ?? time())); ?>;
        
        function updateTimer() {
            if (remainingSeconds <= 0) {
                document.getElementById('timerDisplay').textContent = '00:00';
                document.getElementById('timerProgress').style.width = '0%';
                document.getElementById('timerProgress').className = 'progress-bar bg-danger';
                alert('Session süresi doldu!');
                return;
            }
            
            const hours = Math.floor(remainingSeconds / 3600);
            const minutes = Math.floor((remainingSeconds % 3600) / 60);
            const seconds = remainingSeconds % 60;
            
            const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            document.getElementById('timerDisplay').textContent = timeString;
            
            const percentage = (remainingSeconds / 3600) * 100;
            document.getElementById('timerProgress').style.width = percentage + '%';
            
            if (percentage < 10) {
                document.getElementById('timerProgress').className = 'progress-bar bg-danger';
            } else if (percentage < 25) {
                document.getElementById('timerProgress').className = 'progress-bar bg-warning';
            } else {
                document.getElementById('timerProgress').className = 'progress-bar bg-success';
            }
            
            document.getElementById('remainingTime').textContent = remainingSeconds;
            
            remainingSeconds--;
            setTimeout(updateTimer, 1000);
        }
        
        // Timer'ı başlat
        updateTimer();
    </script>

    <!-- Session Timeout Script -->
    <?php echo getSessionTimeoutScript(); ?>
</body>
</html>
