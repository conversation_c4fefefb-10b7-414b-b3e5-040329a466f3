<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Giriş kontrolü
requireLogin();

// Sad<PERSON>e aile kullanıcıları erişebilir
if ($_SESSION['user_type'] !== 'family') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Yetkisiz erişim']);
    exit;
}

// POST isteği kontrolü
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Geçersiz istek metodu']);
    exit;
}

// JSON response header
header('Content-Type: application/json');

$job_id = safeNumber(safeArray($_POST, 'job_id', 0));
$action = safeArray($_POST, 'action', '');
$user_id = $_SESSION['user_id'];

if (!$job_id || !$action) {
    echo json_encode(['success' => false, 'message' => 'Eksik parametreler']);
    exit;
}

try {
    // İş ilanının sahiplik kontrolü
    $sql = "SELECT id, title, status FROM job_listings WHERE id = ? AND user_id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$job_id, $user_id]);
    $job = $stmt->fetch();
    
    if (!$job) {
        echo json_encode(['success' => false, 'message' => 'İş ilanı bulunamadı veya size ait değil']);
        exit;
    }
    
    $new_status = '';
    $message = '';
    
    switch ($action) {
        case 'pause':
            if ($job['status'] !== 'active') {
                echo json_encode(['success' => false, 'message' => 'Sadece aktif ilanlar duraklatılabilir']);
                exit;
            }
            $new_status = 'paused';
            $message = 'İlan başarıyla duraklatıldı';
            break;
            
        case 'activate':
            if (!in_array($job['status'], ['paused', 'draft'])) {
                echo json_encode(['success' => false, 'message' => 'Bu ilan aktifleştirilemez']);
                exit;
            }
            $new_status = 'active';
            $message = 'İlan başarıyla aktifleştirildi';
            break;
            
        case 'close':
            if ($job['status'] === 'closed') {
                echo json_encode(['success' => false, 'message' => 'İlan zaten kapatılmış']);
                exit;
            }
            $new_status = 'closed';
            $message = 'İlan başarıyla kapatıldı';
            break;
            
        case 'delete':
            // Soft delete - status'u deleted yapmak yerine gerçekten sil
            $delete_sql = "DELETE FROM job_listings WHERE id = ? AND user_id = ?";
            $delete_stmt = $db->prepare($delete_sql);
            $delete_stmt->execute([$job_id, $user_id]);
            
            // Aktivite kaydı
            logActivity($user_id, 'job_delete', 'İş ilanını sildi: ' . $job['title'], 'job_listings', $job_id);
            
            echo json_encode([
                'success' => true, 
                'message' => 'İlan başarıyla silindi',
                'action' => 'delete'
            ]);
            exit;
            
        default:
            echo json_encode(['success' => false, 'message' => 'Geçersiz işlem']);
            exit;
    }
    
    // Durumu güncelle
    $update_sql = "UPDATE job_listings SET status = ?, updated_at = NOW() WHERE id = ? AND user_id = ?";
    $update_stmt = $db->prepare($update_sql);
    $update_stmt->execute([$new_status, $job_id, $user_id]);
    
    // Aktivite kaydı
    $action_desc = [
        'pause' => 'İş ilanını duraklattı',
        'activate' => 'İş ilanını aktifleştirdi', 
        'close' => 'İş ilanını kapattı'
    ];
    
    logActivity($user_id, 'job_' . $action, $action_desc[$action] . ': ' . $job['title'], 'job_listings', $job_id);
    
    // Başarı yanıtı
    echo json_encode([
        'success' => true,
        'message' => $message,
        'new_status' => $new_status,
        'action' => $action
    ]);
    
} catch (PDOException $e) {
    error_log("Job management error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Veritabanı hatası oluştu']);
}
?>
