<?php
require_once 'session.php';

// Sadece AJAX isteklerini kabul et
if (empty($_SERVER['HTTP_X_REQUESTED_WITH']) || 
    strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) != 'xmlhttprequest') {
    http_response_code(403);
    exit('Forbidden');
}

// Sadece POST isteklerini kabul et
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    exit('Method Not Allowed');
}

header('Content-Type: application/json');

try {
    // Kullanıcı giriş yapmış mı kontrol et
    if (!isLoggedIn()) {
        echo json_encode([
            'status' => 'error',
            'message' => 'Kullanıcı giriş yapmamış',
            'code' => 'NOT_LOGGED_IN'
        ]);
        exit;
    }
    
    // Session süresi dolmuş mu kontrol et
    if (isSessionExpired()) {
        echo json_encode([
            'status' => 'error',
            'message' => 'Session süresi dolmuş',
            'code' => 'SESSION_EXPIRED'
        ]);
        exit;
    }
    
    // Session'ı yenile
    $_SESSION['last_activity'] = time();
    
    // Kullanıcı bilgilerini veritabanından kontrol et (güvenlik)
    global $db;
    $user_check_sql = "SELECT id, status FROM users WHERE id = ? AND status = 'active'";
    $user_check_stmt = $db->prepare($user_check_sql);
    $user_check_stmt->execute([$_SESSION['user_id']]);
    $user = $user_check_stmt->fetch();
    
    if (!$user) {
        // Kullanıcı bulunamadı veya aktif değil
        destroySession();
        echo json_encode([
            'status' => 'error',
            'message' => 'Kullanıcı hesabı bulunamadı veya aktif değil',
            'code' => 'USER_NOT_FOUND'
        ]);
        exit;
    }
    
    // Başarılı response
    echo json_encode([
        'status' => 'success',
        'message' => 'Session yenilendi',
        'data' => [
            'last_activity' => $_SESSION['last_activity'],
            'remaining_time' => 3600, // 1 saat
            'user_id' => $_SESSION['user_id']
        ]
    ]);
    
} catch (Exception $e) {
    // Hata durumu
    echo json_encode([
        'status' => 'error',
        'message' => 'Session yenileme hatası: ' . $e->getMessage(),
        'code' => 'REFRESH_ERROR'
    ]);
}
?>
