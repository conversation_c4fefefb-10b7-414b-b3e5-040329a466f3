<?php
/**
 * Trendyol E-Faturam OpenCart 3.0.3.2 Extension
 * Catalog Controller - Handles automatic invoice sending
 * 
 * <AUTHOR> Name
 * @version 1.0.0
 * @license MIT
 */

class ControllerExtensionModuleTrendyolEfaturam extends Controller {
    
    /**
     * Send invoice when order status changes
     * This method is called via event system
     */
    public function sendInvoice($route, $args) {
        // Check if module is enabled
        if (!$this->config->get('module_trendyol_efaturam_status')) {
            return;
        }
        
        // Check if auto send is enabled
        if (!$this->config->get('module_trendyol_efaturam_auto_send')) {
            return;
        }
        
        $order_id = $args[0];
        $order_status_id = $args[1];
        
        // Only send invoice when order is completed (status 5 by default)
        $completed_status_ids = array(5, 15, 17); // Complete, Processed, Shipped
        
        if (!in_array($order_status_id, $completed_status_ids)) {
            return;
        }
        
        $this->load->model('checkout/order');
        $order_info = $this->model_checkout_order->getOrder($order_id);
        
        if (!$order_info) {
            return;
        }
        
        // Check if invoice already sent
        if ($this->isInvoiceAlreadySent($order_id)) {
            return;
        }
        
        try {
            $this->processInvoice($order_info);
        } catch (Exception $e) {
            $this->log('Error sending invoice for order ' . $order_id . ': ' . $e->getMessage());
        }
    }
    
    /**
     * Process invoice sending
     */
    private function processInvoice($order_info) {
        $this->load->library('trendyol_efaturam_api');
        
        // Initialize API
        $api = new TrendyolEfaturamApi(
            $this->config->get('module_trendyol_efaturam_api_url'),
            $this->config->get('module_trendyol_efaturam_username'),
            $this->config->get('module_trendyol_efaturam_password'),
            $this->config->get('module_trendyol_efaturam_mode') == 'test'
        );
        
        // Prepare invoice data
        $invoice_data = $this->prepareInvoiceData($order_info);
        
        // Determine if customer is corporate or individual
        $is_corporate = $this->isCorporateCustomer($order_info);
        
        if ($is_corporate) {
            // Send E-Invoice for corporate customers
            $result = $api->sendInvoice($invoice_data);
        } else {
            // Send E-Archive Invoice for individual customers
            $result = $api->sendArchiveInvoice($invoice_data);
        }
        
        // Log result and save to database
        if ($result['success']) {
            $this->saveInvoiceRecord($order_info['order_id'], $result['data'], $is_corporate ? 'e-invoice' : 'e-archive');
            $this->log('Invoice sent successfully for order ' . $order_info['order_id']);
        } else {
            $this->log('Failed to send invoice for order ' . $order_info['order_id'] . ': ' . $result['error']);
            throw new Exception($result['error']);
        }
    }
    
    /**
     * Prepare invoice data from order information
     */
    private function prepareInvoiceData($order_info) {
        $this->load->model('checkout/order');
        
        // Get order products
        $order_products = $this->model_checkout_order->getOrderProducts($order_info['order_id']);
        
        // Prepare products array
        $products = array();
        foreach ($order_products as $product) {
            $products[] = array(
                'name' => $product['name'],
                'quantity' => $product['quantity'],
                'price' => $product['price'],
                'total' => $product['total'],
                'tax' => $product['tax'],
                'tax_rate' => $this->getTaxRate($product['tax'], $product['total'])
            );
        }
        
        // Get seller information from config
        $seller_info = array(
            'company_name' => $this->config->get('module_trendyol_efaturam_company_name'),
            'tax_number' => $this->config->get('module_trendyol_efaturam_company_tax_number'),
            'tax_office' => $this->config->get('module_trendyol_efaturam_company_tax_office'),
            'alias' => $this->config->get('module_trendyol_efaturam_username'),
            'address' => $this->config->get('config_address'),
            'city' => $this->config->get('config_city'),
            'postcode' => $this->config->get('config_postcode'),
            'country' => $this->config->get('config_country'),
            'district' => ''
        );
        
        // Get buyer information from order
        $buyer_info = array(
            'company_name' => $order_info['payment_company'] ?: $order_info['payment_firstname'] . ' ' . $order_info['payment_lastname'],
            'tax_number' => $this->extractTaxNumber($order_info),
            'tax_office' => $this->extractTaxOffice($order_info),
            'tc_number' => $this->extractTcNumber($order_info),
            'address' => $order_info['payment_address_1'] . ' ' . $order_info['payment_address_2'],
            'city' => $order_info['payment_city'],
            'district' => $order_info['payment_zone'],
            'postcode' => $order_info['payment_postcode'],
            'country' => $order_info['payment_country']
        );
        
        return array(
            'order_id' => $order_info['order_id'],
            'invoice_number' => $this->generateInvoiceNumber($order_info['order_id']),
            'date_added' => $order_info['date_added'],
            'currency_code' => $order_info['currency_code'],
            'exchange_rate' => $order_info['currency_value'],
            'seller' => $seller_info,
            'buyer' => $buyer_info,
            'products' => $products
        );
    }
    
    /**
     * Check if customer is corporate
     */
    private function isCorporateCustomer($order_info) {
        // Check if company name exists and tax number is provided
        return !empty($order_info['payment_company']) && !empty($this->extractTaxNumber($order_info));
    }
    
    /**
     * Extract tax number from order custom fields or payment info
     */
    private function extractTaxNumber($order_info) {
        // Try to get from custom fields first
        $this->load->model('checkout/order');
        $custom_fields = $this->model_checkout_order->getOrderCustomFields($order_info['order_id']);
        
        foreach ($custom_fields as $custom_field) {
            if (stripos($custom_field['name'], 'vergi') !== false && stripos($custom_field['name'], 'no') !== false) {
                return $custom_field['value'];
            }
        }
        
        // If not found in custom fields, try to extract from payment info
        // This would need to be customized based on your checkout form
        return '';
    }
    
    /**
     * Extract tax office from order info
     */
    private function extractTaxOffice($order_info) {
        $this->load->model('checkout/order');
        $custom_fields = $this->model_checkout_order->getOrderCustomFields($order_info['order_id']);
        
        foreach ($custom_fields as $custom_field) {
            if (stripos($custom_field['name'], 'vergi') !== false && stripos($custom_field['name'], 'daire') !== false) {
                return $custom_field['value'];
            }
        }
        
        return '';
    }
    
    /**
     * Extract TC number for individual customers
     */
    private function extractTcNumber($order_info) {
        $this->load->model('checkout/order');
        $custom_fields = $this->model_checkout_order->getOrderCustomFields($order_info['order_id']);
        
        foreach ($custom_fields as $custom_field) {
            if (stripos($custom_field['name'], 'tc') !== false || stripos($custom_field['name'], 'kimlik') !== false) {
                return $custom_field['value'];
            }
        }
        
        return '';
    }
    
    /**
     * Calculate tax rate from tax amount and total
     */
    private function getTaxRate($tax_amount, $total_amount) {
        if ($total_amount > 0) {
            return round(($tax_amount / $total_amount) * 100, 2);
        }
        return 0;
    }
    
    /**
     * Generate invoice number
     */
    private function generateInvoiceNumber($order_id) {
        return 'TY' . date('Y') . str_pad($order_id, 6, '0', STR_PAD_LEFT);
    }
    
    /**
     * Check if invoice already sent for this order
     */
    private function isInvoiceAlreadySent($order_id) {
        $query = $this->db->query("SELECT invoice_id FROM " . DB_PREFIX . "trendyol_efaturam_invoices WHERE order_id = '" . (int)$order_id . "'");
        return $query->num_rows > 0;
    }
    
    /**
     * Save invoice record to database
     */
    private function saveInvoiceRecord($order_id, $invoice_data, $invoice_type) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "trendyol_efaturam_invoices SET 
            order_id = '" . (int)$order_id . "',
            invoice_number = '" . $this->db->escape($invoice_data['invoiceNumber'] ?? '') . "',
            invoice_type = '" . $this->db->escape($invoice_type) . "',
            status = '" . $this->db->escape($invoice_data['status'] ?? 'sent') . "',
            api_response = '" . $this->db->escape(json_encode($invoice_data)) . "',
            date_created = NOW()
        ");
    }
    
    /**
     * Log messages
     */
    private function log($message) {
        $log = new Log('trendyol_efaturam.log');
        $log->write($message);
    }
}
