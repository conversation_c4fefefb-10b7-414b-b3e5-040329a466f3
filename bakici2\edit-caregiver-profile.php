<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Giriş kontrolü
if (!isLoggedIn()) {
    header('Location: auth/login.php');
    exit;
}

// Sad<PERSON>e bakıcılar erişebilir
if ($_SESSION['user_type'] !== 'caregiver') {
    header('Location: dashboard.php');
    exit;
}

$user_id = $_SESSION['user_id'];

// Mevcut profil bilgilerini getir
try {
    $sql = "SELECT cp.* FROM caregiver_profiles cp WHERE cp.user_id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$user_id]);
    $profile = $stmt->fetch();
    
    // Eğer profil yoksa boş bir array oluştur
    if (!$profile) {
        $profile = [
            'experience_years' => 0,
            'hourly_rate' => '',
            'daily_rate' => '',
            'monthly_rate' => '',
            'specializations' => '',
            'bio' => '',
            'gender' => '',
            'education_level' => '',
            'languages' => '',
            'certificates' => '',
            'availability' => '',
            'work_preferences' => '',
            'is_available' => 1
        ];
    }
} catch (PDOException $e) {
    $error = 'Profil bilgileri alınamadı.';
}

// Form gönderildi
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $experience_years = intval($_POST['experience_years'] ?? 0);
    $hourly_rate = floatval($_POST['hourly_rate'] ?? 0);
    $daily_rate = floatval($_POST['daily_rate'] ?? 0);
    $monthly_rate = floatval($_POST['monthly_rate'] ?? 0);
    $specializations = trim($_POST['specializations'] ?? '');
    $bio = trim($_POST['bio'] ?? '');
    $gender = $_POST['gender'] ?? '';
    $education_level = trim($_POST['education_level'] ?? '');
    $languages = trim($_POST['languages'] ?? '');
    $certificates = trim($_POST['certificates'] ?? '');
    $availability = trim($_POST['availability'] ?? '');
    $work_preferences = trim($_POST['work_preferences'] ?? '');
    $is_available = isset($_POST['is_available']) ? 1 : 0;
    
    $errors = [];
    
    // Validasyon
    if ($experience_years < 0 || $experience_years > 50) {
        $errors[] = 'Deneyim yılı 0-50 arasında olmalıdır.';
    }
    
    if ($hourly_rate < 0 || $hourly_rate > 1000) {
        $errors[] = 'Saatlik ücret 0-1000 TL arasında olmalıdır.';
    }
    
    if ($daily_rate < 0 || $daily_rate > 5000) {
        $errors[] = 'Günlük ücret 0-5000 TL arasında olmalıdır.';
    }
    
    if ($monthly_rate < 0 || $monthly_rate > 50000) {
        $errors[] = 'Aylık ücret 0-50000 TL arasında olmalıdır.';
    }
    
    // Güncelleme veya ekleme
    if (empty($errors)) {
        try {
            if ($profile && isset($profile['user_id'])) {
                // Güncelleme
                $update_sql = "UPDATE caregiver_profiles SET 
                              experience_years = ?, hourly_rate = ?, daily_rate = ?, monthly_rate = ?,
                              specializations = ?, bio = ?, gender = ?, education_level = ?,
                              languages = ?, certificates = ?, availability = ?, work_preferences = ?,
                              is_available = ?, updated_at = NOW()
                              WHERE user_id = ?";
                $update_stmt = $db->prepare($update_sql);
                $update_stmt->execute([
                    $experience_years, $hourly_rate ?: null, $daily_rate ?: null, $monthly_rate ?: null,
                    $specializations, $bio, $gender, $education_level,
                    $languages, $certificates, $availability, $work_preferences,
                    $is_available, $user_id
                ]);
            } else {
                // Yeni profil oluşturma
                $insert_sql = "INSERT INTO caregiver_profiles 
                              (user_id, experience_years, hourly_rate, daily_rate, monthly_rate,
                               specializations, bio, gender, education_level, languages, 
                               certificates, availability, work_preferences, is_available)
                              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $insert_stmt = $db->prepare($insert_sql);
                $insert_stmt->execute([
                    $user_id, $experience_years, $hourly_rate ?: null, $daily_rate ?: null, $monthly_rate ?: null,
                    $specializations, $bio, $gender, $education_level, $languages,
                    $certificates, $availability, $work_preferences, $is_available
                ]);
            }
            
            header('Location: caregiver-profile.php?id=' . $user_id . '&success=profile_updated');
            exit;
            
        } catch (PDOException $e) {
            $errors[] = 'Profil güncellenirken hata oluştu: ' . $e->getMessage();
        }
    }
}

$page_title = 'Bakıcı Profili Düzenle';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bakıcı Platform</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c5aa0;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="index.php">
                <i class="bi bi-heart-fill me-2"></i>Bakıcı Platform
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.php">Dashboard</a>
                <a class="nav-link" href="caregiver-profile.php?id=<?php echo $user_id; ?>">Profilim</a>
                <a class="nav-link" href="auth/logout.php">Çıkış</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="bi bi-pencil me-2"></i>Bakıcı Profili Düzenle</h4>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0">
                                <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                                <li class="breadcrumb-item"><a href="caregiver-profile.php?id=<?php echo $user_id; ?>">Profilim</a></li>
                                <li class="breadcrumb-item active">Düzenle</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="card-body">
                        <!-- Errors -->
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <form method="POST">
                            <div class="row g-3">
                                <!-- Temel Bilgiler -->
                                <div class="col-12">
                                    <h5 class="border-bottom pb-2">Temel Bilgiler</h5>
                                </div>
                                
                                <div class="col-md-4">
                                    <label for="experience_years" class="form-label">Deneyim (Yıl)</label>
                                    <input type="number" class="form-control" id="experience_years" name="experience_years" 
                                           value="<?php echo $profile['experience_years']; ?>" min="0" max="50">
                                </div>
                                
                                <div class="col-md-4">
                                    <label for="gender" class="form-label">Cinsiyet</label>
                                    <select class="form-select" id="gender" name="gender">
                                        <option value="">Seçiniz</option>
                                        <option value="female" <?php echo $profile['gender'] === 'female' ? 'selected' : ''; ?>>Kadın</option>
                                        <option value="male" <?php echo $profile['gender'] === 'male' ? 'selected' : ''; ?>>Erkek</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-4">
                                    <label for="education_level" class="form-label">Eğitim Seviyesi</label>
                                    <select class="form-select" id="education_level" name="education_level">
                                        <option value="">Seçiniz</option>
                                        <option value="İlkokul" <?php echo $profile['education_level'] === 'İlkokul' ? 'selected' : ''; ?>>İlkokul</option>
                                        <option value="Ortaokul" <?php echo $profile['education_level'] === 'Ortaokul' ? 'selected' : ''; ?>>Ortaokul</option>
                                        <option value="Lise" <?php echo $profile['education_level'] === 'Lise' ? 'selected' : ''; ?>>Lise</option>
                                        <option value="Meslek Lisesi" <?php echo $profile['education_level'] === 'Meslek Lisesi' ? 'selected' : ''; ?>>Meslek Lisesi</option>
                                        <option value="Üniversite" <?php echo $profile['education_level'] === 'Üniversite' ? 'selected' : ''; ?>>Üniversite</option>
                                        <option value="Yüksek Lisans" <?php echo $profile['education_level'] === 'Yüksek Lisans' ? 'selected' : ''; ?>>Yüksek Lisans</option>
                                    </select>
                                </div>
                                
                                <!-- Ücret Bilgileri -->
                                <div class="col-12 mt-4">
                                    <h5 class="border-bottom pb-2">Ücret Bilgileri</h5>
                                </div>
                                
                                <div class="col-md-4">
                                    <label for="hourly_rate" class="form-label">Saatlik Ücret (₺)</label>
                                    <input type="number" class="form-control" id="hourly_rate" name="hourly_rate" 
                                           value="<?php echo $profile['hourly_rate']; ?>" min="0" max="1000" step="0.01">
                                </div>
                                
                                <div class="col-md-4">
                                    <label for="daily_rate" class="form-label">Günlük Ücret (₺)</label>
                                    <input type="number" class="form-control" id="daily_rate" name="daily_rate" 
                                           value="<?php echo $profile['daily_rate']; ?>" min="0" max="5000" step="0.01">
                                </div>
                                
                                <div class="col-md-4">
                                    <label for="monthly_rate" class="form-label">Aylık Ücret (₺)</label>
                                    <input type="number" class="form-control" id="monthly_rate" name="monthly_rate" 
                                           value="<?php echo $profile['monthly_rate']; ?>" min="0" max="50000" step="0.01">
                                </div>
                                
                                <!-- Uzmanlık ve Diller -->
                                <div class="col-12 mt-4">
                                    <h5 class="border-bottom pb-2">Uzmanlık ve Yetenekler</h5>
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="specializations" class="form-label">Uzmanlık Alanları</label>
                                    <textarea class="form-control" id="specializations" name="specializations" rows="3" 
                                              placeholder="Örn: Çocuk bakımı, Yaşlı bakımı, Hasta bakımı"><?php echo htmlspecialchars($profile['specializations']); ?></textarea>
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="languages" class="form-label">Konuştuğu Diller</label>
                                    <input type="text" class="form-control" id="languages" name="languages" 
                                           value="<?php echo htmlspecialchars($profile['languages']); ?>" 
                                           placeholder="Örn: Türkçe, İngilizce, Almanca">
                                </div>
                                
                                <div class="col-12">
                                    <label for="certificates" class="form-label">Sertifikalar ve Kurslar</label>
                                    <textarea class="form-control" id="certificates" name="certificates" rows="2" 
                                              placeholder="Örn: İlk Yardım Sertifikası, Çocuk Gelişimi Kursu"><?php echo htmlspecialchars($profile['certificates']); ?></textarea>
                                </div>
                                
                                <!-- Çalışma Tercihleri -->
                                <div class="col-12 mt-4">
                                    <h5 class="border-bottom pb-2">Çalışma Tercihleri</h5>
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="availability" class="form-label">Müsaitlik</label>
                                    <textarea class="form-control" id="availability" name="availability" rows="3" 
                                              placeholder="Örn: Hafta içi 08:00-18:00, Hafta sonu müsait"><?php echo htmlspecialchars($profile['availability']); ?></textarea>
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="work_preferences" class="form-label">Çalışma Tercihleri</label>
                                    <textarea class="form-control" id="work_preferences" name="work_preferences" rows="3" 
                                              placeholder="Örn: Yatılı çalışma tercih etmem, günlük çalışma uygun"><?php echo htmlspecialchars($profile['work_preferences']); ?></textarea>
                                </div>
                                
                                <div class="col-12">
                                    <label for="bio" class="form-label">Hakkımda</label>
                                    <textarea class="form-control" id="bio" name="bio" rows="4" 
                                              placeholder="Kendinizi tanıtın, deneyimlerinizi ve yaklaşımınızı anlatın..."><?php echo htmlspecialchars($profile['bio']); ?></textarea>
                                </div>
                                
                                <div class="col-12">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_available" name="is_available"
                                               <?php echo isset($profile['is_available']) && $profile['is_available'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="is_available">
                                            Şu anda yeni işler için müsaitim
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-between mt-4">
                                <a href="caregiver-profile.php?id=<?php echo $user_id; ?>" class="btn btn-secondary">
                                    <i class="bi bi-arrow-left me-2"></i>Geri Dön
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check me-2"></i>Profili Güncelle
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
