# Changelog 4.0

## [v4.0.5](https://github.com/codeigniter4/CodeIgniter4/tree/v4.0.5) (2021-01-31)

[Full Changelog](https://github.com/codeigniter4/CodeIgniter4/compare/v4.0.4...v4.0.5)

**Deprecations:**

- `CodeIgniter\Database\ModelFactory` is now deprecated in favor of `CodeIgniter\Config\Factories::models()`
- `CodeIgniter\Config\Config` is now deprecated in favor of `CodeIgniter\Config\Factories::config()`
- HTTP Layer Refactor: Numerous deprecations have been made towards a transition to a PSR-compliant HTTP layer. [See the User Guide](user_guide_src/source/installation/upgrade_405.rst)

**Mime Type Detection**

- `Config\Mimes::guessExtensionFromType` now only reverse searches the `$mimes` array if no extension is proposed (i.e., usually not for uploaded files).
- The fallback values of `UploadedFile->getExtension()` and `UploadedFile->guessExtension()` have been changed. `UploadedFile->getExtension()` now returns `$this->getClientExtension()` instead of `''`; `UploadedFile->guessExtension()` now returns `''` instead of `$this->getClientExtension()`.
These changes increase security when handling uploaded files as the client can no longer force a wrong mime type on the application. However, these might affect how file extensions are detected in your application.

**Implemented enhancements:**

- Bug: controller routing on modules not working [\#3927](https://github.com/codeigniter4/CodeIgniter4/issues/3927)
- CLI: method prompt should accept array validation rules [\#3766](https://github.com/codeigniter4/CodeIgniter4/issues/3766)
- Validation: permit\_empty, optional arguments [\#3670](https://github.com/codeigniter4/CodeIgniter4/issues/3670)
- php 8 support [\#3498](https://github.com/codeigniter4/CodeIgniter4/issues/3498)
- getRoutesOptions should return the controller and method if available [\#3445](https://github.com/codeigniter4/CodeIgniter4/issues/3445)
- before function in FilterInterface is missing response param [\#2085](https://github.com/codeigniter4/CodeIgniter4/issues/2085)
- Feature Request: Centralized loggedInUser before RC [\#2055](https://github.com/codeigniter4/CodeIgniter4/issues/2055)

**Fixed bugs:**

- Bug: UploadedFile::store\(\) can't return null [\#4183](https://github.com/codeigniter4/CodeIgniter4/issues/4183)
- Bug: BaseBuilder::getCompiledDelete\(\) runs real query [\#4180](https://github.com/codeigniter4/CodeIgniter4/issues/4180)
- Bug: Deprecated: Required parameter $userAgent follows optional parameter $body in /opt/lampp/htdocs/framework-4.0.4/system/HTTP/IncomingRequest.php on line 161 [\#4172](https://github.com/codeigniter4/CodeIgniter4/issues/4172)
- Bug: table template closes tbody after tfoot [\#4155](https://github.com/codeigniter4/CodeIgniter4/issues/4155)
- Bug: delete\_cookie\(\) helper not working [\#4149](https://github.com/codeigniter4/CodeIgniter4/issues/4149)
- Bug: Required parameter $userAgent follows optional parameter $body in [\#4148](https://github.com/codeigniter4/CodeIgniter4/issues/4148)
- Bug: spark issue [\#4144](https://github.com/codeigniter4/CodeIgniter4/issues/4144)
- Bug: PostgreSQL driver issues [\#4142](https://github.com/codeigniter4/CodeIgniter4/issues/4142)
- Bug: phpunit coverage report causes `Cannot declare class Config\App, because the name is already in use` [\#4114](https://github.com/codeigniter4/CodeIgniter4/issues/4114)
- Bug: Wrong file/line in exceptions created by "factory" methods  [\#4110](https://github.com/codeigniter4/CodeIgniter4/issues/4110)
- Bug: Request::withMethod\(\) uses deprecated code [\#4109](https://github.com/codeigniter4/CodeIgniter4/issues/4109)
- Bug: View rendering,  extracted variables overwrites existed variables\(arguments and etc.\) [\#4108](https://github.com/codeigniter4/CodeIgniter4/issues/4108)
- Bug: Loss of escape value and data in the model  [\#4087](https://github.com/codeigniter4/CodeIgniter4/issues/4087)
- Bug: classes overwrites parameter defaults, phpDoc's defined in interfaces [\#4086](https://github.com/codeigniter4/CodeIgniter4/issues/4086)
- Bug: getRandomName Return file extension .csv when upload docx or xlsx becouse mimetype application/octet-stream are set  [\#4084](https://github.com/codeigniter4/CodeIgniter4/issues/4084)
- Bug: Required parameter $from follows optional parameter $verbs [\#4076](https://github.com/codeigniter4/CodeIgniter4/issues/4076)
- Bug: username input form shows root as a default value [\#4062](https://github.com/codeigniter4/CodeIgniter4/issues/4062)
- Bug: Issue with pagination [\#4045](https://github.com/codeigniter4/CodeIgniter4/issues/4045)
- Bug: Model calls builder before initializing [\#4036](https://github.com/codeigniter4/CodeIgniter4/issues/4036)
- Bug: can't run migrations when CI\_ENVIRONMENT = testing [\#4033](https://github.com/codeigniter4/CodeIgniter4/issues/4033)
- Bug: can't get environment variable with dot [\#4026](https://github.com/codeigniter4/CodeIgniter4/issues/4026)
- Bug: CodeIgniter model not recognizing custom deletedField in find queries [\#3999](https://github.com/codeigniter4/CodeIgniter4/issues/3999)
- Bug: assertSee\(\) can not assert title tag. [\#3984](https://github.com/codeigniter4/CodeIgniter4/issues/3984)
- Bug: RAR file detected as CSV [\#3979](https://github.com/codeigniter4/CodeIgniter4/issues/3979)
- Bug: Session Initialization via DatabaseHandler [\#3978](https://github.com/codeigniter4/CodeIgniter4/issues/3978)
- Bug: required\_with validation rule does not work with arrays [\#3965](https://github.com/codeigniter4/CodeIgniter4/issues/3965)
- Bug: helper cookie not working [\#3939](https://github.com/codeigniter4/CodeIgniter4/issues/3939)
- Bug: Uploaded SRT files are saved as CSV [\#3921](https://github.com/codeigniter4/CodeIgniter4/issues/3921)
- Bug: Pre-commit hook for Phpstan and sniffer stop working after e111f04d74569e413c5aede3ed9bd9fa1ce7dca2  [\#3920](https://github.com/codeigniter4/CodeIgniter4/issues/3920)
- Bug: Documentation Example Leads to Bug [\#3914](https://github.com/codeigniter4/CodeIgniter4/issues/3914)
- Bug: Route filter runs twice [\#3902](https://github.com/codeigniter4/CodeIgniter4/issues/3902)
- Bug: Premature empty check in Model-\>update function. [\#3896](https://github.com/codeigniter4/CodeIgniter4/issues/3896)
- Bug: Sqldrv problems to insert when change DBPrefix  [\#3881](https://github.com/codeigniter4/CodeIgniter4/issues/3881)
- Bug: CI4 won't recognise current namespace for language files [\#3867](https://github.com/codeigniter4/CodeIgniter4/issues/3867)
- Unexpected validation bug [\#3859](https://github.com/codeigniter4/CodeIgniter4/issues/3859)
- Bug: single\_service does not set service's arguments correctly [\#3854](https://github.com/codeigniter4/CodeIgniter4/issues/3854)
- Bug: debugbar should not insert code in code tag [\#3847](https://github.com/codeigniter4/CodeIgniter4/issues/3847)
- Parser won't properly parse tags that have a similar name [\#3841](https://github.com/codeigniter4/CodeIgniter4/issues/3841)
- Bug: insertBatch not generating createdField [\#3838](https://github.com/codeigniter4/CodeIgniter4/issues/3838)
- Bug: Parser - Replacements happening even if key does not fully matches { value } [\#3825](https://github.com/codeigniter4/CodeIgniter4/issues/3825)
- Bug: Parser preg\_replace\_callback exception if template has \# [\#3824](https://github.com/codeigniter4/CodeIgniter4/issues/3824)
- Translation function 'lang' does not work as described [\#3822](https://github.com/codeigniter4/CodeIgniter4/issues/3822)
- Bug: File rewrite.php is always lost in large-request unhandled error [\#3818](https://github.com/codeigniter4/CodeIgniter4/issues/3818)
- Bug: Model::builder\(\) ignores parameter [\#3793](https://github.com/codeigniter4/CodeIgniter4/issues/3793)
- Bug: Warning no tests found in CodeIgiter\Tests\... [\#3788](https://github.com/codeigniter4/CodeIgniter4/issues/3788)
- Bug: Class '\CodeIgniter\Database\pdo\Connection' not found  [\#3785](https://github.com/codeigniter4/CodeIgniter4/issues/3785)
- Bug: Composer php spark migrate error [\#3771](https://github.com/codeigniter4/CodeIgniter4/issues/3771)
- Bug: ORDER BY RANDOM ON SQLite3 [\#3768](https://github.com/codeigniter4/CodeIgniter4/issues/3768)
- Bug: CLI: generateDimensions fails with a uncaught exception when exec is disabled [\#3762](https://github.com/codeigniter4/CodeIgniter4/issues/3762)
- Bug: createTable if not exists not working [\#3757](https://github.com/codeigniter4/CodeIgniter4/issues/3757)
- Bug: SQLite drop column corrupts table cache [\#3752](https://github.com/codeigniter4/CodeIgniter4/issues/3752)
- Bug: route filters don't apply for different methods [\#3733](https://github.com/codeigniter4/CodeIgniter4/issues/3733)
- /system/Images/Image.php image\(\)-\>copy\(\) mkdir [\#3732](https://github.com/codeigniter4/CodeIgniter4/issues/3732)
- Bug: route bug [\#3731](https://github.com/codeigniter4/CodeIgniter4/issues/3731)
- page not  redirecting. when i redirecting by calling function [\#3729](https://github.com/codeigniter4/CodeIgniter4/issues/3729)
- Bug: \Config\Services::image\(\)-\>flatten\(\) [\#3728](https://github.com/codeigniter4/CodeIgniter4/issues/3728)
- Bug: Parser will overwrite the value if the initial variable is same [\#3726](https://github.com/codeigniter4/CodeIgniter4/issues/3726)
- Bug: Validating Json Requests [\#3719](https://github.com/codeigniter4/CodeIgniter4/issues/3719)
- Bug: view caching not work on windows [\#3711](https://github.com/codeigniter4/CodeIgniter4/issues/3711)
- Bug: getRoutesOptions return wrong when I have \>2 routes has same name, but different method \(HTTPVerb\) [\#3700](https://github.com/codeigniter4/CodeIgniter4/issues/3700)
- Bug: Validation with request raw body. [\#3694](https://github.com/codeigniter4/CodeIgniter4/issues/3694)
- Image format webp is not preserve transparent [\#3690](https://github.com/codeigniter4/CodeIgniter4/issues/3690)
- problem:  [\#3686](https://github.com/codeigniter4/CodeIgniter4/issues/3686)
- Documentation Error [\#3668](https://github.com/codeigniter4/CodeIgniter4/issues/3668)
- Bug: BaseBuilder's query is wrong [\#3659](https://github.com/codeigniter4/CodeIgniter4/issues/3659)
- Bug: The lifetime of the CSRF check cookie cannot be set to 0 \(Session\). [\#3655](https://github.com/codeigniter4/CodeIgniter4/issues/3655)
- Bug: isRedirect does not respond depending on how redirects are set up. [\#3654](https://github.com/codeigniter4/CodeIgniter4/issues/3654)
- Bug: SQL Error when countAllResults, groupBy and DBPrefix used together [\#3651](https://github.com/codeigniter4/CodeIgniter4/issues/3651)
- Bug: helper current\_url\(\) return wrong protocol [\#3648](https://github.com/codeigniter4/CodeIgniter4/issues/3648)
- Bug: IncomingRequest::setLocale\(\) [\#3640](https://github.com/codeigniter4/CodeIgniter4/issues/3640)
- CodeIgniter\Database\Exceptions\DatabaseException \#8  Unable to connect to the database.  [\#3639](https://github.com/codeigniter4/CodeIgniter4/issues/3639)
- Bug: Cannot use view filter on array values [\#3630](https://github.com/codeigniter4/CodeIgniter4/issues/3630)
- Bug: Changelog navigation is not working properly [\#3625](https://github.com/codeigniter4/CodeIgniter4/issues/3625)
- Bug: Routing 404 Override confusion under route group [\#3623](https://github.com/codeigniter4/CodeIgniter4/issues/3623)
- Bug: Target batch not found when running `php spark migrate:rollback` [\#3620](https://github.com/codeigniter4/CodeIgniter4/issues/3620)
- Bug: getHeaders returns Array with no values [\#3616](https://github.com/codeigniter4/CodeIgniter4/issues/3616)
- Ignore this, was a false report [\#3611](https://github.com/codeigniter4/CodeIgniter4/issues/3611)
- Bug: Incorrect type that fails strict\_types=1 [\#3610](https://github.com/codeigniter4/CodeIgniter4/issues/3610)
- The isAJAX method does not exist in filters [\#3604](https://github.com/codeigniter4/CodeIgniter4/issues/3604)
- Bug: `current\_url\(\)` helper returns wrong url with slash between host and port [\#3603](https://github.com/codeigniter4/CodeIgniter4/issues/3603)
- Bug: Connection ID unknown immediately after connection [\#3601](https://github.com/codeigniter4/CodeIgniter4/issues/3601)
- Bug: Join Query is not working in Library [\#3600](https://github.com/codeigniter4/CodeIgniter4/issues/3600)
- Bug: Database cache not working [\#3597](https://github.com/codeigniter4/CodeIgniter4/issues/3597)
- Bug: Issue in the route, redirect to parent domain, if you put / at the end of url [\#3595](https://github.com/codeigniter4/CodeIgniter4/issues/3595)
- Bug: Segments Also Include The Segments In BASE\_URL [\#3594](https://github.com/codeigniter4/CodeIgniter4/issues/3594)
- Bug: Route Filters doesn't restart in FeatureTestCase. [\#3591](https://github.com/codeigniter4/CodeIgniter4/issues/3591)
- Bug: CURL call returns always code 200 in case de server uses HTTP/2 [\#3586](https://github.com/codeigniter4/CodeIgniter4/issues/3586)
- Bug: Language folders inside locale folders NOT Working ! [\#3582](https://github.com/codeigniter4/CodeIgniter4/issues/3582)
- Bug: Uninitialized string offset: 1    CI 4 [\#3573](https://github.com/codeigniter4/CodeIgniter4/issues/3573)
- Deprecated assertArraySubset in PHPUnit8 used in FeatureResponse::assertJSONFragment [\#3562](https://github.com/codeigniter4/CodeIgniter4/issues/3562)
- Wrong HTTP status code [\#3558](https://github.com/codeigniter4/CodeIgniter4/issues/3558)
- Bug: Invalid serialization data for DateTime object [\#3553](https://github.com/codeigniter4/CodeIgniter4/issues/3553)
- Bug: Setting session expires parameter via $this-\>sessionExpiration [\#3543](https://github.com/codeigniter4/CodeIgniter4/issues/3543)
- Bug: Upload file validation, $model-\>validate return true if failed [\#3532](https://github.com/codeigniter4/CodeIgniter4/issues/3532)
- Bug: Inconsistent behavior of view renderer on Windows and Linux [\#3529](https://github.com/codeigniter4/CodeIgniter4/issues/3529)
- Bug: Add dash to parser plugin regex [\#3523](https://github.com/codeigniter4/CodeIgniter4/issues/3523)
- Bug: When Cronjob run the ip address of the request is 0.0.0.0 [\#3512](https://github.com/codeigniter4/CodeIgniter4/issues/3512)
- Bug: Inconsistency in replace\(\) method [\#3510](https://github.com/codeigniter4/CodeIgniter4/issues/3510)
- Bug: setPath function in UploadedFile.php is writing the index.html wrong [\#3506](https://github.com/codeigniter4/CodeIgniter4/issues/3506)
- Bug: Cannot get session data after server redirecting [\#3503](https://github.com/codeigniter4/CodeIgniter4/issues/3503)
- Bug: Database group defined in .env doesn't work [\#3497](https://github.com/codeigniter4/CodeIgniter4/issues/3497)
- Bug: I cant upload mp4 files  [\#3494](https://github.com/codeigniter4/CodeIgniter4/issues/3494)
- Bug: Error message for matches rule doesn't support nested params [\#3492](https://github.com/codeigniter4/CodeIgniter4/issues/3492)
-  CI\_VERSION = '4.0.0-beta.4'; session 文件file缓存问题失效问题；session\_start\(\): Failed to decode session object. Session has been destroyed [\#3485](https://github.com/codeigniter4/CodeIgniter4/issues/3485)
- Bug: onlyDeleted\(\) conflicts to paginate\(\) [\#3482](https://github.com/codeigniter4/CodeIgniter4/issues/3482)
- Bug: Unable to connect to the database [\#3477](https://github.com/codeigniter4/CodeIgniter4/issues/3477)
- Bug: Argument 1 passed to CodeIgniter\Config\Services::request\(\) must be an instance of Config\App or null, instance of BackEnd\Config\App [\#3475](https://github.com/codeigniter4/CodeIgniter4/issues/3475)
- Bug:  Fatal error running [\#3473](https://github.com/codeigniter4/CodeIgniter4/issues/3473)
- Bug: set\(\) doesn't work on DateTime database field types [\#3471](https://github.com/codeigniter4/CodeIgniter4/issues/3471)
- before\(\) Filters that are executed are missing from codeigniter debug toolbar. [\#3470](https://github.com/codeigniter4/CodeIgniter4/issues/3470)
- Bug: Model insert method always insert with current datetime on updatedField [\#3469](https://github.com/codeigniter4/CodeIgniter4/issues/3469)
- Bug: The search function in the documentation is not working [\#3458](https://github.com/codeigniter4/CodeIgniter4/issues/3458)
- Bug: env variable database.default.dsn not working for mysqli [\#3456](https://github.com/codeigniter4/CodeIgniter4/issues/3456)
- Bug:  [\#3453](https://github.com/codeigniter4/CodeIgniter4/issues/3453)
- Bug: form\_textarea in form\_helper does not create specified 'rows' [\#3452](https://github.com/codeigniter4/CodeIgniter4/issues/3452)
- Bug: afterUpdate event return array instead of key value [\#3450](https://github.com/codeigniter4/CodeIgniter4/issues/3450)
- Using Cronjob with php line argument while using Crontab redirects the script and never executes [\#3444](https://github.com/codeigniter4/CodeIgniter4/issues/3444)
- Bug: Support for SameSite cookie setting missing [\#3442](https://github.com/codeigniter4/CodeIgniter4/issues/3442)
- Bug: Validation mime\_in SVG files not working correctly [\#3439](https://github.com/codeigniter4/CodeIgniter4/issues/3439)
- Bug: Cannot declare class Config\Paths, because the name is already in use [\#3434](https://github.com/codeigniter4/CodeIgniter4/issues/3434)
- Bug: delete\_cookie\(\) helper function not working [\#3433](https://github.com/codeigniter4/CodeIgniter4/issues/3433)
- Bug: insertBatch not working correctly [\#3432](https://github.com/codeigniter4/CodeIgniter4/issues/3432)
- Feature request : CodeIgniter\File with SplFileInfo does not have a method to rewrite parts of the file only to append an CSV style row [\#3431](https://github.com/codeigniter4/CodeIgniter4/issues/3431)
- Bug: SMTP to port 465 should use TLS from the start [\#3429](https://github.com/codeigniter4/CodeIgniter4/issues/3429)
- Bug: Form data and file is not receiving well formed with PUT Method [\#3417](https://github.com/codeigniter4/CodeIgniter4/issues/3417)
- Bug: form\_textarea helper row and col defaults not overwriting when defined as $extra [\#3412](https://github.com/codeigniter4/CodeIgniter4/issues/3412)
- Encryption Class does not allow to change digest parameter from SHA512 to SHA256 or others [\#3404](https://github.com/codeigniter4/CodeIgniter4/issues/3404)
- Bug:  table.Array in first\(\) ... - and complex primary keys! ;-\) [\#3394](https://github.com/codeigniter4/CodeIgniter4/issues/3394)
- Error: Email SMTP configured wrong gives error [\#3390](https://github.com/codeigniter4/CodeIgniter4/issues/3390)
- Bug: CodeIgniter\Router\Router hasLocale returns true even if {locale} is absent [\#3386](https://github.com/codeigniter4/CodeIgniter4/issues/3386)
- Bug: Logs Collector isn't collecting logs for Debug Toolbar [\#3376](https://github.com/codeigniter4/CodeIgniter4/issues/3376)
- Bug: Entity's original is not set [\#3370](https://github.com/codeigniter4/CodeIgniter4/issues/3370)
- Bug: warning in Routes.php [\#3369](https://github.com/codeigniter4/CodeIgniter4/issues/3369)
- Bug: Model can not insert Entity [\#3368](https://github.com/codeigniter4/CodeIgniter4/issues/3368)
- Bug: stringify\_attributes\(\) looks unfinished. [\#3363](https://github.com/codeigniter4/CodeIgniter4/issues/3363)
- Bug: php spark migrate throws wrongful CodeIgniter\Database\Exceptions\DatabaseException Unable to connect to the database [\#3359](https://github.com/codeigniter4/CodeIgniter4/issues/3359)
- Bug: Image watermark after save.. text not align in center  middle when resolution lower and higher. [\#3356](https://github.com/codeigniter4/CodeIgniter4/issues/3356)
- Bug: SQL Lite driver with Builder -\> InsertBatch has intermittend lastError reporting issues [\#3350](https://github.com/codeigniter4/CodeIgniter4/issues/3350)
- Bug: isCLI error when calling a controller from a Command Line [\#3342](https://github.com/codeigniter4/CodeIgniter4/issues/3342)
- Bug: missing clear upgrade instructions [\#3332](https://github.com/codeigniter4/CodeIgniter4/issues/3332)
- Bug: API requests with a trailing forward slash [\#3330](https://github.com/codeigniter4/CodeIgniter4/issues/3330)
- Bug: Pager pagination \( page value in url \) [\#3328](https://github.com/codeigniter4/CodeIgniter4/issues/3328)
- Bug: View layout name hierarchy is not displaying correctly on Debugbar Views [\#3327](https://github.com/codeigniter4/CodeIgniter4/issues/3327)
- Bug: php spark migrate on testing environment doesn't work [\#3309](https://github.com/codeigniter4/CodeIgniter4/issues/3309)
- Bug: Empty Entity date attributes receive current timestamp when accessed [\#3251](https://github.com/codeigniter4/CodeIgniter4/issues/3251)
- Bug: no Filter arguments passed [\#3216](https://github.com/codeigniter4/CodeIgniter4/issues/3216)
- Bug: Url Helper have a bug [\#3180](https://github.com/codeigniter4/CodeIgniter4/issues/3180)
- $pager-\>links\(\) not working inside view layouts [\#3164](https://github.com/codeigniter4/CodeIgniter4/issues/3164)
- Bug: AH01075: Error dispatching request on Basic CI4 [\#3110](https://github.com/codeigniter4/CodeIgniter4/issues/3110)
- Bug: Using assertJSONFragment with respond\(\) in ResponseTrait [\#3079](https://github.com/codeigniter4/CodeIgniter4/issues/3079)
- Bug: Filters 'except' option not removing DebugToolbar comment for view [\#3002](https://github.com/codeigniter4/CodeIgniter4/issues/3002)
- Bug: useSoftDeletes with like function [\#2380](https://github.com/codeigniter4/CodeIgniter4/issues/2380)

**Closed issues:**

- Seed Command in document Not found [\#4154](https://github.com/codeigniter4/CodeIgniter4/issues/4154)
- I18n/L10n: auto update translations [\#4151](https://github.com/codeigniter4/CodeIgniter4/issues/4151)
- Calling update\(\) with an Entity throws an error [\#4143](https://github.com/codeigniter4/CodeIgniter4/issues/4143)
- BaseConfig property issue [\#4140](https://github.com/codeigniter4/CodeIgniter4/issues/4140)
- Bug:  [\#4126](https://github.com/codeigniter4/CodeIgniter4/issues/4126)
- Bug: a new installation of codeignitor 4.0.4 show 404 error Sorry! Cannot seem to find the page you were looking for. [\#4111](https://github.com/codeigniter4/CodeIgniter4/issues/4111)
- Bug PHP SPARK MIGRATE [\#4101](https://github.com/codeigniter4/CodeIgniter4/issues/4101)
- Pagination work in local, but error in server [\#4096](https://github.com/codeigniter4/CodeIgniter4/issues/4096)
- updated\_at field is filled with the current timestamp when inserting new data along with created\_at [\#4038](https://github.com/codeigniter4/CodeIgniter4/issues/4038)
- Config for pager [\#4030](https://github.com/codeigniter4/CodeIgniter4/issues/4030)
- setUpdateBatch [\#4025](https://github.com/codeigniter4/CodeIgniter4/issues/4025)
- Empty html-file created when moving uploaded files with $img-\>move\(\) [\#4020](https://github.com/codeigniter4/CodeIgniter4/issues/4020)
- php spark serve error on php 8 [\#3980](https://github.com/codeigniter4/CodeIgniter4/issues/3980)
- Bug: PHP8 session flush data is not deleted [\#3974](https://github.com/codeigniter4/CodeIgniter4/issues/3974)
- PHP8: Deprecate required param after optional [\#3957](https://github.com/codeigniter4/CodeIgniter4/issues/3957)
- Undefined function [\#3954](https://github.com/codeigniter4/CodeIgniter4/issues/3954)
- Missing function locale\_set\_default\(...\) in Codeigniter 4 [\#3953](https://github.com/codeigniter4/CodeIgniter4/issues/3953)
- Language\en\Language.php is only used in testing [\#3948](https://github.com/codeigniter4/CodeIgniter4/issues/3948)
- Bug: locale\_set\_default causing "Whoops" failure in MacOS PHP 7.3.9 on develop branch [\#3933](https://github.com/codeigniter4/CodeIgniter4/issues/3933)
-  Pdo driver not found [\#3922](https://github.com/codeigniter4/CodeIgniter4/issues/3922)
- Mysqli Backup utility? [\#3906](https://github.com/codeigniter4/CodeIgniter4/issues/3906)
- Relationships - Many to many [\#3885](https://github.com/codeigniter4/CodeIgniter4/issues/3885)
- Bug: How can remove Codeinatore default icon  [\#3883](https://github.com/codeigniter4/CodeIgniter4/issues/3883)
- How Can remove CodeIgniter4 icon [\#3882](https://github.com/codeigniter4/CodeIgniter4/issues/3882)
- Codeigniter review 2021 [\#3880](https://github.com/codeigniter4/CodeIgniter4/issues/3880)
- url\_title doesn't work with decimal number in title [\#3878](https://github.com/codeigniter4/CodeIgniter4/issues/3878)
- IncomingRequest Class "hasFile" function does not exist as described in the documentation. [\#3852](https://github.com/codeigniter4/CodeIgniter4/issues/3852)
- Dynamically changing supported languages [\#3844](https://github.com/codeigniter4/CodeIgniter4/issues/3844)
- CodeIgniter\Database\Exceptions\DatabaseException \#8 [\#3826](https://github.com/codeigniter4/CodeIgniter4/issues/3826)
- Blank page show [\#3812](https://github.com/codeigniter4/CodeIgniter4/issues/3812)
- Bug: folder tests not found [\#3807](https://github.com/codeigniter4/CodeIgniter4/issues/3807)
- Typo in the doc: cache\_info\(\) [\#3800](https://github.com/codeigniter4/CodeIgniter4/issues/3800)
- only\_full\_group\_by - mysqli\_sql\_exception \#1055 - MySql 5.7.24 [\#3795](https://github.com/codeigniter4/CodeIgniter4/issues/3795)
- Dev: Split ModelTest [\#3792](https://github.com/codeigniter4/CodeIgniter4/issues/3792)
- The formatMessage function of the Language class works intermittently [\#3784](https://github.com/codeigniter4/CodeIgniter4/issues/3784)
- .htaccess problem with syntax [\#3778](https://github.com/codeigniter4/CodeIgniter4/issues/3778)
- The page does not open correctly:\( [\#3770](https://github.com/codeigniter4/CodeIgniter4/issues/3770)
- Restore method for CodeIgniter Model. [\#3767](https://github.com/codeigniter4/CodeIgniter4/issues/3767)
- Bug: Model update\(\) only set first array value [\#3764](https://github.com/codeigniter4/CodeIgniter4/issues/3764)
- Bug:  CLI: Exception view shows full path [\#3763](https://github.com/codeigniter4/CodeIgniter4/issues/3763)
- Bug: Maximum execution time exceeds while handling IPv6 Reverse Proxy IPs [\#3760](https://github.com/codeigniter4/CodeIgniter4/issues/3760)
- Fabricators documentation error [\#3743](https://github.com/codeigniter4/CodeIgniter4/issues/3743)
- `is\_unique` is not considering the db prefix in checking for unique values in db [\#3741](https://github.com/codeigniter4/CodeIgniter4/issues/3741)
- Grouping routes should let me pass additional variables within the closure function [\#3691](https://github.com/codeigniter4/CodeIgniter4/issues/3691)
- cannot find Ftp library. [\#3679](https://github.com/codeigniter4/CodeIgniter4/issues/3679)
- Make sessions never expire with $config\['sess\_expiration'\] = -1 [\#3677](https://github.com/codeigniter4/CodeIgniter4/issues/3677)
- Migration trouble  [\#3624](https://github.com/codeigniter4/CodeIgniter4/issues/3624)
- Files as optional in validation rules [\#3619](https://github.com/codeigniter4/CodeIgniter4/issues/3619)
- $\_SERVER should not have .env file info [\#3615](https://github.com/codeigniter4/CodeIgniter4/issues/3615)
- Bug pars xml [\#3588](https://github.com/codeigniter4/CodeIgniter4/issues/3588)
- Bug: When have multi filter class, and "before" function return true，other filters after this filter not working [\#3579](https://github.com/codeigniter4/CodeIgniter4/issues/3579)
- can not install on linux os [\#3572](https://github.com/codeigniter4/CodeIgniter4/issues/3572)
- Incorrect grouping function names in documentation [\#3551](https://github.com/codeigniter4/CodeIgniter4/issues/3551)
- New Edge Browser missing from user agents config [\#3513](https://github.com/codeigniter4/CodeIgniter4/issues/3513)
- Form validation does not have a rule to validate if field value differs from a string [\#3462](https://github.com/codeigniter4/CodeIgniter4/issues/3462)
- updateBatch does not accept entities [\#3451](https://github.com/codeigniter4/CodeIgniter4/issues/3451)
- Support PSR4 [\#3405](https://github.com/codeigniter4/CodeIgniter4/issues/3405)
- CodeIgniter4 is ready to work with PHP 7.4? [\#3389](https://github.com/codeigniter4/CodeIgniter4/issues/3389)
- Using CodeIgniter4 with Oracle Database [\#3388](https://github.com/codeigniter4/CodeIgniter4/issues/3388)
- Can't get Controllers subfolder working [\#3347](https://github.com/codeigniter4/CodeIgniter4/issues/3347)
- Why redirect\(\)-\>to\(\) doesn't work inside events Model? [\#3346](https://github.com/codeigniter4/CodeIgniter4/issues/3346)
- Bug: requests PUT type without segment redirects to index method instead update method [\#3343](https://github.com/codeigniter4/CodeIgniter4/issues/3343)
- Migrations not working [\#3317](https://github.com/codeigniter4/CodeIgniter4/issues/3317)
- CURL Error htts SSL  [\#3314](https://github.com/codeigniter4/CodeIgniter4/issues/3314)
- codeigniter composer global installer [\#3266](https://github.com/codeigniter4/CodeIgniter4/issues/3266)
- Bug: Migrations Deprecated [\#3195](https://github.com/codeigniter4/CodeIgniter4/issues/3195)
- Managed to reproduce this issue. Was uploading an image with jquery.dm-uploader.min.js, and trying to do some image manipulation, but when I got this error: [\#3174](https://github.com/codeigniter4/CodeIgniter4/issues/3174)
- Make the Model work without auto\_increment primary key [\#3134](https://github.com/codeigniter4/CodeIgniter4/issues/3134)
- Call to undefined function CodeIgniter\Debug\current\_url\(\) [\#3106](https://github.com/codeigniter4/CodeIgniter4/issues/3106)
- Bug: select\(\) & selectSum\(\) not consistent / aligned with docs [\#3019](https://github.com/codeigniter4/CodeIgniter4/issues/3019)
- Remove /public in URL not working if using localhost [\#2930](https://github.com/codeigniter4/CodeIgniter4/issues/2930)
- The problem is in getting the file sharing by Mime [\#2732](https://github.com/codeigniter4/CodeIgniter4/issues/2732)
- Migration on PostgreSQL failes for tables with foreign keys [\#2575](https://github.com/codeigniter4/CodeIgniter4/issues/2575)
- Automatic Entity cast for Parser [\#2317](https://github.com/codeigniter4/CodeIgniter4/issues/2317)
- Feature: Cache Driver - Serialize/Unserialize Objects [\#2111](https://github.com/codeigniter4/CodeIgniter4/issues/2111)
- Model set $escape problem [\#1929](https://github.com/codeigniter4/CodeIgniter4/issues/1929)
- \I18n\Time setTimezone does not work as expected [\#1807](https://github.com/codeigniter4/CodeIgniter4/issues/1807)
- TODO Database utility backup builder [\#1257](https://github.com/codeigniter4/CodeIgniter4/issues/1257)
- Port FTP library from CI3 [\#506](https://github.com/codeigniter4/CodeIgniter4/issues/506)
- Port MSSql Database driver from CI3 [\#503](https://github.com/codeigniter4/CodeIgniter4/issues/503)

**Merged pull requests:**

- Finishing touches to generator refactor [\#4197](https://github.com/codeigniter4/CodeIgniter4/pull/4197) ([paulbalandan](https://github.com/paulbalandan))
- Add additional empty checks after field protection for update/insert. [\#4195](https://github.com/codeigniter4/CodeIgniter4/pull/4195) ([sfadschm](https://github.com/sfadschm))
- Minor fixes in Common.php [\#4192](https://github.com/codeigniter4/CodeIgniter4/pull/4192) ([kenjis](https://github.com/kenjis))
- Fix Parser file path in ViewException message is empty [\#4191](https://github.com/codeigniter4/CodeIgniter4/pull/4191) ([kenjis](https://github.com/kenjis))
- docs: Fix double "the" [\#4190](https://github.com/codeigniter4/CodeIgniter4/pull/4190) ([kenjis](https://github.com/kenjis))
- Fix typo in IncomingRequestTest [\#4189](https://github.com/codeigniter4/CodeIgniter4/pull/4189) ([kenjis](https://github.com/kenjis))
- feat: add methods to get page numbers in PagerRenderer [\#4188](https://github.com/codeigniter4/CodeIgniter4/pull/4188) ([kenjis](https://github.com/kenjis))
- fix: UploadedFile::store\(\) return type inconsistancy [\#4187](https://github.com/codeigniter4/CodeIgniter4/pull/4187) ([kenjis](https://github.com/kenjis))
- Add STL mime support [\#4186](https://github.com/codeigniter4/CodeIgniter4/pull/4186) ([MGatner](https://github.com/MGatner))
- \[Rector\] Run Rector when composer.json updated [\#4185](https://github.com/codeigniter4/CodeIgniter4/pull/4185) ([samsonasik](https://github.com/samsonasik))
- new array helper: array\_flatten\_with\_dots [\#4184](https://github.com/codeigniter4/CodeIgniter4/pull/4184) ([paulbalandan](https://github.com/paulbalandan))
- fix: BaseBuilder::getCompiledDelete\(\) runs real query [\#4181](https://github.com/codeigniter4/CodeIgniter4/pull/4181) ([kenjis](https://github.com/kenjis))
- fix the missing / hidden userguide [\#4175](https://github.com/codeigniter4/CodeIgniter4/pull/4175) ([totoprayogo1916](https://github.com/totoprayogo1916))
- Revert "Update phpstan/phpstan requirement from 0.12.69 to 0.12.70" [\#4173](https://github.com/codeigniter4/CodeIgniter4/pull/4173) ([samsonasik](https://github.com/samsonasik))
- Add missing escaping in error\_exception.php [\#4171](https://github.com/codeigniter4/CodeIgniter4/pull/4171) ([kenjis](https://github.com/kenjis))
- Update phpstan/phpstan requirement from 0.12.69 to 0.12.70 [\#4170](https://github.com/codeigniter4/CodeIgniter4/pull/4170) ([dependabot[bot]](https://github.com/apps/dependabot))
- add phpunit.xml.dist to .gitattributes for admin/framework [\#4163](https://github.com/codeigniter4/CodeIgniter4/pull/4163) ([samsonasik](https://github.com/samsonasik))
- Fix strict checking for SQLite3 memory filename [\#4161](https://github.com/codeigniter4/CodeIgniter4/pull/4161) ([paulbalandan](https://github.com/paulbalandan))
- Discuss dbcreate in userguide [\#4160](https://github.com/codeigniter4/CodeIgniter4/pull/4160) ([paulbalandan](https://github.com/paulbalandan))
- Fix misplaced closing tbody [\#4159](https://github.com/codeigniter4/CodeIgniter4/pull/4159) ([paulbalandan](https://github.com/paulbalandan))
- \[Develop\] Fixes \#4114 Cannot declare class Config\App error on running PHPUnit [\#4157](https://github.com/codeigniter4/CodeIgniter4/pull/4157) ([samsonasik](https://github.com/samsonasik))
- Specifically exclude migrations from class mapping [\#4156](https://github.com/codeigniter4/CodeIgniter4/pull/4156) ([paulbalandan](https://github.com/paulbalandan))
- config: add logger.threshold in env as comment [\#4153](https://github.com/codeigniter4/CodeIgniter4/pull/4153) ([kenjis](https://github.com/kenjis))
- Update phpstan/phpstan requirement from 0.12.68 to 0.12.69 [\#4152](https://github.com/codeigniter4/CodeIgniter4/pull/4152) ([dependabot[bot]](https://github.com/apps/dependabot))
- convert indentation from tabs to spaces & update code-block \[changelogs, database\] [\#4150](https://github.com/codeigniter4/CodeIgniter4/pull/4150) ([totoprayogo1916](https://github.com/totoprayogo1916))
- Fix for retrieving migration history [\#4147](https://github.com/codeigniter4/CodeIgniter4/pull/4147) ([michalsn](https://github.com/michalsn))
- Fix phpstan notice [\#4146](https://github.com/codeigniter4/CodeIgniter4/pull/4146) ([samsonasik](https://github.com/samsonasik))
- Update docs for Entity  [\#4145](https://github.com/codeigniter4/CodeIgniter4/pull/4145) ([michalsn](https://github.com/michalsn))
- update faker [\#4139](https://github.com/codeigniter4/CodeIgniter4/pull/4139) ([totoprayogo1916](https://github.com/totoprayogo1916))
- docs: add table of contents in 2 libs [\#4138](https://github.com/codeigniter4/CodeIgniter4/pull/4138) ([kenjis](https://github.com/kenjis))
- PHPStan Fixes [\#4136](https://github.com/codeigniter4/CodeIgniter4/pull/4136) ([MGatner](https://github.com/MGatner))
- prep\_url\(\) with https:// [\#4135](https://github.com/codeigniter4/CodeIgniter4/pull/4135) ([totoprayogo1916](https://github.com/totoprayogo1916))
- Update phpstan/phpstan requirement from 0.12.65 to 0.12.68 [\#4134](https://github.com/codeigniter4/CodeIgniter4/pull/4134) ([dependabot[bot]](https://github.com/apps/dependabot))
- set uppercase [\#4132](https://github.com/codeigniter4/CodeIgniter4/pull/4132) ([totoprayogo1916](https://github.com/totoprayogo1916))
- remove useless "raw html" [\#4131](https://github.com/codeigniter4/CodeIgniter4/pull/4131) ([totoprayogo1916](https://github.com/totoprayogo1916))
- Fix errors/html/error\_exception.php [\#4128](https://github.com/codeigniter4/CodeIgniter4/pull/4128) ([kenjis](https://github.com/kenjis))
- set note-block for some notes [\#4127](https://github.com/codeigniter4/CodeIgniter4/pull/4127) ([totoprayogo1916](https://github.com/totoprayogo1916))
- Fix `@var` in SessionTestCase. [\#4124](https://github.com/codeigniter4/CodeIgniter4/pull/4124) ([kenjis](https://github.com/kenjis))
- Fix Session phpdoc [\#4123](https://github.com/codeigniter4/CodeIgniter4/pull/4123) ([kenjis](https://github.com/kenjis))
- Refactor Generators [\#4121](https://github.com/codeigniter4/CodeIgniter4/pull/4121) ([mostafakhudair](https://github.com/mostafakhudair))
- Fix few typos. [\#4119](https://github.com/codeigniter4/CodeIgniter4/pull/4119) ([npwsamarasinghe](https://github.com/npwsamarasinghe))
- Precise exception trace [\#4118](https://github.com/codeigniter4/CodeIgniter4/pull/4118) ([paulbalandan](https://github.com/paulbalandan))
- Wrap extract calls in IIFEs in View [\#4113](https://github.com/codeigniter4/CodeIgniter4/pull/4113) ([paulbalandan](https://github.com/paulbalandan))
- Fix Request::withMethod\(\) [\#4112](https://github.com/codeigniter4/CodeIgniter4/pull/4112) ([paulbalandan](https://github.com/paulbalandan))
- Cache remember [\#4107](https://github.com/codeigniter4/CodeIgniter4/pull/4107) ([agungsugiarto](https://github.com/agungsugiarto))
- docs: change sample code of redirect\(\) to be more common [\#4106](https://github.com/codeigniter4/CodeIgniter4/pull/4106) ([kenjis](https://github.com/kenjis))
- Add Cache File mode [\#4103](https://github.com/codeigniter4/CodeIgniter4/pull/4103) ([MGatner](https://github.com/MGatner))
- Clarify Renderer discrepancy [\#4102](https://github.com/codeigniter4/CodeIgniter4/pull/4102) ([MGatner](https://github.com/MGatner))
- Catch DateTime failure [\#4097](https://github.com/codeigniter4/CodeIgniter4/pull/4097) ([MGatner](https://github.com/MGatner))
- Fix URL type. [\#4095](https://github.com/codeigniter4/CodeIgniter4/pull/4095) ([npwsamarasinghe](https://github.com/npwsamarasinghe))
- Fixed a bug where a newline was treated as a valid value even if it was included at the end. [\#4094](https://github.com/codeigniter4/CodeIgniter4/pull/4094) ([ytetsuro](https://github.com/ytetsuro))
- BaseModel/Model - Removed $escape from doUpdate [\#4090](https://github.com/codeigniter4/CodeIgniter4/pull/4090) ([najdanovicivan](https://github.com/najdanovicivan))
- BaseConnection - Added automatic handling of query class for 3rd party drivers [\#4089](https://github.com/codeigniter4/CodeIgniter4/pull/4089) ([najdanovicivan](https://github.com/najdanovicivan))
- Fix loss of escape value and data in the model [\#4088](https://github.com/codeigniter4/CodeIgniter4/pull/4088) ([michalsn](https://github.com/michalsn))
- Use getMimeType instead of getClientMimeType. [\#4085](https://github.com/codeigniter4/CodeIgniter4/pull/4085) ([sfadschm](https://github.com/sfadschm))
- fix codeblock in installing\_composer.rst [\#4083](https://github.com/codeigniter4/CodeIgniter4/pull/4083) ([totoprayogo1916](https://github.com/totoprayogo1916))
- Fixing a bug in Message::isJSON [\#4081](https://github.com/codeigniter4/CodeIgniter4/pull/4081) ([caswell-wc](https://github.com/caswell-wc))
- Get JSON Variable [\#4080](https://github.com/codeigniter4/CodeIgniter4/pull/4080) ([caswell-wc](https://github.com/caswell-wc))
- Fix userguide indent [\#4078](https://github.com/codeigniter4/CodeIgniter4/pull/4078) ([totoprayogo1916](https://github.com/totoprayogo1916))
- DebugToolbar - Handle Query display in Query class [\#4077](https://github.com/codeigniter4/CodeIgniter4/pull/4077) ([najdanovicivan](https://github.com/najdanovicivan))
- Update userguide indentation [\#4075](https://github.com/codeigniter4/CodeIgniter4/pull/4075) ([totoprayogo1916](https://github.com/totoprayogo1916))
- docs: fix models indentation [\#4073](https://github.com/codeigniter4/CodeIgniter4/pull/4073) ([kenjis](https://github.com/kenjis))
- BaseModel/Model - Attempt to rework escape parameter [\#4072](https://github.com/codeigniter4/CodeIgniter4/pull/4072) ([najdanovicivan](https://github.com/najdanovicivan))
- Model/BaseModel - Fix primary key and  add @throws for builder method [\#4071](https://github.com/codeigniter4/CodeIgniter4/pull/4071) ([najdanovicivan](https://github.com/najdanovicivan))
- Fix DOMParser rules to search also outside the body tag [\#4070](https://github.com/codeigniter4/CodeIgniter4/pull/4070) ([michalsn](https://github.com/michalsn))
- Warn users on system messages being for internal use [\#4068](https://github.com/codeigniter4/CodeIgniter4/pull/4068) ([paulbalandan](https://github.com/paulbalandan))
- Remove discussion on LoggerAwareTrait [\#4067](https://github.com/codeigniter4/CodeIgniter4/pull/4067) ([paulbalandan](https://github.com/paulbalandan))
- PHPStan Ignore File [\#4065](https://github.com/codeigniter4/CodeIgniter4/pull/4065) ([MGatner](https://github.com/MGatner))
- site\_url tests [\#4063](https://github.com/codeigniter4/CodeIgniter4/pull/4063) ([MGatner](https://github.com/MGatner))
- Use full table name with schema for SQLSRV [\#4058](https://github.com/codeigniter4/CodeIgniter4/pull/4058) ([michalsn](https://github.com/michalsn))
- fix userguide config path [\#4057](https://github.com/codeigniter4/CodeIgniter4/pull/4057) ([totoprayogo1916](https://github.com/totoprayogo1916))
- Solidate mime type guessing. [\#4056](https://github.com/codeigniter4/CodeIgniter4/pull/4056) ([sfadschm](https://github.com/sfadschm))
- Add mime type for rar files [\#4054](https://github.com/codeigniter4/CodeIgniter4/pull/4054) ([michalsn](https://github.com/michalsn))
- Fix for deleting session flash data in php8 [\#4053](https://github.com/codeigniter4/CodeIgniter4/pull/4053) ([michalsn](https://github.com/michalsn))
- Added omitted function parameter description. [\#4052](https://github.com/codeigniter4/CodeIgniter4/pull/4052) ([francis94c](https://github.com/francis94c))
- New Year 2021 [\#4051](https://github.com/codeigniter4/CodeIgniter4/pull/4051) ([totoprayogo1916](https://github.com/totoprayogo1916))
- Fix countAll\(\) docs [\#4050](https://github.com/codeigniter4/CodeIgniter4/pull/4050) ([kenjis](https://github.com/kenjis))
- adds BaseResult::getNumRows\(\). adds getNumRows to various DBMS Result classes [\#4049](https://github.com/codeigniter4/CodeIgniter4/pull/4049) ([sneakyimp](https://github.com/sneakyimp))
- \[UG\] a comma after use: i.e., and e.g., [\#4042](https://github.com/codeigniter4/CodeIgniter4/pull/4042) ([totoprayogo1916](https://github.com/totoprayogo1916))
- Fix database test case test [\#4041](https://github.com/codeigniter4/CodeIgniter4/pull/4041) ([kenjis](https://github.com/kenjis))
- Add initDriver Method [\#4040](https://github.com/codeigniter4/CodeIgniter4/pull/4040) ([mostafakhudair](https://github.com/mostafakhudair))
- docs: fix general indentation [\#4039](https://github.com/codeigniter4/CodeIgniter4/pull/4039) ([kenjis](https://github.com/kenjis))
- fix codeblock [\#4037](https://github.com/codeigniter4/CodeIgniter4/pull/4037) ([totoprayogo1916](https://github.com/totoprayogo1916))
- Remove 'tests' [\#4034](https://github.com/codeigniter4/CodeIgniter4/pull/4034) ([totoprayogo1916](https://github.com/totoprayogo1916))
- Alias Forge with use operator [\#4032](https://github.com/codeigniter4/CodeIgniter4/pull/4032) ([mostafakhudair](https://github.com/mostafakhudair))
- New line for License copyright  [\#4029](https://github.com/codeigniter4/CodeIgniter4/pull/4029) ([totoprayogo1916](https://github.com/totoprayogo1916))
- Rename Sqlsrv driver [\#4023](https://github.com/codeigniter4/CodeIgniter4/pull/4023) ([mostafakhudair](https://github.com/mostafakhudair))
- PHP 8 Actions [\#4012](https://github.com/codeigniter4/CodeIgniter4/pull/4012) ([MGatner](https://github.com/MGatner))
- feat: make migration/seed settings flexible on database testing [\#3993](https://github.com/codeigniter4/CodeIgniter4/pull/3993) ([kenjis](https://github.com/kenjis))
- Deprecate redundant HTTP keys [\#3973](https://github.com/codeigniter4/CodeIgniter4/pull/3973) ([paulbalandan](https://github.com/paulbalandan))
- Replace Core Services [\#3943](https://github.com/codeigniter4/CodeIgniter4/pull/3943) ([MGatner](https://github.com/MGatner))
- Handling requests sent back from filters [\#3900](https://github.com/codeigniter4/CodeIgniter4/pull/3900) ([caswell-wc](https://github.com/caswell-wc))
- DX: Split Model testing into several subunits [\#3891](https://github.com/codeigniter4/CodeIgniter4/pull/3891) ([paulbalandan](https://github.com/paulbalandan))

## [v4.0.4](https://github.com/codeigniter4/CodeIgniter4/tree/v4.0.4) (2020-07-15)

[Full Changelog](https://github.com/codeigniter4/CodeIgniter4/compare/v4.0.3...v4.0.4)

**Implemented enhancements:**

- Bug: incorrect type - system/Database/MYSQLi/Connection.php [\#2996](https://github.com/codeigniter4/CodeIgniter4/issues/2996)
- Bug: CI\_DEBUG incorrect type and fails strict validation [\#2975](https://github.com/codeigniter4/CodeIgniter4/issues/2975)

**Fixed bugs:**

- Bug: Fix protocol version for DownloadResponse [\#3320](https://github.com/codeigniter4/CodeIgniter4/pull/3320)
- Bug: Add Honeypot::$container to env file [\#3318](https://github.com/codeigniter4/CodeIgniter4/pull/3318)
- Bug: Add multibyte support on DOMParser::see() [\#3324](https://github.com/codeigniter4/CodeIgniter4/pull/3324)
- Bug: Image width debug-bar has conflict [\#3323](https://github.com/codeigniter4/CodeIgniter4/pull/3323)
- Bug: Setting Encryption Service Key in app/Config/Encyption.php [\#3297](https://github.com/codeigniter4/CodeIgniter4/issues/3297)
- Bug: CodeIgniter\I18n -\> Class 'Locale' not found [\#3294](https://github.com/codeigniter4/CodeIgniter4/issues/3294)
- bug cURL - Parse header and Body [\#3261](https://github.com/codeigniter4/CodeIgniter4/issues/3261)
- Bug: Forced HTTPS wrong redirect [\#3260](https://github.com/codeigniter4/CodeIgniter4/issues/3260)
- Bug:  [\#3255](https://github.com/codeigniter4/CodeIgniter4/issues/3255)
- Testing ajax header problems [\#3246](https://github.com/codeigniter4/CodeIgniter4/issues/3246)
- Bug: Url's not working [\#3245](https://github.com/codeigniter4/CodeIgniter4/issues/3245)
- Missing code formatting for migration block in documentation [\#3243](https://github.com/codeigniter4/CodeIgniter4/issues/3243)
- Hint message in tutorial does not correspond to the example code shown [\#3242](https://github.com/codeigniter4/CodeIgniter4/issues/3242)
- How to call resource route [\#3239](https://github.com/codeigniter4/CodeIgniter4/issues/3239)
- Can't successfully validate CLI::prompt when field param has periods [\#3233](https://github.com/codeigniter4/CodeIgniter4/issues/3233)
- Bug: set\_checkbox, set\_radio not working default true. [\#3228](https://github.com/codeigniter4/CodeIgniter4/issues/3228)
- Bug: Validation wrong behaviour - Session mixed with internal validator state  [\#3210](https://github.com/codeigniter4/CodeIgniter4/issues/3210)
- Bug: CLI: Smell on reading parameters [\#3205](https://github.com/codeigniter4/CodeIgniter4/issues/3205)
- Bug: MySQL Errors are not reported [\#3204](https://github.com/codeigniter4/CodeIgniter4/issues/3204)
- Bug: Error exception layout when its an error inside a view where code can go outside the limits [\#3199](https://github.com/codeigniter4/CodeIgniter4/issues/3199)
- Bug: MessageFormatter argType of "String" isn't valid [\#3191](https://github.com/codeigniter4/CodeIgniter4/issues/3191)
- Bug: pagination not working with segment [\#3188](https://github.com/codeigniter4/CodeIgniter4/issues/3188)
- Bug: is\_image validation is vulnerable [\#3184](https://github.com/codeigniter4/CodeIgniter4/issues/3184)
- Bug: model-\>save\(\) insert/update data [\#3177](https://github.com/codeigniter4/CodeIgniter4/issues/3177)
- Bug: The CLI::color cannot create multicolored strings if ordering of strings is reversed [\#3173](https://github.com/codeigniter4/CodeIgniter4/issues/3173)
- Bug: Missing function locale\_set\_default\(...\) [\#3171](https://github.com/codeigniter4/CodeIgniter4/issues/3171)
- Router RegEx not working.  [\#3169](https://github.com/codeigniter4/CodeIgniter4/issues/3169)
- Bug: BaseBuilder::insertBatch\(\) has 3 params and not 4 [\#3158](https://github.com/codeigniter4/CodeIgniter4/issues/3158)
- Bug: Issue using image library when original image and resized are the same size [\#3146](https://github.com/codeigniter4/CodeIgniter4/issues/3146)
- Bug: cannot call constructor on controller. [\#3145](https://github.com/codeigniter4/CodeIgniter4/issues/3145)
- Bug: CodeIgniter 4.0.3 Controller will not display pdf files in browser [\#3144](https://github.com/codeigniter4/CodeIgniter4/issues/3144)
- Bug: $request-\>getVar\('test', FILTER\_VALIDATE\_INT\) does not work if the input is an array [\#3128](https://github.com/codeigniter4/CodeIgniter4/issues/3128)
- Bug: A wrong escape on BaseBuilder::set\(\) [\#3127](https://github.com/codeigniter4/CodeIgniter4/issues/3127)
- Bug:  Can't override Translations Package Files / Keys [\#3125](https://github.com/codeigniter4/CodeIgniter4/issues/3125)
- Bug: Validation rules max\_size [\#3122](https://github.com/codeigniter4/CodeIgniter4/issues/3122)
- Bug: Pagination not working with soft deleted items [\#3121](https://github.com/codeigniter4/CodeIgniter4/issues/3121)
- Bug: SQLite3 database file created in /public folder [\#3113](https://github.com/codeigniter4/CodeIgniter4/issues/3113)
- Bug: RedisHandler does not working on sessionExpiration is zero [\#3111](https://github.com/codeigniter4/CodeIgniter4/issues/3111)
- Bug: Implicit controller methods failing [\#3105](https://github.com/codeigniter4/CodeIgniter4/issues/3105)
- Bug: Custom Validation Error Messages [\#3097](https://github.com/codeigniter4/CodeIgniter4/issues/3097)
- Bug: API\ResponseTrait::respondNoContent return content-type header [\#3087](https://github.com/codeigniter4/CodeIgniter4/issues/3087)
- Bug: Running Feature Tests on multiple endpoints using filters [\#3085](https://github.com/codeigniter4/CodeIgniter4/issues/3085)
- Bug: FeatureResponse::isOk and redirects [\#3072](https://github.com/codeigniter4/CodeIgniter4/issues/3072)
- Documentation: "First Application" form validating before submitting [\#3071](https://github.com/codeigniter4/CodeIgniter4/issues/3071)
- Bug: Fatal error: Cannot declare class CodeIgniter\Exceptions\PageNotFoundException [\#3067](https://github.com/codeigniter4/CodeIgniter4/issues/3067)
- Bug: Risky feature tests with Controller returns [\#3063](https://github.com/codeigniter4/CodeIgniter4/issues/3063)
- Bug: Documentation news app project [\#3054](https://github.com/codeigniter4/CodeIgniter4/issues/3054)
- Bug: ClassMethod [\#3050](https://github.com/codeigniter4/CodeIgniter4/issues/3050)
- Bug: CodeIgniter\Router\RouteCollection-\>fillRouteParams\(\) ErrorException on 'from' parameter containing regex pattern with '|' symbol [\#3048](https://github.com/codeigniter4/CodeIgniter4/issues/3048)
- Bug:  Database connection [\#3043](https://github.com/codeigniter4/CodeIgniter4/issues/3043)
- Bug: Route redirection not working [\#3041](https://github.com/codeigniter4/CodeIgniter4/issues/3041)
- Bug: Model::getValidationRules\(\) cant handle ::$validationRules if its a string [\#3039](https://github.com/codeigniter4/CodeIgniter4/issues/3039)
- Bug: Body data from curlrequest become unreadable when endpoint return long data [\#3034](https://github.com/codeigniter4/CodeIgniter4/issues/3034)
- Bug: File Validation not validate correctly. [\#3032](https://github.com/codeigniter4/CodeIgniter4/issues/3032)
- Bug: Fatal error when no user-agent is available [\#3029](https://github.com/codeigniter4/CodeIgniter4/issues/3029)
- Bug:  Not possible manipulate results of renderSection\(\) on View Layouts [\#3028](https://github.com/codeigniter4/CodeIgniter4/issues/3028)
- Bug: Helpers at non-default locations are not found [\#3026](https://github.com/codeigniter4/CodeIgniter4/issues/3026)
- Bug: Most validation rules enforce requirement of the field [\#3025](https://github.com/codeigniter4/CodeIgniter4/issues/3025)
- Bug:  [\#3021](https://github.com/codeigniter4/CodeIgniter4/issues/3021)
- Bug: getPostGet\($index\) return $\_POST if there is no $index  in post and get [\#3020](https://github.com/codeigniter4/CodeIgniter4/issues/3020)
- Bug: Multiple File Uploads validation rule "uploaded\[inputName\]" does not work when the input name is an Array [\#3018](https://github.com/codeigniter4/CodeIgniter4/issues/3018)
- Bug: delete\_files\(\) and hidden directories [\#3015](https://github.com/codeigniter4/CodeIgniter4/issues/3015)
- Bug: Model::Insert\(\) does not throw exception when object without data is passed as parameter [\#2998](https://github.com/codeigniter4/CodeIgniter4/issues/2998)
- Bug: Force download [\#2995](https://github.com/codeigniter4/CodeIgniter4/issues/2995)
- Bug: The example of "The Test Class“ is not working in Documets [\#2993](https://github.com/codeigniter4/CodeIgniter4/issues/2993)
- Bug: Difference in behaviour of native DateTime::setTimezone\(\) and CI's Time:setTimezone\(\) [\#2989](https://github.com/codeigniter4/CodeIgniter4/issues/2989)
- Bug: Blank Page on Production Server [\#2980](https://github.com/codeigniter4/CodeIgniter4/issues/2980)
- Bug: Mistake in uri\_string\(\) documentation [\#2972](https://github.com/codeigniter4/CodeIgniter4/issues/2972)
- Bug: route\_to\(\) does not return relative part of the path [\#2971](https://github.com/codeigniter4/CodeIgniter4/issues/2971)
- Bug: Encrypter-\>decrypt issue? [\#2970](https://github.com/codeigniter4/CodeIgniter4/issues/2970)
- Bug: form\_upload second parameter VALUE is not used in source code  [\#2967](https://github.com/codeigniter4/CodeIgniter4/issues/2967)
- Bug: There's no way to change default spark serve port using code [\#2966](https://github.com/codeigniter4/CodeIgniter4/issues/2966)
- Bug: 'method' is an empty string in PATH\_INFO causes Unhandled Exception [\#2965](https://github.com/codeigniter4/CodeIgniter4/issues/2965)
- Bug: URI class - working with segments is really strange [\#2962](https://github.com/codeigniter4/CodeIgniter4/issues/2962)
- Bug: Documentation Issue [\#2960](https://github.com/codeigniter4/CodeIgniter4/issues/2960)
- Query Builder set update not working correctly [\#2959](https://github.com/codeigniter4/CodeIgniter4/issues/2959)
- Bug: Validation permit\_empty does no work together with required\_with and required\_without [\#2953](https://github.com/codeigniter4/CodeIgniter4/issues/2953)
- Bug: Validation tries to validate inputs even for empty values with no `required` parameter [\#2951](https://github.com/codeigniter4/CodeIgniter4/issues/2951)
- Bug: Request URI segment is our of range CI 4.0.3 [\#2949](https://github.com/codeigniter4/CodeIgniter4/issues/2949)
- Bug:  start migration in manual mode [\#2942](https://github.com/codeigniter4/CodeIgniter4/issues/2942)
- valid\_url rule from validation not working correctly same as valid\_url|required [\#2941](https://github.com/codeigniter4/CodeIgniter4/issues/2941)
- redirect route when route is more complex [\#2937](https://github.com/codeigniter4/CodeIgniter4/issues/2937)
- set\_value does not work with NULL as second parameter [\#2935](https://github.com/codeigniter4/CodeIgniter4/issues/2935)
- Bug: init of $data arrays in controllers [\#2933](https://github.com/codeigniter4/CodeIgniter4/issues/2933)
- Bug: Translation key separated by dot [\#2932](https://github.com/codeigniter4/CodeIgniter4/issues/2932)
- Bug: Model Instantiation [\#2924](https://github.com/codeigniter4/CodeIgniter4/issues/2924)
- Model [\#2923](https://github.com/codeigniter4/CodeIgniter4/issues/2923)
- Bug: CURLRequest baseURI option in user guide [\#2922](https://github.com/codeigniter4/CodeIgniter4/issues/2922)
- Bug: Not bug, but Fix Documentation, please [\#2920](https://github.com/codeigniter4/CodeIgniter4/issues/2920)
- Bug: Postgresql API call delete use -\>connID-\>affected\_rows after Model::delete\(\) got error [\#2918](https://github.com/codeigniter4/CodeIgniter4/issues/2918)
- Multiple table query Model first  [\#2885](https://github.com/codeigniter4/CodeIgniter4/issues/2885)
- Bug: pager "prev" and "next" links pointing to wrong URIs [\#2881](https://github.com/codeigniter4/CodeIgniter4/issues/2881)
- Bug: Automatic no CLI colors for Windows terminals [\#2849](https://github.com/codeigniter4/CodeIgniter4/issues/2849)
- Bug: $format in ResourceController is ignored [\#2828](https://github.com/codeigniter4/CodeIgniter4/issues/2828)
- Bug:  &quot;Type is not supported&quot; in Postgresql POST restful [\#2812](https://github.com/codeigniter4/CodeIgniter4/issues/2812)
- Bug: Cookie Helper and Response class issue [\#2783](https://github.com/codeigniter4/CodeIgniter4/issues/2783)
- Bug: Models, useSoftDeletes not found in findAll [\#2658](https://github.com/codeigniter4/CodeIgniter4/issues/2658)
- Feature: About the SameSite COOKIE RFC changes for PHP \> 7.3 [\#2374](https://github.com/codeigniter4/CodeIgniter4/issues/2374)

**Closed issues:**

- Language folders inside locale folders [\#3300](https://github.com/codeigniter4/CodeIgniter4/issues/3300)
- Encryption Class - Decrypting: authentication failed [\#3258](https://github.com/codeigniter4/CodeIgniter4/issues/3258)
- form\_upload second parameter VALUE was not used in source code, i found a way to do it. [\#3256](https://github.com/codeigniter4/CodeIgniter4/issues/3256)
- erorr line in resize image using fit in visual studio code [\#3249](https://github.com/codeigniter4/CodeIgniter4/issues/3249)
- Testing withSession\(\) generates an error. [\#3190](https://github.com/codeigniter4/CodeIgniter4/issues/3190)
- Why is getGetPost\(\) returning all data instead of null when index not found? [\#3187](https://github.com/codeigniter4/CodeIgniter4/issues/3187)
- Request setGlobal not works [\#3186](https://github.com/codeigniter4/CodeIgniter4/issues/3186)
- Cannot extend core HTTPException class [\#3178](https://github.com/codeigniter4/CodeIgniter4/issues/3178)
- Add this relationship in model. [\#3170](https://github.com/codeigniter4/CodeIgniter4/issues/3170)
- Execute bootstrapEnvironment\(\) & detectEnvironment\(\) before Services::exceptions\(\)  [\#3138](https://github.com/codeigniter4/CodeIgniter4/issues/3138)
- Bag pars array to xml  [\#3092](https://github.com/codeigniter4/CodeIgniter4/issues/3092)
- utf-8 slug character doesn't work in url [\#3089](https://github.com/codeigniter4/CodeIgniter4/issues/3089)
- Grammar in comment [\#3064](https://github.com/codeigniter4/CodeIgniter4/issues/3064)
- line 374 in system/Entity.php json\_encode add JSON\_UNESCAPED\_UNICODE [\#3059](https://github.com/codeigniter4/CodeIgniter4/issues/3059)
- Link to User Guide on README.md [\#3053](https://github.com/codeigniter4/CodeIgniter4/issues/3053)
- How to set private properties in CodeIgniter\Database\BaseResult::getCustomResultObject\(\) [\#3051](https://github.com/codeigniter4/CodeIgniter4/issues/3051)
- url\_title not detecting some special characteres [\#3038](https://github.com/codeigniter4/CodeIgniter4/issues/3038)
- Error: No input file specified. [\#3030](https://github.com/codeigniter4/CodeIgniter4/issues/3030)
- Can findAll  function in modeling data accept 3rd parameter reset [\#3024](https://github.com/codeigniter4/CodeIgniter4/issues/3024)
- Class 'App\Models\UserModel' not found [\#3014](https://github.com/codeigniter4/CodeIgniter4/issues/3014)
- Image reorient with exif [\#3006](https://github.com/codeigniter4/CodeIgniter4/issues/3006)
- set cookieHTTPOnly not work [\#2999](https://github.com/codeigniter4/CodeIgniter4/issues/2999)
- \[Feature request\] Slugs Link [\#2988](https://github.com/codeigniter4/CodeIgniter4/issues/2988)
- \[Feature request\] End processing app [\#2982](https://github.com/codeigniter4/CodeIgniter4/issues/2982)
- Pager should have getTotalResults method function [\#2954](https://github.com/codeigniter4/CodeIgniter4/issues/2954)
- URL rewrite problem [\#2925](https://github.com/codeigniter4/CodeIgniter4/issues/2925)
- Translation file not get correct locale in 4.0.3  [\#2921](https://github.com/codeigniter4/CodeIgniter4/issues/2921)
- Why this route does not work ? [\#2919](https://github.com/codeigniter4/CodeIgniter4/issues/2919)
- Cache redis or memcached [\#2909](https://github.com/codeigniter4/CodeIgniter4/issues/2909)
- Cookie helper not woking [\#2848](https://github.com/codeigniter4/CodeIgniter4/issues/2848)
- Bug: Routing group filter not working on nested or complex routes [\#2390](https://github.com/codeigniter4/CodeIgniter4/issues/2390)

**Merged pull requests:**

- Changelog update [\#3322](https://github.com/codeigniter4/CodeIgniter4/pull/3322) ([michalsn](https://github.com/michalsn))
- Changelog for 4.0.4 update [\#3321](https://github.com/codeigniter4/CodeIgniter4/pull/3321) ([michalsn](https://github.com/michalsn))
- Fix protocol version for DownloadResponse [\#3320](https://github.com/codeigniter4/CodeIgniter4/pull/3320) ([michalsn](https://github.com/michalsn))
- Add Honeypot::$container to env file [\#3318](https://github.com/codeigniter4/CodeIgniter4/pull/3318) ([paulbalandan](https://github.com/paulbalandan))
- Pass filter arguments to after\(\) and before\(\) methods [\#3316](https://github.com/codeigniter4/CodeIgniter4/pull/3316) ([tangix](https://github.com/tangix))
- count on \Config\Services [\#3308](https://github.com/codeigniter4/CodeIgniter4/pull/3308) ([mostafakhudair](https://github.com/mostafakhudair))
- Add hex2bin prefix handling for encryption key [\#3307](https://github.com/codeigniter4/CodeIgniter4/pull/3307) ([michalsn](https://github.com/michalsn))
- add break; in foreach at Time::getDst\(\) when daylightSaving set [\#3305](https://github.com/codeigniter4/CodeIgniter4/pull/3305) ([samsonasik](https://github.com/samsonasik))
- New command: cache:clear [\#3304](https://github.com/codeigniter4/CodeIgniter4/pull/3304) ([lonnieezell](https://github.com/lonnieezell))
- force\_https didn't force https [\#3302](https://github.com/codeigniter4/CodeIgniter4/pull/3302) ([colethorsen](https://github.com/colethorsen))
- add test for CommandRunner::\_remap\(\) with empty first params [\#3301](https://github.com/codeigniter4/CodeIgniter4/pull/3301) ([samsonasik](https://github.com/samsonasik))
- FieldData -\> add typeName field and length field  [\#3299](https://github.com/codeigniter4/CodeIgniter4/pull/3299) ([devorama](https://github.com/devorama))
- reduce repetitive getDefaultNamespace\(\) and controllerName\(\) function call in Router [\#3298](https://github.com/codeigniter4/CodeIgniter4/pull/3298) ([samsonasik](https://github.com/samsonasik))
- Fix PHPDocs for Filters [\#3296](https://github.com/codeigniter4/CodeIgniter4/pull/3296) ([paulbalandan](https://github.com/paulbalandan))
- Fix PHPDocs for HTTP [\#3295](https://github.com/codeigniter4/CodeIgniter4/pull/3295) ([paulbalandan](https://github.com/paulbalandan))
- Update phpdoc.dist.xml [\#3293](https://github.com/codeigniter4/CodeIgniter4/pull/3293) ([paulbalandan](https://github.com/paulbalandan))
- Fix for force\_https\(\) function [\#3292](https://github.com/codeigniter4/CodeIgniter4/pull/3292) ([michalsn](https://github.com/michalsn))
- Fix PHPDocs for I18n [\#3291](https://github.com/codeigniter4/CodeIgniter4/pull/3291) ([paulbalandan](https://github.com/paulbalandan))
- Fix PHPDocs for Router [\#3290](https://github.com/codeigniter4/CodeIgniter4/pull/3290) ([paulbalandan](https://github.com/paulbalandan))
- Fix PHPDocs for CLI [\#3289](https://github.com/codeigniter4/CodeIgniter4/pull/3289) ([paulbalandan](https://github.com/paulbalandan))
- add JSON\_NUMERIC\_CHECK to json encode options [\#3288](https://github.com/codeigniter4/CodeIgniter4/pull/3288) ([devorama](https://github.com/devorama))
- typo fix s/Memcached/Redis in RedisHandler cache [\#3285](https://github.com/codeigniter4/CodeIgniter4/pull/3285) ([samsonasik](https://github.com/samsonasik))
- optimize Session : use foreach instead of for with count when possible [\#3284](https://github.com/codeigniter4/CodeIgniter4/pull/3284) ([samsonasik](https://github.com/samsonasik))
- using strpos instead of substr when possible [\#3283](https://github.com/codeigniter4/CodeIgniter4/pull/3283) ([samsonasik](https://github.com/samsonasik))
- optimize Database BaseBuilder : use foreach instead of for with count when possible [\#3282](https://github.com/codeigniter4/CodeIgniter4/pull/3282) ([samsonasik](https://github.com/samsonasik))
- optimize RouteCollection : use foreach instead of for with count when possible [\#3281](https://github.com/codeigniter4/CodeIgniter4/pull/3281) ([samsonasik](https://github.com/samsonasik))
- optimize FileLocator autoloader : use foreach instead of for with count when possible [\#3280](https://github.com/codeigniter4/CodeIgniter4/pull/3280) ([samsonasik](https://github.com/samsonasik))
- Fix "100 Continue" header handling in CURLRequest class [\#3274](https://github.com/codeigniter4/CodeIgniter4/pull/3274) ([michalsn](https://github.com/michalsn))
- cs fix in Session class [\#3272](https://github.com/codeigniter4/CodeIgniter4/pull/3272) ([samsonasik](https://github.com/samsonasik))
- Throttler code style update [\#3271](https://github.com/codeigniter4/CodeIgniter4/pull/3271) ([michalsn](https://github.com/michalsn))
- cs : remove unused import use statements and sort use statements [\#3270](https://github.com/codeigniter4/CodeIgniter4/pull/3270) ([samsonasik](https://github.com/samsonasik))
- Add more URI class tests to fully illustrate current behavior [\#3269](https://github.com/codeigniter4/CodeIgniter4/pull/3269) ([michalsn](https://github.com/michalsn))
- Fix Image::save\(\) when target value is null [\#3268](https://github.com/codeigniter4/CodeIgniter4/pull/3268) ([michalsn](https://github.com/michalsn))
- Use named variable in honeypot container [\#3267](https://github.com/codeigniter4/CodeIgniter4/pull/3267) ([michalsn](https://github.com/michalsn))
- Check server headers via Request class [\#3265](https://github.com/codeigniter4/CodeIgniter4/pull/3265) ([michalsn](https://github.com/michalsn))
- PHPUnit-annotate untestable code in CLI [\#3264](https://github.com/codeigniter4/CodeIgniter4/pull/3264) ([paulbalandan](https://github.com/paulbalandan))
- Update phpdocs of Cache library [\#3263](https://github.com/codeigniter4/CodeIgniter4/pull/3263) ([paulbalandan](https://github.com/paulbalandan))
- Update htaccess [\#3262](https://github.com/codeigniter4/CodeIgniter4/pull/3262) ([paulbalandan](https://github.com/paulbalandan))
- Fixes \#3125 : add ability to override existing translation en in system language from App [\#3254](https://github.com/codeigniter4/CodeIgniter4/pull/3254) ([samsonasik](https://github.com/samsonasik))
- Add Fabricator model error [\#3253](https://github.com/codeigniter4/CodeIgniter4/pull/3253) ([MGatner](https://github.com/MGatner))
- Implement model callback overrides [\#3252](https://github.com/codeigniter4/CodeIgniter4/pull/3252) ([MGatner](https://github.com/MGatner))
- Fix PHPDocBlock of Images library [\#3250](https://github.com/codeigniter4/CodeIgniter4/pull/3250) ([paulbalandan](https://github.com/paulbalandan))
- Update html\_helper.rst [\#3248](https://github.com/codeigniter4/CodeIgniter4/pull/3248) ([avegacms](https://github.com/avegacms))
- Update html\_helper.php [\#3247](https://github.com/codeigniter4/CodeIgniter4/pull/3247) ([avegacms](https://github.com/avegacms))
- Syntax fix for migration.rst [\#3244](https://github.com/codeigniter4/CodeIgniter4/pull/3244) ([paulbalandan](https://github.com/paulbalandan))
- Run apidocs action only when system files are changed [\#3241](https://github.com/codeigniter4/CodeIgniter4/pull/3241) ([paulbalandan](https://github.com/paulbalandan))
- Fix formatting issue in userguide/outgoing/view\_parser [\#3240](https://github.com/codeigniter4/CodeIgniter4/pull/3240) ([Connum](https://github.com/Connum))
- Add ability to call commands programatically. [\#3238](https://github.com/codeigniter4/CodeIgniter4/pull/3238) ([lonnieezell](https://github.com/lonnieezell))
- Reset Filters between feature tests [\#3237](https://github.com/codeigniter4/CodeIgniter4/pull/3237) ([MGatner](https://github.com/MGatner))
- Fix CLI::validate\(\) usage when using dot sign [\#3236](https://github.com/codeigniter4/CodeIgniter4/pull/3236) ([michalsn](https://github.com/michalsn))
- Remove cilexer from gitignore [\#3235](https://github.com/codeigniter4/CodeIgniter4/pull/3235) ([paulbalandan](https://github.com/paulbalandan))
- Feature seed command [\#3234](https://github.com/codeigniter4/CodeIgniter4/pull/3234) ([abilioposada](https://github.com/abilioposada))
- more test Files\File class [\#3232](https://github.com/codeigniter4/CodeIgniter4/pull/3232) ([samsonasik](https://github.com/samsonasik))
- more tests on Autoloader\FileLocator [\#3231](https://github.com/codeigniter4/CodeIgniter4/pull/3231) ([samsonasik](https://github.com/samsonasik))
- Fix set\_checkbox\(\) and set\_radio\(\) when default is set to true [\#3229](https://github.com/codeigniter4/CodeIgniter4/pull/3229) ([michalsn](https://github.com/michalsn))
- Use Throwable in catch block of filesystem helper [\#3227](https://github.com/codeigniter4/CodeIgniter4/pull/3227) ([paulbalandan](https://github.com/paulbalandan))
- Check color support for STDERR for CLI::error [\#3226](https://github.com/codeigniter4/CodeIgniter4/pull/3226) ([paulbalandan](https://github.com/paulbalandan))
- Fix 'exit code 23' in apidocs action [\#3225](https://github.com/codeigniter4/CodeIgniter4/pull/3225) ([paulbalandan](https://github.com/paulbalandan))
- Fix result object handling in Model class [\#3224](https://github.com/codeigniter4/CodeIgniter4/pull/3224) ([michalsn](https://github.com/michalsn))
- Fix update query to return false on error [\#3223](https://github.com/codeigniter4/CodeIgniter4/pull/3223) ([michalsn](https://github.com/michalsn))
- Fix insert Entity object on postgres [\#3222](https://github.com/codeigniter4/CodeIgniter4/pull/3222) ([MashinaMashina](https://github.com/MashinaMashina))
- DatabaseTestCase migrations [\#3221](https://github.com/codeigniter4/CodeIgniter4/pull/3221) ([MGatner](https://github.com/MGatner))
- Allow ignoring \*.db inside folders [\#3220](https://github.com/codeigniter4/CodeIgniter4/pull/3220) ([paulbalandan](https://github.com/paulbalandan))
- SQLite3 escapeChar fix [\#3219](https://github.com/codeigniter4/CodeIgniter4/pull/3219) ([michalsn](https://github.com/michalsn))
- Remove underscore in UG build path [\#3218](https://github.com/codeigniter4/CodeIgniter4/pull/3218) ([paulbalandan](https://github.com/paulbalandan))
- add test for Filters with empty except [\#3215](https://github.com/codeigniter4/CodeIgniter4/pull/3215) ([samsonasik](https://github.com/samsonasik))
- remove unnecessary foreach in RouteCollection::checkSubdomains\(\) [\#3214](https://github.com/codeigniter4/CodeIgniter4/pull/3214) ([samsonasik](https://github.com/samsonasik))
- Add Fabricator counts [\#3213](https://github.com/codeigniter4/CodeIgniter4/pull/3213) ([MGatner](https://github.com/MGatner))
- Cast currentPage value to integer in Pager class [\#3209](https://github.com/codeigniter4/CodeIgniter4/pull/3209) ([michalsn](https://github.com/michalsn))
- Allow dashes in the CLI segment [\#3208](https://github.com/codeigniter4/CodeIgniter4/pull/3208) ([michalsn](https://github.com/michalsn))
- Fix for getting database error [\#3207](https://github.com/codeigniter4/CodeIgniter4/pull/3207) ([michalsn](https://github.com/michalsn))
- Fixed \#3199 [\#3203](https://github.com/codeigniter4/CodeIgniter4/pull/3203) ([mpmont](https://github.com/mpmont))
- Fix extra slash in URI [\#3202](https://github.com/codeigniter4/CodeIgniter4/pull/3202) ([MGatner](https://github.com/MGatner))
- Update for FabricatorLiveTest [\#3201](https://github.com/codeigniter4/CodeIgniter4/pull/3201) ([michalsn](https://github.com/michalsn))
- Add closing parenthesis [\#3200](https://github.com/codeigniter4/CodeIgniter4/pull/3200) ([abilioposada](https://github.com/abilioposada))
- Hide in bootstrap4 [\#3197](https://github.com/codeigniter4/CodeIgniter4/pull/3197) ([Sosko](https://github.com/Sosko))
- The permissions of the new file should be modified, not the old file [\#3196](https://github.com/codeigniter4/CodeIgniter4/pull/3196) ([wangyupeng](https://github.com/wangyupeng))
- Fix default value for page in Model::paginate\(\) [\#3194](https://github.com/codeigniter4/CodeIgniter4/pull/3194) ([michalsn](https://github.com/michalsn))
- Test Case Mocking [\#3193](https://github.com/codeigniter4/CodeIgniter4/pull/3193) ([MGatner](https://github.com/MGatner))
- ArgType 'String' is not valid in MessageFormatter class \(fixes \#3191\) [\#3192](https://github.com/codeigniter4/CodeIgniter4/pull/3192) ([HughieW](https://github.com/HughieW))
- Bugfix: Multipart Content-Length [\#3189](https://github.com/codeigniter4/CodeIgniter4/pull/3189) ([MGatner](https://github.com/MGatner))
- Add apidocs action [\#3183](https://github.com/codeigniter4/CodeIgniter4/pull/3183) ([paulbalandan](https://github.com/paulbalandan))
- Improve CLI clear screen [\#3182](https://github.com/codeigniter4/CodeIgniter4/pull/3182) ([paulbalandan](https://github.com/paulbalandan))
- Refactor color detection in CLI [\#3181](https://github.com/codeigniter4/CodeIgniter4/pull/3181) ([paulbalandan](https://github.com/paulbalandan))
- Use explicit 'PHP\_EOL' in wordwrap [\#3179](https://github.com/codeigniter4/CodeIgniter4/pull/3179) ([paulbalandan](https://github.com/paulbalandan))
- Add webp support to Image class  [\#3176](https://github.com/codeigniter4/CodeIgniter4/pull/3176) ([michalsn](https://github.com/michalsn))
- Fix for multicolored strings in CLI [\#3175](https://github.com/codeigniter4/CodeIgniter4/pull/3175) ([michalsn](https://github.com/michalsn))
- Add Unicode support for regular expressions in router [\#3172](https://github.com/codeigniter4/CodeIgniter4/pull/3172) ([michalsn](https://github.com/michalsn))
- Add the upload artifact action [\#3167](https://github.com/codeigniter4/CodeIgniter4/pull/3167) ([paulbalandan](https://github.com/paulbalandan))
- More robust color support detection in CLI [\#3165](https://github.com/codeigniter4/CodeIgniter4/pull/3165) ([paulbalandan](https://github.com/paulbalandan))
- Fix testMode\(\) for batch methods in Model [\#3163](https://github.com/codeigniter4/CodeIgniter4/pull/3163) ([michalsn](https://github.com/michalsn))
- Update delete\_files\(\) helper function [\#3162](https://github.com/codeigniter4/CodeIgniter4/pull/3162) ([michalsn](https://github.com/michalsn))
- Refresh Composer files [\#3153](https://github.com/codeigniter4/CodeIgniter4/pull/3153) ([MGatner](https://github.com/MGatner))
- use writable directory for sqlite default location [\#3151](https://github.com/codeigniter4/CodeIgniter4/pull/3151) ([samsonasik](https://github.com/samsonasik))
- Add webp support to Image Manipulation Class [\#3084](https://github.com/codeigniter4/CodeIgniter4/pull/3084) ([nicojmb](https://github.com/nicojmb))
- Bug fix in Throttler class check validation [\#2873](https://github.com/codeigniter4/CodeIgniter4/pull/2873) ([jlamim](https://github.com/jlamim))

## [v4.0.3](https://github.com/codeigniter4/CodeIgniter4/tree/v4.0.3) (2020-05-01)

[Full Changelog](https://github.com/codeigniter4/CodeIgniter4/compare/4.0.2...v4.0.3)

**Fixed bugs:**

- Bug: is\_unique validation rule with model-\>save\(\) fails [\#2906](https://github.com/codeigniter4/CodeIgniter4/issues/2906)
- Bug:  Debugging toolbar not showing [\#2893](https://github.com/codeigniter4/CodeIgniter4/issues/2893)
- Bug: database query bug [\#2890](https://github.com/codeigniter4/CodeIgniter4/issues/2890)
- Bug: Routes - missing \(:id\) [\#2889](https://github.com/codeigniter4/CodeIgniter4/issues/2889)
- Bug: Typo example "Using Named Routes" [\#2888](https://github.com/codeigniter4/CodeIgniter4/issues/2888)
- Bug: I am able to make POST request to a GET route [\#2880](https://github.com/codeigniter4/CodeIgniter4/issues/2880)
- Bug: Argument 1 passed to CodeIgniter\Database\BaseResult::getFirstRow\(\) must be of the type string, null given, called in /system/Model.php on line 383 [\#2877](https://github.com/codeigniter4/CodeIgniter4/issues/2877)
- Bug: Can't override already defined language in pager [\#2875](https://github.com/codeigniter4/CodeIgniter4/issues/2875)
- Installation Problem: Composer installation halts while installing kint-php/kint [\#2863](https://github.com/codeigniter4/CodeIgniter4/issues/2863)
- Bug: `composer require codeigniter4/translations` without stable version [\#2862](https://github.com/codeigniter4/CodeIgniter4/issues/2862)
- Bug: Method Spoofing Validation [\#2855](https://github.com/codeigniter4/CodeIgniter4/issues/2855)
- Bug: You made it just as impossible to get going as Laravel – BUMMER! [\#2850](https://github.com/codeigniter4/CodeIgniter4/issues/2850)
- Bug: localised validation messages [\#2845](https://github.com/codeigniter4/CodeIgniter4/issues/2845)
- Bug: Commands discovery in custom namespaces [\#2840](https://github.com/codeigniter4/CodeIgniter4/issues/2840)
- Bug: When the $index parameter of getGetPost or getPostGet is null, you will get an error result [\#2839](https://github.com/codeigniter4/CodeIgniter4/issues/2839)
- Bug: delete\_cookie\(\) doesn't work [\#2836](https://github.com/codeigniter4/CodeIgniter4/issues/2836)
- Bug: Model save method not working if PRIMARY KEY is VARCHAR [\#2835](https://github.com/codeigniter4/CodeIgniter4/issues/2835)
- Bug: Alias Filter with Multiple Filter Class Doesn't Work [\#2831](https://github.com/codeigniter4/CodeIgniter4/issues/2831)
- Bug: Kint Config isnt loaded [\#2830](https://github.com/codeigniter4/CodeIgniter4/issues/2830)
- Bug: RouteCollection::resource\(\) doesn't work with grouped rules [\#2829](https://github.com/codeigniter4/CodeIgniter4/issues/2829)
- Bug: $forge Property in Seeder Class Never Initialize [\#2825](https://github.com/codeigniter4/CodeIgniter4/issues/2825)
- Bug: getSegments\(\) returns an array with 2 empty strings when accessing /  [\#2822](https://github.com/codeigniter4/CodeIgniter4/issues/2822)
- Bug: Cell Caching in View Cells and SOLVE! [\#2821](https://github.com/codeigniter4/CodeIgniter4/issues/2821)
- Bug: saveData option doesn't work in Views [\#2818](https://github.com/codeigniter4/CodeIgniter4/issues/2818)
- Bug: Validation placeholder not being replaced [\#2817](https://github.com/codeigniter4/CodeIgniter4/issues/2817)
- Bug: Problems with QueryBuilder when run multiple queries one by one [\#2800](https://github.com/codeigniter4/CodeIgniter4/issues/2800)
- Bug: Routing placeholder in "controller"part of route doesn't work [\#2787](https://github.com/codeigniter4/CodeIgniter4/issues/2787)
- Bug: session\(\)-\>push\(\) Strange behavior [\#2786](https://github.com/codeigniter4/CodeIgniter4/issues/2786)
- Bug: php spark serve [\#2784](https://github.com/codeigniter4/CodeIgniter4/issues/2784)
- Bug: Can't paginate query with group by [\#2776](https://github.com/codeigniter4/CodeIgniter4/issues/2776)
- Bug: negotiateLocale bug in Safari with fr-ca locale [\#2774](https://github.com/codeigniter4/CodeIgniter4/issues/2774)
- Bug: Controller in Sub Directory is not working [\#2764](https://github.com/codeigniter4/CodeIgniter4/issues/2764)
- Bug: rename release  By "v" [\#2763](https://github.com/codeigniter4/CodeIgniter4/issues/2763)
- Bug: db query '?' bind is not working when use sql with ':=' operator. [\#2762](https://github.com/codeigniter4/CodeIgniter4/issues/2762)
- Bug: Multiple select validation problem [\#2757](https://github.com/codeigniter4/CodeIgniter4/issues/2757)
- Bug: Official Site is not working [\#2749](https://github.com/codeigniter4/CodeIgniter4/issues/2749)
- Bug: Logger context placeholders {file} and {line} are wrong [\#2743](https://github.com/codeigniter4/CodeIgniter4/issues/2743)
- Bug: Decimal validation fails without leading digit [\#2740](https://github.com/codeigniter4/CodeIgniter4/issues/2740)
- Bug: Model insert Created\_at and updated\_at get when new record added [\#2737](https://github.com/codeigniter4/CodeIgniter4/issues/2737)
- Bug: appendHeader 500 error if header does not exist [\#2730](https://github.com/codeigniter4/CodeIgniter4/issues/2730)
- Bug: codeigniter4 download link 404 resource not found [\#2727](https://github.com/codeigniter4/CodeIgniter4/issues/2727)
- Bug: Logger `path` property ignored [\#2725](https://github.com/codeigniter4/CodeIgniter4/issues/2725)
- Bug: $this-\>request-\>getPost\(\) is empty when json is send by postman [\#2720](https://github.com/codeigniter4/CodeIgniter4/issues/2720)
- Bug: open path /0 at uri got error "Class Home does not exist" on development environment [\#2716](https://github.com/codeigniter4/CodeIgniter4/issues/2716)
- Bug: calling countAllResults after find\($id\) produce wrong result [\#2705](https://github.com/codeigniter4/CodeIgniter4/issues/2705)
- Bug: $routes-\>cli\(\) accessible via web browser if autoroute is true [\#2704](https://github.com/codeigniter4/CodeIgniter4/issues/2704)
- Bug: Controllers and Views in subdirectories not working [\#2701](https://github.com/codeigniter4/CodeIgniter4/issues/2701)
- Bug: undefined model method should throw exception [\#2688](https://github.com/codeigniter4/CodeIgniter4/issues/2688)
- Bug:   The custom error config of validation is not working [\#2678](https://github.com/codeigniter4/CodeIgniter4/issues/2678)
- Bug: Can't test redirect\(\)-\>route\('routename'\), while redirect\(\)-\>to\('path'\) is working with ControllerTester [\#2676](https://github.com/codeigniter4/CodeIgniter4/issues/2676)
- Bug: php spark migrate:create File -n NameSpace doesn't create migration class under NameSpace/Database/Migrations directory with composer autoload [\#2664](https://github.com/codeigniter4/CodeIgniter4/issues/2664)
- Bug: \I18n\Time object displaying +1 year when object date is set to 2021-12-31 [\#2663](https://github.com/codeigniter4/CodeIgniter4/issues/2663)
- Bug: Route options filter didn't working  [\#2654](https://github.com/codeigniter4/CodeIgniter4/issues/2654)
- Bug: Error in Seeder  [\#2653](https://github.com/codeigniter4/CodeIgniter4/issues/2653)
- Bug: spark no longer lists function when used by without any parameters [\#2645](https://github.com/codeigniter4/CodeIgniter4/issues/2645)
- Bug: Number Helper, Currency Fraction issue [\#2634](https://github.com/codeigniter4/CodeIgniter4/issues/2634)
- Bug: forceHTTPS method ignores baseURL configuration when redirecting [\#2633](https://github.com/codeigniter4/CodeIgniter4/issues/2633)
- While serving Application on CLI using different port debugbar is still using a default 8080 port [\#2630](https://github.com/codeigniter4/CodeIgniter4/issues/2630)
- Bug: spark migrate -all with appstarter [\#2627](https://github.com/codeigniter4/CodeIgniter4/issues/2627)
- Bug: Problem when compiled vendor as PHAR file [\#2623](https://github.com/codeigniter4/CodeIgniter4/issues/2623)
- Bug: debugbar javascript error [\#2621](https://github.com/codeigniter4/CodeIgniter4/issues/2621)
- Bug: ResourceController json response always empty [\#2617](https://github.com/codeigniter4/CodeIgniter4/issues/2617)
- Bug: Chrome logger does not work. [\#2616](https://github.com/codeigniter4/CodeIgniter4/issues/2616)
- Bug:  [\#2608](https://github.com/codeigniter4/CodeIgniter4/issues/2608)
- User Guide is not in HTML in the download file [\#2607](https://github.com/codeigniter4/CodeIgniter4/issues/2607)
- Unnecessary files in the download installation [\#2606](https://github.com/codeigniter4/CodeIgniter4/issues/2606)
- Bug:  Class 'Kint\Renderer\Renderer' not found [\#2605](https://github.com/codeigniter4/CodeIgniter4/issues/2605)
- Bug: Codeigniter4/framework composer.json not updated [\#2601](https://github.com/codeigniter4/CodeIgniter4/issues/2601)
- \[Docs\] Loading Environment into Configuration documentation described wrong [\#2554](https://github.com/codeigniter4/CodeIgniter4/issues/2554)
- Bug: Sessions dont work on PostgreSQL [\#2546](https://github.com/codeigniter4/CodeIgniter4/issues/2546)
- Bug: router service adds backslash to controllername if route is configured [\#2520](https://github.com/codeigniter4/CodeIgniter4/issues/2520)
- Bug: JSONFormatter-\>format\(\) cannot handle errordata, only outputs it's own error [\#2434](https://github.com/codeigniter4/CodeIgniter4/issues/2434)
- Bug: HTTP Feature Testing only runs the FIRST test [\#2393](https://github.com/codeigniter4/CodeIgniter4/issues/2393)
- Bug: Spark issue with PHP install location [\#2367](https://github.com/codeigniter4/CodeIgniter4/issues/2367)
- spark route issue [\#2194](https://github.com/codeigniter4/CodeIgniter4/issues/2194)

**Closed issues:**

- Modular MVP on CI4 [\#2900](https://github.com/codeigniter4/CodeIgniter4/issues/2900)
- About javascript: void \(0\); [\#2887](https://github.com/codeigniter4/CodeIgniter4/issues/2887)
- Entity returns null when used on the model [\#2838](https://github.com/codeigniter4/CodeIgniter4/issues/2838)
-  php spark migrate -g does not work [\#2832](https://github.com/codeigniter4/CodeIgniter4/issues/2832)
- Bug: Namespacing of app/Config folder vs. app/Controller [\#2826](https://github.com/codeigniter4/CodeIgniter4/issues/2826)
- Controller Call to a member function getPost\(\) on null  [\#2823](https://github.com/codeigniter4/CodeIgniter4/issues/2823)
- QueryBuilder - Does not support JOIN in UPDATE [\#2799](https://github.com/codeigniter4/CodeIgniter4/issues/2799)
- Database model error when limiting delete\(\) [\#2780](https://github.com/codeigniter4/CodeIgniter4/issues/2780)
- codeigniter4/codeigniter4 package not exists in packagist [\#2753](https://github.com/codeigniter4/CodeIgniter4/issues/2753)
- datamap Entities not works! [\#2747](https://github.com/codeigniter4/CodeIgniter4/issues/2747)
- Error: Call to undefined function CodeIgniter\CLI\mb\_strpos\(\) [\#2746](https://github.com/codeigniter4/CodeIgniter4/issues/2746)
- CodeIgniter\Log\Logger::logPath property is never used. [\#2738](https://github.com/codeigniter4/CodeIgniter4/issues/2738)
- Bug: set\_radio\(\) in Form Helper does not work when radio button value equals "0" [\#2728](https://github.com/codeigniter4/CodeIgniter4/issues/2728)
- Array validation has a problem [\#2714](https://github.com/codeigniter4/CodeIgniter4/issues/2714)
- delete cookie not working [\#2700](https://github.com/codeigniter4/CodeIgniter4/issues/2700)
- remove default language local from url [\#2682](https://github.com/codeigniter4/CodeIgniter4/issues/2682)
- OpenSSLHandler: Encrypt/Decrypt [\#2680](https://github.com/codeigniter4/CodeIgniter4/issues/2680)
- RESTFUL API with CORS problem [\#2667](https://github.com/codeigniter4/CodeIgniter4/issues/2667)
- I guess there's no the third parameter [\#2657](https://github.com/codeigniter4/CodeIgniter4/issues/2657)
- set ci4 repo default branch = master [\#2643](https://github.com/codeigniter4/CodeIgniter4/issues/2643)
- BUG: 4.0.2 Kint not found [\#2639](https://github.com/codeigniter4/CodeIgniter4/issues/2639)
- Feature: Migrate:Rollback/Refresh confirmation in production environment [\#2385](https://github.com/codeigniter4/CodeIgniter4/issues/2385)

**Merged pull requests:**

- 4.0.3 release [\#2912](https://github.com/codeigniter4/CodeIgniter4/pull/2912) ([lonnieezell](https://github.com/lonnieezell))
- url\_title\(\) used CI3 style in user guide [\#2911](https://github.com/codeigniter4/CodeIgniter4/pull/2911) ([jreklund](https://github.com/jreklund))
- fix undefined class 'CodeIgniter' [\#2910](https://github.com/codeigniter4/CodeIgniter4/pull/2910) ([PingZii](https://github.com/PingZii))
- Improved subjects in Controller and Routing chapter [\#2908](https://github.com/codeigniter4/CodeIgniter4/pull/2908) ([jreklund](https://github.com/jreklund))
- Fix Model::first\(\) only use orderBy\(\) when group by is not empty [\#2907](https://github.com/codeigniter4/CodeIgniter4/pull/2907) ([samsonasik](https://github.com/samsonasik))
- Allow bypassing content negotiation during API responses. [\#2904](https://github.com/codeigniter4/CodeIgniter4/pull/2904) ([lonnieezell](https://github.com/lonnieezell))
- Ugtweaks [\#2903](https://github.com/codeigniter4/CodeIgniter4/pull/2903) ([lonnieezell](https://github.com/lonnieezell))
- Carbonads [\#2902](https://github.com/codeigniter4/CodeIgniter4/pull/2902) ([lonnieezell](https://github.com/lonnieezell))
- Added information about the new features of the Pagination library [\#2901](https://github.com/codeigniter4/CodeIgniter4/pull/2901) ([jlamim](https://github.com/jlamim))
- New features for pagination [\#2899](https://github.com/codeigniter4/CodeIgniter4/pull/2899) ([jlamim](https://github.com/jlamim))
- Fixed lang\(\) example in user guide [\#2898](https://github.com/codeigniter4/CodeIgniter4/pull/2898) ([nmolinos](https://github.com/nmolinos))
- Make validation placeholders always available [\#2897](https://github.com/codeigniter4/CodeIgniter4/pull/2897) ([jreklund](https://github.com/jreklund))
- \[ci skip\] Add `make.bat` for Windows users [\#2895](https://github.com/codeigniter4/CodeIgniter4/pull/2895) ([paulbalandan](https://github.com/paulbalandan))
- Added ability to delete row with string primary key via Model::delete\($id\) [\#2894](https://github.com/codeigniter4/CodeIgniter4/pull/2894) ([samsonasik](https://github.com/samsonasik))
- Update of the pagination template to make the correct use of the locale [\#2892](https://github.com/codeigniter4/CodeIgniter4/pull/2892) ([jlamim](https://github.com/jlamim))
- \[ci skip\] route placeholders 'id' to 'num' [\#2891](https://github.com/codeigniter4/CodeIgniter4/pull/2891) ([Instrye](https://github.com/Instrye))
- \[ci skip\] fix warnings on compiling user guide [\#2886](https://github.com/codeigniter4/CodeIgniter4/pull/2886) ([paulbalandan](https://github.com/paulbalandan))
- Added more Common functions and improved rendering in userguide [\#2884](https://github.com/codeigniter4/CodeIgniter4/pull/2884) ([jreklund](https://github.com/jreklund))
- Build Your First Application used url\_title incorrectly [\#2883](https://github.com/codeigniter4/CodeIgniter4/pull/2883) ([jreklund](https://github.com/jreklund))
- \[User guide\] Correcting some details in the part that talks about model and entities [\#2878](https://github.com/codeigniter4/CodeIgniter4/pull/2878) ([jlamim](https://github.com/jlamim))
- Shifted basic URI Routing examples down [\#2874](https://github.com/codeigniter4/CodeIgniter4/pull/2874) ([nmolinos](https://github.com/nmolinos))
- Better locale matching against broad groups. Fixes \#2774 [\#2872](https://github.com/codeigniter4/CodeIgniter4/pull/2872) ([lonnieezell](https://github.com/lonnieezell))
- Fixes session active detection on force\_https function and add more test CodeIgniter::forceSecureAccess\(\) run force\_https\(\) [\#2871](https://github.com/codeigniter4/CodeIgniter4/pull/2871) ([samsonasik](https://github.com/samsonasik))
- clean up use statements: remove unused and sort [\#2870](https://github.com/codeigniter4/CodeIgniter4/pull/2870) ([samsonasik](https://github.com/samsonasik))
- more test for View::renderString\(\) for null tempData [\#2869](https://github.com/codeigniter4/CodeIgniter4/pull/2869) ([samsonasik](https://github.com/samsonasik))
- Localized label in validation rules [\#2868](https://github.com/codeigniter4/CodeIgniter4/pull/2868) ([michalsn](https://github.com/michalsn))
- \[ci skip\] update translations version [\#2867](https://github.com/codeigniter4/CodeIgniter4/pull/2867) ([Instrye](https://github.com/Instrye))
- Initialize $forge property in Seeder Class - fixes \#2825 [\#2864](https://github.com/codeigniter4/CodeIgniter4/pull/2864) ([jlamim](https://github.com/jlamim))
- fix. saveData not work [\#2861](https://github.com/codeigniter4/CodeIgniter4/pull/2861) ([Instrye](https://github.com/Instrye))
- fix. getGetPost and getPostGet can't work in index empty [\#2860](https://github.com/codeigniter4/CodeIgniter4/pull/2860) ([Instrye](https://github.com/Instrye))
- \[ci skip\]fix. getHeader return header object [\#2859](https://github.com/codeigniter4/CodeIgniter4/pull/2859) ([Instrye](https://github.com/Instrye))
- fix. filters alias multiple [\#2857](https://github.com/codeigniter4/CodeIgniter4/pull/2857) ([Instrye](https://github.com/Instrye))
- \[ci skip\] typo fix Initial Configuration & Set Up [\#2856](https://github.com/codeigniter4/CodeIgniter4/pull/2856) ([samsonasik](https://github.com/samsonasik))
- Enclose file paths in double quotes to capture spaces [\#2853](https://github.com/codeigniter4/CodeIgniter4/pull/2853) ([paulbalandan](https://github.com/paulbalandan))
- Strip directory separators from auto-generated cell cache name. Fixes… [\#2851](https://github.com/codeigniter4/CodeIgniter4/pull/2851) ([lonnieezell](https://github.com/lonnieezell))
- Normalize dir separator of Exceptions::cleanPath and added more paths to clean [\#2847](https://github.com/codeigniter4/CodeIgniter4/pull/2847) ([paulbalandan](https://github.com/paulbalandan))
- Improve readability in the userguide with a fixed size [\#2846](https://github.com/codeigniter4/CodeIgniter4/pull/2846) ([jreklund](https://github.com/jreklund))
- Fixed Issue \#2840 on discovery of classes by FileLocator [\#2844](https://github.com/codeigniter4/CodeIgniter4/pull/2844) ([paulbalandan](https://github.com/paulbalandan))
- add $segment parameter in pager call by Model.php [\#2843](https://github.com/codeigniter4/CodeIgniter4/pull/2843) ([paul45](https://github.com/paul45))
- Improve flash of unstyled content in userguide [\#2842](https://github.com/codeigniter4/CodeIgniter4/pull/2842) ([jreklund](https://github.com/jreklund))
- Add English message for "string" validation rule [\#2841](https://github.com/codeigniter4/CodeIgniter4/pull/2841) ([rmilecki](https://github.com/rmilecki))
- more tests for Common functions [\#2837](https://github.com/codeigniter4/CodeIgniter4/pull/2837) ([samsonasik](https://github.com/samsonasik))
- Pagination: open page \> pageCount get last page [\#2834](https://github.com/codeigniter4/CodeIgniter4/pull/2834) ([samsonasik](https://github.com/samsonasik))
- add ability for nested language definition [\#2833](https://github.com/codeigniter4/CodeIgniter4/pull/2833) ([samsonasik](https://github.com/samsonasik))
- Documentation fixes [\#2827](https://github.com/codeigniter4/CodeIgniter4/pull/2827) ([pjio](https://github.com/pjio))
- fix. URI path is empty [\#2824](https://github.com/codeigniter4/CodeIgniter4/pull/2824) ([Instrye](https://github.com/Instrye))
- ignore coverage on exit and die [\#2820](https://github.com/codeigniter4/CodeIgniter4/pull/2820) ([samsonasik](https://github.com/samsonasik))
- add respondUpdated\(\) method into API\ResponseTrait [\#2816](https://github.com/codeigniter4/CodeIgniter4/pull/2816) ([samsonasik](https://github.com/samsonasik))
- ignore coverage on !CI\_DEBUG [\#2814](https://github.com/codeigniter4/CodeIgniter4/pull/2814) ([samsonasik](https://github.com/samsonasik))
- Fix missing InvalidArgumentException in Database\BaseBuilder [\#2813](https://github.com/codeigniter4/CodeIgniter4/pull/2813) ([samsonasik](https://github.com/samsonasik))
- Ensure $\_SERVER\['SCRIPT\_NAME'\] ends with PHP [\#2810](https://github.com/codeigniter4/CodeIgniter4/pull/2810) ([willnode](https://github.com/willnode))
- make named constructor in Exception classes consistent: use return instead of throw [\#2809](https://github.com/codeigniter4/CodeIgniter4/pull/2809) ([samsonasik](https://github.com/samsonasik))
- Check if dataset is empty before Model update. [\#2808](https://github.com/codeigniter4/CodeIgniter4/pull/2808) ([vibbow](https://github.com/vibbow))
- test Controller::validate\(\) with string rules [\#2807](https://github.com/codeigniter4/CodeIgniter4/pull/2807) ([samsonasik](https://github.com/samsonasik))
- clean up buffer tweak in FeatureTestCaseTest  [\#2805](https://github.com/codeigniter4/CodeIgniter4/pull/2805) ([samsonasik](https://github.com/samsonasik))
- using realpath\(\) for define $pathsPath in index.php [\#2804](https://github.com/codeigniter4/CodeIgniter4/pull/2804) ([samsonasik](https://github.com/samsonasik))
- add ext-mbstring to required and update regex that sanitize file name [\#2803](https://github.com/codeigniter4/CodeIgniter4/pull/2803) ([samsonasik](https://github.com/samsonasik))
- Add resetting QBFrom part [\#2802](https://github.com/codeigniter4/CodeIgniter4/pull/2802) ([michalsn](https://github.com/michalsn))
- Update Routes.php [\#2801](https://github.com/codeigniter4/CodeIgniter4/pull/2801) ([mostafakhudair](https://github.com/mostafakhudair))
- add more test for Entity : 100% tested [\#2798](https://github.com/codeigniter4/CodeIgniter4/pull/2798) ([samsonasik](https://github.com/samsonasik))
- \[ci skip\] Fix download badge total shows [\#2797](https://github.com/codeigniter4/CodeIgniter4/pull/2797) ([samsonasik](https://github.com/samsonasik))
- test for I18n\Time::toFormattedDateString [\#2796](https://github.com/codeigniter4/CodeIgniter4/pull/2796) ([samsonasik](https://github.com/samsonasik))
- test Logger::determineFile\(\) for no stack trace [\#2795](https://github.com/codeigniter4/CodeIgniter4/pull/2795) ([samsonasik](https://github.com/samsonasik))
- test CLI\CLI::strlen\(null\) [\#2794](https://github.com/codeigniter4/CodeIgniter4/pull/2794) ([samsonasik](https://github.com/samsonasik))
- test for API\ResponseTrait::format\(\) with format is not json or xml [\#2793](https://github.com/codeigniter4/CodeIgniter4/pull/2793) ([samsonasik](https://github.com/samsonasik))
- test for View\Cell::render\(\) with class has initController\(\) method [\#2792](https://github.com/codeigniter4/CodeIgniter4/pull/2792) ([samsonasik](https://github.com/samsonasik))
- test Autoloader::initialize\(\) with composer path not found [\#2791](https://github.com/codeigniter4/CodeIgniter4/pull/2791) ([samsonasik](https://github.com/samsonasik))
- add ability to replace {locale} to request-\>getLocale\(\) in form\_open\('action'\) [\#2790](https://github.com/codeigniter4/CodeIgniter4/pull/2790) ([samsonasik](https://github.com/samsonasik))
- test for IncomingRequest::getFileMultiple\(\) [\#2789](https://github.com/codeigniter4/CodeIgniter4/pull/2789) ([samsonasik](https://github.com/samsonasik))
- add MockEmail class [\#2788](https://github.com/codeigniter4/CodeIgniter4/pull/2788) ([samsonasik](https://github.com/samsonasik))
- test for CodeIgniter\Config\Services::email\(\) [\#2785](https://github.com/codeigniter4/CodeIgniter4/pull/2785) ([samsonasik](https://github.com/samsonasik))
- make Model::paginate\(\) use default perPage from Config\Pager-\>perPage if $perPage parameter not passed [\#2782](https://github.com/codeigniter4/CodeIgniter4/pull/2782) ([samsonasik](https://github.com/samsonasik))
- \#2780 - LIMIT. [\#2781](https://github.com/codeigniter4/CodeIgniter4/pull/2781) ([nowackipawel](https://github.com/nowackipawel))
- \[ci skip\] \_remap method must have return [\#2779](https://github.com/codeigniter4/CodeIgniter4/pull/2779) ([Instrye](https://github.com/Instrye))
- Rework get\_filenames [\#2778](https://github.com/codeigniter4/CodeIgniter4/pull/2778) ([MGatner](https://github.com/MGatner))
- Fix \#2776 add ability to paginate\(\) query with group by [\#2777](https://github.com/codeigniter4/CodeIgniter4/pull/2777) ([samsonasik](https://github.com/samsonasik))
- Update on "Build Your First Application" [\#2775](https://github.com/codeigniter4/CodeIgniter4/pull/2775) ([jreklund](https://github.com/jreklund))
- Fix ? bind with := bind [\#2773](https://github.com/codeigniter4/CodeIgniter4/pull/2773) ([musmanikram](https://github.com/musmanikram))
- Fixed some styling in "Installation" chapter \[ci skip\] [\#2772](https://github.com/codeigniter4/CodeIgniter4/pull/2772) ([jreklund](https://github.com/jreklund))
- Uncommented tests, mistake in my previous PR :\( [\#2767](https://github.com/codeigniter4/CodeIgniter4/pull/2767) ([musmanikram](https://github.com/musmanikram))
- fix. MYSQLI::DBDebug can't woker [\#2755](https://github.com/codeigniter4/CodeIgniter4/pull/2755) ([Instrye](https://github.com/Instrye))
- fix. delete\_cookite can't delete alreday set Cookie [\#2709](https://github.com/codeigniter4/CodeIgniter4/pull/2709) ([Instrye](https://github.com/Instrye))
- Re-write userguide to support Python 3 and future proofing Sphinx [\#2671](https://github.com/codeigniter4/CodeIgniter4/pull/2671) ([jreklund](https://github.com/jreklund))
- Colored table in CLI [\#2624](https://github.com/codeigniter4/CodeIgniter4/pull/2624) ([enix-app](https://github.com/enix-app))

## [4.0.2](https://github.com/codeigniter4/CodeIgniter4/tree/4.0.2) (2020-02-25)

[Full Changelog](https://github.com/codeigniter4/CodeIgniter4/compare/v4.0.1...4.0.2)

**Fixed bugs:**

- Bug: Your requirements could not be resolved to an installable set of packages. [\#2613](https://github.com/codeigniter4/CodeIgniter4/issues/2613)

**Merged pull requests:**

- Removed unused test class that was causing appstarter not to work from CLI. [\#2614](https://github.com/codeigniter4/CodeIgniter4/pull/2614) ([lonnieezell](https://github.com/lonnieezell))
- \[UG\] Fix all Sphinx warnings [\#2611](https://github.com/codeigniter4/CodeIgniter4/pull/2611) ([LittleJ](https://github.com/LittleJ))
- \[UG\] Sphinx\_rtd\_theme fixes and improvements [\#2610](https://github.com/codeigniter4/CodeIgniter4/pull/2610) ([LittleJ](https://github.com/LittleJ))

## [v4.0.1](https://github.com/codeigniter4/CodeIgniter4/tree/v4.0.1) (2020-02-24)

[Full Changelog](https://github.com/codeigniter4/CodeIgniter4/compare/4.0.0...v4.0.1)

**Fixed bugs:**

- Bug: Difficult architecture of the codeigniter4 project [\#2602](https://github.com/codeigniter4/CodeIgniter4/issues/2602)
- Bug: mentioned rc4 in the changelog file of V4 userguide [\#2599](https://github.com/codeigniter4/CodeIgniter4/issues/2599)

**Merged pull requests:**

- \[doc\] Removal of the 'rc' parameter from the installation / update co… [\#2604](https://github.com/codeigniter4/CodeIgniter4/pull/2604) ([jlamim](https://github.com/jlamim))

## [4.0.0](https://github.com/codeigniter4/CodeIgniter4/tree/4.0.0) (2020-02-24)

[Full Changelog](https://github.com/codeigniter4/CodeIgniter4/compare/v4.0.0-rc.4...4.0.0)

**Fixed bugs:**

- Bug: CI cant display error with API Response Trait [\#2586](https://github.com/codeigniter4/CodeIgniter4/issues/2586)
- Bug: testOrHavingBy\(\) requires a select [\#2584](https://github.com/codeigniter4/CodeIgniter4/issues/2584)
- Bug: Email-\>validateEmail\(\) on wrong email address leads to TypeError  [\#2580](https://github.com/codeigniter4/CodeIgniter4/issues/2580)
- Bug: $forge-\>dropColumn not allowing arrays [\#2576](https://github.com/codeigniter4/CodeIgniter4/issues/2576)
- Bug: CI4 set ID to NULL when I try insert a row [\#2557](https://github.com/codeigniter4/CodeIgniter4/issues/2557)
- "Required" rules are not being enforced at all by the model [\#2555](https://github.com/codeigniter4/CodeIgniter4/issues/2555)
- Bug: Debug Bar showing before the head tag [\#2545](https://github.com/codeigniter4/CodeIgniter4/issues/2545)
- Bug: Docs / Library Reference / Validation: missing important information on reset\(\) method [\#2535](https://github.com/codeigniter4/CodeIgniter4/issues/2535)
- Bug: system/Config/BaseService.php getSharedInstance\(\) will not return mocks with non-lowercase key [\#2534](https://github.com/codeigniter4/CodeIgniter4/issues/2534)
- Bug: multi-column WHERE not prefixed [\#2532](https://github.com/codeigniter4/CodeIgniter4/issues/2532)
- Bug: CodeIgniter\Files\File::getSize\(\) - wrong type of result [\#2476](https://github.com/codeigniter4/CodeIgniter4/issues/2476)
- Bug: Move work with $\_GET\['page'\] from Model to Pager [\#2467](https://github.com/codeigniter4/CodeIgniter4/issues/2467)
- Bug: ImageHandler has no reality checks [\#2421](https://github.com/codeigniter4/CodeIgniter4/issues/2421)
- Bug: No default HTTP protocol version is set when creating a new Response [\#2383](https://github.com/codeigniter4/CodeIgniter4/issues/2383)
- Bug: Filter wildcards ignore default methods [\#2455](https://github.com/codeigniter4/CodeIgniter4/issues/2455)
- trailing slash cause redirect to root [\#2445](https://github.com/codeigniter4/CodeIgniter4/issues/2445)
- Bug: Commands cannot accept many paths [\#2148](https://github.com/codeigniter4/CodeIgniter4/issues/2148)

**Closed issues:**

- Add an 'alpha\_numeric\_punct' rule to FormatRules [\#2549](https://github.com/codeigniter4/CodeIgniter4/issues/2549)
- Feature: Forge, Adding Foreign key for table already created  [\#2543](https://github.com/codeigniter4/CodeIgniter4/issues/2543)
- Error In UserGuide [\#2530](https://github.com/codeigniter4/CodeIgniter4/issues/2530)
- Lack of test on Travis for PHP 7.4 and tests don't run on upcoming PHP versions [\#2293](https://github.com/codeigniter4/CodeIgniter4/issues/2293)
- Feature: model helper  [\#2292](https://github.com/codeigniter4/CodeIgniter4/issues/2292)
- Issue with layouts renderer and sections =\> merge sections in one view [\#2491](https://github.com/codeigniter4/CodeIgniter4/issues/2491)
- Update package dependency version for kint-php/kint [\#2373](https://github.com/codeigniter4/CodeIgniter4/issues/2373)

**Merged pull requests:**

- Deprecate Devstarter, add `builds` [\#2598](https://github.com/codeigniter4/CodeIgniter4/pull/2598) ([MGatner](https://github.com/MGatner))
- Fix typo issues causing Sphinx warnings + Update "Welcome page" screenshot [\#2597](https://github.com/codeigniter4/CodeIgniter4/pull/2597) ([LittleJ](https://github.com/LittleJ))
- Update sphinx\_rtd\_theme from version 0.2.4 to version 0.4.3 [\#2596](https://github.com/codeigniter4/CodeIgniter4/pull/2596) ([LittleJ](https://github.com/LittleJ))
- Fixed Bug: testOrHavingBy\(\) requires a select | \#2584 [\#2595](https://github.com/codeigniter4/CodeIgniter4/pull/2595) ([jlamim](https://github.com/jlamim))
- Add Slack to the "Support" section of the documentation [\#2594](https://github.com/codeigniter4/CodeIgniter4/pull/2594) ([LittleJ](https://github.com/LittleJ))
- Fixed links to the AJAX Requests details page [\#2593](https://github.com/codeigniter4/CodeIgniter4/pull/2593) ([jlamim](https://github.com/jlamim))
- Add color scheme information in the documentation [\#2592](https://github.com/codeigniter4/CodeIgniter4/pull/2592) ([LittleJ](https://github.com/LittleJ))
- User Guide authentication recommendations [\#2591](https://github.com/codeigniter4/CodeIgniter4/pull/2591) ([MGatner](https://github.com/MGatner))
- Add a "Go further" section on the welcome page [\#2590](https://github.com/codeigniter4/CodeIgniter4/pull/2590) ([LittleJ](https://github.com/LittleJ))
- Add DotEnv::parse\(\) [\#2588](https://github.com/codeigniter4/CodeIgniter4/pull/2588) ([MGatner](https://github.com/MGatner))
- Better debug routes [\#2587](https://github.com/codeigniter4/CodeIgniter4/pull/2587) ([atishamte](https://github.com/atishamte))
- Typos change in contributing.md [\#2583](https://github.com/codeigniter4/CodeIgniter4/pull/2583) ([atishamte](https://github.com/atishamte))
- Fix type error in email validation [\#2582](https://github.com/codeigniter4/CodeIgniter4/pull/2582) ([musmanikram](https://github.com/musmanikram))
- Fixed Markdown heading [\#2581](https://github.com/codeigniter4/CodeIgniter4/pull/2581) ([ImMaax](https://github.com/ImMaax))
- Cache FileHandler unlink exception [\#2579](https://github.com/codeigniter4/CodeIgniter4/pull/2579) ([MGatner](https://github.com/MGatner))
- Fix drop column with array [\#2578](https://github.com/codeigniter4/CodeIgniter4/pull/2578) ([musmanikram](https://github.com/musmanikram))
- Refactor Tests [\#2577](https://github.com/codeigniter4/CodeIgniter4/pull/2577) ([MGatner](https://github.com/MGatner))
- Subfolder base\_url\(\) with parameter [\#2574](https://github.com/codeigniter4/CodeIgniter4/pull/2574) ([MGatner](https://github.com/MGatner))
- Image verification [\#2573](https://github.com/codeigniter4/CodeIgniter4/pull/2573) ([MGatner](https://github.com/MGatner))
- Use default protocol if unspecified [\#2572](https://github.com/codeigniter4/CodeIgniter4/pull/2572) ([MGatner](https://github.com/MGatner))
- Retain CLI segments [\#2571](https://github.com/codeigniter4/CodeIgniter4/pull/2571) ([MGatner](https://github.com/MGatner))
- Model's set method should accept not only string [\#2570](https://github.com/codeigniter4/CodeIgniter4/pull/2570) ([nowackipawel](https://github.com/nowackipawel))
- Use lowercase service names [\#2569](https://github.com/codeigniter4/CodeIgniter4/pull/2569) ([MGatner](https://github.com/MGatner))
- Apply User Guide code style to Errors [\#2567](https://github.com/codeigniter4/CodeIgniter4/pull/2567) ([MGatner](https://github.com/MGatner))
- Move debug toolbar after head tag. Fixes \#2545 [\#2566](https://github.com/codeigniter4/CodeIgniter4/pull/2566) ([MGatner](https://github.com/MGatner))
- Updates To Kint Loading [\#2565](https://github.com/codeigniter4/CodeIgniter4/pull/2565) ([najdanovicivan](https://github.com/najdanovicivan))
- Updated loader and composer script to use Kint 3.3 for \#2373 [\#2564](https://github.com/codeigniter4/CodeIgniter4/pull/2564) ([lonnieezell](https://github.com/lonnieezell))
- Added rule "alpha\_numeric\_punct" [\#2562](https://github.com/codeigniter4/CodeIgniter4/pull/2562) ([dafriend](https://github.com/dafriend))
- Fix - Add ajax to docs "General " page \[ci skip\] [\#2561](https://github.com/codeigniter4/CodeIgniter4/pull/2561) ([dafriend](https://github.com/dafriend))
- MySQLi: Incorrect DBDebug flag used for connection charset [\#2558](https://github.com/codeigniter4/CodeIgniter4/pull/2558) ([jreklund](https://github.com/jreklund))
- Update File.php [\#2552](https://github.com/codeigniter4/CodeIgniter4/pull/2552) ([thanhtaivtt](https://github.com/thanhtaivtt))
- disable buffer check on "testing" environment [\#2551](https://github.com/codeigniter4/CodeIgniter4/pull/2551) ([samsonasik](https://github.com/samsonasik))
- Improved view: welcome\_message.php [\#2550](https://github.com/codeigniter4/CodeIgniter4/pull/2550) ([Vizzielli](https://github.com/Vizzielli))
- Add retry creation server when the port is used [\#2544](https://github.com/codeigniter4/CodeIgniter4/pull/2544) ([thanhtaivtt](https://github.com/thanhtaivtt))
- New "welcome" page [\#2541](https://github.com/codeigniter4/CodeIgniter4/pull/2541) ([LittleJ](https://github.com/LittleJ))
- valid\_ip removed $data which was causing exception [\#2540](https://github.com/codeigniter4/CodeIgniter4/pull/2540) ([nowackipawel](https://github.com/nowackipawel))
- explanation of reset\(\) method in section Working With Validation fixes \#2535 [\#2539](https://github.com/codeigniter4/CodeIgniter4/pull/2539) ([bivanbi](https://github.com/bivanbi))
- Update TravisCI config for PHP7.4 [\#2537](https://github.com/codeigniter4/CodeIgniter4/pull/2537) ([musmanikram](https://github.com/musmanikram))
- Fix multi-column WHERE not prefixed with DBPrefix [\#2533](https://github.com/codeigniter4/CodeIgniter4/pull/2533) ([musmanikram](https://github.com/musmanikram))
- Update images.rst [\#2529](https://github.com/codeigniter4/CodeIgniter4/pull/2529) ([avegacms](https://github.com/avegacms))
- Added new model helper method. [\#2514](https://github.com/codeigniter4/CodeIgniter4/pull/2514) ([lonnieezell](https://github.com/lonnieezell))
- Debug bar: Dark/light mode + Complete CSS refactoring [\#2478](https://github.com/codeigniter4/CodeIgniter4/pull/2478) ([LittleJ](https://github.com/LittleJ))
- WIP Update Validation.php [\#2083](https://github.com/codeigniter4/CodeIgniter4/pull/2083) ([MohKari](https://github.com/MohKari))

## [v4.0.0-rc.4](https://github.com/codeigniter4/CodeIgniter4/tree/v4.0.0-rc.4) (2020-02-07)

[Full Changelog](https://github.com/codeigniter4/CodeIgniter4/compare/v4.0.0-rc.3...v4.0.0-rc.4)

**Fixed bugs:**

- Bug: Class 'Locale' not found when I try to use Time::parse\(\) [\#2519](https://github.com/codeigniter4/CodeIgniter4/issues/2519)
- Bug: Wrong Links for pager when having subfolders [\#2505](https://github.com/codeigniter4/CodeIgniter4/issues/2505)
- View Cell render not be call initController [\#2500](https://github.com/codeigniter4/CodeIgniter4/issues/2500)
- Bug: user guide compile failed. [\#2492](https://github.com/codeigniter4/CodeIgniter4/issues/2492)
- Bug: setAutoRoute to false not working [\#2480](https://github.com/codeigniter4/CodeIgniter4/issues/2480)
- Bug: CodeIgniter\Router\Router-\>checkRoutes\(\) strpos\(\) expects parameter 1 to be string, array given [\#2479](https://github.com/codeigniter4/CodeIgniter4/issues/2479)
- Bug: Unable to use ul\(\) helper with out modification [\#2473](https://github.com/codeigniter4/CodeIgniter4/issues/2473)
- Bug: Cannot pass value in the function in ControllerTest [\#2470](https://github.com/codeigniter4/CodeIgniter4/issues/2470)
- Bug: $useTimestamps cause insert\(\) exception [\#2469](https://github.com/codeigniter4/CodeIgniter4/issues/2469)
- Bug: prototype of setBody\($data\); method in CodeIgniter\HTTP\Message should be changed to setBody\(string $data\); [\#2466](https://github.com/codeigniter4/CodeIgniter4/issues/2466)
- Don't update it. It's rubbishBug:  [\#2463](https://github.com/codeigniter4/CodeIgniter4/issues/2463)
- Bug:  Autoload over Composer [\#2461](https://github.com/codeigniter4/CodeIgniter4/issues/2461)
- Bug: The docs say `composer install` instead of `composer required` [\#2457](https://github.com/codeigniter4/CodeIgniter4/issues/2457)
- Bug: if not CSPEnaled but i have some  [\#2456](https://github.com/codeigniter4/CodeIgniter4/issues/2456)
- Bug: IsAJAX\(\) relies on inconsistent headers [\#2454](https://github.com/codeigniter4/CodeIgniter4/issues/2454)
- Bug:  [\#2448](https://github.com/codeigniter4/CodeIgniter4/issues/2448)
- Bug: Double use where and etc [\#2444](https://github.com/codeigniter4/CodeIgniter4/issues/2444)
- Bug: Double use esc function with form\_input and  etc... [\#2443](https://github.com/codeigniter4/CodeIgniter4/issues/2443)
- Bug: Entity casts do not cast the original data [\#2441](https://github.com/codeigniter4/CodeIgniter4/issues/2441)
- Bug: namespace view returns empty value [\#2440](https://github.com/codeigniter4/CodeIgniter4/issues/2440)
- Bug: php spark Call to undefined function CodeIgniter\Autoloader\get\_filenames\(\) [\#2439](https://github.com/codeigniter4/CodeIgniter4/issues/2439)
- Bug: Curly brace deprecation [\#2430](https://github.com/codeigniter4/CodeIgniter4/issues/2430)
- Bug: Routes and Namespace [\#2423](https://github.com/codeigniter4/CodeIgniter4/issues/2423)
- Bug: Validation not working  [\#2418](https://github.com/codeigniter4/CodeIgniter4/issues/2418)
- Bug: Baseservice - getSharedInstance [\#2414](https://github.com/codeigniter4/CodeIgniter4/issues/2414)
- Bug: base\_url\(\) and redirect\(\) not honoring baseURL with paths [\#2409](https://github.com/codeigniter4/CodeIgniter4/issues/2409)
- form\_input double escaping data why ? [\#2405](https://github.com/codeigniter4/CodeIgniter4/issues/2405)
- Bug: initController not called in ResourceController [\#2404](https://github.com/codeigniter4/CodeIgniter4/issues/2404)
- Bug:  [\#2397](https://github.com/codeigniter4/CodeIgniter4/issues/2397)
- URL Helper safe\_mailto  UTF8Bug:  [\#2396](https://github.com/codeigniter4/CodeIgniter4/issues/2396)
- CSRF Filter redirect back not working [\#2395](https://github.com/codeigniter4/CodeIgniter4/issues/2395)
- Bug: 404 error page override with cache [\#2391](https://github.com/codeigniter4/CodeIgniter4/issues/2391)
- Bug: Mixed migration formats don't order [\#2386](https://github.com/codeigniter4/CodeIgniter4/issues/2386)
- Bug: \CodeIgniter\Model::validate\(\) returns TRUE if $data is empty [\#2384](https://github.com/codeigniter4/CodeIgniter4/issues/2384)
- Bug: Usage of `static::methodName` in  CodeIgniter\Config\Services prevents Service overriding [\#2376](https://github.com/codeigniter4/CodeIgniter4/issues/2376)
- Bug: Duplicate headers in response [\#2375](https://github.com/codeigniter4/CodeIgniter4/issues/2375)
- Bug: Nothing work with minimal config \(DIRECTORY SEPARATOR\) [\#2370](https://github.com/codeigniter4/CodeIgniter4/issues/2370)
- Bug: current\_url function not working as expected. [\#2365](https://github.com/codeigniter4/CodeIgniter4/issues/2365)
- Bug: localhost development server after edit the content not updated or reloaded [\#2363](https://github.com/codeigniter4/CodeIgniter4/issues/2363)
- Bug: with the parser, nl2br in a foreach duplicates entries. [\#2360](https://github.com/codeigniter4/CodeIgniter4/issues/2360)
- Bug: Prevents the use of global functions with parameters \[Validation\] [\#2357](https://github.com/codeigniter4/CodeIgniter4/issues/2357)
- Bug: lang\('app.name'\) should prefer APPPATH.Language/Validation/en/app.php over installed packages [\#2354](https://github.com/codeigniter4/CodeIgniter4/issues/2354)
- Bug: Inappropriate delimiter used in fillRouteParams [\#2353](https://github.com/codeigniter4/CodeIgniter4/issues/2353)
- Bug: Please there is issue on the time and date guide in codeigniter 4 [\#2351](https://github.com/codeigniter4/CodeIgniter4/issues/2351)
- Bug: Model\(\)-\>find\(null\) should return null value [\#2350](https://github.com/codeigniter4/CodeIgniter4/issues/2350)
- Bug: URL, Redirect and Pagination misbehave [\#2347](https://github.com/codeigniter4/CodeIgniter4/issues/2347)
- Bug: Toolbar ErrorException  Division by zero [\#2340](https://github.com/codeigniter4/CodeIgniter4/issues/2340)
- Bug: Cannot pass a string param with a space to a custom parser plugin [\#2318](https://github.com/codeigniter4/CodeIgniter4/issues/2318)
- Bug: Logger Path Duplicated [\#2286](https://github.com/codeigniter4/CodeIgniter4/issues/2286)
- Bug: Email:  SMTP Protocol Implementation @ Data Termination [\#2274](https://github.com/codeigniter4/CodeIgniter4/issues/2274)
- Bug: Redirect to route ignores path set in baseurl [\#2119](https://github.com/codeigniter4/CodeIgniter4/issues/2119)

**Closed issues:**

- $routes not do the job as well [\#2531](https://github.com/codeigniter4/CodeIgniter4/issues/2531)
- Multiple composer.json handling in codeigniter!! [\#2528](https://github.com/codeigniter4/CodeIgniter4/issues/2528)
- Mention about events in the upgrading doc pages [\#2521](https://github.com/codeigniter4/CodeIgniter4/issues/2521)
- Missing Constant in E-Mail [\#2512](https://github.com/codeigniter4/CodeIgniter4/issues/2512)
- Image Manipulation Class [\#2498](https://github.com/codeigniter4/CodeIgniter4/issues/2498)
- Schema param in the .env file [\#2483](https://github.com/codeigniter4/CodeIgniter4/issues/2483)
- system\Database\MigrationRunner-\>regress\(\) resets instance variable $namespace to null [\#2474](https://github.com/codeigniter4/CodeIgniter4/issues/2474)
- Issue passing data to views [\#2464](https://github.com/codeigniter4/CodeIgniter4/issues/2464)
- currentURL & previousURL doesnt work in parser [\#2460](https://github.com/codeigniter4/CodeIgniter4/issues/2460)
- Double quotes [\#2459](https://github.com/codeigniter4/CodeIgniter4/issues/2459)
- Feature about Localization [\#2419](https://github.com/codeigniter4/CodeIgniter4/issues/2419)
- Documentation Fix Needed [\#2412](https://github.com/codeigniter4/CodeIgniter4/issues/2412)
- No such file or Directory found In Ubuntu 19.10 [\#2394](https://github.com/codeigniter4/CodeIgniter4/issues/2394)
- previous\_url\(\) not loading the base path together [\#2378](https://github.com/codeigniter4/CodeIgniter4/issues/2378)
- Wrong Logo on GitHub page [\#2372](https://github.com/codeigniter4/CodeIgniter4/issues/2372)
- How to use the pagination with view parser? [\#2371](https://github.com/codeigniter4/CodeIgniter4/issues/2371)
- Feature Request: Validation: in\_db\[table.field\] [\#2366](https://github.com/codeigniter4/CodeIgniter4/issues/2366)
- Feature request [\#2361](https://github.com/codeigniter4/CodeIgniter4/issues/2361)
- Feature: AJAX route option [\#2310](https://github.com/codeigniter4/CodeIgniter4/issues/2310)
- Return value of CodeIgniter\Database\BaseConnection::getConnectStart\(\) must be of the type float, null returned [\#2158](https://github.com/codeigniter4/CodeIgniter4/issues/2158)
- Create Security Guideline [\#73](https://github.com/codeigniter4/CodeIgniter4/issues/73)

**Merged pull requests:**

- Update manual.rst [\#2527](https://github.com/codeigniter4/CodeIgniter4/pull/2527) ([avegacms](https://github.com/avegacms))
- Page in the official documentation on ajax requests with iSAJAX\(\) fixes \#2454 [\#2526](https://github.com/codeigniter4/CodeIgniter4/pull/2526) ([jlamim](https://github.com/jlamim))
- Remove incorrect inline doc type [\#2525](https://github.com/codeigniter4/CodeIgniter4/pull/2525) ([MGatner](https://github.com/MGatner))
- Restore namespace after regress. Fixes \#2474 [\#2524](https://github.com/codeigniter4/CodeIgniter4/pull/2524) ([MGatner](https://github.com/MGatner))
- Replace legacy CI3 constant. Fixes \#2512 [\#2523](https://github.com/codeigniter4/CodeIgniter4/pull/2523) ([MGatner](https://github.com/MGatner))
- Adding Events information in the 'Upgrading from 3.x to 4.x' section [\#2522](https://github.com/codeigniter4/CodeIgniter4/pull/2522) ([jlamim](https://github.com/jlamim))
- Fix pager URI to work in subfolders. [\#2518](https://github.com/codeigniter4/CodeIgniter4/pull/2518) ([lonnieezell](https://github.com/lonnieezell))
- HTML Helper - Fix attribute type for lists [\#2516](https://github.com/codeigniter4/CodeIgniter4/pull/2516) ([najdanovicivan](https://github.com/najdanovicivan))
- Layout Renderer Fix [\#2515](https://github.com/codeigniter4/CodeIgniter4/pull/2515) ([najdanovicivan](https://github.com/najdanovicivan))
- \[ci skip\] Typo in userguide "Entity Classes - Business Logic" [\#2513](https://github.com/codeigniter4/CodeIgniter4/pull/2513) ([jreklund](https://github.com/jreklund))
- Database add highlight [\#2511](https://github.com/codeigniter4/CodeIgniter4/pull/2511) ([MashinaMashina](https://github.com/MashinaMashina))
- Revert Renderer section reset [\#2509](https://github.com/codeigniter4/CodeIgniter4/pull/2509) ([MGatner](https://github.com/MGatner))
- Update ordering of search locations for better prioritization. Fixes \#2354 [\#2507](https://github.com/codeigniter4/CodeIgniter4/pull/2507) ([lonnieezell](https://github.com/lonnieezell))
- Proposal: HTTP Response - Fix crash on CSP methods CSP is disabled [\#2506](https://github.com/codeigniter4/CodeIgniter4/pull/2506) ([najdanovicivan](https://github.com/najdanovicivan))
- BaseConnection - Nullable return type in getConnectStart\(\) [\#2504](https://github.com/codeigniter4/CodeIgniter4/pull/2504) ([najdanovicivan](https://github.com/najdanovicivan))
- View Renderer - Reset sections after generating the ouput [\#2502](https://github.com/codeigniter4/CodeIgniter4/pull/2502) ([najdanovicivan](https://github.com/najdanovicivan))
- view\_cell call controller on initController method. [\#2501](https://github.com/codeigniter4/CodeIgniter4/pull/2501) ([byazrail](https://github.com/byazrail))
- View Parser - Fix ParsePair\(\) with filter [\#2499](https://github.com/codeigniter4/CodeIgniter4/pull/2499) ([najdanovicivan](https://github.com/najdanovicivan))
- Fix splitQueryPart\(\) [\#2497](https://github.com/codeigniter4/CodeIgniter4/pull/2497) ([MashinaMashina](https://github.com/MashinaMashina))
- Use site\_url for RedirectResponse. Fixes \#2119 [\#2496](https://github.com/codeigniter4/CodeIgniter4/pull/2496) ([lonnieezell](https://github.com/lonnieezell))
- \[ci skip\] update toolbar userguide [\#2495](https://github.com/codeigniter4/CodeIgniter4/pull/2495) ([Instrye](https://github.com/Instrye))
- Debug Toolbar - Fix Debugbar-Time header, Render in \<head\> [\#2494](https://github.com/codeigniter4/CodeIgniter4/pull/2494) ([najdanovicivan](https://github.com/najdanovicivan))
- fix sphinx version. [\#2493](https://github.com/codeigniter4/CodeIgniter4/pull/2493) ([ytetsuro](https://github.com/ytetsuro))
- fix. Toolbar init view Error [\#2490](https://github.com/codeigniter4/CodeIgniter4/pull/2490) ([Instrye](https://github.com/Instrye))
- Fix pager [\#2489](https://github.com/codeigniter4/CodeIgniter4/pull/2489) ([MashinaMashina](https://github.com/MashinaMashina))
- Update current\_url and previous\_url in the docs for View Parser. Fixes \#2460 [\#2486](https://github.com/codeigniter4/CodeIgniter4/pull/2486) ([lonnieezell](https://github.com/lonnieezell))
- Typo in user guide "Running via the Command Line" [\#2485](https://github.com/codeigniter4/CodeIgniter4/pull/2485) ([jreklund](https://github.com/jreklund))
- Services request add URI Core System extend support [\#2482](https://github.com/codeigniter4/CodeIgniter4/pull/2482) ([byazrail](https://github.com/byazrail))
- Fix \#2479. Priority Redirection. [\#2481](https://github.com/codeigniter4/CodeIgniter4/pull/2481) ([Instrye](https://github.com/Instrye))
- ControllerTest should work without URI specified. Fixes \#2470 [\#2472](https://github.com/codeigniter4/CodeIgniter4/pull/2472) ([lonnieezell](https://github.com/lonnieezell))
- Transition from Zend Escaper to Laminas Escaper  [\#2471](https://github.com/codeigniter4/CodeIgniter4/pull/2471) ([lonnieezell](https://github.com/lonnieezell))
-  Fix impossible length for migration table id. [\#2462](https://github.com/codeigniter4/CodeIgniter4/pull/2462) ([ytetsuro](https://github.com/ytetsuro))
- Replace `composer install` by `composer require` [\#2458](https://github.com/codeigniter4/CodeIgniter4/pull/2458) ([SteeveDroz](https://github.com/SteeveDroz))
- \[ci skip\] Error correction in reference to Query Builder emptyTable m… [\#2452](https://github.com/codeigniter4/CodeIgniter4/pull/2452) ([jlamim](https://github.com/jlamim))
- CRITICAL when $\_SESSION is null / Argument 2 passed to dot\_array\_search\(\) must be \[\] [\#2450](https://github.com/codeigniter4/CodeIgniter4/pull/2450) ([nowackipawel](https://github.com/nowackipawel))
- User Guide: Query Builder selectCount - error correction in example [\#2449](https://github.com/codeigniter4/CodeIgniter4/pull/2449) ([jlamim](https://github.com/jlamim))
- Existing File checks \(Nowackipawel/patch-69\) [\#2447](https://github.com/codeigniter4/CodeIgniter4/pull/2447) ([MGatner](https://github.com/MGatner))
- DB Insert Ignore \(Tada5hi/database-feature\) [\#2446](https://github.com/codeigniter4/CodeIgniter4/pull/2446) ([MGatner](https://github.com/MGatner))
- Nice array view in debug toolbar  [\#2438](https://github.com/codeigniter4/CodeIgniter4/pull/2438) ([MashinaMashina](https://github.com/MashinaMashina))
- \[ci skip\] Fix Message method reference [\#2436](https://github.com/codeigniter4/CodeIgniter4/pull/2436) ([MGatner](https://github.com/MGatner))
- Inserting through a model should respect all validation rules. Fixes \#2384 [\#2433](https://github.com/codeigniter4/CodeIgniter4/pull/2433) ([lonnieezell](https://github.com/lonnieezell))
- Fix curly brace deprecation in php 7.4 [\#2432](https://github.com/codeigniter4/CodeIgniter4/pull/2432) ([musmanikram](https://github.com/musmanikram))
- fix. safe\_mailto multi-byte safe [\#2429](https://github.com/codeigniter4/CodeIgniter4/pull/2429) ([Instrye](https://github.com/Instrye))
- Add $recipients property to Config\Email [\#2427](https://github.com/codeigniter4/CodeIgniter4/pull/2427) ([dafriend](https://github.com/dafriend))
- Add hex validation rule, test, Guide [\#2426](https://github.com/codeigniter4/CodeIgniter4/pull/2426) ([MGatner](https://github.com/MGatner))
- fix: Router setDefaultNameSpace can't worker [\#2425](https://github.com/codeigniter4/CodeIgniter4/pull/2425) ([Instrye](https://github.com/Instrye))
- Don't show duplicate Date headers when running under PHPs server. Fixes \#2375 [\#2422](https://github.com/codeigniter4/CodeIgniter4/pull/2422) ([lonnieezell](https://github.com/lonnieezell))
- Change current\_url\(\) to use cloned URI [\#2420](https://github.com/codeigniter4/CodeIgniter4/pull/2420) ([MGatner](https://github.com/MGatner))
- Revise Encryption Service Documentation \[ci skip\] [\#2417](https://github.com/codeigniter4/CodeIgniter4/pull/2417) ([dafriend](https://github.com/dafriend))
- Add missing closing braces of condition 'hasError\(\)' under Check If… [\#2416](https://github.com/codeigniter4/CodeIgniter4/pull/2416) ([musmanikram](https://github.com/musmanikram))
- Add 'nullable' to MySQL field data [\#2415](https://github.com/codeigniter4/CodeIgniter4/pull/2415) ([MGatner](https://github.com/MGatner))
- fix. toolbar file 301 [\#2413](https://github.com/codeigniter4/CodeIgniter4/pull/2413) ([Instrye](https://github.com/Instrye))
- \#2318 - fix parse params of plugin [\#2411](https://github.com/codeigniter4/CodeIgniter4/pull/2411) ([oleg1540](https://github.com/oleg1540))
- Looks like a typo. [\#2410](https://github.com/codeigniter4/CodeIgniter4/pull/2410) ([AndiKod](https://github.com/AndiKod))
- Ensure previous\_url\(\) gets accurate URI. [\#2408](https://github.com/codeigniter4/CodeIgniter4/pull/2408) ([lonnieezell](https://github.com/lonnieezell))
- Fix url helper functions to work when site hosted in subfolders.  [\#2407](https://github.com/codeigniter4/CodeIgniter4/pull/2407) ([lonnieezell](https://github.com/lonnieezell))
- Fix issue \#2391 CodeIgniter::display404errors\(\) [\#2406](https://github.com/codeigniter4/CodeIgniter4/pull/2406) ([dafriend](https://github.com/dafriend))
- Removed pointless isset\(\) check [\#2402](https://github.com/codeigniter4/CodeIgniter4/pull/2402) ([dafriend](https://github.com/dafriend))
- Remove pointless check from conditional [\#2401](https://github.com/codeigniter4/CodeIgniter4/pull/2401) ([dafriend](https://github.com/dafriend))
- Remove redundant check in conditionals [\#2400](https://github.com/codeigniter4/CodeIgniter4/pull/2400) ([dafriend](https://github.com/dafriend))
- Revise Controllers Documentation  \[ci skip\] [\#2399](https://github.com/codeigniter4/CodeIgniter4/pull/2399) ([dafriend](https://github.com/dafriend))
- Edit .htaccess [\#2398](https://github.com/codeigniter4/CodeIgniter4/pull/2398) ([MashinaMashina](https://github.com/MashinaMashina))
- Add validation function `is\_not\_unique` [\#2392](https://github.com/codeigniter4/CodeIgniter4/pull/2392) ([kennylajara](https://github.com/kennylajara))
- Confer silent status to nested seeders [\#2389](https://github.com/codeigniter4/CodeIgniter4/pull/2389) ([MGatner](https://github.com/MGatner))
- Fix copypaste command comment [\#2388](https://github.com/codeigniter4/CodeIgniter4/pull/2388) ([MGatner](https://github.com/MGatner))
- Use only digits for migrations order [\#2387](https://github.com/codeigniter4/CodeIgniter4/pull/2387) ([MGatner](https://github.com/MGatner))
- quick fix postgresql insert id [\#2382](https://github.com/codeigniter4/CodeIgniter4/pull/2382) ([iam-adty](https://github.com/iam-adty))
- Fix: Use of CodeIgniter\Config\Services prevents Service overriding [\#2381](https://github.com/codeigniter4/CodeIgniter4/pull/2381) ([dafriend](https://github.com/dafriend))
- Replace null log file extension check [\#2379](https://github.com/codeigniter4/CodeIgniter4/pull/2379) ([MGatner](https://github.com/MGatner))
- Docs Rev: Replacing Core Classes \[ci skip\] [\#2377](https://github.com/codeigniter4/CodeIgniter4/pull/2377) ([dafriend](https://github.com/dafriend))
- Remove LoggerAwareTrait from Email class [\#2369](https://github.com/codeigniter4/CodeIgniter4/pull/2369) ([dafriend](https://github.com/dafriend))
- Remove log\_message from Email::\_\_construct [\#2368](https://github.com/codeigniter4/CodeIgniter4/pull/2368) ([dafriend](https://github.com/dafriend))
- Email config doesn't incorporate .env items [\#2364](https://github.com/codeigniter4/CodeIgniter4/pull/2364) ([dafriend](https://github.com/dafriend))
- Fix SMTP protocol problem [\#2362](https://github.com/codeigniter4/CodeIgniter4/pull/2362) ([jim-parry](https://github.com/jim-parry))
- Bugfix Model after event data [\#2359](https://github.com/codeigniter4/CodeIgniter4/pull/2359) ([MGatner](https://github.com/MGatner))
- Fix Logger config [\#2358](https://github.com/codeigniter4/CodeIgniter4/pull/2358) ([jim-parry](https://github.com/jim-parry))
- Fix typo in comments of Services.php [\#2356](https://github.com/codeigniter4/CodeIgniter4/pull/2356) ([mladoux](https://github.com/mladoux))
- Fix method name to 'toDateString\(\)' in Date and Times user guide [\#2352](https://github.com/codeigniter4/CodeIgniter4/pull/2352) ([musmanikram](https://github.com/musmanikram))
- Inccorectly formated JSON response , if body is string [\#2276](https://github.com/codeigniter4/CodeIgniter4/pull/2276) ([nowackipawel](https://github.com/nowackipawel))

## [v4.0.0-rc.3](https://github.com/codeigniter4/CodeIgniter4/tree/v4.0.0-rc.3) (2019-10-19)

[Full Changelog](https://github.com/codeigniter4/CodeIgniter4/compare/v4.0.0-rc.2b...v4.0.0-rc.3)

**Fixed bugs:**

- Route can not work: Controller or its method is not found [\#2299](https://github.com/codeigniter4/CodeIgniter4/issues/2299)
- CURLRequest - supplied argument is not a valid File-Handle resource \#Windows x64 [\#2202](https://github.com/codeigniter4/CodeIgniter4/issues/2202)
- FilterExceptions result in blank page [\#2077](https://github.com/codeigniter4/CodeIgniter4/issues/2077)

**Closed issues:**

- Codeigniter 4 not redirecting well. is not including the baseUrl well on redirect [\#2342](https://github.com/codeigniter4/CodeIgniter4/issues/2342)
- Session variable value set to 0 [\#2334](https://github.com/codeigniter4/CodeIgniter4/issues/2334)
- Undefined variable: errors SYSTEMPATH/Validation/Validation.php at line 651 [\#2331](https://github.com/codeigniter4/CodeIgniter4/issues/2331)
- Router Regex not working with controllers [\#2330](https://github.com/codeigniter4/CodeIgniter4/issues/2330)
- --host,--port and others are not working [\#2329](https://github.com/codeigniter4/CodeIgniter4/issues/2329)
- Type juggling can be eliminated if these three small changes are made [\#2326](https://github.com/codeigniter4/CodeIgniter4/issues/2326)
- url\_title doesn't handle diacritics [\#2323](https://github.com/codeigniter4/CodeIgniter4/issues/2323)
- View Cell Feature ? [\#2322](https://github.com/codeigniter4/CodeIgniter4/issues/2322)
- autoRoute function issue in case of sub-directory [\#2319](https://github.com/codeigniter4/CodeIgniter4/issues/2319)
- Can't store multidimensional data with Session Library [\#2309](https://github.com/codeigniter4/CodeIgniter4/issues/2309)
- Model\(\)-\>find\(\) return NULL for existing row [\#2306](https://github.com/codeigniter4/CodeIgniter4/issues/2306)
- Requesting Model::getValidationRules\(\) documentation [\#2304](https://github.com/codeigniter4/CodeIgniter4/issues/2304)
- Routes Not working [\#2301](https://github.com/codeigniter4/CodeIgniter4/issues/2301)
- ViewPath cannot be moved only another path added [\#2291](https://github.com/codeigniter4/CodeIgniter4/issues/2291)
- Version not updated? [\#2287](https://github.com/codeigniter4/CodeIgniter4/issues/2287)
- \_remap is not working [\#2277](https://github.com/codeigniter4/CodeIgniter4/issues/2277)
- Debug Toolbar error not found tpl error and fix [\#2275](https://github.com/codeigniter4/CodeIgniter4/issues/2275)
- cURL request returns 404 [\#2250](https://github.com/codeigniter4/CodeIgniter4/issues/2250)
- Problem with renaming deleted\_at column inside model [\#2248](https://github.com/codeigniter4/CodeIgniter4/issues/2248)
- App\Config\Routes loaded twice [\#2203](https://github.com/codeigniter4/CodeIgniter4/issues/2203)
- Feature idea: Model results by key [\#2167](https://github.com/codeigniter4/CodeIgniter4/issues/2167)
- Remove "separator" comment between function declarations? [\#2146](https://github.com/codeigniter4/CodeIgniter4/issues/2146)
- find\(\) is returning one character string instead of boolean [\#2096](https://github.com/codeigniter4/CodeIgniter4/issues/2096)
- Database Groups in Migrations [\#2087](https://github.com/codeigniter4/CodeIgniter4/issues/2087)
- "Cannot call session save handler in a recursive manner" [\#2056](https://github.com/codeigniter4/CodeIgniter4/issues/2056)
- Model afterInsert return originals? [\#2045](https://github.com/codeigniter4/CodeIgniter4/issues/2045)
- debug toolbar renderTimeline couses a non well formed numeric value encountered error [\#2034](https://github.com/codeigniter4/CodeIgniter4/issues/2034)
- Mysql update , affectedRows return bug [\#2003](https://github.com/codeigniter4/CodeIgniter4/issues/2003)
- Add validation on exists database before created [\#1759](https://github.com/codeigniter4/CodeIgniter4/issues/1759)

**Merged pull requests:**

- Prep changelog for RC.3 \[ci skip\] [\#2349](https://github.com/codeigniter4/CodeIgniter4/pull/2349) ([jim-parry](https://github.com/jim-parry))
- CodeIgniter Foundation gets copyright \[ci skip\] [\#2348](https://github.com/codeigniter4/CodeIgniter4/pull/2348) ([jim-parry](https://github.com/jim-parry))
- Fix FilerHandlerTest.php wierdness [\#2346](https://github.com/codeigniter4/CodeIgniter4/pull/2346) ([dafriend](https://github.com/dafriend))
- Tests readme polish [\#2345](https://github.com/codeigniter4/CodeIgniter4/pull/2345) ([dafriend](https://github.com/dafriend))
- Setup vs Set Up [\#2344](https://github.com/codeigniter4/CodeIgniter4/pull/2344) ([dafriend](https://github.com/dafriend))
- User guide minor fixes. Fix class names and code area. [\#2343](https://github.com/codeigniter4/CodeIgniter4/pull/2343) ([natanfelles](https://github.com/natanfelles))
- Simplify Validation::getErrors\(\) [\#2341](https://github.com/codeigniter4/CodeIgniter4/pull/2341) ([dafriend](https://github.com/dafriend))
- Fix Session::get\('key'\) returns null when value is \(int\) 0 [\#2339](https://github.com/codeigniter4/CodeIgniter4/pull/2339) ([dafriend](https://github.com/dafriend))
- Revert RedirectException change [\#2338](https://github.com/codeigniter4/CodeIgniter4/pull/2338) ([MGatner](https://github.com/MGatner))
- \[ci skip\] Guide: Minor grammar corrections [\#2337](https://github.com/codeigniter4/CodeIgniter4/pull/2337) ([dafriend](https://github.com/dafriend))
- Correct cleaning of namespaces in FileLocater for better Windows compatibility. See \#2203 [\#2336](https://github.com/codeigniter4/CodeIgniter4/pull/2336) ([lonnieezell](https://github.com/lonnieezell))
- \[ci skip\] Guide: RESTful table formatting [\#2333](https://github.com/codeigniter4/CodeIgniter4/pull/2333) ([MGatner](https://github.com/MGatner))
- Change after methods to use actual data [\#2332](https://github.com/codeigniter4/CodeIgniter4/pull/2332) ([MGatner](https://github.com/MGatner))
- Update Application Structure [\#2328](https://github.com/codeigniter4/CodeIgniter4/pull/2328) ([kenjis](https://github.com/kenjis))
- Correct the routing UG page [\#2327](https://github.com/codeigniter4/CodeIgniter4/pull/2327) ([jim-parry](https://github.com/jim-parry))
- Fix bug in url\_title\(\) function with diacritics [\#2325](https://github.com/codeigniter4/CodeIgniter4/pull/2325) ([michalsn](https://github.com/michalsn))
- Renderer Toolbar Debug Toggle [\#2324](https://github.com/codeigniter4/CodeIgniter4/pull/2324) ([MGatner](https://github.com/MGatner))
- \[ci skip\] Update RESTful User Guide [\#2321](https://github.com/codeigniter4/CodeIgniter4/pull/2321) ([MGatner](https://github.com/MGatner))
- Add getValidationRules\(\) to model UG page [\#2316](https://github.com/codeigniter4/CodeIgniter4/pull/2316) ([jim-parry](https://github.com/jim-parry))
- Enhance Toolbar::renderTimeline [\#2315](https://github.com/codeigniter4/CodeIgniter4/pull/2315) ([jim-parry](https://github.com/jim-parry))
- RESTful User Guide cleanup [\#2313](https://github.com/codeigniter4/CodeIgniter4/pull/2313) ([MGatner](https://github.com/MGatner))
- BaseBuilder variable type fix [\#2312](https://github.com/codeigniter4/CodeIgniter4/pull/2312) ([TysiacSzescset](https://github.com/TysiacSzescset))
- Convert all language returns to single quote [\#2311](https://github.com/codeigniter4/CodeIgniter4/pull/2311) ([MGatner](https://github.com/MGatner))
- Bugfix extra autoroute slashes [\#2308](https://github.com/codeigniter4/CodeIgniter4/pull/2308) ([MGatner](https://github.com/MGatner))
- Resolve session save handler issue [\#2307](https://github.com/codeigniter4/CodeIgniter4/pull/2307) ([jim-parry](https://github.com/jim-parry))
- Fix curl debug bug [\#2305](https://github.com/codeigniter4/CodeIgniter4/pull/2305) ([michalsn](https://github.com/michalsn))
- Use DBGroup variable from migration class if defined [\#2303](https://github.com/codeigniter4/CodeIgniter4/pull/2303) ([michalsn](https://github.com/michalsn))
- Fix MySql \_fromTables\(\) [\#2302](https://github.com/codeigniter4/CodeIgniter4/pull/2302) ([pjsde](https://github.com/pjsde))
- \[ci skip\] Routes collector for toolbar should not die when a method name is calculated through \_remap [\#2300](https://github.com/codeigniter4/CodeIgniter4/pull/2300) ([lonnieezell](https://github.com/lonnieezell))
- fix issue on session\_regenerate. [\#2298](https://github.com/codeigniter4/CodeIgniter4/pull/2298) ([pjsde](https://github.com/pjsde))
- Add counted\(\) to Inflector Helper [\#2296](https://github.com/codeigniter4/CodeIgniter4/pull/2296) ([MGatner](https://github.com/MGatner))
- Test set\(\) method in Builder class more [\#2295](https://github.com/codeigniter4/CodeIgniter4/pull/2295) ([michalsn](https://github.com/michalsn))
- Fix Code Modules documentation for psr4 namespace configuration [\#2290](https://github.com/codeigniter4/CodeIgniter4/pull/2290) ([romaven](https://github.com/romaven))
- Don't restrict model's access to properties in a read-only manner [\#2289](https://github.com/codeigniter4/CodeIgniter4/pull/2289) ([lonnieezell](https://github.com/lonnieezell))
- Fix line numbering in Debug/Exceptions class [\#2288](https://github.com/codeigniter4/CodeIgniter4/pull/2288) ([michalsn](https://github.com/michalsn))
- Fix error with Host header for CURLRequest class [\#2285](https://github.com/codeigniter4/CodeIgniter4/pull/2285) ([michalsn](https://github.com/michalsn))
- Fix getErrors\(\) for validation with redirect [\#2284](https://github.com/codeigniter4/CodeIgniter4/pull/2284) ([michalsn](https://github.com/michalsn))
- Rename collectors \_\*.tpl.php to \_\*.tpl [\#2283](https://github.com/codeigniter4/CodeIgniter4/pull/2283) ([MGatner](https://github.com/MGatner))
- Bug in CSRF parameter cleanup [\#2279](https://github.com/codeigniter4/CodeIgniter4/pull/2279) ([michalsn](https://github.com/michalsn))
- WIP fix store\(\) default value bug [\#2123](https://github.com/codeigniter4/CodeIgniter4/pull/2123) ([s-proj](https://github.com/s-proj))
- WIP Added validation on exists database before created for MySQLi… [\#2100](https://github.com/codeigniter4/CodeIgniter4/pull/2100) ([oleg1540](https://github.com/oleg1540))

## [v4.0.0-rc.2b](https://github.com/codeigniter4/CodeIgniter4/tree/v4.0.0-rc.2b) (2019-09-28)

[Full Changelog](https://github.com/codeigniter4/CodeIgniter4/compare/v4.0.0-rc.2.1...v4.0.0-rc.2b)

**Merged pull requests:**

- Fix user guide for Message class [\#2282](https://github.com/codeigniter4/CodeIgniter4/pull/2282) ([michalsn](https://github.com/michalsn))
- Handle X-CSRF-TOKEN - CSRF [\#2272](https://github.com/codeigniter4/CodeIgniter4/pull/2272) ([nowackipawel](https://github.com/nowackipawel))
- QUICKFIX Batch Update Where Reset [\#2252](https://github.com/codeigniter4/CodeIgniter4/pull/2252) ([searchy2](https://github.com/searchy2))

## [v4.0.0-rc.2.1](https://github.com/codeigniter4/CodeIgniter4/tree/v4.0.0-rc.2.1) (2019-09-28)

[Full Changelog](https://github.com/codeigniter4/CodeIgniter4/compare/v4.0.0-rc.2...v4.0.0-rc.2.1)

**Closed issues:**

- listTables\(\) failing to use correct prefix [\#2210](https://github.com/codeigniter4/CodeIgniter4/issues/2210)
- Query Builder Class documentation [\#2140](https://github.com/codeigniter4/CodeIgniter4/issues/2140)

## [v4.0.0-rc.2](https://github.com/codeigniter4/CodeIgniter4/tree/v4.0.0-rc.2) (2019-09-27)

[Full Changelog](https://github.com/codeigniter4/CodeIgniter4/compare/v4.0.0-rc.1...v4.0.0-rc.2)

**Fixed bugs:**

- Add magic \_\_isset to classes with \_\_get [\#2219](https://github.com/codeigniter4/CodeIgniter4/issues/2219)
- CIDatabaseTestCase double-deletes tables [\#2206](https://github.com/codeigniter4/CodeIgniter4/issues/2206)
- Locals problems [\#2195](https://github.com/codeigniter4/CodeIgniter4/issues/2195)
- Translations are not loaded from composer repository [\#2120](https://github.com/codeigniter4/CodeIgniter4/issues/2120)

**Closed issues:**

- Query grouping not working for HAVING clause [\#2247](https://github.com/codeigniter4/CodeIgniter4/issues/2247)
- $builder-\>like\(\) not support for HAVING clause [\#2242](https://github.com/codeigniter4/CodeIgniter4/issues/2242)
- invalid switch parameter  [\#2239](https://github.com/codeigniter4/CodeIgniter4/issues/2239)
-  DateTime::createFromFormat\(\)  parameter incorrect [\#2238](https://github.com/codeigniter4/CodeIgniter4/issues/2238)
-  strlen\(\) expects parameter string, integer given [\#2237](https://github.com/codeigniter4/CodeIgniter4/issues/2237)
- ini\_set\(\)  expects parameter 2 to be string, integer given [\#2236](https://github.com/codeigniter4/CodeIgniter4/issues/2236)
- ini\_set\(\) expects parameter 2 to be string, integer given [\#2235](https://github.com/codeigniter4/CodeIgniter4/issues/2235)
- \# ini\_set\(\) expects parameter 2 to be string, integer given [\#2234](https://github.com/codeigniter4/CodeIgniter4/issues/2234)
- Extending The Model [\#2223](https://github.com/codeigniter4/CodeIgniter4/issues/2223)
- BUG curl\_setopt\_array\(\): supplied argument is not a valid file-handle resource [\#2222](https://github.com/codeigniter4/CodeIgniter4/issues/2222)
- How do I dynamically modify the configuration? [\#2214](https://github.com/codeigniter4/CodeIgniter4/issues/2214)
- Document the "whoops" error page [\#2198](https://github.com/codeigniter4/CodeIgniter4/issues/2198)
- Fail to open system/bootstrap.php [\#2193](https://github.com/codeigniter4/CodeIgniter4/issues/2193)
- Function lang\(\) / Type of return value [\#2192](https://github.com/codeigniter4/CodeIgniter4/issues/2192)
- Can we use codeigniter 4 on live server?? [\#2188](https://github.com/codeigniter4/CodeIgniter4/issues/2188)
- Custom query in model CI4 [\#2187](https://github.com/codeigniter4/CodeIgniter4/issues/2187)
- conflict between php zlib.output\_compression and output buffering [\#2182](https://github.com/codeigniter4/CodeIgniter4/issues/2182)
- API Trait documentation fix - failValidationError [\#2176](https://github.com/codeigniter4/CodeIgniter4/issues/2176)
- Validation issue on multiple file upload [\#2175](https://github.com/codeigniter4/CodeIgniter4/issues/2175)
- exif\_read\_data [\#2161](https://github.com/codeigniter4/CodeIgniter4/issues/2161)
- Database count methods [\#2159](https://github.com/codeigniter4/CodeIgniter4/issues/2159)
- Devstarter $salt [\#2156](https://github.com/codeigniter4/CodeIgniter4/issues/2156)
- Migration migrate, rollback and create problems [\#2147](https://github.com/codeigniter4/CodeIgniter4/issues/2147)
- Query Builder getWhere Crash [\#2143](https://github.com/codeigniter4/CodeIgniter4/issues/2143)
- View: $parser-\>render\(\); [\#2086](https://github.com/codeigniter4/CodeIgniter4/issues/2086)
- Return value of lang\(\) must be of the type string, array returned [\#2075](https://github.com/codeigniter4/CodeIgniter4/issues/2075)
- Wrong links for pager [\#2016](https://github.com/codeigniter4/CodeIgniter4/issues/2016)
- base\_url\(\) value dropped between namespaces [\#1942](https://github.com/codeigniter4/CodeIgniter4/issues/1942)
- Unable to use \_remap without default method in controller [\#1928](https://github.com/codeigniter4/CodeIgniter4/issues/1928)
- RESTful resources [\#1765](https://github.com/codeigniter4/CodeIgniter4/issues/1765)

**Merged pull requests:**

- Fix changelog \[ci skip\] [\#2273](https://github.com/codeigniter4/CodeIgniter4/pull/2273) ([jim-parry](https://github.com/jim-parry))
- fix ResourcePresenter::setModel\(\) [\#2271](https://github.com/codeigniter4/CodeIgniter4/pull/2271) ([pjsde](https://github.com/pjsde))
- groupStart\(\) refactorization [\#2270](https://github.com/codeigniter4/CodeIgniter4/pull/2270) ([michalsn](https://github.com/michalsn))
- testMode\(\) method for BaseBuilder [\#2269](https://github.com/codeigniter4/CodeIgniter4/pull/2269) ([michalsn](https://github.com/michalsn))
- Validation session use only if exists [\#2268](https://github.com/codeigniter4/CodeIgniter4/pull/2268) ([jim-parry](https://github.com/jim-parry))
- Tests setUp and tearDown: void [\#2267](https://github.com/codeigniter4/CodeIgniter4/pull/2267) ([MGatner](https://github.com/MGatner))
- RC.2 release prep [\#2266](https://github.com/codeigniter4/CodeIgniter4/pull/2266) ([jim-parry](https://github.com/jim-parry))
- Fix a validation issue on multiple file upload [\#2265](https://github.com/codeigniter4/CodeIgniter4/pull/2265) ([pjsde](https://github.com/pjsde))
- fix. Parser allow other extension [\#2264](https://github.com/codeigniter4/CodeIgniter4/pull/2264) ([Instrye](https://github.com/Instrye))
- Fix parameter type in Debug/Exceptions [\#2262](https://github.com/codeigniter4/CodeIgniter4/pull/2262) ([jim-parry](https://github.com/jim-parry))
- Fix lang\(\) signature [\#2261](https://github.com/codeigniter4/CodeIgniter4/pull/2261) ([jim-parry](https://github.com/jim-parry))
- Explain the whoops page [\#2260](https://github.com/codeigniter4/CodeIgniter4/pull/2260) ([jim-parry](https://github.com/jim-parry))
- Add URI & url\_helper tests [\#2259](https://github.com/codeigniter4/CodeIgniter4/pull/2259) ([jim-parry](https://github.com/jim-parry))
- Several updates to the HAVING clauses [\#2257](https://github.com/codeigniter4/CodeIgniter4/pull/2257) ([michalsn](https://github.com/michalsn))
- Fix invalid parameters [\#2253](https://github.com/codeigniter4/CodeIgniter4/pull/2253) ([pjsde](https://github.com/pjsde))
- EXIF not supported for GIF [\#2246](https://github.com/codeigniter4/CodeIgniter4/pull/2246) ([jim-parry](https://github.com/jim-parry))
- Fix class ref parameter types [\#2245](https://github.com/codeigniter4/CodeIgniter4/pull/2245) ([jim-parry](https://github.com/jim-parry))
- Fix ini\_set parameter type [\#2241](https://github.com/codeigniter4/CodeIgniter4/pull/2241) ([jim-parry](https://github.com/jim-parry))
- Handle JSON POSTs in CSRF [\#2240](https://github.com/codeigniter4/CodeIgniter4/pull/2240) ([nowackipawel](https://github.com/nowackipawel))
- Fixes BaseBuilder getWhere\(\) bug [\#2232](https://github.com/codeigniter4/CodeIgniter4/pull/2232) ([michalsn](https://github.com/michalsn))
- Add magic \_\_isset to classes with \_\_get [\#2231](https://github.com/codeigniter4/CodeIgniter4/pull/2231) ([MGatner](https://github.com/MGatner))
- Add escape to SQLite \_listTables\(\) [\#2230](https://github.com/codeigniter4/CodeIgniter4/pull/2230) ([MGatner](https://github.com/MGatner))
- MySQLi escapeLikeStringDirect\(\) [\#2229](https://github.com/codeigniter4/CodeIgniter4/pull/2229) ([MGatner](https://github.com/MGatner))
- Exclude `sqlite\_%` from listTables\(\) [\#2228](https://github.com/codeigniter4/CodeIgniter4/pull/2228) ([MGatner](https://github.com/MGatner))
- fix. CONTRIBUTING.md link [\#2226](https://github.com/codeigniter4/CodeIgniter4/pull/2226) ([Instrye](https://github.com/Instrye))
- \[ci skip\] Fix malformed table in view\_parser.rst [\#2225](https://github.com/codeigniter4/CodeIgniter4/pull/2225) ([jim-parry](https://github.com/jim-parry))
- change new \Config\Database\(\) to config\('Database'\) [\#2224](https://github.com/codeigniter4/CodeIgniter4/pull/2224) ([techoner](https://github.com/techoner))
- Documentation fixes [\#2221](https://github.com/codeigniter4/CodeIgniter4/pull/2221) ([najdanovicivan](https://github.com/najdanovicivan))
- Typo corrected [\#2218](https://github.com/codeigniter4/CodeIgniter4/pull/2218) ([dangereyes88](https://github.com/dangereyes88))
- Update uri.rst [\#2216](https://github.com/codeigniter4/CodeIgniter4/pull/2216) ([dangereyes88](https://github.com/dangereyes88))
- Filter listTables cache response on constrainPrefix [\#2213](https://github.com/codeigniter4/CodeIgniter4/pull/2213) ([MGatner](https://github.com/MGatner))
- Add listTable\(\) tests [\#2211](https://github.com/codeigniter4/CodeIgniter4/pull/2211) ([MGatner](https://github.com/MGatner))
- Add trace\(\) [\#2209](https://github.com/codeigniter4/CodeIgniter4/pull/2209) ([MGatner](https://github.com/MGatner))
- Add $db-\>getPrefix\(\) [\#2208](https://github.com/codeigniter4/CodeIgniter4/pull/2208) ([MGatner](https://github.com/MGatner))
- Fix empty\(\) bug on DBPrefix [\#2205](https://github.com/codeigniter4/CodeIgniter4/pull/2205) ([MGatner](https://github.com/MGatner))
- Foreign key columns [\#2201](https://github.com/codeigniter4/CodeIgniter4/pull/2201) ([MGatner](https://github.com/MGatner))
- Notify Kint of dd alias [\#2200](https://github.com/codeigniter4/CodeIgniter4/pull/2200) ([MGatner](https://github.com/MGatner))
- Add getForeignKeyData to User Guide [\#2199](https://github.com/codeigniter4/CodeIgniter4/pull/2199) ([MGatner](https://github.com/MGatner))
- Update Session.php [\#2197](https://github.com/codeigniter4/CodeIgniter4/pull/2197) ([cstechsandesh](https://github.com/cstechsandesh))
- Migration rollback reverse [\#2191](https://github.com/codeigniter4/CodeIgniter4/pull/2191) ([MGatner](https://github.com/MGatner))
- \[ci skip\] Fix name of ForeignKeyChecks [\#2190](https://github.com/codeigniter4/CodeIgniter4/pull/2190) ([MGatner](https://github.com/MGatner))
- missing return [\#2189](https://github.com/codeigniter4/CodeIgniter4/pull/2189) ([titounnes](https://github.com/titounnes))
- Fix case on "Seeds/" directory [\#2184](https://github.com/codeigniter4/CodeIgniter4/pull/2184) ([MGatner](https://github.com/MGatner))
- Check `defined` for constants [\#2183](https://github.com/codeigniter4/CodeIgniter4/pull/2183) ([MGatner](https://github.com/MGatner))
- Remove copy-paste extraneous text [\#2181](https://github.com/codeigniter4/CodeIgniter4/pull/2181) ([MGatner](https://github.com/MGatner))
- Fix \_fromTables\(\) [\#2174](https://github.com/codeigniter4/CodeIgniter4/pull/2174) ([pjsde](https://github.com/pjsde))
- Fix for CURL for 'debug' option [\#2168](https://github.com/codeigniter4/CodeIgniter4/pull/2168) ([MGatner](https://github.com/MGatner))

## [v4.0.0-rc.1](https://github.com/codeigniter4/CodeIgniter4/tree/v4.0.0-rc.1) (2019-09-03)

[Full Changelog](https://github.com/codeigniter4/CodeIgniter4/compare/v4.0.0-beta.4...v4.0.0-rc.1)

**Implemented enhancements:**

- BaseConfig should support array values with dot syntax [\#454](https://github.com/codeigniter4/CodeIgniter4/issues/454)

**Closed issues:**

- \[internal function\]: CodeIgniter\Debug\Exceptions-\>shutdownHandler\(\) [\#2173](https://github.com/codeigniter4/CodeIgniter4/issues/2173)
- Message-\>setHeader allowing duplicates [\#2170](https://github.com/codeigniter4/CodeIgniter4/issues/2170)
- CLI: Exit status [\#2163](https://github.com/codeigniter4/CodeIgniter4/issues/2163)
- QB countAllResults shouldn't use LIMIT settings [\#2152](https://github.com/codeigniter4/CodeIgniter4/issues/2152)
- BaseBuilder::get\(\) resets query even if reset = false [\#2141](https://github.com/codeigniter4/CodeIgniter4/issues/2141)
- Some migrations not running [\#2139](https://github.com/codeigniter4/CodeIgniter4/issues/2139)
- Migrations Refactor Namespaces [\#2138](https://github.com/codeigniter4/CodeIgniter4/issues/2138)
- $primaryKey forcefully 'needs' to be auto\_increment [\#2133](https://github.com/codeigniter4/CodeIgniter4/issues/2133)
- response data not set [\#2124](https://github.com/codeigniter4/CodeIgniter4/issues/2124)
- RESTful behaviour [\#2122](https://github.com/codeigniter4/CodeIgniter4/issues/2122)
- Redis [\#2121](https://github.com/codeigniter4/CodeIgniter4/issues/2121)
- Toolbar download bug [\#2117](https://github.com/codeigniter4/CodeIgniter4/issues/2117)
- Packagist not updated with latest release? [\#2115](https://github.com/codeigniter4/CodeIgniter4/issues/2115)
- Fatal error Installing using composer [\#2114](https://github.com/codeigniter4/CodeIgniter4/issues/2114)
- Allow loading Common.php function overrides [\#2101](https://github.com/codeigniter4/CodeIgniter4/issues/2101)
- Result from database was auto encoded when using Entity [\#2088](https://github.com/codeigniter4/CodeIgniter4/issues/2088)
- Honeypot does not close the form [\#2084](https://github.com/codeigniter4/CodeIgniter4/issues/2084)
- Imagick Image library handler return array instead of boolean [\#2029](https://github.com/codeigniter4/CodeIgniter4/issues/2029)
- Migrations command should use the UTC datetime when creating new migrations [\#2018](https://github.com/codeigniter4/CodeIgniter4/issues/2018)
- FileLocator-\>getNamespaces with parameter [\#1866](https://github.com/codeigniter4/CodeIgniter4/issues/1866)

**Merged pull requests:**

- Fix query builder user guide page [\#2180](https://github.com/codeigniter4/CodeIgniter4/pull/2180) ([jim-parry](https://github.com/jim-parry))
- RC.1 prep [\#2179](https://github.com/codeigniter4/CodeIgniter4/pull/2179) ([jim-parry](https://github.com/jim-parry))
- Add fallback for missing finfo\_open [\#2178](https://github.com/codeigniter4/CodeIgniter4/pull/2178) ([MGatner](https://github.com/MGatner))
- Fix missing form close tag [\#2177](https://github.com/codeigniter4/CodeIgniter4/pull/2177) ([jim-parry](https://github.com/jim-parry))
- Base FeatureTestCase on CIUnitTestCase [\#2172](https://github.com/codeigniter4/CodeIgniter4/pull/2172) ([jim-parry](https://github.com/jim-parry))
- Setheader dupes [\#2171](https://github.com/codeigniter4/CodeIgniter4/pull/2171) ([MGatner](https://github.com/MGatner))
- Add $quality usage for Image Library [\#2169](https://github.com/codeigniter4/CodeIgniter4/pull/2169) ([MGatner](https://github.com/MGatner))
- Cookie error [\#2166](https://github.com/codeigniter4/CodeIgniter4/pull/2166) ([pjsde](https://github.com/pjsde))
- RESTful help [\#2165](https://github.com/codeigniter4/CodeIgniter4/pull/2165) ([jim-parry](https://github.com/jim-parry))
- Exit error code on CLI Command failure [\#2164](https://github.com/codeigniter4/CodeIgniter4/pull/2164) ([MGatner](https://github.com/MGatner))
- User Guide updates for Common.php [\#2162](https://github.com/codeigniter4/CodeIgniter4/pull/2162) ([MGatner](https://github.com/MGatner))
- Add BaseBuilder SelectCount [\#2160](https://github.com/codeigniter4/CodeIgniter4/pull/2160) ([MGatner](https://github.com/MGatner))
- Update migrations config [\#2157](https://github.com/codeigniter4/CodeIgniter4/pull/2157) ([jim-parry](https://github.com/jim-parry))
- Include .gitignore in starters [\#2155](https://github.com/codeigniter4/CodeIgniter4/pull/2155) ([MGatner](https://github.com/MGatner))
- Fix email & migrations docs; update changelog [\#2154](https://github.com/codeigniter4/CodeIgniter4/pull/2154) ([jim-parry](https://github.com/jim-parry))
- Bug fix countAllResults with LIMIT [\#2153](https://github.com/codeigniter4/CodeIgniter4/pull/2153) ([tangix](https://github.com/tangix))
- ImageMagick-\>save\(\) return value [\#2151](https://github.com/codeigniter4/CodeIgniter4/pull/2151) ([MGatner](https://github.com/MGatner))
- New logic for Image-\>fit\(\) [\#2150](https://github.com/codeigniter4/CodeIgniter4/pull/2150) ([MGatner](https://github.com/MGatner))
- listNamespaceFiles: Ensure trailing slash  [\#2149](https://github.com/codeigniter4/CodeIgniter4/pull/2149) ([MGatner](https://github.com/MGatner))
- Remove UserModel reference from Home controller [\#2145](https://github.com/codeigniter4/CodeIgniter4/pull/2145) ([andreportaro](https://github.com/andreportaro))
- Update Redis legacy function [\#2144](https://github.com/codeigniter4/CodeIgniter4/pull/2144) ([MGatner](https://github.com/MGatner))
- Fixing BuilderBase resetting when getting the SQL [\#2142](https://github.com/codeigniter4/CodeIgniter4/pull/2142) ([tangix](https://github.com/tangix))
- New Migration Logic [\#2137](https://github.com/codeigniter4/CodeIgniter4/pull/2137) ([MGatner](https://github.com/MGatner))
- Migrations user guide fixes [\#2136](https://github.com/codeigniter4/CodeIgniter4/pull/2136) ([MGatner](https://github.com/MGatner))
- Encryption [\#2135](https://github.com/codeigniter4/CodeIgniter4/pull/2135) ([jim-parry](https://github.com/jim-parry))
- Fix localization writeup [\#2134](https://github.com/codeigniter4/CodeIgniter4/pull/2134) ([jim-parry](https://github.com/jim-parry))
- Update migration User Guide [\#2132](https://github.com/codeigniter4/CodeIgniter4/pull/2132) ([MGatner](https://github.com/MGatner))
- Added No Content response to API\ResponseTrait [\#2131](https://github.com/codeigniter4/CodeIgniter4/pull/2131) ([tangix](https://github.com/tangix))
- Add setFileName\(\) to DownloadResponse [\#2129](https://github.com/codeigniter4/CodeIgniter4/pull/2129) ([MGatner](https://github.com/MGatner))
- guessExtension fallback to clientExtension [\#2128](https://github.com/codeigniter4/CodeIgniter4/pull/2128) ([MGatner](https://github.com/MGatner))
- Update limit function since $offset is nullable [\#2127](https://github.com/codeigniter4/CodeIgniter4/pull/2127) ([vibbow](https://github.com/vibbow))
- Limit storePreviousURL to certain requests [\#2126](https://github.com/codeigniter4/CodeIgniter4/pull/2126) ([MGatner](https://github.com/MGatner))
- Updated redis session handler to support redis 5.0.x [\#2125](https://github.com/codeigniter4/CodeIgniter4/pull/2125) ([tangix](https://github.com/tangix))
- Disabled Toolbar on downloads [\#2118](https://github.com/codeigniter4/CodeIgniter4/pull/2118) ([MGatner](https://github.com/MGatner))
- Add Image-\>convert\(\) [\#2113](https://github.com/codeigniter4/CodeIgniter4/pull/2113) ([MGatner](https://github.com/MGatner))
- Update `Entity.php` `\_\_isset` method [\#2112](https://github.com/codeigniter4/CodeIgniter4/pull/2112) ([vibbow](https://github.com/vibbow))
- Added app/Common.php [\#2110](https://github.com/codeigniter4/CodeIgniter4/pull/2110) ([jason-napolitano](https://github.com/jason-napolitano))
- Fix typo in checking if exists db\_connect\(\) [\#2109](https://github.com/codeigniter4/CodeIgniter4/pull/2109) ([xbotkaj](https://github.com/xbotkaj))
- Original email port [\#2092](https://github.com/codeigniter4/CodeIgniter4/pull/2092) ([jim-parry](https://github.com/jim-parry))
- Fix prevent soft delete all without conditions set [\#2090](https://github.com/codeigniter4/CodeIgniter4/pull/2090) ([rino7](https://github.com/rino7))
- Update BaseConfig.php [\#2082](https://github.com/codeigniter4/CodeIgniter4/pull/2082) ([zl59503020](https://github.com/zl59503020))
- WIP: Migration updates for more wholistic functionality [\#2065](https://github.com/codeigniter4/CodeIgniter4/pull/2065) ([lonnieezell](https://github.com/lonnieezell))
- clean base controller code  [\#2046](https://github.com/codeigniter4/CodeIgniter4/pull/2046) ([behnampro](https://github.com/behnampro))
- Fix CSRF hash regeneration [\#2027](https://github.com/codeigniter4/CodeIgniter4/pull/2027) ([Workoverflow](https://github.com/Workoverflow))
- WIP Verbiage revisions [\#2010](https://github.com/codeigniter4/CodeIgniter4/pull/2010) ([kydojo](https://github.com/kydojo))
- Subqueries in BaseBuilder [\#2001](https://github.com/codeigniter4/CodeIgniter4/pull/2001) ([iRedds](https://github.com/iRedds))

## [v4.0.0-beta.4](https://github.com/codeigniter4/CodeIgniter4/tree/v4.0.0-beta.4) (2019-07-25)

[Full Changelog](https://github.com/codeigniter4/CodeIgniter4/compare/v4.0.0-beta.3...v4.0.0-beta.4)

**Fixed bugs:**

- Controller filters match too loosely. [\#2038](https://github.com/codeigniter4/CodeIgniter4/issues/2038)

**Closed issues:**

- File-\>getDestination fails without extension [\#2103](https://github.com/codeigniter4/CodeIgniter4/issues/2103)
- User Guide: UploadedFile Class [\#2102](https://github.com/codeigniter4/CodeIgniter4/issues/2102)
- Worries about postgresql errors [\#2097](https://github.com/codeigniter4/CodeIgniter4/issues/2097)
- README.md - Link to Announcement on Forums is a 404? [\#2094](https://github.com/codeigniter4/CodeIgniter4/issues/2094)
- Entity castAsJson returns an empty array [\#2080](https://github.com/codeigniter4/CodeIgniter4/issues/2080)
- Migrations Sequential field information is required [\#2076](https://github.com/codeigniter4/CodeIgniter4/issues/2076)
- function gussExtension return wrong result, return csv instead of right answer "txt" or "text" [\#2066](https://github.com/codeigniter4/CodeIgniter4/issues/2066)
- Unexpected empty "query" property when returning CodeIgniter\HTTP\URI [\#2062](https://github.com/codeigniter4/CodeIgniter4/issues/2062)
- Multiple rules for file upload always return false [\#2061](https://github.com/codeigniter4/CodeIgniter4/issues/2061)
- The assets of the public\_folder are not loaded [\#2047](https://github.com/codeigniter4/CodeIgniter4/issues/2047)
- Modify Model's deleted field to be a date [\#2041](https://github.com/codeigniter4/CodeIgniter4/issues/2041)
- Filter Config not quite working with Routes? [\#2037](https://github.com/codeigniter4/CodeIgniter4/issues/2037)
- force\_https\(\) doesn't redirect [\#2033](https://github.com/codeigniter4/CodeIgniter4/issues/2033)
- URI segments passed in as method parameters skips segments with value as 0 \(zero\) [\#2032](https://github.com/codeigniter4/CodeIgniter4/issues/2032)
- /System/Debug/Toolbar/Collectors/Routes.php on line 83	 [\#2028](https://github.com/codeigniter4/CodeIgniter4/issues/2028)
- php spark not working [\#2025](https://github.com/codeigniter4/CodeIgniter4/issues/2025)
- PR\#2012 caused 404 exception in spark  [\#2021](https://github.com/codeigniter4/CodeIgniter4/issues/2021)
- Cache config [\#2017](https://github.com/codeigniter4/CodeIgniter4/issues/2017)
- CodeIgniter\Entity Setter doesn't work  [\#2013](https://github.com/codeigniter4/CodeIgniter4/issues/2013)
- validation match\[x\] don't work anymore... if custom setter is used. [\#2006](https://github.com/codeigniter4/CodeIgniter4/issues/2006)
- Paths issue when moving Views outside of app folder [\#1998](https://github.com/codeigniter4/CodeIgniter4/issues/1998)
- View Parser Register Plugins as closures not works! [\#1997](https://github.com/codeigniter4/CodeIgniter4/issues/1997)
- View Parser site\_url not works? [\#1995](https://github.com/codeigniter4/CodeIgniter4/issues/1995)
- CURLRequest not respecting debug flag [\#1994](https://github.com/codeigniter4/CodeIgniter4/issues/1994)
- Entity null values cause database error [\#1992](https://github.com/codeigniter4/CodeIgniter4/issues/1992)
- SQLite driver throws exception when using dropForeignKey [\#1982](https://github.com/codeigniter4/CodeIgniter4/issues/1982)
- Security: DotEnv loads DB password plaintext in $\_SERVER [\#1969](https://github.com/codeigniter4/CodeIgniter4/issues/1969)
- Feature: FK Constraint Enable/Disable [\#1964](https://github.com/codeigniter4/CodeIgniter4/issues/1964)
- redirect\($namedRoute\) missing helpful exception [\#1953](https://github.com/codeigniter4/CodeIgniter4/issues/1953)

**Merged pull requests:**

- Update the starters [\#2108](https://github.com/codeigniter4/CodeIgniter4/pull/2108) ([jim-parry](https://github.com/jim-parry))
- Prep for beta.4 [\#2107](https://github.com/codeigniter4/CodeIgniter4/pull/2107) ([jim-parry](https://github.com/jim-parry))
- File & UploadFile Fixes [\#2104](https://github.com/codeigniter4/CodeIgniter4/pull/2104) ([MGatner](https://github.com/MGatner))
- Timezone select [\#2091](https://github.com/codeigniter4/CodeIgniter4/pull/2091) ([MGatner](https://github.com/MGatner))
- JSON format checking improved [\#2081](https://github.com/codeigniter4/CodeIgniter4/pull/2081) ([nowackipawel](https://github.com/nowackipawel))
- Update config\(\) to check all namespaces [\#2079](https://github.com/codeigniter4/CodeIgniter4/pull/2079) ([MGatner](https://github.com/MGatner))
- Throttler can access bucket for bucket life time [\#2074](https://github.com/codeigniter4/CodeIgniter4/pull/2074) ([MohKari](https://github.com/MohKari))
- Fix autoloader.rst formatting [\#2071](https://github.com/codeigniter4/CodeIgniter4/pull/2071) ([jim-parry](https://github.com/jim-parry))
- validation rule: then -\> than \(spelling\) [\#2069](https://github.com/codeigniter4/CodeIgniter4/pull/2069) ([nowackipawel](https://github.com/nowackipawel))
- Bugfix file locator slash error [\#2064](https://github.com/codeigniter4/CodeIgniter4/pull/2064) ([MGatner](https://github.com/MGatner))
- Ensure query vars are part of request-\>uri. Fixes \#2062 [\#2063](https://github.com/codeigniter4/CodeIgniter4/pull/2063) ([lonnieezell](https://github.com/lonnieezell))
- Cache Drive Backups  [\#2060](https://github.com/codeigniter4/CodeIgniter4/pull/2060) ([MohKari](https://github.com/MohKari))
- Add multi-path support to `locateFile\(\)` [\#2059](https://github.com/codeigniter4/CodeIgniter4/pull/2059) ([MGatner](https://github.com/MGatner))
- Add model exceptions for missing/invalid dateFormat [\#2054](https://github.com/codeigniter4/CodeIgniter4/pull/2054) ([MGatner](https://github.com/MGatner))
- Change Model's deleted flag to a deleted\_at datetime/timestamp. Fixes \#2041 [\#2053](https://github.com/codeigniter4/CodeIgniter4/pull/2053) ([lonnieezell](https://github.com/lonnieezell))
- Add various tests for \(not\) null [\#2052](https://github.com/codeigniter4/CodeIgniter4/pull/2052) ([MGatner](https://github.com/MGatner))
- Soft deletes use deleted\_at [\#2051](https://github.com/codeigniter4/CodeIgniter4/pull/2051) ([MGatner](https://github.com/MGatner))
- Stash insert ID before event trigger [\#2050](https://github.com/codeigniter4/CodeIgniter4/pull/2050) ([MGatner](https://github.com/MGatner))
- Zero params should be passed through when routing. Fixes \#2032 [\#2043](https://github.com/codeigniter4/CodeIgniter4/pull/2043) ([lonnieezell](https://github.com/lonnieezell))
- SQLite3 now supports dropping foreign keys. Fixes \#1982 [\#2042](https://github.com/codeigniter4/CodeIgniter4/pull/2042) ([lonnieezell](https://github.com/lonnieezell))
- Update CURLRequest.php [\#2040](https://github.com/codeigniter4/CodeIgniter4/pull/2040) ([nowackipawel](https://github.com/nowackipawel))
- Restrict filter matching of uris so they require an exact match. Fixes \#2038 [\#2039](https://github.com/codeigniter4/CodeIgniter4/pull/2039) ([lonnieezell](https://github.com/lonnieezell))
- Make `force\_https\(\)` send headers before exit [\#2036](https://github.com/codeigniter4/CodeIgniter4/pull/2036) ([MGatner](https://github.com/MGatner))
- Various typos and Guide corrections [\#2035](https://github.com/codeigniter4/CodeIgniter4/pull/2035) ([MGatner](https://github.com/MGatner))
- Fallback to server request for default method [\#2031](https://github.com/codeigniter4/CodeIgniter4/pull/2031) ([MGatner](https://github.com/MGatner))
- Support the new `router` service in Debug Toolbar [\#2030](https://github.com/codeigniter4/CodeIgniter4/pull/2030) ([MGatner](https://github.com/MGatner))
- Extension Pager::makeLinks \(optional grup name\) [\#2026](https://github.com/codeigniter4/CodeIgniter4/pull/2026) ([nowackipawel](https://github.com/nowackipawel))
- Refactor the way the router and route collection determine the current HTTP verb. [\#2024](https://github.com/codeigniter4/CodeIgniter4/pull/2024) ([lonnieezell](https://github.com/lonnieezell))
- SQLite and Mysql driver additional tests and migration runner test fixes [\#2019](https://github.com/codeigniter4/CodeIgniter4/pull/2019) ([lonnieezell](https://github.com/lonnieezell))
- Direct user to follow the upgrade steps after installation [\#2015](https://github.com/codeigniter4/CodeIgniter4/pull/2015) ([agmckee](https://github.com/agmckee))
- Added a new Session/ArrayHandler that can be used during testing. [\#2014](https://github.com/codeigniter4/CodeIgniter4/pull/2014) ([lonnieezell](https://github.com/lonnieezell))
- Use request-\>method for HTTP verb [\#2012](https://github.com/codeigniter4/CodeIgniter4/pull/2012) ([MGatner](https://github.com/MGatner))
- Set the raw data array without any mutations for the Entity [\#2011](https://github.com/codeigniter4/CodeIgniter4/pull/2011) ([iRedds](https://github.com/iRedds))
- Add `patch` method to command "routes" [\#2008](https://github.com/codeigniter4/CodeIgniter4/pull/2008) ([MGatner](https://github.com/MGatner))
- Plugin closures docs update and test [\#2005](https://github.com/codeigniter4/CodeIgniter4/pull/2005) ([lonnieezell](https://github.com/lonnieezell))
- Allow hasChanged\(\) without parameter [\#2004](https://github.com/codeigniter4/CodeIgniter4/pull/2004) ([MGatner](https://github.com/MGatner))
- Entity refactor [\#2002](https://github.com/codeigniter4/CodeIgniter4/pull/2002) ([lonnieezell](https://github.com/lonnieezell))
- use CodeIgniter\Controller; not needed since Home Controller extends … [\#1999](https://github.com/codeigniter4/CodeIgniter4/pull/1999) ([titounnes](https://github.com/titounnes))
- Attempting to fix CURLRequest debug issue. \#1994 [\#1996](https://github.com/codeigniter4/CodeIgniter4/pull/1996) ([lonnieezell](https://github.com/lonnieezell))
- argument set\(\) must by type of string - cannot agree [\#1989](https://github.com/codeigniter4/CodeIgniter4/pull/1989) ([nowackipawel](https://github.com/nowackipawel))
- Prevent reverseRoute from searching closures [\#1959](https://github.com/codeigniter4/CodeIgniter4/pull/1959) ([MGatner](https://github.com/MGatner))

## [v4.0.0-beta.3](https://github.com/codeigniter4/CodeIgniter4/tree/v4.0.0-beta.3) (2019-05-06)

[Full Changelog](https://github.com/codeigniter4/CodeIgniter4/compare/v4.0.0-beta.1...v4.0.0-beta.3)

**Fixed bugs:**

- travis-ci build broken [\#1830](https://github.com/codeigniter4/CodeIgniter4/issues/1830)
- \[Re-opened\] 404 File not found when running CodeIgniter on local Apache web server and virtual hosts [\#1400](https://github.com/codeigniter4/CodeIgniter4/issues/1400)
- MySQLi SSL verify [\#1219](https://github.com/codeigniter4/CodeIgniter4/issues/1219)

**Closed issues:**

- SQLite driver doesn't drop indexes when dropping a table [\#1983](https://github.com/codeigniter4/CodeIgniter4/issues/1983)
- About mysqli options MYSQLI\_OPT\_INT\_AND\_FLOAT\_NATIVE [\#1979](https://github.com/codeigniter4/CodeIgniter4/issues/1979)
- Toolbar won't accept custom collectors [\#1971](https://github.com/codeigniter4/CodeIgniter4/issues/1971)
- 404 Error in pathinfo mode [\#1965](https://github.com/codeigniter4/CodeIgniter4/issues/1965)
- A controller parameter without default value creates ReflectionException error [\#1948](https://github.com/codeigniter4/CodeIgniter4/issues/1948)
- Wrong datetime on history debug toolbar [\#1944](https://github.com/codeigniter4/CodeIgniter4/issues/1944)
- Appstarter is not working after installation [\#1941](https://github.com/codeigniter4/CodeIgniter4/issues/1941)
- AppStarter Vanilla Installation - No hint that writable directory is not writable [\#1934](https://github.com/codeigniter4/CodeIgniter4/issues/1934)
- Updating appstarter with composer does not take composer.phar into account [\#1932](https://github.com/codeigniter4/CodeIgniter4/issues/1932)
- Problem with extending core class. [\#1922](https://github.com/codeigniter4/CodeIgniter4/issues/1922)
- Debug Toolbar causes error if an array is provided as session value since Beta 2 [\#1919](https://github.com/codeigniter4/CodeIgniter4/issues/1919)
- Wrong controller filter processing because of faulty regular expression generation [\#1907](https://github.com/codeigniter4/CodeIgniter4/issues/1907)
- Toolbar not supporting IE11 \(realXHR.responseURL\) [\#1905](https://github.com/codeigniter4/CodeIgniter4/issues/1905)
- Validation Always Print Error Message [\#1903](https://github.com/codeigniter4/CodeIgniter4/issues/1903)
- Using soft deletes can lead to mysql ambiguous exception [\#1881](https://github.com/codeigniter4/CodeIgniter4/issues/1881)
- Error when running `php spark serve` \(spaces in folder names\) [\#1880](https://github.com/codeigniter4/CodeIgniter4/issues/1880)
- Class 'CodeIgniter\Filters\DebugToolbar' not found [\#1871](https://github.com/codeigniter4/CodeIgniter4/issues/1871)
- Should unmatched cache\(\) return null? [\#1870](https://github.com/codeigniter4/CodeIgniter4/issues/1870)
- Class 'CodeIgniter\Test\CIUnitTestCase' not found when testing in Appstarter project [\#1864](https://github.com/codeigniter4/CodeIgniter4/issues/1864)
- Public methods in system/Controller [\#1849](https://github.com/codeigniter4/CodeIgniter4/issues/1849)
- Controller in folder - method not accessible unless I set a route on Linux - Windows OK  [\#1841](https://github.com/codeigniter4/CodeIgniter4/issues/1841)
- Routing to hardcoded ids does not work [\#1838](https://github.com/codeigniter4/CodeIgniter4/issues/1838)
- Form Validation: required\_with and required\_without implementations [\#1837](https://github.com/codeigniter4/CodeIgniter4/issues/1837)
- Events.php on method $callable parameter accepts only callable [\#1835](https://github.com/codeigniter4/CodeIgniter4/issues/1835)
- Controller response property overriding by ControllerResponse inPHPUnit  [\#1834](https://github.com/codeigniter4/CodeIgniter4/issues/1834)
- ValidationInterface run method $data attribute should be nullable [\#1833](https://github.com/codeigniter4/CodeIgniter4/issues/1833)
- Fail to render view in a view with layout [\#1826](https://github.com/codeigniter4/CodeIgniter4/issues/1826)
- UploadedFile::move and File::move have different implementation [\#1825](https://github.com/codeigniter4/CodeIgniter4/issues/1825)
- Missing documentation: parser is not able to handle nested loops [\#1821](https://github.com/codeigniter4/CodeIgniter4/issues/1821)
- Is hashId function missing? [\#1801](https://github.com/codeigniter4/CodeIgniter4/issues/1801)
- Parser is not able to handle nested loops [\#1799](https://github.com/codeigniter4/CodeIgniter4/issues/1799)
- Routing rules order                    \[suspended / probably csrf\] [\#1798](https://github.com/codeigniter4/CodeIgniter4/issues/1798)
- I need to call session\(\) if I want to be able to use old\(\) in the forms. [\#1795](https://github.com/codeigniter4/CodeIgniter4/issues/1795)
- Output getting buffered when running via command line  [\#1792](https://github.com/codeigniter4/CodeIgniter4/issues/1792)
- Wrong CodeIgniter::handleRequest method definition [\#1786](https://github.com/codeigniter4/CodeIgniter4/issues/1786)
- File::move is not moving file [\#1785](https://github.com/codeigniter4/CodeIgniter4/issues/1785)
- Question about date helper [\#1783](https://github.com/codeigniter4/CodeIgniter4/issues/1783)
- Intention or bug? File::move does not update path [\#1782](https://github.com/codeigniter4/CodeIgniter4/issues/1782)
- Small typos in documentation section "Taking Advantage of Spl" [\#1781](https://github.com/codeigniter4/CodeIgniter4/issues/1781)
- Documentation mistake: Model::save does not return a boolean only [\#1780](https://github.com/codeigniter4/CodeIgniter4/issues/1780)
- Toolbar::run produces incompatible data for json\_encode [\#1779](https://github.com/codeigniter4/CodeIgniter4/issues/1779)
- History::setFiles may crash when reading empty file [\#1778](https://github.com/codeigniter4/CodeIgniter4/issues/1778)
- Can't set subquery as WHERE condition. [\#1775](https://github.com/codeigniter4/CodeIgniter4/issues/1775)
- Ignoring 'required' validation rule for inserts. [\#1773](https://github.com/codeigniter4/CodeIgniter4/issues/1773)
- save\(\) method trying to insert instead of update [\#1770](https://github.com/codeigniter4/CodeIgniter4/issues/1770)
- Controller Test / Feature Testing output issues [\#1767](https://github.com/codeigniter4/CodeIgniter4/issues/1767)
- MigrationRunner::version should return "current version string on success" [\#1766](https://github.com/codeigniter4/CodeIgniter4/issues/1766)
- DIRECTORY\_SEPARATOR / Different Behavior under Windows [\#1760](https://github.com/codeigniter4/CodeIgniter4/issues/1760)
- HTTP Feature Testing not working  [\#1710](https://github.com/codeigniter4/CodeIgniter4/issues/1710)
- alpha4-\>5 requires to have primary key in every model/table [\#1706](https://github.com/codeigniter4/CodeIgniter4/issues/1706)
- route\_to\('name'\); does not work for other subdomains [\#1697](https://github.com/codeigniter4/CodeIgniter4/issues/1697)
- Router issue - overwriting. [\#1692](https://github.com/codeigniter4/CodeIgniter4/issues/1692)
- Using Memcache as Session Handler cause exception during regenerate. [\#1676](https://github.com/codeigniter4/CodeIgniter4/issues/1676)
- Model's without primary keys get pagination counts wrong [\#1597](https://github.com/codeigniter4/CodeIgniter4/issues/1597)
- Unable set ENVIRONMENT with Spark [\#1268](https://github.com/codeigniter4/CodeIgniter4/issues/1268)
- WIP Improve unit tests [\#512](https://github.com/codeigniter4/CodeIgniter4/issues/512)

**Merged pull requests:**

- Prep for beta.3 [\#1990](https://github.com/codeigniter4/CodeIgniter4/pull/1990) ([jim-parry](https://github.com/jim-parry))
- Correct API docblock problems for phpdocs [\#1987](https://github.com/codeigniter4/CodeIgniter4/pull/1987) ([jim-parry](https://github.com/jim-parry))
- Update docblock version to 4.0.0 [\#1986](https://github.com/codeigniter4/CodeIgniter4/pull/1986) ([jim-parry](https://github.com/jim-parry))
- Fix filter processing. Fixes \#1907 [\#1985](https://github.com/codeigniter4/CodeIgniter4/pull/1985) ([jim-parry](https://github.com/jim-parry))
- Add footing to HTML Table [\#1984](https://github.com/codeigniter4/CodeIgniter4/pull/1984) ([jim-parry](https://github.com/jim-parry))
- Using soft deletes should not return an ambiguous field message when joining tables. Closes \#1881 [\#1981](https://github.com/codeigniter4/CodeIgniter4/pull/1981) ([lonnieezell](https://github.com/lonnieezell))
- Corrected return value for Session/RedisHandler::read to string, per PHP specs [\#1980](https://github.com/codeigniter4/CodeIgniter4/pull/1980) ([lonnieezell](https://github.com/lonnieezell))
- Implement HTML Table for CI4 [\#1978](https://github.com/codeigniter4/CodeIgniter4/pull/1978) ([jim-parry](https://github.com/jim-parry))
- Test/featuretestcase [\#1977](https://github.com/codeigniter4/CodeIgniter4/pull/1977) ([jim-parry](https://github.com/jim-parry))
- Fix validation rules table format [\#1975](https://github.com/codeigniter4/CodeIgniter4/pull/1975) ([jim-parry](https://github.com/jim-parry))
- Remove framework classes from the autoloader classmap.  [\#1974](https://github.com/codeigniter4/CodeIgniter4/pull/1974) ([lonnieezell](https://github.com/lonnieezell))
- Defaultfixes [\#1973](https://github.com/codeigniter4/CodeIgniter4/pull/1973) ([lonnieezell](https://github.com/lonnieezell))
- Toolbar fix for custom collectors [\#1972](https://github.com/codeigniter4/CodeIgniter4/pull/1972) ([MGatner](https://github.com/MGatner))
- Add back filter arguments [\#1970](https://github.com/codeigniter4/CodeIgniter4/pull/1970) ([MGatner](https://github.com/MGatner))
- Fixed pathinfo mode 404 error, rebuild array index of uri segments from array\_filter\(\) [\#1968](https://github.com/codeigniter4/CodeIgniter4/pull/1968) ([viosion](https://github.com/viosion))
- String type primary key should also wrap into an array during db update [\#1963](https://github.com/codeigniter4/CodeIgniter4/pull/1963) ([vibbow](https://github.com/vibbow))
- WIP - Fix side issue [\#1962](https://github.com/codeigniter4/CodeIgniter4/pull/1962) ([vibbow](https://github.com/vibbow))
- Fix Debugbar url tail slash issue [\#1961](https://github.com/codeigniter4/CodeIgniter4/pull/1961) ([vibbow](https://github.com/vibbow))
- New generic string validation rule. [\#1957](https://github.com/codeigniter4/CodeIgniter4/pull/1957) ([lonnieezell](https://github.com/lonnieezell))
- Use Null Coalesce Operator [\#1956](https://github.com/codeigniter4/CodeIgniter4/pull/1956) ([carusogabriel](https://github.com/carusogabriel))
- Travis-CI build failed fix [\#1955](https://github.com/codeigniter4/CodeIgniter4/pull/1955) ([atishamte](https://github.com/atishamte))
- Fix validation table format [\#1954](https://github.com/codeigniter4/CodeIgniter4/pull/1954) ([jim-parry](https://github.com/jim-parry))
- Add Validations for `equals\(\)` and `not\_equals\(\)` [\#1952](https://github.com/codeigniter4/CodeIgniter4/pull/1952) ([MGatner](https://github.com/MGatner))
- System typos changes & code cleanup [\#1951](https://github.com/codeigniter4/CodeIgniter4/pull/1951) ([atishamte](https://github.com/atishamte))
- Fix some side issue [\#1950](https://github.com/codeigniter4/CodeIgniter4/pull/1950) ([vibbow](https://github.com/vibbow))
- Toobar/Routes correction [\#1949](https://github.com/codeigniter4/CodeIgniter4/pull/1949) ([atishamte](https://github.com/atishamte))
- Fix BaseConfig didn't load Registrar files properly [\#1947](https://github.com/codeigniter4/CodeIgniter4/pull/1947) ([vibbow](https://github.com/vibbow))
- Fix datetime extraction from debugbar file [\#1945](https://github.com/codeigniter4/CodeIgniter4/pull/1945) ([soft2u](https://github.com/soft2u))
- Model, Entity, Exception & Migration test cases [\#1943](https://github.com/codeigniter4/CodeIgniter4/pull/1943) ([atishamte](https://github.com/atishamte))
- Remove section that prevents hotlinking [\#1939](https://github.com/codeigniter4/CodeIgniter4/pull/1939) ([MGatner](https://github.com/MGatner))
- Database typos changes [\#1938](https://github.com/codeigniter4/CodeIgniter4/pull/1938) ([atishamte](https://github.com/atishamte))
- Docs: improve app testing writeup [\#1936](https://github.com/codeigniter4/CodeIgniter4/pull/1936) ([jim-parry](https://github.com/jim-parry))
- Update phpunit.xml scripts. Fixes \#1932 [\#1935](https://github.com/codeigniter4/CodeIgniter4/pull/1935) ([jim-parry](https://github.com/jim-parry))
- having \(Is NULL deletion\) [\#1933](https://github.com/codeigniter4/CodeIgniter4/pull/1933) ([nowackipawel](https://github.com/nowackipawel))
- Toolbar IE11 fix [\#1931](https://github.com/codeigniter4/CodeIgniter4/pull/1931) ([REJack](https://github.com/REJack))
- Model Changes w.r.t. \#1773 [\#1930](https://github.com/codeigniter4/CodeIgniter4/pull/1930) ([atishamte](https://github.com/atishamte))
- Entity exception for non existed props. [\#1927](https://github.com/codeigniter4/CodeIgniter4/pull/1927) ([nowackipawel](https://github.com/nowackipawel))
- Docs: update installation guide [\#1926](https://github.com/codeigniter4/CodeIgniter4/pull/1926) ([jim-parry](https://github.com/jim-parry))
- removed $\_SERVER\['CI\_ENVIRONMENT'\] [\#1925](https://github.com/codeigniter4/CodeIgniter4/pull/1925) ([truelineinfotech](https://github.com/truelineinfotech))
- missing return [\#1923](https://github.com/codeigniter4/CodeIgniter4/pull/1923) ([titounnes](https://github.com/titounnes))
- JSONFormatter [\#1918](https://github.com/codeigniter4/CodeIgniter4/pull/1918) ([nowackipawel](https://github.com/nowackipawel))
- Database Test Cases [\#1917](https://github.com/codeigniter4/CodeIgniter4/pull/1917) ([atishamte](https://github.com/atishamte))
- Check if the value is string [\#1916](https://github.com/codeigniter4/CodeIgniter4/pull/1916) ([daif](https://github.com/daif))
- Fix for POST + JSON \(Content-Length added\) [\#1915](https://github.com/codeigniter4/CodeIgniter4/pull/1915) ([nowackipawel](https://github.com/nowackipawel))
- Housekeeping - prep for beta.2 [\#1914](https://github.com/codeigniter4/CodeIgniter4/pull/1914) ([jim-parry](https://github.com/jim-parry))
- More RouteCollection tests for overwriting. Closes \#1692 [\#1913](https://github.com/codeigniter4/CodeIgniter4/pull/1913) ([jim-parry](https://github.com/jim-parry))
- Additional RouteCollectionTests [\#1912](https://github.com/codeigniter4/CodeIgniter4/pull/1912) ([jim-parry](https://github.com/jim-parry))
- JSON Cast exception test cases [\#1911](https://github.com/codeigniter4/CodeIgniter4/pull/1911) ([atishamte](https://github.com/atishamte))
- Added print method to CLI library so you can print multiple times on same line [\#1910](https://github.com/codeigniter4/CodeIgniter4/pull/1910) ([lonnieezell](https://github.com/lonnieezell))
- Add filter parameters to User Guide [\#1908](https://github.com/codeigniter4/CodeIgniter4/pull/1908) ([MGatner](https://github.com/MGatner))
- SubQuery related test cases w.r.t \#1775 [\#1906](https://github.com/codeigniter4/CodeIgniter4/pull/1906) ([atishamte](https://github.com/atishamte))
- BaseBuilder Corrections [\#1902](https://github.com/codeigniter4/CodeIgniter4/pull/1902) ([atishamte](https://github.com/atishamte))
- Update .htaccess for better security and caching [\#1900](https://github.com/codeigniter4/CodeIgniter4/pull/1900) ([atishamte](https://github.com/atishamte))
- Database Forge correction [\#1899](https://github.com/codeigniter4/CodeIgniter4/pull/1899) ([atishamte](https://github.com/atishamte))
- Toolbar fix w.r.t \#1779 [\#1897](https://github.com/codeigniter4/CodeIgniter4/pull/1897) ([atishamte](https://github.com/atishamte))
- Mysql connection issue with MYSQLI\_CLIENT\_SSL\_DONT\_VERIFY\_SERVER\_CERT \#1219 [\#1896](https://github.com/codeigniter4/CodeIgniter4/pull/1896) ([atishamte](https://github.com/atishamte))
- Unmatched Cache Library `get\(\)` return null [\#1895](https://github.com/codeigniter4/CodeIgniter4/pull/1895) ([MGatner](https://github.com/MGatner))
- New method Find Column w.r.t. \#1619 [\#1861](https://github.com/codeigniter4/CodeIgniter4/pull/1861) ([atishamte](https://github.com/atishamte))

## [v4.0.0-beta.1](https://github.com/codeigniter4/CodeIgniter4/tree/v4.0.0-beta.1) (2019-03-01)

[Full Changelog](https://github.com/codeigniter4/CodeIgniter4/compare/v4.0.0.0-alpha.5...v4.0.0-beta.1)

**Fixed bugs:**

- Sequential Migrations don't run to the correct version [\#1666](https://github.com/codeigniter4/CodeIgniter4/issues/1666)

**Closed issues:**

- Back references not working in route [\#1761](https://github.com/codeigniter4/CodeIgniter4/issues/1761)
- setDefaultController not working as expected [\#1758](https://github.com/codeigniter4/CodeIgniter4/issues/1758)
- Spark doesn't work when using devstarter [\#1748](https://github.com/codeigniter4/CodeIgniter4/issues/1748)
- required\_with and required\_without validation rules [\#1735](https://github.com/codeigniter4/CodeIgniter4/issues/1735)
- validation rule password never match [\#1728](https://github.com/codeigniter4/CodeIgniter4/issues/1728)
- Entities errors [\#1727](https://github.com/codeigniter4/CodeIgniter4/issues/1727)
- Loading namespace helpers doesn't work as expected CI4 alpha5 [\#1726](https://github.com/codeigniter4/CodeIgniter4/issues/1726)
- spark migrate:latest ErrorException alpha5 [\#1724](https://github.com/codeigniter4/CodeIgniter4/issues/1724)
- redirect\(\)-\>to lost $baseURL config [\#1721](https://github.com/codeigniter4/CodeIgniter4/issues/1721)
- Bug Report: Seeding [\#1720](https://github.com/codeigniter4/CodeIgniter4/issues/1720)
- Spark missing arguments [\#1718](https://github.com/codeigniter4/CodeIgniter4/issues/1718)
- Model required validation rule not working  [\#1717](https://github.com/codeigniter4/CodeIgniter4/issues/1717)
- ZendEscaper - duplicate? [\#1716](https://github.com/codeigniter4/CodeIgniter4/issues/1716)
- Why we required form pointed to correct url? [\#1713](https://github.com/codeigniter4/CodeIgniter4/issues/1713)
- Why there is only 1 function in ArrayHelper? Can we introduce more? [\#1711](https://github.com/codeigniter4/CodeIgniter4/issues/1711)
- CodeIgniter\Model::cleanValidationRules\(\) must be of the type array, string given [\#1707](https://github.com/codeigniter4/CodeIgniter4/issues/1707)
- alpha 4-\>5 query param binding [\#1705](https://github.com/codeigniter4/CodeIgniter4/issues/1705)
- failValidationError\($description\) [\#1702](https://github.com/codeigniter4/CodeIgniter4/issues/1702)
- Bug : changing viewsDirectory misses errors folder when exception occures  [\#1701](https://github.com/codeigniter4/CodeIgniter4/issues/1701)
- Cannot define complex routes , i.e. date [\#1700](https://github.com/codeigniter4/CodeIgniter4/issues/1700)
- lang bug or not? \(empty translations\) [\#1698](https://github.com/codeigniter4/CodeIgniter4/issues/1698)
- Issue Extend Core Class [\#1653](https://github.com/codeigniter4/CodeIgniter4/issues/1653)
- Turn OFF getMyProperty\(\) method during DB save. [\#1646](https://github.com/codeigniter4/CodeIgniter4/issues/1646)
- Model class crashes when handling complex validation rules [\#1574](https://github.com/codeigniter4/CodeIgniter4/issues/1574)
- Database ForgeTest hiccup [\#1478](https://github.com/codeigniter4/CodeIgniter4/issues/1478)
- SQLLite3 Forge needs better column handling [\#1255](https://github.com/codeigniter4/CodeIgniter4/issues/1255)
- TODO BaseConnection needs better error handling [\#1254](https://github.com/codeigniter4/CodeIgniter4/issues/1254)
- Model Alternative Keys [\#428](https://github.com/codeigniter4/CodeIgniter4/issues/428)

**Merged pull requests:**

- Housekeeping for beta.1 [\#1774](https://github.com/codeigniter4/CodeIgniter4/pull/1774) ([jim-parry](https://github.com/jim-parry))
- Helper changes [\#1768](https://github.com/codeigniter4/CodeIgniter4/pull/1768) ([atishamte](https://github.com/atishamte))
- Fix routing when no default route has been specified. Fixes \#1758 [\#1764](https://github.com/codeigniter4/CodeIgniter4/pull/1764) ([lonnieezell](https://github.com/lonnieezell))
- Ensure validation works in Model with errors as part of rules. Fixes \#1574 [\#1763](https://github.com/codeigniter4/CodeIgniter4/pull/1763) ([lonnieezell](https://github.com/lonnieezell))
- Correct the unneeded double-quote \(typo\) [\#1757](https://github.com/codeigniter4/CodeIgniter4/pull/1757) ([smhnaji](https://github.com/smhnaji))
- lowercase 'vfsStream' in composer files [\#1755](https://github.com/codeigniter4/CodeIgniter4/pull/1755) ([MGatner](https://github.com/MGatner))
- Fixed typo preventing link format [\#1752](https://github.com/codeigniter4/CodeIgniter4/pull/1752) ([MGatner](https://github.com/MGatner))
- Guide: Moving misplaced text under correct heading [\#1751](https://github.com/codeigniter4/CodeIgniter4/pull/1751) ([MGatner](https://github.com/MGatner))
- Remove reference to Encryption Key in User Guide [\#1750](https://github.com/codeigniter4/CodeIgniter4/pull/1750) ([MGatner](https://github.com/MGatner))
- Adding environment to .env [\#1749](https://github.com/codeigniter4/CodeIgniter4/pull/1749) ([MGatner](https://github.com/MGatner))
- Updated composite key tests for SQLite3 support. Fixes \#1478 [\#1745](https://github.com/codeigniter4/CodeIgniter4/pull/1745) ([lonnieezell](https://github.com/lonnieezell))
- Update entity docs for current framework state. Fixes \#1727 [\#1744](https://github.com/codeigniter4/CodeIgniter4/pull/1744) ([lonnieezell](https://github.com/lonnieezell))
- Manually sort migrations found instead of relying on the OS. Fixes \#1666 [\#1743](https://github.com/codeigniter4/CodeIgniter4/pull/1743) ([lonnieezell](https://github.com/lonnieezell))
- Fix required\_without rule bug. [\#1742](https://github.com/codeigniter4/CodeIgniter4/pull/1742) ([bangbangda](https://github.com/bangbangda))
- Helpers with a specific namespace can be loaded now. Fixes \#1726 [\#1741](https://github.com/codeigniter4/CodeIgniter4/pull/1741) ([lonnieezell](https://github.com/lonnieezell))
- Refactor test support for app starter [\#1740](https://github.com/codeigniter4/CodeIgniter4/pull/1740) ([jim-parry](https://github.com/jim-parry))
- Fix typo [\#1739](https://github.com/codeigniter4/CodeIgniter4/pull/1739) ([vibbow](https://github.com/vibbow))
- Fix required\_with rule bug. Fixes \#1728 [\#1738](https://github.com/codeigniter4/CodeIgniter4/pull/1738) ([bangbangda](https://github.com/bangbangda))
- Added support for dropTable and modifyTable with SQLite driver [\#1737](https://github.com/codeigniter4/CodeIgniter4/pull/1737) ([lonnieezell](https://github.com/lonnieezell))
- Accommodate long travis execution times [\#1736](https://github.com/codeigniter4/CodeIgniter4/pull/1736) ([jim-parry](https://github.com/jim-parry))
- Fix increment and decrement errors with Postgres [\#1733](https://github.com/codeigniter4/CodeIgniter4/pull/1733) ([lonnieezell](https://github.com/lonnieezell))
- Don't check  from CLI in Routes. Fixes \#1724 [\#1732](https://github.com/codeigniter4/CodeIgniter4/pull/1732) ([lonnieezell](https://github.com/lonnieezell))
- Revert "Ensure  isn't checked during RouteCollection calls when called from CLI" [\#1731](https://github.com/codeigniter4/CodeIgniter4/pull/1731) ([lonnieezell](https://github.com/lonnieezell))
- Ensure  isn't checked during RouteCollection calls when called from CLI [\#1730](https://github.com/codeigniter4/CodeIgniter4/pull/1730) ([lonnieezell](https://github.com/lonnieezell))
- New View Layout functionality for simple template functionality. [\#1729](https://github.com/codeigniter4/CodeIgniter4/pull/1729) ([lonnieezell](https://github.com/lonnieezell))
- Update Request.php [\#1725](https://github.com/codeigniter4/CodeIgniter4/pull/1725) ([HieuPT7](https://github.com/HieuPT7))
- Log an error if redis authentication is failed. [\#1723](https://github.com/codeigniter4/CodeIgniter4/pull/1723) ([vibbow](https://github.com/vibbow))
- Seeder adds default namespace to seeds [\#1722](https://github.com/codeigniter4/CodeIgniter4/pull/1722) ([lonnieezell](https://github.com/lonnieezell))
- Update Cache RedisHandler to support select database. [\#1719](https://github.com/codeigniter4/CodeIgniter4/pull/1719) ([vibbow](https://github.com/vibbow))
- minors \(Model.php\) [\#1712](https://github.com/codeigniter4/CodeIgniter4/pull/1712) ([nowackipawel](https://github.com/nowackipawel))
- Fix/rc [\#1709](https://github.com/codeigniter4/CodeIgniter4/pull/1709) ([jim-parry](https://github.com/jim-parry))
- UploadFile - language support [\#1708](https://github.com/codeigniter4/CodeIgniter4/pull/1708) ([nowackipawel](https://github.com/nowackipawel))
- Fix viewsDirectory bug Fixes \#1701 [\#1704](https://github.com/codeigniter4/CodeIgniter4/pull/1704) ([bangbangda](https://github.com/bangbangda))
- Fix install link in user guide [\#1699](https://github.com/codeigniter4/CodeIgniter4/pull/1699) ([jim-parry](https://github.com/jim-parry))
- Fix page structure etc [\#1696](https://github.com/codeigniter4/CodeIgniter4/pull/1696) ([jim-parry](https://github.com/jim-parry))
- Tidy up code blocks in the user guide [\#1695](https://github.com/codeigniter4/CodeIgniter4/pull/1695) ([jim-parry](https://github.com/jim-parry))

## [v4.0.0.0-alpha.5](https://github.com/codeigniter4/CodeIgniter4/tree/v4.0.0.0-alpha.5) (2019-01-30)

[Full Changelog](https://github.com/codeigniter4/CodeIgniter4/compare/v4.0.0-alpha.5...v4.0.0.0-alpha.5)

**Implemented enhancements:**

- Cache system should support site-wide prefixes [\#1659](https://github.com/codeigniter4/CodeIgniter4/issues/1659)

**Fixed bugs:**

- Problem with Database BaseBuilder binds [\#1226](https://github.com/codeigniter4/CodeIgniter4/issues/1226)

**Closed issues:**

- DB query not working with disabled escaping [\#1687](https://github.com/codeigniter4/CodeIgniter4/issues/1687)
- migrate:refresh ArgumentCountError [\#1682](https://github.com/codeigniter4/CodeIgniter4/issues/1682)
- Do I need to clear old session files manually? [\#1681](https://github.com/codeigniter4/CodeIgniter4/issues/1681)
- Pagination / pager-\>links\(\) double encodes links [\#1680](https://github.com/codeigniter4/CodeIgniter4/issues/1680)
- Document Method Spoofing for forms. [\#1668](https://github.com/codeigniter4/CodeIgniter4/issues/1668)
- insertBatch with escape=false still escapes values [\#1667](https://github.com/codeigniter4/CodeIgniter4/issues/1667)
- Filters should not be case sensitive [\#1664](https://github.com/codeigniter4/CodeIgniter4/issues/1664)
- RouteCollection::discoverRoutes incomplete [\#1662](https://github.com/codeigniter4/CodeIgniter4/issues/1662)
- Feature request make is\_unique - more than one field. [\#1655](https://github.com/codeigniter4/CodeIgniter4/issues/1655)
- Toolbar logs tab - not logging [\#1651](https://github.com/codeigniter4/CodeIgniter4/issues/1651)
- DebugToolbar - too much recursion [\#1650](https://github.com/codeigniter4/CodeIgniter4/issues/1650)
- \[documentation\] Typing mistake in transaction example [\#1639](https://github.com/codeigniter4/CodeIgniter4/issues/1639)
- Transaction documentation error and/or bug [\#1638](https://github.com/codeigniter4/CodeIgniter4/issues/1638)
- Bug : pagination broken when using 1 as perPage [\#1628](https://github.com/codeigniter4/CodeIgniter4/issues/1628)
- View data not being passed between each call? [\#1621](https://github.com/codeigniter4/CodeIgniter4/issues/1621)
- Composer Installation downloads app and application folder. [\#1620](https://github.com/codeigniter4/CodeIgniter4/issues/1620)
- countAllResults\(\) should respect soft deletes [\#1617](https://github.com/codeigniter4/CodeIgniter4/issues/1617)
- redirect function don't redirect to base\_url [\#1611](https://github.com/codeigniter4/CodeIgniter4/issues/1611)
- Memory issue - Toolbar collects every query [\#1607](https://github.com/codeigniter4/CodeIgniter4/issues/1607)
- Pls remove string type in parameter $group at Database::forge [\#1605](https://github.com/codeigniter4/CodeIgniter4/issues/1605)
- SQL JOIN : bad aliasing on join with prefixed db tables [\#1599](https://github.com/codeigniter4/CodeIgniter4/issues/1599)
- Model's update method fails when validation rules exist [\#1584](https://github.com/codeigniter4/CodeIgniter4/issues/1584)
- maybe need to modify session garbage collector section. \(FileHandler\) [\#1565](https://github.com/codeigniter4/CodeIgniter4/issues/1565)
- Maybe routes has problem. \[setTranslateURIDashes\] [\#1564](https://github.com/codeigniter4/CodeIgniter4/issues/1564)
- ErrorException  Trying to get property 'affected\_rows' of non-object  [\#1559](https://github.com/codeigniter4/CodeIgniter4/issues/1559)
- UG - typo in Managing Apps [\#1558](https://github.com/codeigniter4/CodeIgniter4/issues/1558)
- Database migration uses wrong database when initialising migration classes [\#1532](https://github.com/codeigniter4/CodeIgniter4/issues/1532)
- Database migration table not correctly created when a non-default database connection is used [\#1531](https://github.com/codeigniter4/CodeIgniter4/issues/1531)
- MYSQL : orderBy\(\) considers CASE statement as a column [\#1528](https://github.com/codeigniter4/CodeIgniter4/issues/1528)
- getCompiledSelect\(\) return query without binds [\#1526](https://github.com/codeigniter4/CodeIgniter4/issues/1526)
- Commit pre-hook misbehaving [\#1404](https://github.com/codeigniter4/CodeIgniter4/issues/1404)
- Lack of proper instruction in documentation for changing Application and System folder name [\#1366](https://github.com/codeigniter4/CodeIgniter4/issues/1366)
- SubQueries \(tables from outside of the current model\) [\#1175](https://github.com/codeigniter4/CodeIgniter4/issues/1175)
- FileHandler Garbage Collector fails to delete expired session files.  [\#942](https://github.com/codeigniter4/CodeIgniter4/issues/942)

**Merged pull requests:**

- Update changelog for alpha.5 [\#1694](https://github.com/codeigniter4/CodeIgniter4/pull/1694) ([jim-parry](https://github.com/jim-parry))
- Docs/tutorial [\#1693](https://github.com/codeigniter4/CodeIgniter4/pull/1693) ([jim-parry](https://github.com/jim-parry))
- Update the running docs [\#1691](https://github.com/codeigniter4/CodeIgniter4/pull/1691) ([jim-parry](https://github.com/jim-parry))
- Rework install docs [\#1690](https://github.com/codeigniter4/CodeIgniter4/pull/1690) ([jim-parry](https://github.com/jim-parry))
- Model Validation Fix [\#1689](https://github.com/codeigniter4/CodeIgniter4/pull/1689) ([lonnieezell](https://github.com/lonnieezell))
- Add copyright blocks to filters [\#1688](https://github.com/codeigniter4/CodeIgniter4/pull/1688) ([jim-parry](https://github.com/jim-parry))
- Refactor/filters [\#1686](https://github.com/codeigniter4/CodeIgniter4/pull/1686) ([jim-parry](https://github.com/jim-parry))
- Fix admin - app starter creation [\#1685](https://github.com/codeigniter4/CodeIgniter4/pull/1685) ([jim-parry](https://github.com/jim-parry))
- Updating session id cleanup for filehandler. Fixes \#1681 Fixes \#1565 [\#1684](https://github.com/codeigniter4/CodeIgniter4/pull/1684) ([lonnieezell](https://github.com/lonnieezell))
- Fix migrate:refresh bug Fixes \#1682 [\#1683](https://github.com/codeigniter4/CodeIgniter4/pull/1683) ([bangbangda](https://github.com/bangbangda))
- save\_path - for memcached \(Session.php\) + sess\_prefix \(..Handler.php\) [\#1679](https://github.com/codeigniter4/CodeIgniter4/pull/1679) ([nowackipawel](https://github.com/nowackipawel))
- fix route not replacing forward slashes [\#1678](https://github.com/codeigniter4/CodeIgniter4/pull/1678) ([puschie286](https://github.com/puschie286))
- Implement Don't Escape feature for db engine [\#1677](https://github.com/codeigniter4/CodeIgniter4/pull/1677) ([lonnieezell](https://github.com/lonnieezell))
- Add missing test group directives [\#1675](https://github.com/codeigniter4/CodeIgniter4/pull/1675) ([jim-parry](https://github.com/jim-parry))
- Changelog alpha.5 so far [\#1674](https://github.com/codeigniter4/CodeIgniter4/pull/1674) ([jim-parry](https://github.com/jim-parry))
- Updated download & installation docs [\#1673](https://github.com/codeigniter4/CodeIgniter4/pull/1673) ([jim-parry](https://github.com/jim-parry))
- Update Autoloader.php [\#1672](https://github.com/codeigniter4/CodeIgniter4/pull/1672) ([zl59503020](https://github.com/zl59503020))
- Update docs [\#1671](https://github.com/codeigniter4/CodeIgniter4/pull/1671) ([jim-parry](https://github.com/jim-parry))
- Update PHP dependency to 7.2 [\#1670](https://github.com/codeigniter4/CodeIgniter4/pull/1670) ([jim-parry](https://github.com/jim-parry))
- Enhance Parser & Plugin testing [\#1669](https://github.com/codeigniter4/CodeIgniter4/pull/1669) ([jim-parry](https://github.com/jim-parry))
- Composer PSR4 namespaces are now part of the modules auto-discovery [\#1665](https://github.com/codeigniter4/CodeIgniter4/pull/1665) ([lonnieezell](https://github.com/lonnieezell))
- Fix bind issue that occurred when using whereIn or orWhereIn with a c… [\#1663](https://github.com/codeigniter4/CodeIgniter4/pull/1663) ([lonnieezell](https://github.com/lonnieezell))
- Migrations Tests and database tweaks [\#1660](https://github.com/codeigniter4/CodeIgniter4/pull/1660) ([lonnieezell](https://github.com/lonnieezell))
- DBGroup  in \_\_get\(\), allows to validate "database" data outside the model. [\#1656](https://github.com/codeigniter4/CodeIgniter4/pull/1656) ([nowackipawel](https://github.com/nowackipawel))
- Toolbar - Return Logger::$logCache items when collecting [\#1654](https://github.com/codeigniter4/CodeIgniter4/pull/1654) ([natanfelles](https://github.com/natanfelles))
- remove php 7.3 from "allow\_failures" in travis config [\#1649](https://github.com/codeigniter4/CodeIgniter4/pull/1649) ([samsonasik](https://github.com/samsonasik))
- Update "managing apps" docs [\#1648](https://github.com/codeigniter4/CodeIgniter4/pull/1648) ([jim-parry](https://github.com/jim-parry))
- Fix transaction enabling confusing \(docu\) [\#1645](https://github.com/codeigniter4/CodeIgniter4/pull/1645) ([puschie286](https://github.com/puschie286))
- Remove Email module [\#1643](https://github.com/codeigniter4/CodeIgniter4/pull/1643) ([jim-parry](https://github.com/jim-parry))
- CSP nonce attribute value in ""  [\#1642](https://github.com/codeigniter4/CodeIgniter4/pull/1642) ([nowackipawel](https://github.com/nowackipawel))
- More unit testing tweaks [\#1641](https://github.com/codeigniter4/CodeIgniter4/pull/1641) ([jim-parry](https://github.com/jim-parry))
- Update getCompiledX methods in BaseBuilder to return fully compiled q… [\#1640](https://github.com/codeigniter4/CodeIgniter4/pull/1640) ([lonnieezell](https://github.com/lonnieezell))
- Fix starter README [\#1637](https://github.com/codeigniter4/CodeIgniter4/pull/1637) ([kenjis](https://github.com/kenjis))
- Refactor Files module [\#1636](https://github.com/codeigniter4/CodeIgniter4/pull/1636) ([jim-parry](https://github.com/jim-parry))
- Unit testing enhancements [\#1635](https://github.com/codeigniter4/CodeIgniter4/pull/1635) ([jim-parry](https://github.com/jim-parry))
- Uses csrf\_field and form\_hidden instead of inline-html in form\_open  [\#1633](https://github.com/codeigniter4/CodeIgniter4/pull/1633) ([nowackipawel](https://github.com/nowackipawel))
- DBGroup should be passed to -\>run instead of -\>setRules [\#1632](https://github.com/codeigniter4/CodeIgniter4/pull/1632) ([nowackipawel](https://github.com/nowackipawel))
- move use statement after License doc at UploadedFile class [\#1631](https://github.com/codeigniter4/CodeIgniter4/pull/1631) ([samsonasik](https://github.com/samsonasik))
- Update copyright to 2019 [\#1630](https://github.com/codeigniter4/CodeIgniter4/pull/1630) ([jim-parry](https://github.com/jim-parry))
- "application" to "app" directory doc and comments and welcome\_message clean up [\#1629](https://github.com/codeigniter4/CodeIgniter4/pull/1629) ([samsonasik](https://github.com/samsonasik))
- clean up Paths::$viewDirectory property [\#1626](https://github.com/codeigniter4/CodeIgniter4/pull/1626) ([samsonasik](https://github.com/samsonasik))
- fix. After matches is not set empty [\#1625](https://github.com/codeigniter4/CodeIgniter4/pull/1625) ([Instrye](https://github.com/Instrye))
- Property was not cast if was defined as nullable. [\#1623](https://github.com/codeigniter4/CodeIgniter4/pull/1623) ([nowackipawel](https://github.com/nowackipawel))
- Nullable support for \_\_set. [\#1622](https://github.com/codeigniter4/CodeIgniter4/pull/1622) ([nowackipawel](https://github.com/nowackipawel))
- Fix View config merge order [\#1616](https://github.com/codeigniter4/CodeIgniter4/pull/1616) ([jim-parry](https://github.com/jim-parry))
- Typo in documentation [\#1613](https://github.com/codeigniter4/CodeIgniter4/pull/1613) ([tpw1314](https://github.com/tpw1314))
- WIP img fix\(?\) - html\_helper [\#1538](https://github.com/codeigniter4/CodeIgniter4/pull/1538) ([nowackipawel](https://github.com/nowackipawel))

## [v4.0.0-alpha.5](https://github.com/codeigniter4/CodeIgniter4/tree/v4.0.0-alpha.5) (2018-12-15)

[Full Changelog](https://github.com/codeigniter4/CodeIgniter4/compare/v4.0.0-alpha.4...v4.0.0-alpha.5)

## [v4.0.0-alpha.4](https://github.com/codeigniter4/CodeIgniter4/tree/v4.0.0-alpha.4) (2018-12-15)

[Full Changelog](https://github.com/codeigniter4/CodeIgniter4/compare/v4.0.0-alpha.3...v4.0.0-alpha.4)

**Implemented enhancements:**

- WIP Vagrant and/or Docker support [\#1452](https://github.com/codeigniter4/CodeIgniter4/issues/1452)

**Closed issues:**

- Custom routing rule not match the User Guide [\#1609](https://github.com/codeigniter4/CodeIgniter4/issues/1609)
- Memory leak - binds not being cleared after find\(\) [\#1604](https://github.com/codeigniter4/CodeIgniter4/issues/1604)
- Soft deletes need parentheses around proceeding query? [\#1592](https://github.com/codeigniter4/CodeIgniter4/issues/1592)
- Cannot use Model first\(\) and update\(\) in table without primary key [\#1583](https://github.com/codeigniter4/CodeIgniter4/issues/1583)
- tried to allocate [\#1578](https://github.com/codeigniter4/CodeIgniter4/issues/1578)
- Database Migrations inconsistent info about sequential type  [\#1577](https://github.com/codeigniter4/CodeIgniter4/issues/1577)
- Wrong or not precise documentation of magic \_\_get and \_\_set methods in section Handling Business Logic [\#1568](https://github.com/codeigniter4/CodeIgniter4/issues/1568)
- Entity's fill method is not supporting key mapping [\#1567](https://github.com/codeigniter4/CodeIgniter4/issues/1567)
- Ability to add namespace to FileLocator class [\#1552](https://github.com/codeigniter4/CodeIgniter4/issues/1552)
- Should log file contain plain text database username passwords etc? [\#1542](https://github.com/codeigniter4/CodeIgniter4/issues/1542)
- Issues with redirects - had to use exit to make it work and blank page  [\#1501](https://github.com/codeigniter4/CodeIgniter4/issues/1501)
- Use of undefined constant BASEPATH [\#1439](https://github.com/codeigniter4/CodeIgniter4/issues/1439)
- MYSQL : BETWEEN operator loses condition value on JOIN in \(:\) used [\#1403](https://github.com/codeigniter4/CodeIgniter4/issues/1403)
- The problem in catching exceptions [\#1274](https://github.com/codeigniter4/CodeIgniter4/issues/1274)
- TODO Language needs improved locating [\#1262](https://github.com/codeigniter4/CodeIgniter4/issues/1262)
- Email attachment  [\#1008](https://github.com/codeigniter4/CodeIgniter4/issues/1008)

**Merged pull requests:**

- Alpha.4 release prep [\#1612](https://github.com/codeigniter4/CodeIgniter4/pull/1612) ([jim-parry](https://github.com/jim-parry))
- Test, fix & enhance Language [\#1610](https://github.com/codeigniter4/CodeIgniter4/pull/1610) ([jim-parry](https://github.com/jim-parry))
- Note about environment configuration in UG [\#1608](https://github.com/codeigniter4/CodeIgniter4/pull/1608) ([jim-parry](https://github.com/jim-parry))
- release framework script clean up [\#1606](https://github.com/codeigniter4/CodeIgniter4/pull/1606) ([samsonasik](https://github.com/samsonasik))
- Flesh out I18n testing [\#1603](https://github.com/codeigniter4/CodeIgniter4/pull/1603) ([jim-parry](https://github.com/jim-parry))
- Model's first and update didn't work primary key-less tables [\#1602](https://github.com/codeigniter4/CodeIgniter4/pull/1602) ([lonnieezell](https://github.com/lonnieezell))
- clean up \Config\Services in Common.php [\#1601](https://github.com/codeigniter4/CodeIgniter4/pull/1601) ([samsonasik](https://github.com/samsonasik))
- admin/starter/composer.json clean up [\#1600](https://github.com/codeigniter4/CodeIgniter4/pull/1600) ([samsonasik](https://github.com/samsonasik))
- use $defaultGroup as default value for database session DBGroup [\#1598](https://github.com/codeigniter4/CodeIgniter4/pull/1598) ([puschie286](https://github.com/puschie286))
- Retry handle fatal error via pre\_system [\#1595](https://github.com/codeigniter4/CodeIgniter4/pull/1595) ([samsonasik](https://github.com/samsonasik))
- Fix Toolbar invalid css [\#1594](https://github.com/codeigniter4/CodeIgniter4/pull/1594) ([puschie286](https://github.com/puschie286))
- Flesh out the Test package testing [\#1593](https://github.com/codeigniter4/CodeIgniter4/pull/1593) ([jim-parry](https://github.com/jim-parry))
- Fix Toolbar file loading throw exception [\#1589](https://github.com/codeigniter4/CodeIgniter4/pull/1589) ([puschie286](https://github.com/puschie286))
- Fix site\_url generate invalid url [\#1588](https://github.com/codeigniter4/CodeIgniter4/pull/1588) ([puschie286](https://github.com/puschie286))
- Add Language fallback [\#1587](https://github.com/codeigniter4/CodeIgniter4/pull/1587) ([natanfelles](https://github.com/natanfelles))
- Fix model namespace in tutorial [\#1586](https://github.com/codeigniter4/CodeIgniter4/pull/1586) ([jim-parry](https://github.com/jim-parry))
- Type hint MigrationRunner methods  [\#1585](https://github.com/codeigniter4/CodeIgniter4/pull/1585) ([natanfelles](https://github.com/natanfelles))
- Fix changelog index & common functions UG indent [\#1582](https://github.com/codeigniter4/CodeIgniter4/pull/1582) ([jim-parry](https://github.com/jim-parry))
- ContentSecurityPolicy testing & enhancement [\#1581](https://github.com/codeigniter4/CodeIgniter4/pull/1581) ([jim-parry](https://github.com/jim-parry))
- Use Absolute Paths [\#1579](https://github.com/codeigniter4/CodeIgniter4/pull/1579) ([natanfelles](https://github.com/natanfelles))
- Testing13/http [\#1576](https://github.com/codeigniter4/CodeIgniter4/pull/1576) ([jim-parry](https://github.com/jim-parry))
- Adds ?integer, ?double, ?string, etc.  cast types :\) [\#1575](https://github.com/codeigniter4/CodeIgniter4/pull/1575) ([nowackipawel](https://github.com/nowackipawel))
- Lessons learned [\#1573](https://github.com/codeigniter4/CodeIgniter4/pull/1573) ([jim-parry](https://github.com/jim-parry))
- Toolbar updates [\#1571](https://github.com/codeigniter4/CodeIgniter4/pull/1571) ([natanfelles](https://github.com/natanfelles))
- Test esc\(\) with different encodings and ignore app-only helpers [\#1569](https://github.com/codeigniter4/CodeIgniter4/pull/1569) ([natanfelles](https://github.com/natanfelles))
- id attribute support added for csrf\_field [\#1563](https://github.com/codeigniter4/CodeIgniter4/pull/1563) ([nowackipawel](https://github.com/nowackipawel))
- Integrates Autoloader and FileLocator [\#1562](https://github.com/codeigniter4/CodeIgniter4/pull/1562) ([natanfelles](https://github.com/natanfelles))
- Update Connection.php [\#1561](https://github.com/codeigniter4/CodeIgniter4/pull/1561) ([nowackipawel](https://github.com/nowackipawel))
- remove \ prefix on use statements [\#1557](https://github.com/codeigniter4/CodeIgniter4/pull/1557) ([samsonasik](https://github.com/samsonasik))
- using protected intead of public modifier for setUp\(\) function in tests [\#1556](https://github.com/codeigniter4/CodeIgniter4/pull/1556) ([samsonasik](https://github.com/samsonasik))
- autoload clean up: remove Psr\Log namespace from composer.json [\#1555](https://github.com/codeigniter4/CodeIgniter4/pull/1555) ([samsonasik](https://github.com/samsonasik))
- remove manual define "system/" directory prefix at ComposerScripts [\#1551](https://github.com/codeigniter4/CodeIgniter4/pull/1551) ([samsonasik](https://github.com/samsonasik))
- allows to set empty html attr [\#1548](https://github.com/codeigniter4/CodeIgniter4/pull/1548) ([nowackipawel](https://github.com/nowackipawel))
- Add Vagrantfile [\#1459](https://github.com/codeigniter4/CodeIgniter4/pull/1459) ([natanfelles](https://github.com/natanfelles))

## [v4.0.0-alpha.3](https://github.com/codeigniter4/CodeIgniter4/tree/v4.0.0-alpha.3) (2018-11-30)

[Full Changelog](https://github.com/codeigniter4/CodeIgniter4/compare/v4.0.0-alpha.2...v4.0.0-alpha.3)

**Implemented enhancements:**

- Events should pass it's arguments by reference [\#1298](https://github.com/codeigniter4/CodeIgniter4/issues/1298)
- Feature request. Small but useful. Entity class. [\#1176](https://github.com/codeigniter4/CodeIgniter4/issues/1176)

**Fixed bugs:**

- \Config\Database::connect returns unexpected result for custom config values [\#1533](https://github.com/codeigniter4/CodeIgniter4/issues/1533)
- Old\(\) - Seems to have an issue with retrieving array values [\#1492](https://github.com/codeigniter4/CodeIgniter4/issues/1492)
- Language is not merging with modules [\#1433](https://github.com/codeigniter4/CodeIgniter4/issues/1433)
- RedirectResponse does not set cookies [\#1393](https://github.com/codeigniter4/CodeIgniter4/issues/1393)
- ThrottleTest intermittent failure [\#1382](https://github.com/codeigniter4/CodeIgniter4/issues/1382)
- Support replacer functions on validations [\#1363](https://github.com/codeigniter4/CodeIgniter4/issues/1363)
- Filter on route group only attach / [\#1247](https://github.com/codeigniter4/CodeIgniter4/issues/1247)
- TypeError in MemcachedHandler::\_\_construct\(\) [\#1204](https://github.com/codeigniter4/CodeIgniter4/issues/1204)
- Required\_without validation rule [\#1007](https://github.com/codeigniter4/CodeIgniter4/issues/1007)
- Division by zero - fresh install [\#979](https://github.com/codeigniter4/CodeIgniter4/issues/979)

**Closed issues:**

- need change file\_exists to is\_file? [\#1543](https://github.com/codeigniter4/CodeIgniter4/issues/1543)
- Docs : some links are broken due to lack of prefix CodeIgniter4 [\#1537](https://github.com/codeigniter4/CodeIgniter4/issues/1537)
- Spelling mistake /wrong function reference in documentation "Handling Business Logic" [\#1535](https://github.com/codeigniter4/CodeIgniter4/issues/1535)
- Sample code in Entity documentation contains useless statement [\#1534](https://github.com/codeigniter4/CodeIgniter4/issues/1534)
- Model events why is there no beforeFind? [\#1527](https://github.com/codeigniter4/CodeIgniter4/issues/1527)
- \[Documentation\] Bug in code example for "Validating $\_POST data" [\#1520](https://github.com/codeigniter4/CodeIgniter4/issues/1520)
- Before filters with sessions break php spark serve [\#1519](https://github.com/codeigniter4/CodeIgniter4/issues/1519)
- Missing documentation for placeholders in validation errors [\#1503](https://github.com/codeigniter4/CodeIgniter4/issues/1503)
- no $baseURL set will be notice "The baseURL value must be set" [\#1476](https://github.com/codeigniter4/CodeIgniter4/issues/1476)
- $field parameter as string at Forge::addField [\#1474](https://github.com/codeigniter4/CodeIgniter4/issues/1474)
- FeatureTestCaseTest still broken [\#1446](https://github.com/codeigniter4/CodeIgniter4/issues/1446)
- Unit test output not captured [\#1435](https://github.com/codeigniter4/CodeIgniter4/issues/1435)
- Response setJSON body \<?php{ problem [\#1430](https://github.com/codeigniter4/CodeIgniter4/issues/1430)
- Migrations.classNotFound [\#1420](https://github.com/codeigniter4/CodeIgniter4/issues/1420)
- Controller helper Form validate\('groupname'\) error with rules group [\#1419](https://github.com/codeigniter4/CodeIgniter4/issues/1419)
- Use the update\(\) in BaseBuilder method [\#1414](https://github.com/codeigniter4/CodeIgniter4/issues/1414)
- Spark error [\#1408](https://github.com/codeigniter4/CodeIgniter4/issues/1408)
- Fix class namespacing in the user guide [\#1401](https://github.com/codeigniter4/CodeIgniter4/issues/1401)
- 404 File not found error when running CodeIgniter on local Apache web server and virtual hosts [\#1391](https://github.com/codeigniter4/CodeIgniter4/issues/1391)
- boolean cast in entity and validation error for required,  in\_list\[0,1\] [\#1372](https://github.com/codeigniter4/CodeIgniter4/issues/1372)
- guessExtension\(\) [\#1367](https://github.com/codeigniter4/CodeIgniter4/issues/1367)
- insert\(\) doesn't return boolean but CodeIgniter\Database\MySQLi\Result [\#1365](https://github.com/codeigniter4/CodeIgniter4/issues/1365)
- Entity json-array casting not functioning as expected [\#1359](https://github.com/codeigniter4/CodeIgniter4/issues/1359)
- An error occurs in View Class [\#1358](https://github.com/codeigniter4/CodeIgniter4/issues/1358)
- URI Routing bug [\#1354](https://github.com/codeigniter4/CodeIgniter4/issues/1354)
- Query binding - long names with funcs [\#1353](https://github.com/codeigniter4/CodeIgniter4/issues/1353)
- Bug report: Important bug in is\_unique with DBGroup  different than defined in $defaultGroup [\#1326](https://github.com/codeigniter4/CodeIgniter4/issues/1326)
-  Environment Configuration file [\#1309](https://github.com/codeigniter4/CodeIgniter4/issues/1309)
- Can't call run\(\) method with params from migrate:latest  [\#1308](https://github.com/codeigniter4/CodeIgniter4/issues/1308)
- TODO ImageMagickHandler needs resizing fixed [\#1261](https://github.com/codeigniter4/CodeIgniter4/issues/1261)
- TODO CURLRequest needs helpers [\#1259](https://github.com/codeigniter4/CodeIgniter4/issues/1259)
- TODO Toolbar needs logging [\#1258](https://github.com/codeigniter4/CodeIgniter4/issues/1258)
- TODO BaseBuilder error handling [\#1250](https://github.com/codeigniter4/CodeIgniter4/issues/1250)
- TODO Database migrations need more flexibility [\#1249](https://github.com/codeigniter4/CodeIgniter4/issues/1249)
- Kint problem, chinese characters [\#1177](https://github.com/codeigniter4/CodeIgniter4/issues/1177)
- CSP + DebugBar [\#1165](https://github.com/codeigniter4/CodeIgniter4/issues/1165)
- The \_remap function is wrong in the development （CI\_DEBUG） [\#1137](https://github.com/codeigniter4/CodeIgniter4/issues/1137)
- Debug Bar Routes params bug [\#1104](https://github.com/codeigniter4/CodeIgniter4/issues/1104)
- Session expire time refresh by xhr/ajax request. [\#1074](https://github.com/codeigniter4/CodeIgniter4/issues/1074)

**Merged pull requests:**

- Serviceinstances [\#1554](https://github.com/codeigniter4/CodeIgniter4/pull/1554) ([lonnieezell](https://github.com/lonnieezell))
- Admin/scripts [\#1553](https://github.com/codeigniter4/CodeIgniter4/pull/1553) ([jim-parry](https://github.com/jim-parry))
- remove commented CLI::newLine\($tempFiles\) at FileLocator class [\#1550](https://github.com/codeigniter4/CodeIgniter4/pull/1550) ([samsonasik](https://github.com/samsonasik))
- use .gitkeep instead of .gitignore in Database/Seeds directory [\#1549](https://github.com/codeigniter4/CodeIgniter4/pull/1549) ([samsonasik](https://github.com/samsonasik))
- Change file exists to is file. [\#1547](https://github.com/codeigniter4/CodeIgniter4/pull/1547) ([ytetsuro](https://github.com/ytetsuro))
- add extension\_loaded\('imagick'\) and class\_exists\('Imagick'\) check at ImageMagickHandler::\_\_construct [\#1546](https://github.com/codeigniter4/CodeIgniter4/pull/1546) ([samsonasik](https://github.com/samsonasik))
- Update validation class User Guide [\#1540](https://github.com/codeigniter4/CodeIgniter4/pull/1540) ([bangbangda](https://github.com/bangbangda))
- ext-json in composer.json [\#1536](https://github.com/codeigniter4/CodeIgniter4/pull/1536) ([nowackipawel](https://github.com/nowackipawel))
- database performance improvement : use foreach\(\) when possible [\#1530](https://github.com/codeigniter4/CodeIgniter4/pull/1530) ([samsonasik](https://github.com/samsonasik))
- remove mb\_\* \(mb string usage\) in CreditCardRules [\#1529](https://github.com/codeigniter4/CodeIgniter4/pull/1529) ([samsonasik](https://github.com/samsonasik))
- remove unneeded try {} catch {} on @fopen at Cache FileHandler::writeFile\(\) [\#1525](https://github.com/codeigniter4/CodeIgniter4/pull/1525) ([samsonasik](https://github.com/samsonasik))
- Test routes resource with 'websafe' option [\#1524](https://github.com/codeigniter4/CodeIgniter4/pull/1524) ([natanfelles](https://github.com/natanfelles))
- Check if the matched route regex is filtered [\#1523](https://github.com/codeigniter4/CodeIgniter4/pull/1523) ([natanfelles](https://github.com/natanfelles))
- add property\_exists check on BaseBuilder and BaseConnection for $this-\>$key set value [\#1522](https://github.com/codeigniter4/CodeIgniter4/pull/1522) ([samsonasik](https://github.com/samsonasik))
- .gitignore clean up [\#1521](https://github.com/codeigniter4/CodeIgniter4/pull/1521) ([samsonasik](https://github.com/samsonasik))
- Small typo: changed setCreatedOn to setCreatedAt [\#1518](https://github.com/codeigniter4/CodeIgniter4/pull/1518) ([obozdag](https://github.com/obozdag))
- move .htaccess from per-directory in writable/{directory} to writable/ [\#1517](https://github.com/codeigniter4/CodeIgniter4/pull/1517) ([samsonasik](https://github.com/samsonasik))
- More secure redirection [\#1513](https://github.com/codeigniter4/CodeIgniter4/pull/1513) ([jim-parry](https://github.com/jim-parry))
- remove unused use statements [\#1509](https://github.com/codeigniter4/CodeIgniter4/pull/1509) ([samsonasik](https://github.com/samsonasik))
- remove duplicate strtolower\(\) call in URI::setScheme\(\) call [\#1508](https://github.com/codeigniter4/CodeIgniter4/pull/1508) ([samsonasik](https://github.com/samsonasik))
- Fix multi "empty" string separated by "," marked as valid emails [\#1507](https://github.com/codeigniter4/CodeIgniter4/pull/1507) ([samsonasik](https://github.com/samsonasik))
- Flesh out HTTP/File unit testing [\#1506](https://github.com/codeigniter4/CodeIgniter4/pull/1506) ([jim-parry](https://github.com/jim-parry))
- Do not exit until all Response is completed [\#1505](https://github.com/codeigniter4/CodeIgniter4/pull/1505) ([natanfelles](https://github.com/natanfelles))
- Revert RedirectResponse changes [\#1504](https://github.com/codeigniter4/CodeIgniter4/pull/1504) ([jim-parry](https://github.com/jim-parry))
- Revert to buggy oldInput [\#1502](https://github.com/codeigniter4/CodeIgniter4/pull/1502) ([jim-parry](https://github.com/jim-parry))
- Ignoring errors suppressed by @ [\#1500](https://github.com/codeigniter4/CodeIgniter4/pull/1500) ([samsonasik](https://github.com/samsonasik))
- Fix form\_helper's set\_value writeup [\#1499](https://github.com/codeigniter4/CodeIgniter4/pull/1499) ([jim-parry](https://github.com/jim-parry))
- Add CURLRequest helper methods [\#1498](https://github.com/codeigniter4/CodeIgniter4/pull/1498) ([natanfelles](https://github.com/natanfelles))
- Remove unused RedirectException and add some PHPDocs [\#1497](https://github.com/codeigniter4/CodeIgniter4/pull/1497) ([natanfelles](https://github.com/natanfelles))
- Fix Common::old\(\) [\#1496](https://github.com/codeigniter4/CodeIgniter4/pull/1496) ([jim-parry](https://github.com/jim-parry))
- Add URI segment test [\#1495](https://github.com/codeigniter4/CodeIgniter4/pull/1495) ([natanfelles](https://github.com/natanfelles))
- Method naming [\#1494](https://github.com/codeigniter4/CodeIgniter4/pull/1494) ([ghost](https://github.com/ghost))
- Error logging [\#1491](https://github.com/codeigniter4/CodeIgniter4/pull/1491) ([jim-parry](https://github.com/jim-parry))
- Changelog\(s\) restructure [\#1490](https://github.com/codeigniter4/CodeIgniter4/pull/1490) ([jim-parry](https://github.com/jim-parry))
- Add CLI::strlen\(\) [\#1489](https://github.com/codeigniter4/CodeIgniter4/pull/1489) ([natanfelles](https://github.com/natanfelles))
- Load Language strings from other locations [\#1488](https://github.com/codeigniter4/CodeIgniter4/pull/1488) ([natanfelles](https://github.com/natanfelles))
- Test RedirectResponse problem report [\#1486](https://github.com/codeigniter4/CodeIgniter4/pull/1486) ([jim-parry](https://github.com/jim-parry))
- missing slash [\#1484](https://github.com/codeigniter4/CodeIgniter4/pull/1484) ([titounnes](https://github.com/titounnes))
- Small typo in Session\Handlers\BaseHandler.php [\#1483](https://github.com/codeigniter4/CodeIgniter4/pull/1483) ([obozdag](https://github.com/obozdag))
- doc fix: query binding fix in Seeds documentation [\#1482](https://github.com/codeigniter4/CodeIgniter4/pull/1482) ([samsonasik](https://github.com/samsonasik))
- RedisHandler test clean up: remove unneeded 2nd parameter in \_\_construct [\#1481](https://github.com/codeigniter4/CodeIgniter4/pull/1481) ([samsonasik](https://github.com/samsonasik))
- Fix Language Key-File confusion [\#1480](https://github.com/codeigniter4/CodeIgniter4/pull/1480) ([puschie286](https://github.com/puschie286))
- Yet another time test to fix [\#1479](https://github.com/codeigniter4/CodeIgniter4/pull/1479) ([jim-parry](https://github.com/jim-parry))
- Add Response send testing [\#1477](https://github.com/codeigniter4/CodeIgniter4/pull/1477) ([jim-parry](https://github.com/jim-parry))
- Correct phpdocs for Forge::addField\(\) [\#1475](https://github.com/codeigniter4/CodeIgniter4/pull/1475) ([jim-parry](https://github.com/jim-parry))
- Fuzzify another time test [\#1473](https://github.com/codeigniter4/CodeIgniter4/pull/1473) ([jim-parry](https://github.com/jim-parry))
- HTTP\Response cookie testing & missing functionality [\#1472](https://github.com/codeigniter4/CodeIgniter4/pull/1472) ([jim-parry](https://github.com/jim-parry))
- remove unused local variable $result in XMLFormatter::format\(\) [\#1471](https://github.com/codeigniter4/CodeIgniter4/pull/1471) ([samsonasik](https://github.com/samsonasik))
- Allow create table with array field constraints [\#1470](https://github.com/codeigniter4/CodeIgniter4/pull/1470) ([natanfelles](https://github.com/natanfelles))
- use static:: instead of self:: for call protected/public functions as well [\#1469](https://github.com/codeigniter4/CodeIgniter4/pull/1469) ([samsonasik](https://github.com/samsonasik))
- Fix FeatureTestCaseTest output buffer  [\#1468](https://github.com/codeigniter4/CodeIgniter4/pull/1468) ([puschie286](https://github.com/puschie286))
- Provide time testing within tolerance [\#1467](https://github.com/codeigniter4/CodeIgniter4/pull/1467) ([jim-parry](https://github.com/jim-parry))
- Fix phpdocs for BaseBuilder [\#1466](https://github.com/codeigniter4/CodeIgniter4/pull/1466) ([jim-parry](https://github.com/jim-parry))
- use static:: instead of self:: for protected and public properties [\#1465](https://github.com/codeigniter4/CodeIgniter4/pull/1465) ([samsonasik](https://github.com/samsonasik))
- remove unused use statements [\#1464](https://github.com/codeigniter4/CodeIgniter4/pull/1464) ([samsonasik](https://github.com/samsonasik))
- Fix the remaining bcit-ci references [\#1463](https://github.com/codeigniter4/CodeIgniter4/pull/1463) ([jim-parry](https://github.com/jim-parry))
- Typo fix: donload -\> download [\#1461](https://github.com/codeigniter4/CodeIgniter4/pull/1461) ([samsonasik](https://github.com/samsonasik))
- remove unneeded ternary check at HoneyPot::hasContent\(\) [\#1460](https://github.com/codeigniter4/CodeIgniter4/pull/1460) ([samsonasik](https://github.com/samsonasik))
- WIP use $paths-\>systemDirectory in public/index.php  [\#1457](https://github.com/codeigniter4/CodeIgniter4/pull/1457) ([samsonasik](https://github.com/samsonasik))
- Beef up HTTP URI & Response testing [\#1456](https://github.com/codeigniter4/CodeIgniter4/pull/1456) ([jim-parry](https://github.com/jim-parry))
- WIP un-ignore application/Database/Migrations directory from .gitignore [\#1455](https://github.com/codeigniter4/CodeIgniter4/pull/1455) ([samsonasik](https://github.com/samsonasik))
- add missing break; in loop at Email::getEncoding\(\) [\#1454](https://github.com/codeigniter4/CodeIgniter4/pull/1454) ([samsonasik](https://github.com/samsonasik))
- BugFix  if there extension has only one mime type \(string\) [\#1453](https://github.com/codeigniter4/CodeIgniter4/pull/1453) ([nowackipawel](https://github.com/nowackipawel))
- remove unneeded $session-\>start\(\); check on RedirectResponse::ensureSession\(\) [\#1451](https://github.com/codeigniter4/CodeIgniter4/pull/1451) ([samsonasik](https://github.com/samsonasik))
- phpcbf: fix all at once [\#1450](https://github.com/codeigniter4/CodeIgniter4/pull/1450) ([natanfelles](https://github.com/natanfelles))
- Simplify how to get indexData from mysql/mariadb [\#1449](https://github.com/codeigniter4/CodeIgniter4/pull/1449) ([natanfelles](https://github.com/natanfelles))
- documentation: add missing application structures: Database, Filters, ThirdParty directory [\#1448](https://github.com/codeigniter4/CodeIgniter4/pull/1448) ([samsonasik](https://github.com/samsonasik))
- add missing break; on loop cards to get card info at CreditCardRules::valid\_cc\_number\(\) [\#1447](https://github.com/codeigniter4/CodeIgniter4/pull/1447) ([samsonasik](https://github.com/samsonasik))
- using existing is\_cli\(\) function in HTTP\IncomingRequest::isCLI\(\) [\#1445](https://github.com/codeigniter4/CodeIgniter4/pull/1445) ([samsonasik](https://github.com/samsonasik))
- Dox for reorganized repo admin \(4of4\) [\#1444](https://github.com/codeigniter4/CodeIgniter4/pull/1444) ([jim-parry](https://github.com/jim-parry))
- Fixes \#1435 : unit test output not captured [\#1443](https://github.com/codeigniter4/CodeIgniter4/pull/1443) ([samsonasik](https://github.com/samsonasik))
- remove form view in application/View/ and form helper usage in create new items tutorial [\#1442](https://github.com/codeigniter4/CodeIgniter4/pull/1442) ([samsonasik](https://github.com/samsonasik))
- Access to model's last inserted ID [\#1440](https://github.com/codeigniter4/CodeIgniter4/pull/1440) ([nowackipawel](https://github.com/nowackipawel))
- Tailor the last few repo org names \(3of4\) [\#1438](https://github.com/codeigniter4/CodeIgniter4/pull/1438) ([jim-parry](https://github.com/jim-parry))
- Replace repo org name in MOST php docs \(2 of 4\) [\#1437](https://github.com/codeigniter4/CodeIgniter4/pull/1437) ([jim-parry](https://github.com/jim-parry))
- Change github organization name in docs \(1of4\) [\#1436](https://github.com/codeigniter4/CodeIgniter4/pull/1436) ([jim-parry](https://github.com/jim-parry))
- Use mb\_strlen to get length of columns [\#1432](https://github.com/codeigniter4/CodeIgniter4/pull/1432) ([natanfelles](https://github.com/natanfelles))
- can't call run\(\) method with params from commands migrations. [\#1431](https://github.com/codeigniter4/CodeIgniter4/pull/1431) ([bangbangda](https://github.com/bangbangda))
- performance improvement in Database\BaseResult to use truthy check instead of count\($var\) when possible [\#1426](https://github.com/codeigniter4/CodeIgniter4/pull/1426) ([samsonasik](https://github.com/samsonasik))
- Ensure FileHandlerTest uses MockFileHandler [\#1425](https://github.com/codeigniter4/CodeIgniter4/pull/1425) ([jim-parry](https://github.com/jim-parry))
- Fix FileMovingTest leaving cruft [\#1424](https://github.com/codeigniter4/CodeIgniter4/pull/1424) ([jim-parry](https://github.com/jim-parry))
-  Fix Controller use validate bug Fixes \#1419 [\#1423](https://github.com/codeigniter4/CodeIgniter4/pull/1423) ([bangbangda](https://github.com/bangbangda))
- normalize composer.json [\#1418](https://github.com/codeigniter4/CodeIgniter4/pull/1418) ([samsonasik](https://github.com/samsonasik))
- add php 7.3 to travis config [\#1394](https://github.com/codeigniter4/CodeIgniter4/pull/1394) ([samsonasik](https://github.com/samsonasik))
- Add Header Link Pagination [\#622](https://github.com/codeigniter4/CodeIgniter4/pull/622) ([natanfelles](https://github.com/natanfelles))

## [v4.0.0-alpha.2](https://github.com/codeigniter4/CodeIgniter4/tree/v4.0.0-alpha.2) (2018-10-26)

[Full Changelog](https://github.com/codeigniter4/CodeIgniter4/compare/v4.0.0-alpha.1...v4.0.0-alpha.2)

**Implemented enhancements:**

-  Filter in the router [\#1315](https://github.com/codeigniter4/CodeIgniter4/issues/1315)
- Making Views path changeable [\#1296](https://github.com/codeigniter4/CodeIgniter4/issues/1296)

**Fixed bugs:**

- Error in user guide for session config [\#1330](https://github.com/codeigniter4/CodeIgniter4/issues/1330)
- Route in the News Tutorial Routes are ERROR all over. [\#1240](https://github.com/codeigniter4/CodeIgniter4/issues/1240)
- Time testing in travis-ci wonky [\#1229](https://github.com/codeigniter4/CodeIgniter4/issues/1229)

**Closed issues:**

- CLI CommandRunner is trying to instantiate abstract classes [\#1349](https://github.com/codeigniter4/CodeIgniter4/issues/1349)
- redirect\(\)-\> problem [\#1346](https://github.com/codeigniter4/CodeIgniter4/issues/1346)
- Question new form validation rule [\#1332](https://github.com/codeigniter4/CodeIgniter4/issues/1332)
- Download Response Not working [\#1331](https://github.com/codeigniter4/CodeIgniter4/issues/1331)
- Incorrect Error Message ? [\#1328](https://github.com/codeigniter4/CodeIgniter4/issues/1328)
- Wrong variable reference [\#1324](https://github.com/codeigniter4/CodeIgniter4/issues/1324)
- Model Pagination: Problem with total rows [\#1318](https://github.com/codeigniter4/CodeIgniter4/issues/1318)
- Should display an exception when minimum PHP version not met. [\#1307](https://github.com/codeigniter4/CodeIgniter4/issues/1307)
- News Tutorial Error on form submit [\#1301](https://github.com/codeigniter4/CodeIgniter4/issues/1301)
- Small Typo Correction [\#1299](https://github.com/codeigniter4/CodeIgniter4/issues/1299)
- Making config variable global as CI3 [\#1297](https://github.com/codeigniter4/CodeIgniter4/issues/1297)
- Config files aren't discovered automatically when using the config\(\) function. [\#1293](https://github.com/codeigniter4/CodeIgniter4/issues/1293)
- News Tutorial post riute still not working [\#1292](https://github.com/codeigniter4/CodeIgniter4/issues/1292)
- Form Validation [\#1290](https://github.com/codeigniter4/CodeIgniter4/issues/1290)
- News Tutorial Routes [\#1288](https://github.com/codeigniter4/CodeIgniter4/issues/1288)
- I want to separate responsibility of Cast from Entity. [\#1287](https://github.com/codeigniter4/CodeIgniter4/issues/1287)
- error 404 in routing with controller in subdirectories [\#1276](https://github.com/codeigniter4/CodeIgniter4/issues/1276)
- TODO Extending helpers [\#1264](https://github.com/codeigniter4/CodeIgniter4/issues/1264)
- TODO MockResponse needs cookies solution [\#1263](https://github.com/codeigniter4/CodeIgniter4/issues/1263)
- TODO url\_helper needs fixing [\#1260](https://github.com/codeigniter4/CodeIgniter4/issues/1260)
- TODO FileLocator better path checking [\#1252](https://github.com/codeigniter4/CodeIgniter4/issues/1252)
- TODO FileLocator filename sanitizing [\#1251](https://github.com/codeigniter4/CodeIgniter4/issues/1251)
- BUG in form\_hidden with associative array  [\#1244](https://github.com/codeigniter4/CodeIgniter4/issues/1244)
- Save entity after selected find results in null data [\#1234](https://github.com/codeigniter4/CodeIgniter4/issues/1234)
- System/Database/Database.php::loadForge returns Connection when using custom DBDriver [\#1225](https://github.com/codeigniter4/CodeIgniter4/issues/1225)

**Merged pull requests:**

- Add timing assertion to CIUnitTestCase [\#1361](https://github.com/codeigniter4/CodeIgniter4/pull/1361) ([jim-parry](https://github.com/jim-parry))
- Testing/commands [\#1356](https://github.com/codeigniter4/CodeIgniter4/pull/1356) ([jim-parry](https://github.com/jim-parry))
- Handle duplicate HTTP verb and generic rules properly [\#1355](https://github.com/codeigniter4/CodeIgniter4/pull/1355) ([jim-parry](https://github.com/jim-parry))
- Refresh changelog [\#1352](https://github.com/codeigniter4/CodeIgniter4/pull/1352) ([jim-parry](https://github.com/jim-parry))
- Checks if class is instantiable and is a command [\#1350](https://github.com/codeigniter4/CodeIgniter4/pull/1350) ([natanfelles](https://github.com/natanfelles))
- Fix sphinx formatting in sessions [\#1348](https://github.com/codeigniter4/CodeIgniter4/pull/1348) ([jim-parry](https://github.com/jim-parry))
- Fix sphinx formatting in sessions [\#1347](https://github.com/codeigniter4/CodeIgniter4/pull/1347) ([jim-parry](https://github.com/jim-parry))
- Toolbar Styles [\#1342](https://github.com/codeigniter4/CodeIgniter4/pull/1342) ([lonnieezell](https://github.com/lonnieezell))
- Make viewpath configurable in Paths.php. Fixes \#1296 [\#1341](https://github.com/codeigniter4/CodeIgniter4/pull/1341) ([lonnieezell](https://github.com/lonnieezell))
- Update docs for downloads to reflect the need to return it. Fixes \#1331 [\#1340](https://github.com/codeigniter4/CodeIgniter4/pull/1340) ([lonnieezell](https://github.com/lonnieezell))
- Fix error where Forge class might not be returned. Fixes \#1225 [\#1339](https://github.com/codeigniter4/CodeIgniter4/pull/1339) ([lonnieezell](https://github.com/lonnieezell))
- Filter in the router Fixes \#1315 [\#1337](https://github.com/codeigniter4/CodeIgniter4/pull/1337) ([bangbangda](https://github.com/bangbangda))
- Revert alpha.2 [\#1336](https://github.com/codeigniter4/CodeIgniter4/pull/1336) ([jim-parry](https://github.com/jim-parry))
- Proposed changelog for alpha.2 [\#1334](https://github.com/codeigniter4/CodeIgniter4/pull/1334) ([jim-parry](https://github.com/jim-parry))
- Error in user guide for session config. Fixes \#1330 [\#1333](https://github.com/codeigniter4/CodeIgniter4/pull/1333) ([bangbangda](https://github.com/bangbangda))
- Tweaks [\#1329](https://github.com/codeigniter4/CodeIgniter4/pull/1329) ([lonnieezell](https://github.com/lonnieezell))
- FIX   form\_hidden and form\_open - value escaping as is in form\_input. [\#1327](https://github.com/codeigniter4/CodeIgniter4/pull/1327) ([nowackipawel](https://github.com/nowackipawel))
- Fix doc error : show\_404\(\) doesn't exist any more [\#1323](https://github.com/codeigniter4/CodeIgniter4/pull/1323) ([bvrignaud](https://github.com/bvrignaud))
- Added missing xml\_helper UG page [\#1321](https://github.com/codeigniter4/CodeIgniter4/pull/1321) ([jim-parry](https://github.com/jim-parry))
- Testing/entity [\#1319](https://github.com/codeigniter4/CodeIgniter4/pull/1319) ([jim-parry](https://github.com/jim-parry))
- Refactor TimeTest [\#1316](https://github.com/codeigniter4/CodeIgniter4/pull/1316) ([jim-parry](https://github.com/jim-parry))
- Fix & expand Honeypot & its tests [\#1314](https://github.com/codeigniter4/CodeIgniter4/pull/1314) ([jim-parry](https://github.com/jim-parry))
- Clean exception [\#1313](https://github.com/codeigniter4/CodeIgniter4/pull/1313) ([lonnieezell](https://github.com/lonnieezell))
- Add headerEmited \(or not\) assertions to CIUnitTestCase [\#1312](https://github.com/codeigniter4/CodeIgniter4/pull/1312) ([jim-parry](https://github.com/jim-parry))
- Entities store an original stack of values to compare against so we d… [\#1311](https://github.com/codeigniter4/CodeIgniter4/pull/1311) ([lonnieezell](https://github.com/lonnieezell))
- Testing3/http [\#1306](https://github.com/codeigniter4/CodeIgniter4/pull/1306) ([jim-parry](https://github.com/jim-parry))
- Change chdir\('public'\) to chdir\($public\) [\#1305](https://github.com/codeigniter4/CodeIgniter4/pull/1305) ([titounnes](https://github.com/titounnes))
- Refactor script name stripping in parseRequestURI\(\) [\#1304](https://github.com/codeigniter4/CodeIgniter4/pull/1304) ([jim-parry](https://github.com/jim-parry))
- Testing/http [\#1303](https://github.com/codeigniter4/CodeIgniter4/pull/1303) ([jim-parry](https://github.com/jim-parry))
- Exception：No Formatter defined for mime type '' [\#1302](https://github.com/codeigniter4/CodeIgniter4/pull/1302) ([bangbangda](https://github.com/bangbangda))
- Allow redirect with Query Vars from the current request. [\#1300](https://github.com/codeigniter4/CodeIgniter4/pull/1300) ([lonnieezell](https://github.com/lonnieezell))
- Fix grammar in front controller comment. [\#1295](https://github.com/codeigniter4/CodeIgniter4/pull/1295) ([mdwheele](https://github.com/mdwheele))
- Updated final tutorial page. Fixes \#1292 [\#1294](https://github.com/codeigniter4/CodeIgniter4/pull/1294) ([lonnieezell](https://github.com/lonnieezell))
- Allows extending of helpers. Fixes \#1264 [\#1291](https://github.com/codeigniter4/CodeIgniter4/pull/1291) ([lonnieezell](https://github.com/lonnieezell))
- Cookies [\#1286](https://github.com/codeigniter4/CodeIgniter4/pull/1286) ([lonnieezell](https://github.com/lonnieezell))
- Ensure current HTTP verb routes are matched prior to any \* matched ro… [\#1285](https://github.com/codeigniter4/CodeIgniter4/pull/1285) ([lonnieezell](https://github.com/lonnieezell))
- Entities [\#1283](https://github.com/codeigniter4/CodeIgniter4/pull/1283) ([lonnieezell](https://github.com/lonnieezell))
- system/Test/FeatureTestCase::setupRequest\(\), minor fixes phpdoc block… [\#1282](https://github.com/codeigniter4/CodeIgniter4/pull/1282) ([fmertins](https://github.com/fmertins))
- Tut [\#1281](https://github.com/codeigniter4/CodeIgniter4/pull/1281) ([lonnieezell](https://github.com/lonnieezell))
- Add contributing reference to user guide [\#1280](https://github.com/codeigniter4/CodeIgniter4/pull/1280) ([jim-parry](https://github.com/jim-parry))
- Fix/timing [\#1273](https://github.com/codeigniter4/CodeIgniter4/pull/1273) ([jim-parry](https://github.com/jim-parry))
- Fix undefined variable "heading" in cli 404 [\#1272](https://github.com/codeigniter4/CodeIgniter4/pull/1272) ([samsonasik](https://github.com/samsonasik))
- remove inexistent "CodeIgniter\Loader" from AutoloadConfig::classmap [\#1271](https://github.com/codeigniter4/CodeIgniter4/pull/1271) ([samsonasik](https://github.com/samsonasik))
- Release notes & process [\#1269](https://github.com/codeigniter4/CodeIgniter4/pull/1269) ([jim-parry](https://github.com/jim-parry))
- Fix \#1244 \(form\_hidden declaration\) [\#1245](https://github.com/codeigniter4/CodeIgniter4/pull/1245) ([bvrignaud](https://github.com/bvrignaud))
- 【Unsolicited PR】I changed the download method to testable. [\#1239](https://github.com/codeigniter4/CodeIgniter4/pull/1239) ([ytetsuro](https://github.com/ytetsuro))
- Optional parameter for resetSelect\(\) call in Builder's countAll\(\); [\#1217](https://github.com/codeigniter4/CodeIgniter4/pull/1217) ([nowackipawel](https://github.com/nowackipawel))
- Fix undefined function xml\_convert at Database\BaseUtils [\#1209](https://github.com/codeigniter4/CodeIgniter4/pull/1209) ([samsonasik](https://github.com/samsonasik))

## [v4.0.0-alpha.1](https://github.com/codeigniter4/CodeIgniter4/tree/v4.0.0-alpha.1) (2018-09-29)

[Full Changelog](https://github.com/codeigniter4/CodeIgniter4/compare/0d92381e74730331626b49e6f259d9073727c4f1...v4.0.0-alpha.1)

**Implemented enhancements:**

- Parser plugins should be allowed to have non-paired directives [\#547](https://github.com/codeigniter4/CodeIgniter4/issues/547)
- Modify View Parser to support quoted phrases [\#537](https://github.com/codeigniter4/CodeIgniter4/issues/537)
- Move Formatting functionality from the API namespace to it's own namespace [\#451](https://github.com/codeigniter4/CodeIgniter4/issues/451)
- Add method spoofing for forms. [\#432](https://github.com/codeigniter4/CodeIgniter4/issues/432)
- Add overwrite option to UploadedFile-\>move\(\) [\#275](https://github.com/codeigniter4/CodeIgniter4/issues/275)
- Add Download method to Response object. [\#208](https://github.com/codeigniter4/CodeIgniter4/issues/208)
- Cache/Session fallback to predis/predis composer package if phpredis not available [\#192](https://github.com/codeigniter4/CodeIgniter4/issues/192)
- View: saveData [\#181](https://github.com/codeigniter4/CodeIgniter4/issues/181)
- url\_helper implementation [\#157](https://github.com/codeigniter4/CodeIgniter4/issues/157)
- Add true 'prepare' functionality to Queries [\#131](https://github.com/codeigniter4/CodeIgniter4/issues/131)
- Add :hash placeholder for URI parameters [\#130](https://github.com/codeigniter4/CodeIgniter4/issues/130)
- \[URI\] Provide ability for query vars manipulation [\#119](https://github.com/codeigniter4/CodeIgniter4/issues/119)
- \[FilesCollection\] Allow getFiles with dot syntax [\#112](https://github.com/codeigniter4/CodeIgniter4/issues/112)
- Database to fire events [\#105](https://github.com/codeigniter4/CodeIgniter4/issues/105)
- New Toolbar Collector for Events [\#84](https://github.com/codeigniter4/CodeIgniter4/issues/84)
- Debug Toolbar Enhancements [\#83](https://github.com/codeigniter4/CodeIgniter4/issues/83)
- Add indexing functions to Forge [\#65](https://github.com/codeigniter4/CodeIgniter4/issues/65)
- Refactor DB Backup to stream to file instead of holding in memory [\#64](https://github.com/codeigniter4/CodeIgniter4/issues/64)
- Add support for foreign keys to the Forge [\#63](https://github.com/codeigniter4/CodeIgniter4/issues/63)
- Headers must support multiple headers with same name [\#16](https://github.com/codeigniter4/CodeIgniter4/issues/16)
- Add 'secure' option for Routes [\#10](https://github.com/codeigniter4/CodeIgniter4/issues/10)
- \[Test Helper\] assertLogged [\#9](https://github.com/codeigniter4/CodeIgniter4/issues/9)
- Additional Logger Enhancements [\#8](https://github.com/codeigniter4/CodeIgniter4/issues/8)
- Implement Content Secure Policy [\#6](https://github.com/codeigniter4/CodeIgniter4/issues/6)
- Replace our escaper with Zend Escaper [\#5](https://github.com/codeigniter4/CodeIgniter4/issues/5)
- Redirect Security [\#4](https://github.com/codeigniter4/CodeIgniter4/issues/4)
- Reverse Routing [\#3](https://github.com/codeigniter4/CodeIgniter4/issues/3)

**Fixed bugs:**

- Testing output buffer not closed [\#1230](https://github.com/codeigniter4/CodeIgniter4/issues/1230)
- XML formater xmltoarray indexed array incorrect [\#577](https://github.com/codeigniter4/CodeIgniter4/issues/577)
- stringify\_attributes method MUST escape the values [\#282](https://github.com/codeigniter4/CodeIgniter4/issues/282)
- base\_url\(\) function doesn't work properly when it used on page with uri segments [\#240](https://github.com/codeigniter4/CodeIgniter4/issues/240)
- mysqli update bug [\#229](https://github.com/codeigniter4/CodeIgniter4/issues/229)
- database update  [\#201](https://github.com/codeigniter4/CodeIgniter4/issues/201)
- Paginating Multiple Results - user Guides [\#196](https://github.com/codeigniter4/CodeIgniter4/issues/196)
- 'Filters' Bug [\#188](https://github.com/codeigniter4/CodeIgniter4/issues/188)
- POST and debugbar [\#172](https://github.com/codeigniter4/CodeIgniter4/issues/172)
- url\_helper functions don't correctly apply $baseURL [\#155](https://github.com/codeigniter4/CodeIgniter4/issues/155)
- Routes ending in '/' redirect oddly  [\#147](https://github.com/codeigniter4/CodeIgniter4/issues/147)
- Error when trying access URI with Global Function's name [\#136](https://github.com/codeigniter4/CodeIgniter4/issues/136)
- CLI problem with progress complete's message [\#135](https://github.com/codeigniter4/CodeIgniter4/issues/135)

**Closed issues:**

- validation error [\#1214](https://github.com/codeigniter4/CodeIgniter4/issues/1214)
- How to use Controller own constructer? [\#1208](https://github.com/codeigniter4/CodeIgniter4/issues/1208)
- autoload psr4 is not right. [\#1205](https://github.com/codeigniter4/CodeIgniter4/issues/1205)
- multiple rules in validate\(\) for File Upload not working [\#1201](https://github.com/codeigniter4/CodeIgniter4/issues/1201)
- can't use the same model to update, delete and insert record [\#1193](https://github.com/codeigniter4/CodeIgniter4/issues/1193)
- $myModel-\>find\(string "value-of-my-primary-key"\) [\#1188](https://github.com/codeigniter4/CodeIgniter4/issues/1188)
- Undefined variable: \_SESSION in command php spark [\#1183](https://github.com/codeigniter4/CodeIgniter4/issues/1183)
- Typographical error [\#1179](https://github.com/codeigniter4/CodeIgniter4/issues/1179)
- CSP + .kint   d\(foo\) [\#1174](https://github.com/codeigniter4/CodeIgniter4/issues/1174)
- DebugBar -\> Server Error 500 [\#1170](https://github.com/codeigniter4/CodeIgniter4/issues/1170)
- NULL in select is escaped \(mysqli\_sql\_exception\) [\#1169](https://github.com/codeigniter4/CodeIgniter4/issues/1169)
- Routing for "cli" actions. [\#1166](https://github.com/codeigniter4/CodeIgniter4/issues/1166)
- Create Auto-discovery system [\#1161](https://github.com/codeigniter4/CodeIgniter4/issues/1161)
- Routing wrong default value [\#1139](https://github.com/codeigniter4/CodeIgniter4/issues/1139)
- $session-\>push gets wrong [\#1136](https://github.com/codeigniter4/CodeIgniter4/issues/1136)
- Toolbar Oldest files delete bug [\#1135](https://github.com/codeigniter4/CodeIgniter4/issues/1135)
- redirect helper : redirectResponse is ignored [\#1127](https://github.com/codeigniter4/CodeIgniter4/issues/1127)
- redirect to route ignore baseurl [\#1126](https://github.com/codeigniter4/CodeIgniter4/issues/1126)
- redirect-\>route wrong docu or default values [\#1125](https://github.com/codeigniter4/CodeIgniter4/issues/1125)
- \#1109 breaks route setup/uri parsing [\#1114](https://github.com/codeigniter4/CodeIgniter4/issues/1114)
- \[TESTS\] Session tests for php 7.2 [\#1106](https://github.com/codeigniter4/CodeIgniter4/issues/1106)
- set\_cookie - not working [\#1103](https://github.com/codeigniter4/CodeIgniter4/issues/1103)
- safe\_mailto - not working  [\#1102](https://github.com/codeigniter4/CodeIgniter4/issues/1102)
- CLI tool sorting wrong [\#1099](https://github.com/codeigniter4/CodeIgniter4/issues/1099)
- Issues with redirect [\#1098](https://github.com/codeigniter4/CodeIgniter4/issues/1098)
- Can't use validate with regex\_match\[\] [\#1084](https://github.com/codeigniter4/CodeIgniter4/issues/1084)
- problem with redirect\(\) withInput\(\) when validation [\#1081](https://github.com/codeigniter4/CodeIgniter4/issues/1081)
- Redis Handler Fails [\#1079](https://github.com/codeigniter4/CodeIgniter4/issues/1079)
- about cache path [\#1078](https://github.com/codeigniter4/CodeIgniter4/issues/1078)
- validation  error [\#1077](https://github.com/codeigniter4/CodeIgniter4/issues/1077)
- \#Request. Features for REST server. [\#1076](https://github.com/codeigniter4/CodeIgniter4/issues/1076)
- Database Migrations [\#1075](https://github.com/codeigniter4/CodeIgniter4/issues/1075)
- Codeigniter/Model - Select Database Table Fields [\#1072](https://github.com/codeigniter4/CodeIgniter4/issues/1072)
- New Config helper [\#1071](https://github.com/codeigniter4/CodeIgniter4/issues/1071)
- HTTP\ResponseTest Language Problem [\#1069](https://github.com/codeigniter4/CodeIgniter4/issues/1069)
- CLI Error [\#1068](https://github.com/codeigniter4/CodeIgniter4/issues/1068)
- Entity \_options dates [\#1061](https://github.com/codeigniter4/CodeIgniter4/issues/1061)
- class Locale not found when using I18n/Time on xampp localhost [\#1059](https://github.com/codeigniter4/CodeIgniter4/issues/1059)
- Cookie not working [\#1057](https://github.com/codeigniter4/CodeIgniter4/issues/1057)
- Where is class 'MessageFormatter' [\#1054](https://github.com/codeigniter4/CodeIgniter4/issues/1054)
- Is CI 4 ready for production, please? [\#1051](https://github.com/codeigniter4/CodeIgniter4/issues/1051)
- Router 404 [\#1050](https://github.com/codeigniter4/CodeIgniter4/issues/1050)
- Toolbar - memory usage [\#1049](https://github.com/codeigniter4/CodeIgniter4/issues/1049)
- Session saving [\#1045](https://github.com/codeigniter4/CodeIgniter4/issues/1045)
- $Email-\>initialize\($config\) not work! [\#1042](https://github.com/codeigniter4/CodeIgniter4/issues/1042)
- Class '\CodeIgniter\Database\postgre\Connection' not found [\#1038](https://github.com/codeigniter4/CodeIgniter4/issues/1038)
- Tutorial controller Pages and file\_exists\(\) case sensitivity [\#1030](https://github.com/codeigniter4/CodeIgniter4/issues/1030)
- options base\_uri not being based from curlrequest client instantiation [\#1029](https://github.com/codeigniter4/CodeIgniter4/issues/1029)
- Missing method in db result [\#1022](https://github.com/codeigniter4/CodeIgniter4/issues/1022)
- Email Config $fromEmail not work [\#1021](https://github.com/codeigniter4/CodeIgniter4/issues/1021)
- Class 'CodeIgniter\PageNotFoundException' not found [\#1016](https://github.com/codeigniter4/CodeIgniter4/issues/1016)
- Redirect glitch [\#1013](https://github.com/codeigniter4/CodeIgniter4/issues/1013)
- CSRF Error [\#1012](https://github.com/codeigniter4/CodeIgniter4/issues/1012)
- Php serv CLI stopped working [\#1006](https://github.com/codeigniter4/CodeIgniter4/issues/1006)
- Unit testing broken in travis-ci [\#1003](https://github.com/codeigniter4/CodeIgniter4/issues/1003)
- empty php\_errors.log file [\#1001](https://github.com/codeigniter4/CodeIgniter4/issues/1001)
- \[Help\] setVar\('body', $view, 'raw'\) [\#1000](https://github.com/codeigniter4/CodeIgniter4/issues/1000)
- It's blank page and set $baseURL [\#999](https://github.com/codeigniter4/CodeIgniter4/issues/999)
- Unable to use another controller'method in one controller？ [\#997](https://github.com/codeigniter4/CodeIgniter4/issues/997)
- CodeIgniter\Session\Handlers\FileHandler Class and writable\session Directory not found while using .env [\#994](https://github.com/codeigniter4/CodeIgniter4/issues/994)
- route\_to\(\) function not work if  greater than 3 parameters. [\#992](https://github.com/codeigniter4/CodeIgniter4/issues/992)
- Redirect Back [\#991](https://github.com/codeigniter4/CodeIgniter4/issues/991)
- helper method should accept more than one filename.... [\#987](https://github.com/codeigniter4/CodeIgniter4/issues/987)
- New Feature Request - Sub queries using query builder class [\#985](https://github.com/codeigniter4/CodeIgniter4/issues/985)
- MySQL join / missing value for field which was used in join. [\#983](https://github.com/codeigniter4/CodeIgniter4/issues/983)
- Documentation - Session Library - session\(\)-\>start\(\) [\#982](https://github.com/codeigniter4/CodeIgniter4/issues/982)
- Cache with handler file woking incorrect on windown os [\#978](https://github.com/codeigniter4/CodeIgniter4/issues/978)
- Formatter for: "content-type: application/json" [\#977](https://github.com/codeigniter4/CodeIgniter4/issues/977)
- route\_to function working incorrect [\#975](https://github.com/codeigniter4/CodeIgniter4/issues/975)
- Getters and Setters in the Model [\#974](https://github.com/codeigniter4/CodeIgniter4/issues/974)
- About Replacing Core Classes! [\#973](https://github.com/codeigniter4/CodeIgniter4/issues/973)
- CodeIgniter 4 should use PSR-2 [\#972](https://github.com/codeigniter4/CodeIgniter4/issues/972)
- Model Validations and  Insert/Update Batchs [\#967](https://github.com/codeigniter4/CodeIgniter4/issues/967)
- function old\(\) not allow input name type array [\#966](https://github.com/codeigniter4/CodeIgniter4/issues/966)
- test [\#963](https://github.com/codeigniter4/CodeIgniter4/issues/963)
- namespace error in centos7 [\#959](https://github.com/codeigniter4/CodeIgniter4/issues/959)
- Session cannot be saved [\#958](https://github.com/codeigniter4/CodeIgniter4/issues/958)
- Model calling Query Builder replace\(\) [\#957](https://github.com/codeigniter4/CodeIgniter4/issues/957)
- gzuncompress\(\): data error [\#956](https://github.com/codeigniter4/CodeIgniter4/issues/956)
- Additional space in a parameter which is a string  \(mysql\)  [\#955](https://github.com/codeigniter4/CodeIgniter4/issues/955)
- Pager with search queries [\#950](https://github.com/codeigniter4/CodeIgniter4/issues/950)
- IncomingRequest getUserAgent problem only object [\#948](https://github.com/codeigniter4/CodeIgniter4/issues/948)
- url\_helper current\_url https fix. [\#947](https://github.com/codeigniter4/CodeIgniter4/issues/947)
- Validation "matches" rule is not getting the label from the corresponding field [\#946](https://github.com/codeigniter4/CodeIgniter4/issues/946)
- no luck setting up user\_guide [\#944](https://github.com/codeigniter4/CodeIgniter4/issues/944)
- mysqli\_sql\_exception: No such file or directory [\#943](https://github.com/codeigniter4/CodeIgniter4/issues/943)
- Major Security Flaw - Database Credentials get leaked.  [\#935](https://github.com/codeigniter4/CodeIgniter4/issues/935)
- Debug Toolbar is not loaded when CSP is enabled  [\#934](https://github.com/codeigniter4/CodeIgniter4/issues/934)
- This is a problem with frequent operations [\#933](https://github.com/codeigniter4/CodeIgniter4/issues/933)
- Redirects not working [\#931](https://github.com/codeigniter4/CodeIgniter4/issues/931)
- Model fillPlaceholders\(\) rule reference [\#930](https://github.com/codeigniter4/CodeIgniter4/issues/930)
- session in not working on database [\#925](https://github.com/codeigniter4/CodeIgniter4/issues/925)
- Twig [\#919](https://github.com/codeigniter4/CodeIgniter4/issues/919)
- setDefaultNamespace Sub directories [\#917](https://github.com/codeigniter4/CodeIgniter4/issues/917)
- Load language issue [\#913](https://github.com/codeigniter4/CodeIgniter4/issues/913)
- Find a bug ，CSRFVerify [\#912](https://github.com/codeigniter4/CodeIgniter4/issues/912)
- validation form\_error\(\) [\#911](https://github.com/codeigniter4/CodeIgniter4/issues/911)
- About Filters matching rules [\#908](https://github.com/codeigniter4/CodeIgniter4/issues/908)
- Prepared Query Update problem [\#904](https://github.com/codeigniter4/CodeIgniter4/issues/904)
- Model::first\(\) ambiguous id error [\#903](https://github.com/codeigniter4/CodeIgniter4/issues/903)
- Add Model beforeDelete property [\#902](https://github.com/codeigniter4/CodeIgniter4/issues/902)
- passing form\_validation\(\) errors with redirect\(\) to any view | and include methods for post only by using \[ HTTP verbs in routes \] and Resource route [\#900](https://github.com/codeigniter4/CodeIgniter4/issues/900)
- Database failover error on postgresql [\#899](https://github.com/codeigniter4/CodeIgniter4/issues/899)
- valid\_email validation error [\#898](https://github.com/codeigniter4/CodeIgniter4/issues/898)
- Multi language \( on same page \) support [\#891](https://github.com/codeigniter4/CodeIgniter4/issues/891)
- About environnement error\_reporting [\#889](https://github.com/codeigniter4/CodeIgniter4/issues/889)
- Subfolders for lang\(\) [\#887](https://github.com/codeigniter4/CodeIgniter4/issues/887)
- Urgent issues [\#875](https://github.com/codeigniter4/CodeIgniter4/issues/875)
- remark [\#871](https://github.com/codeigniter4/CodeIgniter4/issues/871)
- Bug fix mysqli transaction function call [\#870](https://github.com/codeigniter4/CodeIgniter4/issues/870)
- Debugbar SSL request javascript problem [\#867](https://github.com/codeigniter4/CodeIgniter4/issues/867)
- Backslashes being escaped in where conditions. [\#866](https://github.com/codeigniter4/CodeIgniter4/issues/866)
- Model::delete method [\#865](https://github.com/codeigniter4/CodeIgniter4/issues/865)
- about SessionHandlerInterface error [\#864](https://github.com/codeigniter4/CodeIgniter4/issues/864)
- Respond Error Pages by Content-Type [\#863](https://github.com/codeigniter4/CodeIgniter4/issues/863)
- When codeigniter 4 will be released ?  [\#860](https://github.com/codeigniter4/CodeIgniter4/issues/860)
- Catch in Controllers/Checks.php [\#859](https://github.com/codeigniter4/CodeIgniter4/issues/859)
- View render function  LFI\(local arbitray file include\)  issue [\#857](https://github.com/codeigniter4/CodeIgniter4/issues/857)
- Suggestion regarding codeigniter 4 [\#856](https://github.com/codeigniter4/CodeIgniter4/issues/856)
- Problem with using Entity class \(Could not execute App\Entities\::\_\_construct\(\)\) [\#855](https://github.com/codeigniter4/CodeIgniter4/issues/855)
- Config Unexpectedly Being Overwritten by $\_ENV variable [\#853](https://github.com/codeigniter4/CodeIgniter4/issues/853)
- Use PHP 7.0 or 7.1 typehints? [\#847](https://github.com/codeigniter4/CodeIgniter4/issues/847)
- There is something wrong with ViewTest::testRenderScrapsDataByDefault [\#846](https://github.com/codeigniter4/CodeIgniter4/issues/846)
- Session in filter [\#840](https://github.com/codeigniter4/CodeIgniter4/issues/840)
- Moved uploaded files doesn't retain new filename. [\#839](https://github.com/codeigniter4/CodeIgniter4/issues/839)
- Unable to use like in where？ [\#838](https://github.com/codeigniter4/CodeIgniter4/issues/838)
- Status of the version [\#837](https://github.com/codeigniter4/CodeIgniter4/issues/837)
- when set cache to file, when get a error [\#836](https://github.com/codeigniter4/CodeIgniter4/issues/836)
- Bug toolbar [\#834](https://github.com/codeigniter4/CodeIgniter4/issues/834)
- Table name can not use the alias [\#831](https://github.com/codeigniter4/CodeIgniter4/issues/831)
- Validation - permit\_empty [\#830](https://github.com/codeigniter4/CodeIgniter4/issues/830)
- Unable to install Codeigniter using composer [\#829](https://github.com/codeigniter4/CodeIgniter4/issues/829)
- Throttler usage [\#827](https://github.com/codeigniter4/CodeIgniter4/issues/827)
- Display Label on Form Validation [\#826](https://github.com/codeigniter4/CodeIgniter4/issues/826)
- Question about Filters [\#824](https://github.com/codeigniter4/CodeIgniter4/issues/824)
- uri-\>getHost\(\) [\#815](https://github.com/codeigniter4/CodeIgniter4/issues/815)
- Error changing databases CodeIgniter\Database\MySQLi\Connection::dbSelect\(\) [\#813](https://github.com/codeigniter4/CodeIgniter4/issues/813)
- Create a skeleton system for codeigniter [\#806](https://github.com/codeigniter4/CodeIgniter4/issues/806)
- Feature - CLI Prompt with Validation [\#800](https://github.com/codeigniter4/CodeIgniter4/issues/800)
- Routing problem /sth-abc-\>App:sth/foo \(ok\)    and   /sth/abc-\>App:sth/foo \(nok\) [\#799](https://github.com/codeigniter4/CodeIgniter4/issues/799)
- Update with delete value [\#796](https://github.com/codeigniter4/CodeIgniter4/issues/796)
- redirect\(\)-\>to\(\) is changing value of the base64 parameter [\#790](https://github.com/codeigniter4/CodeIgniter4/issues/790)
- View Parser escaping data even if passed 'raw' as context in setData\(\) [\#788](https://github.com/codeigniter4/CodeIgniter4/issues/788)
- Cant instantiate Parser Directly.  [\#787](https://github.com/codeigniter4/CodeIgniter4/issues/787)
- about redis error [\#783](https://github.com/codeigniter4/CodeIgniter4/issues/783)
- Validation issue and routing issue [\#782](https://github.com/codeigniter4/CodeIgniter4/issues/782)
- Query binding stopped working [\#781](https://github.com/codeigniter4/CodeIgniter4/issues/781)
- Entity exception in line 270 and 143 \(current version\). [\#780](https://github.com/codeigniter4/CodeIgniter4/issues/780)
- about Error Handling [\#778](https://github.com/codeigniter4/CodeIgniter4/issues/778)
- $this-\>CI-\>request-\>getIPAddress\(\) protected [\#776](https://github.com/codeigniter4/CodeIgniter4/issues/776)
- How do you access the $this bound data of the controller in the view? Ci3 is OK, ci4 doesn't seem to work [\#775](https://github.com/codeigniter4/CodeIgniter4/issues/775)
- I wish I could call the $this of the controller in the business model. What's the solution? [\#774](https://github.com/codeigniter4/CodeIgniter4/issues/774)
- Ignore\_value should be the value of ID, how do I get it? [\#772](https://github.com/codeigniter4/CodeIgniter4/issues/772)
- Can you increase the function of unique values in the test database? [\#771](https://github.com/codeigniter4/CodeIgniter4/issues/771)
- about where\(null\) [\#770](https://github.com/codeigniter4/CodeIgniter4/issues/770)
- redirect\(\)-\>to\(\) problem with output buffering [\#769](https://github.com/codeigniter4/CodeIgniter4/issues/769)
- Class 'App\Models\NewsModel' not found [\#766](https://github.com/codeigniter4/CodeIgniter4/issues/766)
- Undefined property: Config\App::$errorViewPath [\#765](https://github.com/codeigniter4/CodeIgniter4/issues/765)
- Zend/Escaper too big ,its not necessary [\#764](https://github.com/codeigniter4/CodeIgniter4/issues/764)
- .htaccess on php7.0.12 nts not work. [\#763](https://github.com/codeigniter4/CodeIgniter4/issues/763)
- DebugBar Routes Collector throws ErrorException when optional parameter is not passed to controller [\#762](https://github.com/codeigniter4/CodeIgniter4/issues/762)
- Debug View change page structure [\#761](https://github.com/codeigniter4/CodeIgniter4/issues/761)
- Debug Tool: Show view filepath [\#758](https://github.com/codeigniter4/CodeIgniter4/issues/758)
- PHPCBF - CodeIgniter4-Standard [\#757](https://github.com/codeigniter4/CodeIgniter4/issues/757)
- about errorViewPath [\#745](https://github.com/codeigniter4/CodeIgniter4/issues/745)
- Combine Composer with Install [\#744](https://github.com/codeigniter4/CodeIgniter4/issues/744)
- I have multiple applications. How can I share helpers and Class Map in multiple applications? [\#742](https://github.com/codeigniter4/CodeIgniter4/issues/742)
- I have multiple applications. How can I share helpers and Class Map in multiple applications? [\#741](https://github.com/codeigniter4/CodeIgniter4/issues/741)
- When can the fourth version be released, too much [\#738](https://github.com/codeigniter4/CodeIgniter4/issues/738)
- about Sub-directories [\#737](https://github.com/codeigniter4/CodeIgniter4/issues/737)
- About 【Use Config\Services as CodeIgniter\Services】 [\#735](https://github.com/codeigniter4/CodeIgniter4/issues/735)
- Mysqli Driver doesn't  throw sql level exceptions / error [\#734](https://github.com/codeigniter4/CodeIgniter4/issues/734)
- LogicException \#403 - CSRF [\#733](https://github.com/codeigniter4/CodeIgniter4/issues/733)
- User guide for ci4 is not responsive on mobile.\(screen width  less then 769px\) [\#730](https://github.com/codeigniter4/CodeIgniter4/issues/730)
- Composer intall, as described in documentation, points to different github repository\(!\) [\#726](https://github.com/codeigniter4/CodeIgniter4/issues/726)
- Fatal error when trying to run using PHP built in server [\#725](https://github.com/codeigniter4/CodeIgniter4/issues/725)
- Passing  array as argument in Parser Plugin not working [\#724](https://github.com/codeigniter4/CodeIgniter4/issues/724)
- $request-\>uri-\>getSegment\(\); return error [\#722](https://github.com/codeigniter4/CodeIgniter4/issues/722)
- Double quotation in getFieldData\(\); [\#721](https://github.com/codeigniter4/CodeIgniter4/issues/721)
- Should Model class implement the magic setter/getter methods for protected properties? [\#720](https://github.com/codeigniter4/CodeIgniter4/issues/720)
- Forge class trying to create UNSIGNED integer fields in Postgresql [\#719](https://github.com/codeigniter4/CodeIgniter4/issues/719)
- Tests Failing [\#717](https://github.com/codeigniter4/CodeIgniter4/issues/717)
- Form validation not working when Passing Empty array \[ \] [\#715](https://github.com/codeigniter4/CodeIgniter4/issues/715)
- Redirects to wrong incomplete URL [\#710](https://github.com/codeigniter4/CodeIgniter4/issues/710)
- pg\_escape\_literal\(\) expects parameter 1 to be resource, boolean given [\#709](https://github.com/codeigniter4/CodeIgniter4/issues/709)
- Separate DebugToolbar from CI\_DEBUG [\#707](https://github.com/codeigniter4/CodeIgniter4/issues/707)
- common.php [\#706](https://github.com/codeigniter4/CodeIgniter4/issues/706)
- Parser::parsePair fails if template contains a $ sign [\#705](https://github.com/codeigniter4/CodeIgniter4/issues/705)
- view parser throws InvalidArgumentException [\#704](https://github.com/codeigniter4/CodeIgniter4/issues/704)
- Auto redirect to root folder [\#703](https://github.com/codeigniter4/CodeIgniter4/issues/703)
- ErrorException \#1 htmlspecialchars\(\) expects parameter 1 to be string [\#702](https://github.com/codeigniter4/CodeIgniter4/issues/702)
- Support for Labels in Validation [\#696](https://github.com/codeigniter4/CodeIgniter4/issues/696)
- Why is system/bootstrap.php lowercase? [\#695](https://github.com/codeigniter4/CodeIgniter4/issues/695)
- To much escaping in where [\#691](https://github.com/codeigniter4/CodeIgniter4/issues/691)
- session in DB not working [\#690](https://github.com/codeigniter4/CodeIgniter4/issues/690)
- imagecopyresampled\(\): supplied resource is not a valid Image resource [\#689](https://github.com/codeigniter4/CodeIgniter4/issues/689)
- Postgres json column search error [\#680](https://github.com/codeigniter4/CodeIgniter4/issues/680)
- multiple File upload issue [\#679](https://github.com/codeigniter4/CodeIgniter4/issues/679)
- Postgres Json type error on save [\#677](https://github.com/codeigniter4/CodeIgniter4/issues/677)
- show strange chracters in kint.php inside system/ThirdParty folder. [\#676](https://github.com/codeigniter4/CodeIgniter4/issues/676)
- Need Parser plugin for validation errors. [\#675](https://github.com/codeigniter4/CodeIgniter4/issues/675)
- Fatal error: Cannot use Config\Services as Services [\#674](https://github.com/codeigniter4/CodeIgniter4/issues/674)
- Routing issue - bad args in function call. [\#672](https://github.com/codeigniter4/CodeIgniter4/issues/672)
- Entity Concerns [\#662](https://github.com/codeigniter4/CodeIgniter4/issues/662)
- Undefined method CLIRequest::isSecure\(\) [\#656](https://github.com/codeigniter4/CodeIgniter4/issues/656)
- PREG\_QUOTE and SQL String Binding [\#655](https://github.com/codeigniter4/CodeIgniter4/issues/655)
- Discussions on Pagination hasNext\(\) and hasPrevious\(\) [\#651](https://github.com/codeigniter4/CodeIgniter4/issues/651)
- Feature requested- Auth and Template Engine [\#647](https://github.com/codeigniter4/CodeIgniter4/issues/647)
- route\_to doesn't work for post routes [\#642](https://github.com/codeigniter4/CodeIgniter4/issues/642)
- CURL response header parsing \(100-continue\) [\#638](https://github.com/codeigniter4/CodeIgniter4/issues/638)
- Q: Support of array notations is not longer supported? [\#627](https://github.com/codeigniter4/CodeIgniter4/issues/627)
- Formvalidation not skip validation rules if field not required and field data empty [\#614](https://github.com/codeigniter4/CodeIgniter4/issues/614)
- Route naming and Groups [\#612](https://github.com/codeigniter4/CodeIgniter4/issues/612)
- Image resizing issue [\#610](https://github.com/codeigniter4/CodeIgniter4/issues/610)
- Too much escaping negative int  \(int \< 0\) [\#606](https://github.com/codeigniter4/CodeIgniter4/issues/606)
- Problem with Sth3Model loading from Sth2Model when Sth3Model is in subdiretory \(Subdir1\) [\#605](https://github.com/codeigniter4/CodeIgniter4/issues/605)
- I'm not sure but captcha seems currently unavailable. Would it be included in ci4? [\#604](https://github.com/codeigniter4/CodeIgniter4/issues/604)
- Issue with UploadFile \[tempName == name/originalName\] ? [\#602](https://github.com/codeigniter4/CodeIgniter4/issues/602)
- Command Line Seeding Error [\#601](https://github.com/codeigniter4/CodeIgniter4/issues/601)
- form\_open\_multipart\('', \[\], $hidden\) [\#598](https://github.com/codeigniter4/CodeIgniter4/issues/598)
- Minor issue with docs - form helper \(there is no html\_escape function\) - shoud be esc [\#595](https://github.com/codeigniter4/CodeIgniter4/issues/595)
- $session-\>destroy\(\) and $session-\>stop\(\) do not work? [\#592](https://github.com/codeigniter4/CodeIgniter4/issues/592)
- file upload issue [\#591](https://github.com/codeigniter4/CodeIgniter4/issues/591)
- \IncomingRequest method getFiles\(\) - Should really return only arrays? [\#590](https://github.com/codeigniter4/CodeIgniter4/issues/590)
- Third argument '$param' missing in validation getErrorMessage [\#589](https://github.com/codeigniter4/CodeIgniter4/issues/589)
- Error on Postgre insertID [\#587](https://github.com/codeigniter4/CodeIgniter4/issues/587)
- \InputRequest $request-\>getFiles\(\) doesn't return FileCollection instance [\#586](https://github.com/codeigniter4/CodeIgniter4/issues/586)
- Documentation out of date? discoverLocal\(\) [\#581](https://github.com/codeigniter4/CodeIgniter4/issues/581)
- Storing data [\#580](https://github.com/codeigniter4/CodeIgniter4/issues/580)
- Propose Moving Model::classToArray\(\) method to Entity class as public method [\#579](https://github.com/codeigniter4/CodeIgniter4/issues/579)
- camelize helper function incorrect [\#576](https://github.com/codeigniter4/CodeIgniter4/issues/576)
- helper function uses include instead of include\_once [\#575](https://github.com/codeigniter4/CodeIgniter4/issues/575)
- system/Validation/Views/list.php fix [\#574](https://github.com/codeigniter4/CodeIgniter4/issues/574)
- \[FEATURE\] Model Class: updateWhere\(\)  [\#572](https://github.com/codeigniter4/CodeIgniter4/issues/572)
- Model Order [\#571](https://github.com/codeigniter4/CodeIgniter4/issues/571)
- Minor thing with View file not found exception... [\#570](https://github.com/codeigniter4/CodeIgniter4/issues/570)
- Error: redirect\(\) - ltrim\(\) expects parameter 1 to be string, object given [\#568](https://github.com/codeigniter4/CodeIgniter4/issues/568)
- Tutorial is Wrong [\#562](https://github.com/codeigniter4/CodeIgniter4/issues/562)
- Model Hooks/Events Suggestion [\#557](https://github.com/codeigniter4/CodeIgniter4/issues/557)
- Initialize on Model \_\_construct [\#556](https://github.com/codeigniter4/CodeIgniter4/issues/556)
- Event trigger "pre\_system" not found [\#555](https://github.com/codeigniter4/CodeIgniter4/issues/555)
- Validation Issues  [\#550](https://github.com/codeigniter4/CodeIgniter4/issues/550)
- Model Entity suggestion [\#549](https://github.com/codeigniter4/CodeIgniter4/issues/549)
- CodeIgniter\Files\File\(\) and FileNotFoundException issue with creating new files [\#548](https://github.com/codeigniter4/CodeIgniter4/issues/548)
- override core files [\#546](https://github.com/codeigniter4/CodeIgniter4/issues/546)
- CodeIgniter\Format\JSONFormatter needs a look. [\#544](https://github.com/codeigniter4/CodeIgniter4/issues/544)
- getenv non covered system method [\#543](https://github.com/codeigniter4/CodeIgniter4/issues/543)
- Couldn't manage without index.php [\#541](https://github.com/codeigniter4/CodeIgniter4/issues/541)
- Debug bar: Uncaught ErrorException: str\_repeat\(\): Second argument has to be greater than or equal to 0 [\#538](https://github.com/codeigniter4/CodeIgniter4/issues/538)
- Create localization filters for View Parser to aid in formatting numbers [\#536](https://github.com/codeigniter4/CodeIgniter4/issues/536)
- Create lang plugin for the View Parser [\#535](https://github.com/codeigniter4/CodeIgniter4/issues/535)
- Minor: Debug bar showing htmlentities \(\<strong\>\) [\#531](https://github.com/codeigniter4/CodeIgniter4/issues/531)
- Autoloading not functional with custom namespaces outside application dir [\#529](https://github.com/codeigniter4/CodeIgniter4/issues/529)
- where i extends Controller, and rewrite \_\_construct, get error [\#527](https://github.com/codeigniter4/CodeIgniter4/issues/527)
- Debug/ImageException.php failed to open stream: No such file or directory  [\#525](https://github.com/codeigniter4/CodeIgniter4/issues/525)
- Superglobals reset [\#524](https://github.com/codeigniter4/CodeIgniter4/issues/524)
- Bring ENV definition back out to the index file.  [\#519](https://github.com/codeigniter4/CodeIgniter4/issues/519)
- Exception issue [\#517](https://github.com/codeigniter4/CodeIgniter4/issues/517)
- Port Zip library from CI3 [\#505](https://github.com/codeigniter4/CodeIgniter4/issues/505)
- Port Encryption library from CI3 [\#504](https://github.com/codeigniter4/CodeIgniter4/issues/504)
- Port SQLite database driver from CI3 [\#502](https://github.com/codeigniter4/CodeIgniter4/issues/502)
- A template engine suggession in this phase [\#500](https://github.com/codeigniter4/CodeIgniter4/issues/500)
- \_\_set in \App\Entities\Sample doesnt works [\#499](https://github.com/codeigniter4/CodeIgniter4/issues/499)
- Router rule: \(:alphanum\) doesn't work [\#498](https://github.com/codeigniter4/CodeIgniter4/issues/498)
- Undefined index: host [\#497](https://github.com/codeigniter4/CodeIgniter4/issues/497)
- Unreasonable default parameter [\#482](https://github.com/codeigniter4/CodeIgniter4/issues/482)
- Model issues [\#479](https://github.com/codeigniter4/CodeIgniter4/issues/479)
- Route Blocking [\#474](https://github.com/codeigniter4/CodeIgniter4/issues/474)
- Class 'CodeIgniter\Hooks\Hooks' not found [\#473](https://github.com/codeigniter4/CodeIgniter4/issues/473)
- Work with entities and validation bug [\#472](https://github.com/codeigniter4/CodeIgniter4/issues/472)
- route bug or not? [\#470](https://github.com/codeigniter4/CodeIgniter4/issues/470)
- serve.php option --host no effect on line 37 [\#469](https://github.com/codeigniter4/CodeIgniter4/issues/469)
- File class should stand on its own [\#468](https://github.com/codeigniter4/CodeIgniter4/issues/468)
- Database can't save connect instances because var "$group" default is NULL [\#466](https://github.com/codeigniter4/CodeIgniter4/issues/466)
- redirect\(\) issue [\#465](https://github.com/codeigniter4/CodeIgniter4/issues/465)
- Join not working in Query Builder [\#464](https://github.com/codeigniter4/CodeIgniter4/issues/464)
- If the controller's method's parameter has a default value , the program will show 500 error [\#461](https://github.com/codeigniter4/CodeIgniter4/issues/461)
- Wrong ROOTPATH on console [\#460](https://github.com/codeigniter4/CodeIgniter4/issues/460)
- Rename "public" Folder Not Working [\#453](https://github.com/codeigniter4/CodeIgniter4/issues/453)
- Environment File [\#452](https://github.com/codeigniter4/CodeIgniter4/issues/452)
- Currency Localisation Not Working [\#448](https://github.com/codeigniter4/CodeIgniter4/issues/448)
- session getFlashdata\(\) is not removing the flash data [\#446](https://github.com/codeigniter4/CodeIgniter4/issues/446)
- CURLRequest: HTTPHEADER and POSTFIELDS [\#445](https://github.com/codeigniter4/CodeIgniter4/issues/445)
- Language Line Prepend Recommendation [\#443](https://github.com/codeigniter4/CodeIgniter4/issues/443)
- Debug Toolbar Array Post Data  [\#442](https://github.com/codeigniter4/CodeIgniter4/issues/442)
- Migration File & Class Name Issues [\#437](https://github.com/codeigniter4/CodeIgniter4/issues/437)
- Validation Rule Constants [\#436](https://github.com/codeigniter4/CodeIgniter4/issues/436)
- CURLRequest: Only variables should be passed by reference [\#434](https://github.com/codeigniter4/CodeIgniter4/issues/434)
- CLI new commands  [\#433](https://github.com/codeigniter4/CodeIgniter4/issues/433)
- issue in phpunit [\#429](https://github.com/codeigniter4/CodeIgniter4/issues/429)
- Model Class To Array Does Not Use Get Magic Method Or Allowed Fields Property [\#427](https://github.com/codeigniter4/CodeIgniter4/issues/427)
- Response 404 not same satus get it [\#425](https://github.com/codeigniter4/CodeIgniter4/issues/425)
- Response Trait Fail Server Error Method [\#424](https://github.com/codeigniter4/CodeIgniter4/issues/424)
- Validation Required Rule Only Accepts Arrays or Strings [\#423](https://github.com/codeigniter4/CodeIgniter4/issues/423)
- CLI with parameters is not working [\#422](https://github.com/codeigniter4/CodeIgniter4/issues/422)
- API Response Trait :: failValidationError\(\) Status Code 422 [\#420](https://github.com/codeigniter4/CodeIgniter4/issues/420)
- Validation Get & Set Rule Group [\#419](https://github.com/codeigniter4/CodeIgniter4/issues/419)
- One-Off Validation for a Single Value [\#418](https://github.com/codeigniter4/CodeIgniter4/issues/418)
- Language File Array Support [\#414](https://github.com/codeigniter4/CodeIgniter4/issues/414)
- Pagination: How to trans string param when using $pager-\>links\(\)? [\#413](https://github.com/codeigniter4/CodeIgniter4/issues/413)
- Validation Exceptions [\#412](https://github.com/codeigniter4/CodeIgniter4/issues/412)
- Incorrect .htaccess [\#410](https://github.com/codeigniter4/CodeIgniter4/issues/410)
- Call to a member function getResult\(\) on boolean [\#409](https://github.com/codeigniter4/CodeIgniter4/issues/409)
- Writable directory question [\#407](https://github.com/codeigniter4/CodeIgniter4/issues/407)
- Paginate Class [\#406](https://github.com/codeigniter4/CodeIgniter4/issues/406)
- Base URL Not Working With HTTPS [\#396](https://github.com/codeigniter4/CodeIgniter4/issues/396)
- How to current page detailed information on router or etc? [\#393](https://github.com/codeigniter4/CodeIgniter4/issues/393)
- timer long time getElapsedTime problem [\#390](https://github.com/codeigniter4/CodeIgniter4/issues/390)
- Problem with system bootstrap file needs to be fixed! [\#389](https://github.com/codeigniter4/CodeIgniter4/issues/389)
- redirect reverseRoute problem [\#387](https://github.com/codeigniter4/CodeIgniter4/issues/387)
- Hooks::on not working \(it was working some time ago\) [\#383](https://github.com/codeigniter4/CodeIgniter4/issues/383)
- disable coveralls auto comment on pull request [\#382](https://github.com/codeigniter4/CodeIgniter4/issues/382)
- phpunit don't work \(fatal error\) on PHP 7.0.0 [\#373](https://github.com/codeigniter4/CodeIgniter4/issues/373)
- session id validation when using php7.1 [\#371](https://github.com/codeigniter4/CodeIgniter4/issues/371)
- travis test coverage report service [\#370](https://github.com/codeigniter4/CodeIgniter4/issues/370)
- The efficiency of 'insertBatch' function [\#368](https://github.com/codeigniter4/CodeIgniter4/issues/368)
- Run phpunit with --coverage-text/html got Error: Undefined variable: matchIP [\#363](https://github.com/codeigniter4/CodeIgniter4/issues/363)
- Extending Core Classes [\#358](https://github.com/codeigniter4/CodeIgniter4/issues/358)
- Validation field name issue? [\#356](https://github.com/codeigniter4/CodeIgniter4/issues/356)
- Old Cache-Control header tag remove? [\#355](https://github.com/codeigniter4/CodeIgniter4/issues/355)
- $this-\>request-\>getPost\(\) // Subkey not included data [\#353](https://github.com/codeigniter4/CodeIgniter4/issues/353)
- Routes static parameter problem not found page [\#352](https://github.com/codeigniter4/CodeIgniter4/issues/352)
- bug-form\_open\_multipart [\#345](https://github.com/codeigniter4/CodeIgniter4/issues/345)
- Recently ci4 everything goes well? I look ci the space named [\#336](https://github.com/codeigniter4/CodeIgniter4/issues/336)
- Request setBody not working [\#332](https://github.com/codeigniter4/CodeIgniter4/issues/332)
- HTTP/URI, CreateURIString yield unexpected result \(http:/// instead of http://\) [\#331](https://github.com/codeigniter4/CodeIgniter4/issues/331)
- session class: inconsistency on get\(\) between document and code [\#330](https://github.com/codeigniter4/CodeIgniter4/issues/330)
- Model.php Return Type Object causes Error when Saving. \(Fixed with this modification\) [\#329](https://github.com/codeigniter4/CodeIgniter4/issues/329)
- a bug about set\(\) [\#325](https://github.com/codeigniter4/CodeIgniter4/issues/325)
- `google map` in Mysqli Could be a Mistake [\#324](https://github.com/codeigniter4/CodeIgniter4/issues/324)
- Modules Controllers sub-directory problem [\#322](https://github.com/codeigniter4/CodeIgniter4/issues/322)
- Post action after Toolbar -\> Vars -\> Headers Content-Type 2 lines [\#321](https://github.com/codeigniter4/CodeIgniter4/issues/321)
- Database builder-\>table\(\) connection reference problem [\#320](https://github.com/codeigniter4/CodeIgniter4/issues/320)
- RedisHandler, Session regenerate id problem [\#318](https://github.com/codeigniter4/CodeIgniter4/issues/318)
- view\_cell optional variables array\_key\_exists null fix. [\#317](https://github.com/codeigniter4/CodeIgniter4/issues/317)
- sessionDriver Database not available [\#315](https://github.com/codeigniter4/CodeIgniter4/issues/315)
- Add support for Content-MD5 headers [\#314](https://github.com/codeigniter4/CodeIgniter4/issues/314)
- Scalar type declaration 'string' must be unqualified [\#312](https://github.com/codeigniter4/CodeIgniter4/issues/312)
- Now ci4 product development can be used? Or how long before they can be released? Very much looking forward [\#311](https://github.com/codeigniter4/CodeIgniter4/issues/311)
- Sessions in Debug Bar Could be a Mistake [\#310](https://github.com/codeigniter4/CodeIgniter4/issues/310)
- Should namespaces be used or fully written at method heads [\#309](https://github.com/codeigniter4/CodeIgniter4/issues/309)
- More filtering issues with Query [\#306](https://github.com/codeigniter4/CodeIgniter4/issues/306)
- Too much escaping in DB? [\#302](https://github.com/codeigniter4/CodeIgniter4/issues/302)
- Error Cell caching [\#297](https://github.com/codeigniter4/CodeIgniter4/issues/297)
- A suggestion for debug exceptions layout [\#295](https://github.com/codeigniter4/CodeIgniter4/issues/295)
- getSharedInstance doesn't work on costum services [\#294](https://github.com/codeigniter4/CodeIgniter4/issues/294)
- Separated services default with trait [\#292](https://github.com/codeigniter4/CodeIgniter4/issues/292)
- HTML-Purifier [\#291](https://github.com/codeigniter4/CodeIgniter4/issues/291)
- Implement native ORM [\#289](https://github.com/codeigniter4/CodeIgniter4/issues/289)
- Igniter 4 services [\#287](https://github.com/codeigniter4/CodeIgniter4/issues/287)
- Error Documentation - Tutorial [\#286](https://github.com/codeigniter4/CodeIgniter4/issues/286)
- Request is doing the filter\_var even if value is not in the $\_REQUEST array [\#285](https://github.com/codeigniter4/CodeIgniter4/issues/285)
- controller routing [\#284](https://github.com/codeigniter4/CodeIgniter4/issues/284)
- URI Routing issues [\#278](https://github.com/codeigniter4/CodeIgniter4/issues/278)
- Email Library [\#276](https://github.com/codeigniter4/CodeIgniter4/issues/276)
- sql error [\#273](https://github.com/codeigniter4/CodeIgniter4/issues/273)
- DB Needs Transactions implemented. [\#268](https://github.com/codeigniter4/CodeIgniter4/issues/268)
- Error Call to undefined method CodeIgniter\Database\MySQLi\Connection::close\(\)	 [\#267](https://github.com/codeigniter4/CodeIgniter4/issues/267)
- Application/ThirdParty folder necessity [\#265](https://github.com/codeigniter4/CodeIgniter4/issues/265)
- ErrorException when Connecting to Multiple Databases [\#255](https://github.com/codeigniter4/CodeIgniter4/issues/255)
- Toolbar.php :: ErrorException: Array to string conversion [\#254](https://github.com/codeigniter4/CodeIgniter4/issues/254)
- The Hooks feature does not work [\#248](https://github.com/codeigniter4/CodeIgniter4/issues/248)
- $baseUrl problem with Router [\#238](https://github.com/codeigniter4/CodeIgniter4/issues/238)
- File upload bugs [\#236](https://github.com/codeigniter4/CodeIgniter4/issues/236)
- standardize comments [\#234](https://github.com/codeigniter4/CodeIgniter4/issues/234)
- 3.1.0 Email Library Corrupting PDF Attachments [\#220](https://github.com/codeigniter4/CodeIgniter4/issues/220)
- Error DotEnv.php on line 121 [\#216](https://github.com/codeigniter4/CodeIgniter4/issues/216)
- Typography Helper [\#214](https://github.com/codeigniter4/CodeIgniter4/issues/214)
- Security Helper [\#213](https://github.com/codeigniter4/CodeIgniter4/issues/213)
- Number Helper [\#212](https://github.com/codeigniter4/CodeIgniter4/issues/212)
- Text Helper [\#211](https://github.com/codeigniter4/CodeIgniter4/issues/211)
- Inflector Helper [\#210](https://github.com/codeigniter4/CodeIgniter4/issues/210)
- HTML Helper [\#209](https://github.com/codeigniter4/CodeIgniter4/issues/209)
- FileSystem "Helper" [\#207](https://github.com/codeigniter4/CodeIgniter4/issues/207)
- Date Helper [\#206](https://github.com/codeigniter4/CodeIgniter4/issues/206)
- Cookie Helper [\#205](https://github.com/codeigniter4/CodeIgniter4/issues/205)
- Missing {memory\_usage} ? [\#197](https://github.com/codeigniter4/CodeIgniter4/issues/197)
- Exception or disable toolbar for specific URI  [\#195](https://github.com/codeigniter4/CodeIgniter4/issues/195)
- Config Settings Usability [\#186](https://github.com/codeigniter4/CodeIgniter4/issues/186)
- Binders [\#185](https://github.com/codeigniter4/CodeIgniter4/issues/185)
- Mess Detector rules [\#184](https://github.com/codeigniter4/CodeIgniter4/issues/184)
- Coding Standards Fixer rules [\#183](https://github.com/codeigniter4/CodeIgniter4/issues/183)
- Code Sniffer Rules [\#182](https://github.com/codeigniter4/CodeIgniter4/issues/182)
- Placing view template outside of the 'Views' dir when using view\(\) [\#180](https://github.com/codeigniter4/CodeIgniter4/issues/180)
- Controller return output instead of echo view [\#179](https://github.com/codeigniter4/CodeIgniter4/issues/179)
- Honeypot Filter [\#176](https://github.com/codeigniter4/CodeIgniter4/issues/176)
- Form Helper [\#174](https://github.com/codeigniter4/CodeIgniter4/issues/174)
- ILIKE-based portion of the query for PostgreSQL [\#173](https://github.com/codeigniter4/CodeIgniter4/issues/173)
- “&get\_instance\(\)” in the Ci3 how to use it? [\#166](https://github.com/codeigniter4/CodeIgniter4/issues/166)
- system/Database/BaseConnection.php Change in getFieldNames\(\) method [\#164](https://github.com/codeigniter4/CodeIgniter4/issues/164)
- Error in session FileHandler and BaseHandler [\#152](https://github.com/codeigniter4/CodeIgniter4/issues/152)
- No listFields\(\) method in Postgre connection [\#151](https://github.com/codeigniter4/CodeIgniter4/issues/151)
- Controller Filters [\#150](https://github.com/codeigniter4/CodeIgniter4/issues/150)
- insert bug [\#149](https://github.com/codeigniter4/CodeIgniter4/issues/149)
- Router striping real dirpath from the urls. [\#148](https://github.com/codeigniter4/CodeIgniter4/issues/148)
- Problem throw error for default controller [\#146](https://github.com/codeigniter4/CodeIgniter4/issues/146)
- Routing issues.  [\#145](https://github.com/codeigniter4/CodeIgniter4/issues/145)
- Pagination Library [\#142](https://github.com/codeigniter4/CodeIgniter4/issues/142)
- \[i18n\] Localization In Core [\#141](https://github.com/codeigniter4/CodeIgniter4/issues/141)
- Language [\#140](https://github.com/codeigniter4/CodeIgniter4/issues/140)
- Parser [\#139](https://github.com/codeigniter4/CodeIgniter4/issues/139)
- Application directory is missing the Helpers folder [\#133](https://github.com/codeigniter4/CodeIgniter4/issues/133)
- HTTP/Request.php Error [\#132](https://github.com/codeigniter4/CodeIgniter4/issues/132)
- Public properties issue? [\#124](https://github.com/codeigniter4/CodeIgniter4/issues/124)
- ci4 support websocket it? [\#121](https://github.com/codeigniter4/CodeIgniter4/issues/121)
- View Cells [\#116](https://github.com/codeigniter4/CodeIgniter4/issues/116)
- Cache Engine [\#115](https://github.com/codeigniter4/CodeIgniter4/issues/115)
- Image Class [\#114](https://github.com/codeigniter4/CodeIgniter4/issues/114)
- Uploader Class [\#113](https://github.com/codeigniter4/CodeIgniter4/issues/113)
- API Response Trait [\#86](https://github.com/codeigniter4/CodeIgniter4/issues/86)
- phpDocumentor bug [\#85](https://github.com/codeigniter4/CodeIgniter4/issues/85)
- Reserved method name [\#76](https://github.com/codeigniter4/CodeIgniter4/issues/76)
- Provide Throttler Filter [\#75](https://github.com/codeigniter4/CodeIgniter4/issues/75)
- Ensure docs are updated for current code. [\#72](https://github.com/codeigniter4/CodeIgniter4/issues/72)
- Writing Testing Docs [\#71](https://github.com/codeigniter4/CodeIgniter4/issues/71)
- Update Tutorial [\#70](https://github.com/codeigniter4/CodeIgniter4/issues/70)
- Review Contribution Guidelines [\#69](https://github.com/codeigniter4/CodeIgniter4/issues/69)
- Database Connection setDatabase\(\) and getVersion\(\) methods [\#68](https://github.com/codeigniter4/CodeIgniter4/issues/68)
- BaseConfig getEnvValue type juggling [\#67](https://github.com/codeigniter4/CodeIgniter4/issues/67)
- Migrations should track history per db group. [\#66](https://github.com/codeigniter4/CodeIgniter4/issues/66)
- Allow passing custom config values to Config\Database::connect\(\) [\#62](https://github.com/codeigniter4/CodeIgniter4/issues/62)
- SessionInterface: unset\(\) vs remove\(\) [\#60](https://github.com/codeigniter4/CodeIgniter4/issues/60)
- Remove Query Builder caching [\#59](https://github.com/codeigniter4/CodeIgniter4/issues/59)
- Why is `$getShared = false` the default? [\#55](https://github.com/codeigniter4/CodeIgniter4/issues/55)
- Why IncomingRequest has setCookie\(\)? [\#52](https://github.com/codeigniter4/CodeIgniter4/issues/52)
- AutoRoute and method arguments [\#45](https://github.com/codeigniter4/CodeIgniter4/issues/45)
- Rename the loader class [\#39](https://github.com/codeigniter4/CodeIgniter4/issues/39)
- exit\(\) prevents from testing [\#31](https://github.com/codeigniter4/CodeIgniter4/issues/31)
- getHeaders [\#27](https://github.com/codeigniter4/CodeIgniter4/issues/27)
- SYSDIR [\#25](https://github.com/codeigniter4/CodeIgniter4/issues/25)
- Coding style checker [\#21](https://github.com/codeigniter4/CodeIgniter4/issues/21)
- Test folder structure [\#20](https://github.com/codeigniter4/CodeIgniter4/issues/20)
- Namespace for test case classes [\#17](https://github.com/codeigniter4/CodeIgniter4/issues/17)
- Missing protocolVersion in response header [\#15](https://github.com/codeigniter4/CodeIgniter4/issues/15)
- Problem with Code Coverage Reporting [\#13](https://github.com/codeigniter4/CodeIgniter4/issues/13)
- Class 'Config\App' not found [\#12](https://github.com/codeigniter4/CodeIgniter4/issues/12)
- Can't get Code Coverage [\#7](https://github.com/codeigniter4/CodeIgniter4/issues/7)
- APPPATH, SYSPATH and similar constants. Rename? [\#2](https://github.com/codeigniter4/CodeIgniter4/issues/2)

**Merged pull requests:**

- Adjusting the release build scripts [\#1266](https://github.com/codeigniter4/CodeIgniter4/pull/1266) ([jim-parry](https://github.com/jim-parry))
- WIP Fix docs re PHP server [\#1265](https://github.com/codeigniter4/CodeIgniter4/pull/1265) ([jim-parry](https://github.com/jim-parry))
- Release prep part 1 [\#1248](https://github.com/codeigniter4/CodeIgniter4/pull/1248) ([jim-parry](https://github.com/jim-parry))
- Tweaking the release builder [\#1246](https://github.com/codeigniter4/CodeIgniter4/pull/1246) ([jim-parry](https://github.com/jim-parry))
- Move Response & APIResponseTrait to outgoing section of UG [\#1243](https://github.com/codeigniter4/CodeIgniter4/pull/1243) ([jim-parry](https://github.com/jim-parry))
- workaround for buffer problem [\#1242](https://github.com/codeigniter4/CodeIgniter4/pull/1242) ([puschie286](https://github.com/puschie286))
- Docs/restructure [\#1241](https://github.com/codeigniter4/CodeIgniter4/pull/1241) ([jim-parry](https://github.com/jim-parry))
- doc fix: replace validation\_errors\(\) function with \Config\Services::validation\(\)-\>listErrors\(\) [\#1238](https://github.com/codeigniter4/CodeIgniter4/pull/1238) ([samsonasik](https://github.com/samsonasik))
- doc fix: remove unneeded call helper\(url\) as already bootstrapped by default [\#1237](https://github.com/codeigniter4/CodeIgniter4/pull/1237) ([samsonasik](https://github.com/samsonasik))
- Fix gh-pages deployment [\#1236](https://github.com/codeigniter4/CodeIgniter4/pull/1236) ([jim-parry](https://github.com/jim-parry))
- Doc/fixes [\#1235](https://github.com/codeigniter4/CodeIgniter4/pull/1235) ([jim-parry](https://github.com/jim-parry))
- typo in unset [\#1233](https://github.com/codeigniter4/CodeIgniter4/pull/1233) ([titounnes](https://github.com/titounnes))
- Release build script [\#1231](https://github.com/codeigniter4/CodeIgniter4/pull/1231) ([jim-parry](https://github.com/jim-parry))
- Fix user guide errors [\#1228](https://github.com/codeigniter4/CodeIgniter4/pull/1228) ([jim-parry](https://github.com/jim-parry))
- Admin script for user guide build & deploy [\#1227](https://github.com/codeigniter4/CodeIgniter4/pull/1227) ([jim-parry](https://github.com/jim-parry))
- use short array syntax [\#1223](https://github.com/codeigniter4/CodeIgniter4/pull/1223) ([samsonasik](https://github.com/samsonasik))
- doc fix: FormatterInterface namespace [\#1222](https://github.com/codeigniter4/CodeIgniter4/pull/1222) ([samsonasik](https://github.com/samsonasik))
- Improved division logic of validation rules. [\#1220](https://github.com/codeigniter4/CodeIgniter4/pull/1220) ([ytetsuro](https://github.com/ytetsuro))
- Docs/contributing [\#1218](https://github.com/codeigniter4/CodeIgniter4/pull/1218) ([jim-parry](https://github.com/jim-parry))
- Niggly fixes [\#1216](https://github.com/codeigniter4/CodeIgniter4/pull/1216) ([jim-parry](https://github.com/jim-parry))
- Autodiscovery [\#1215](https://github.com/codeigniter4/CodeIgniter4/pull/1215) ([lonnieezell](https://github.com/lonnieezell))
- Fix warnings in welcome\_message.php [\#1211](https://github.com/codeigniter4/CodeIgniter4/pull/1211) ([puschie286](https://github.com/puschie286))
- Correct helper tests namespace [\#1207](https://github.com/codeigniter4/CodeIgniter4/pull/1207) ([jim-parry](https://github.com/jim-parry))
- Validation Class - corresponding about the escaped separator. [\#1203](https://github.com/codeigniter4/CodeIgniter4/pull/1203) ([ytetsuro](https://github.com/ytetsuro))
- Fixes FileRules::max\_size\(\) to use file-\>getSize\(\) instead of number\_formatted size [\#1199](https://github.com/codeigniter4/CodeIgniter4/pull/1199) ([samsonasik](https://github.com/samsonasik))
- use Validation-\>getErrors\(\) call instead of Valdation::errors to handle errors that came from session [\#1197](https://github.com/codeigniter4/CodeIgniter4/pull/1197) ([samsonasik](https://github.com/samsonasik))
- allows to get table and primary key name out of the model [\#1196](https://github.com/codeigniter4/CodeIgniter4/pull/1196) ([nowackipawel](https://github.com/nowackipawel))
- pagination - optional page number [\#1195](https://github.com/codeigniter4/CodeIgniter4/pull/1195) ([nowackipawel](https://github.com/nowackipawel))
- add writable/session directory and set default App::sessionSavePath to it [\#1194](https://github.com/codeigniter4/CodeIgniter4/pull/1194) ([samsonasik](https://github.com/samsonasik))
- Fix travis error build on Router and RouteCollection [\#1192](https://github.com/codeigniter4/CodeIgniter4/pull/1192) ([samsonasik](https://github.com/samsonasik))
- add bool type hint for getShared parameter [\#1191](https://github.com/codeigniter4/CodeIgniter4/pull/1191) ([samsonasik](https://github.com/samsonasik))
- Catch Email Exceptions [\#1190](https://github.com/codeigniter4/CodeIgniter4/pull/1190) ([puschie286](https://github.com/puschie286))
- $myModel-\>find\(string "value-of-my-primary-key"\) \#1188 [\#1189](https://github.com/codeigniter4/CodeIgniter4/pull/1189) ([nowackipawel](https://github.com/nowackipawel))
- Adds valid\_json which is using json\_last\_error\(\) === JSON\_ERROR\_NONE [\#1187](https://github.com/codeigniter4/CodeIgniter4/pull/1187) ([nowackipawel](https://github.com/nowackipawel))
- remove start\(\) call on session\(\) function call at Validation [\#1185](https://github.com/codeigniter4/CodeIgniter4/pull/1185) ([samsonasik](https://github.com/samsonasik))
- remove unused salt in Model [\#1184](https://github.com/codeigniter4/CodeIgniter4/pull/1184) ([samsonasik](https://github.com/samsonasik))
- log file:failed to delete buffer. No buffer to delete. [\#1182](https://github.com/codeigniter4/CodeIgniter4/pull/1182) ([bangbangda](https://github.com/bangbangda))
- use string type hint in $file parameter in DotEnv::\_\_construct\(\) [\#1181](https://github.com/codeigniter4/CodeIgniter4/pull/1181) ([samsonasik](https://github.com/samsonasik))
- Adjust log level to match RFC 5424 [\#1178](https://github.com/codeigniter4/CodeIgniter4/pull/1178) ([sugenganthos](https://github.com/sugenganthos))
- Update Response.php [\#1173](https://github.com/codeigniter4/CodeIgniter4/pull/1173) ([sugenganthos](https://github.com/sugenganthos))
- comparison fix at CIDatabaseTestCase [\#1172](https://github.com/codeigniter4/CodeIgniter4/pull/1172) ([samsonasik](https://github.com/samsonasik))
- remove if \($template==forums/categories\) check in View/Parser [\#1171](https://github.com/codeigniter4/CodeIgniter4/pull/1171) ([samsonasik](https://github.com/samsonasik))
- show PageNotFoundException message [\#1168](https://github.com/codeigniter4/CodeIgniter4/pull/1168) ([puschie286](https://github.com/puschie286))
- make name parameter in HTTP\Header mandatory [\#1164](https://github.com/codeigniter4/CodeIgniter4/pull/1164) ([samsonasik](https://github.com/samsonasik))
- Fixed insufficient validation of parameters related to pager. [\#1162](https://github.com/codeigniter4/CodeIgniter4/pull/1162) ([ytetsuro](https://github.com/ytetsuro))
- remove unneeded helper\('url'\) call in plugins and form helper as already called in bootstrap file [\#1160](https://github.com/codeigniter4/CodeIgniter4/pull/1160) ([samsonasik](https://github.com/samsonasik))
- reduce str\_replace in View/Parser::render\(\) [\#1159](https://github.com/codeigniter4/CodeIgniter4/pull/1159) ([samsonasik](https://github.com/samsonasik))
- add missing string parameter type hint in Autoload methods [\#1158](https://github.com/codeigniter4/CodeIgniter4/pull/1158) ([samsonasik](https://github.com/samsonasik))
- remove unneeded isset\($options\[hostname\]\) when next check is !empty\($options\[hostname\]\) [\#1157](https://github.com/codeigniter4/CodeIgniter4/pull/1157) ([samsonasik](https://github.com/samsonasik))
- remove commented and never used methods in View\Parser class [\#1156](https://github.com/codeigniter4/CodeIgniter4/pull/1156) ([samsonasik](https://github.com/samsonasik))
- Fixes \#1135 : Toolbar oldest file delete and show maximum in the list as App::toolbarMaxHistory [\#1155](https://github.com/codeigniter4/CodeIgniter4/pull/1155) ([samsonasik](https://github.com/samsonasik))
- add ext-intl to require at composer.json [\#1153](https://github.com/codeigniter4/CodeIgniter4/pull/1153) ([samsonasik](https://github.com/samsonasik))
- remove unneeded \(string\) cast as type hinted [\#1152](https://github.com/codeigniter4/CodeIgniter4/pull/1152) ([samsonasik](https://github.com/samsonasik))
- implements session-\>push\(\) [\#1151](https://github.com/codeigniter4/CodeIgniter4/pull/1151) ([samsonasik](https://github.com/samsonasik))
- SplFileInfo type case [\#1150](https://github.com/codeigniter4/CodeIgniter4/pull/1150) ([samsonasik](https://github.com/samsonasik))
- Update views.rst [\#1149](https://github.com/codeigniter4/CodeIgniter4/pull/1149) ([sugenganthos](https://github.com/sugenganthos))
- remove unneeded @todo for Time::setMonth\(\) to check max [\#1148](https://github.com/codeigniter4/CodeIgniter4/pull/1148) ([samsonasik](https://github.com/samsonasik))
- fix RedirectResponse::route and added test [\#1147](https://github.com/codeigniter4/CodeIgniter4/pull/1147) ([puschie286](https://github.com/puschie286))
- Fix uri detection with no index in uri [\#1146](https://github.com/codeigniter4/CodeIgniter4/pull/1146) ([puschie286](https://github.com/puschie286))
- remove unneeded isset\($\_SESSION\) when next check is !empty\($\_SESSION\) [\#1145](https://github.com/codeigniter4/CodeIgniter4/pull/1145) ([samsonasik](https://github.com/samsonasik))
- Add a namespace to the Pages.php [\#1143](https://github.com/codeigniter4/CodeIgniter4/pull/1143) ([fdjkgh580](https://github.com/fdjkgh580))
- Add a namespace to the controller. [\#1142](https://github.com/codeigniter4/CodeIgniter4/pull/1142) ([fdjkgh580](https://github.com/fdjkgh580))
- remove unused use statements [\#1141](https://github.com/codeigniter4/CodeIgniter4/pull/1141) ([samsonasik](https://github.com/samsonasik))
- implements @todo max day in current month at Time::setDay\(\) [\#1140](https://github.com/codeigniter4/CodeIgniter4/pull/1140) ([samsonasik](https://github.com/samsonasik))
- remove unneeded \(int\) casting as use int type hint or certainly an int [\#1138](https://github.com/codeigniter4/CodeIgniter4/pull/1138) ([samsonasik](https://github.com/samsonasik))
- Update html\_helper.php [\#1133](https://github.com/codeigniter4/CodeIgniter4/pull/1133) ([WaldemarStanislawski](https://github.com/WaldemarStanislawski))
- update to latest php-coveralls [\#1131](https://github.com/codeigniter4/CodeIgniter4/pull/1131) ([samsonasik](https://github.com/samsonasik))
- Update View.php [\#1130](https://github.com/codeigniter4/CodeIgniter4/pull/1130) ([sugenganthos](https://github.com/sugenganthos))
- Fix debugbar loading while csp is enabled [\#1129](https://github.com/codeigniter4/CodeIgniter4/pull/1129) ([puschie286](https://github.com/puschie286))
- Run session tests in separate processes - fix for \#1106 [\#1128](https://github.com/codeigniter4/CodeIgniter4/pull/1128) ([andreif23](https://github.com/andreif23))
- Feature/sqlite [\#793](https://github.com/codeigniter4/CodeIgniter4/pull/793) ([lonnieezell](https://github.com/lonnieezell))
