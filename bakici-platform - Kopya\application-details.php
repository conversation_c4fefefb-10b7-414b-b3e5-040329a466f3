<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Giriş kontrolü
if (!isLoggedIn()) {
    echo '<div class="alert alert-danger"><PERSON><PERSON><PERSON> yapmanız gerekiyor.</div>';
    exit;
}

$application_id = intval($_GET['id'] ?? 0);
if (!$application_id) {
    echo '<div class="alert alert-danger">Geçersiz başvuru ID.</div>';
    exit;
}

// Başvuru bilgilerini getir
try {
    $sql = "SELECT ja.*,
                   jl.title as job_title, jl.description as job_description, jl.location, jl.budget_min, jl.budget_max, jl.budget_type, jl.user_id as employer_id,
                   caregiver.full_name as caregiver_name, caregiver.email as caregiver_email, caregiver.phone as caregiver_phone, caregiver.city as caregiver_city,
                   employer.full_name as employer_name, employer.email as employer_email,
                   cp.experience_years, cp.rating, cp.total_reviews, cp.hourly_rate, cp.daily_rate, cp.monthly_rate, cp.bio, cp.specializations
            FROM job_applications ja
            JOIN job_listings jl ON ja.job_id = jl.id
            JOIN users caregiver ON ja.caregiver_id = caregiver.id
            JOIN users employer ON jl.user_id = employer.id
            LEFT JOIN caregiver_profiles cp ON caregiver.id = cp.user_id
            WHERE ja.id = ?";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([$application_id]);
    $application = $stmt->fetch();
    
    if (!$application) {
        echo '<div class="alert alert-danger">Başvuru bulunamadı.</div>';
        exit;
    }
    
    // Yetki kontrolü - sadece ilgili taraflar görebilir
    $user_id = $_SESSION['user_id'];
    $user_type = $_SESSION['user_type'];

    if ($user_type !== 'admin' &&
        $user_id != $application['caregiver_id'] &&
        $user_id != $application['employer_id']) {
        echo '<div class="alert alert-danger">Bu başvuruyu görüntüleme yetkiniz yok.</div>';
        exit;
    }
    
} catch (PDOException $e) {
    echo '<div class="alert alert-danger">Veri yüklenirken hata oluştu.</div>';
    exit;
}

// Durum renkleri
$status_colors = [
    'pending' => 'warning',
    'viewed' => 'info',
    'shortlisted' => 'primary',
    'interview' => 'secondary',
    'accepted' => 'success',
    'rejected' => 'danger',
    'withdrawn' => 'dark',
    'hired' => 'success'
];

$status_names = [
    'pending' => 'Bekliyor',
    'viewed' => 'Görüldü',
    'shortlisted' => 'Kısa Liste',
    'interview' => 'Görüşme',
    'accepted' => 'Kabul Edildi',
    'rejected' => 'Reddedildi',
    'withdrawn' => 'Geri Çekildi',
    'hired' => 'İşe Alındı'
];
?>

<div class="row">
    <div class="col-md-8">
        <!-- Başvuru Bilgileri -->
        <div class="card mb-3">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">Başvuru Bilgileri</h6>
                    <span class="badge bg-<?php echo $status_colors[$application['status']] ?? 'secondary'; ?> fs-6">
                        <?php echo $status_names[$application['status']] ?? $application['status']; ?>
                    </span>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>İş İlanı:</strong></p>
                        <h6 class="text-primary"><?php echo htmlspecialchars($application['job_title']); ?></h6>
                        
                        <p class="mt-3"><strong>Lokasyon:</strong></p>
                        <p><i class="bi bi-geo-alt text-muted me-1"></i><?php echo htmlspecialchars($application['location']); ?></p>
                        
                        <p><strong>Bütçe:</strong></p>
                        <p>
                            <?php if ($application['budget_min'] || $application['budget_max']): ?>
                                <?php if ($application['budget_min'] && $application['budget_max']): ?>
                                    <?php echo formatMoney($application['budget_min']); ?> - <?php echo formatMoney($application['budget_max']); ?>
                                <?php elseif ($application['budget_min']): ?>
                                    <?php echo formatMoney($application['budget_min']); ?>+
                                <?php else: ?>
                                    <?php echo formatMoney($application['budget_max']); ?>'e kadar
                                <?php endif; ?>
                                <small class="text-muted">(<?php echo $application['budget_type']; ?>)</small>
                            <?php else: ?>
                                <span class="text-muted">Görüşülür</span>
                            <?php endif; ?>
                        </p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Başvuru Tarihi:</strong></p>
                        <p><?php echo date('d.m.Y H:i', strtotime($application['applied_at'])); ?></p>
                        
                        <p><strong>Son Güncelleme:</strong></p>
                        <p><?php echo $application['updated_at'] ? date('d.m.Y H:i', strtotime($application['updated_at'])) : 'Hiç'; ?></p>
                        
                        <?php if ($application['message']): ?>
                            <p><strong>Başvuru Mesajı:</strong></p>
                            <div class="bg-light p-3 rounded">
                                <?php echo nl2br(htmlspecialchars($application['message'])); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- İş İlanı Detayları -->
        <?php if ($application['job_description']): ?>
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">İş İlanı Detayları</h6>
                </div>
                <div class="card-body">
                    <?php echo nl2br(htmlspecialchars($application['job_description'])); ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
    
    <div class="col-md-4">
        <!-- Bakıcı Bilgileri -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">Bakıcı Bilgileri</h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-2" 
                         style="width: 60px; height: 60px; font-size: 1.5rem;">
                        <i class="bi bi-person"></i>
                    </div>
                    <h6><?php echo htmlspecialchars($application['caregiver_name']); ?></h6>
                    <?php if ($application['rating'] > 0): ?>
                        <div class="mb-2">
                            <?php echo getRatingStars($application['rating']); ?>
                            <small>(<?php echo number_format($application['rating'], 1); ?> - <?php echo $application['total_reviews']; ?> değerlendirme)</small>
                        </div>
                    <?php endif; ?>
                </div>
                
                <table class="table table-sm">
                    <tr>
                        <td><strong>Email:</strong></td>
                        <td><?php echo htmlspecialchars($application['caregiver_email']); ?></td>
                    </tr>
                    <?php if ($application['caregiver_phone']): ?>
                        <tr>
                            <td><strong>Telefon:</strong></td>
                            <td><?php echo htmlspecialchars($application['caregiver_phone']); ?></td>
                        </tr>
                    <?php endif; ?>
                    <tr>
                        <td><strong>Şehir:</strong></td>
                        <td><?php echo htmlspecialchars($application['caregiver_city']); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Deneyim:</strong></td>
                        <td><?php echo $application['experience_years'] ?? 0; ?> yıl</td>
                    </tr>
                </table>
                
                <?php if ($application['specializations']): ?>
                    <div class="mt-3">
                        <strong>Uzmanlık Alanları:</strong>
                        <div class="mt-2">
                            <?php foreach (explode(',', $application['specializations']) as $spec): ?>
                                <span class="badge bg-light text-dark me-1"><?php echo trim($spec); ?></span>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- Ücret Bilgileri -->
                <?php if ($application['hourly_rate'] || $application['daily_rate'] || $application['monthly_rate']): ?>
                    <div class="mt-3">
                        <strong>Ücret Bilgileri:</strong>
                        <div class="mt-2">
                            <?php if ($application['hourly_rate']): ?>
                                <small class="d-block">Saatlik: <?php echo formatMoney($application['hourly_rate']); ?></small>
                            <?php endif; ?>
                            <?php if ($application['daily_rate']): ?>
                                <small class="d-block">Günlük: <?php echo formatMoney($application['daily_rate']); ?></small>
                            <?php endif; ?>
                            <?php if ($application['monthly_rate']): ?>
                                <small class="d-block">Aylık: <?php echo formatMoney($application['monthly_rate']); ?></small>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <?php if ($application['bio']): ?>
                    <div class="mt-3">
                        <strong>Hakkında:</strong>
                        <p class="small mt-2"><?php echo nl2br(htmlspecialchars(substr($application['bio'], 0, 200))); ?>
                            <?php if (strlen($application['bio']) > 200): ?>...<?php endif; ?>
                        </p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- İşveren Bilgileri -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">İşveren Bilgileri</h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-2" 
                         style="width: 60px; height: 60px; font-size: 1.5rem;">
                        <i class="bi bi-building"></i>
                    </div>
                    <h6><?php echo htmlspecialchars($application['employer_name']); ?></h6>
                </div>
                
                <table class="table table-sm">
                    <tr>
                        <td><strong>Email:</strong></td>
                        <td><?php echo htmlspecialchars($application['employer_email']); ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- İşlem Butonları -->
<div class="mt-3 d-flex justify-content-between">
    <div>
        <?php if ($user_type === 'admin' || $user_id == $application['employer_id']): ?>
            <!-- İşveren veya Admin İşlemleri -->
            <?php if ($application['status'] === 'pending'): ?>
                <button class="btn btn-success me-2" onclick="updateStatus('accepted')">
                    <i class="bi bi-check me-1"></i>Kabul Et
                </button>
                <button class="btn btn-danger me-2" onclick="updateStatus('rejected')">
                    <i class="bi bi-x me-1"></i>Reddet
                </button>
                <button class="btn btn-info me-2" onclick="updateStatus('shortlisted')">
                    <i class="bi bi-star me-1"></i>Kısa Listeye Al
                </button>
            <?php elseif ($application['status'] === 'shortlisted'): ?>
                <button class="btn btn-primary me-2" onclick="updateStatus('interview')">
                    <i class="bi bi-calendar me-1"></i>Görüşmeye Çağır
                </button>
            <?php elseif ($application['status'] === 'accepted'): ?>
                <button class="btn btn-success me-2" onclick="updateStatus('hired')">
                    <i class="bi bi-check-circle me-1"></i>İşe Al
                </button>
            <?php endif; ?>
        <?php endif; ?>
        
        <?php if ($user_id == $application['caregiver_id'] && in_array($application['status'], ['pending', 'viewed'])): ?>
            <!-- Bakıcı İşlemleri -->
            <button class="btn btn-warning me-2" onclick="updateStatus('withdrawn')">
                <i class="bi bi-arrow-counterclockwise me-1"></i>Geri Çek
            </button>
        <?php endif; ?>
        
        <a href="send-message.php?to=<?php echo $user_id == $application['caregiver_id'] ? $application['employer_id'] : $application['caregiver_id']; ?>" 
           class="btn btn-outline-primary">
            <i class="bi bi-chat-dots me-1"></i>Mesaj Gönder
        </a>
    </div>
    
    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Kapat</button>
</div>

<script>
function updateStatus(status) {
    if (confirm('Başvuru durumunu değiştirmek istediğinizden emin misiniz?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'update-application-status.php';
        form.innerHTML = `
            <input type="hidden" name="application_id" value="<?php echo $application['id']; ?>">
            <input type="hidden" name="status" value="${status}">
            <input type="hidden" name="redirect" value="dashboard.php">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
