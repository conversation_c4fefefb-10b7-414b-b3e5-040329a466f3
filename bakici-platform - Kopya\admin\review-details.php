<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Giriş kontrolü
if (!isLoggedIn()) {
    http_response_code(403);
    exit('Unauthorized');
}

// Sadece admin kullanıcıları erişebilir
if ($_SESSION['user_type'] !== 'admin') {
    http_response_code(403);
    exit('Forbidden');
}

$review_id = intval($_GET['id'] ?? 0);
if (!$review_id) {
    exit('Geçersiz değerlendirme ID');
}

// Değerlendirme bilgilerini getir
try {
    $sql = "SELECT r.*,
                   reviewer.full_name as reviewer_name, reviewer.email as reviewer_email, reviewer.phone as reviewer_phone,
                   reviewed.full_name as reviewed_name, reviewed.email as reviewed_email, reviewed.phone as reviewed_phone
            FROM reviews r
            LEFT JOIN users reviewer ON r.reviewer_id = reviewer.id
            LEFT JOIN users reviewed ON r.reviewed_id = reviewed.id
            WHERE r.id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$review_id]);
    $review = $stmt->fetch();
    
    if (!$review) {
        exit('Değerlendirme bulunamadı');
    }
    
} catch (PDOException $e) {
    exit('Veritabanı hatası: ' . $e->getMessage());
}

$service_types = [
    'child_care' => 'Çocuk Bakımı',
    'elderly_care' => 'Yaşlı Bakımı',
    'patient_care' => 'Hasta Bakımı',
    'house_cleaning' => 'Ev Temizliği',
    'companion' => 'Refakatçi',
    'other' => 'Diğer'
];
?>

<div class="row">
    <div class="col-md-6">
        <h6><i class="bi bi-person me-2"></i>Değerlendiren Kişi</h6>
        <div class="card mb-3">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" 
                         style="width: 50px; height: 50px;">
                        <i class="bi bi-person"></i>
                    </div>
                    <div>
                        <h6 class="mb-1"><?php echo htmlspecialchars($review['reviewer_name']); ?></h6>
                        <small class="text-muted"><?php echo htmlspecialchars($review['reviewer_email']); ?></small>
                    </div>
                </div>
                <?php if ($review['reviewer_phone']): ?>
                    <p class="mb-2">
                        <i class="bi bi-telephone me-2"></i>
                        <a href="tel:<?php echo $review['reviewer_phone']; ?>"><?php echo htmlspecialchars($review['reviewer_phone']); ?></a>
                    </p>
                <?php endif; ?>
                <div class="d-grid gap-2">
                    <a href="user-details.php?id=<?php echo $review['reviewer_id']; ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-eye me-2"></i>Profili Görüntüle
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <h6><i class="bi bi-star me-2"></i>Değerlendirilen Kişi</h6>
        <div class="card mb-3">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3" 
                         style="width: 50px; height: 50px;">
                        <i class="bi bi-person"></i>
                    </div>
                    <div>
                        <h6 class="mb-1"><?php echo htmlspecialchars($review['reviewed_name']); ?></h6>
                        <small class="text-muted"><?php echo htmlspecialchars($review['reviewed_email']); ?></small>
                    </div>
                </div>
                <?php if ($review['reviewed_phone']): ?>
                    <p class="mb-2">
                        <i class="bi bi-telephone me-2"></i>
                        <a href="tel:<?php echo $review['reviewed_phone']; ?>"><?php echo htmlspecialchars($review['reviewed_phone']); ?></a>
                    </p>
                <?php endif; ?>
                <div class="d-grid gap-2">
                    <a href="user-details.php?id=<?php echo $review['reviewed_id']; ?>" target="_blank" class="btn btn-sm btn-outline-success">
                        <i class="bi bi-eye me-2"></i>Profili Görüntüle
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <h6><i class="bi bi-info-circle me-2"></i>Değerlendirme Detayları</h6>
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Değerlendirme ID:</strong></td>
                                <td>#<?php echo $review['id']; ?></td>
                            </tr>
                            <tr>
                                <td><strong>Hizmet Türü:</strong></td>
                                <td>
                                    <?php if ($review['service_type']): ?>
                                        <span class="badge bg-info"><?php echo $service_types[$review['service_type']] ?? $review['service_type']; ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">Belirtilmemiş</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Puan:</strong></td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="me-2 fw-bold"><?php echo $review['rating']; ?>/5</span>
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <?php if ($i <= $review['rating']): ?>
                                                <i class="bi bi-star-fill text-warning"></i>
                                            <?php else: ?>
                                                <i class="bi bi-star text-muted"></i>
                                            <?php endif; ?>
                                        <?php endfor; ?>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Durum:</strong></td>
                                <td>
                                    <span class="badge bg-<?php echo $review['is_active'] ? 'success' : 'secondary'; ?>">
                                        <?php echo $review['is_active'] ? 'Aktif' : 'Pasif'; ?>
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Oluşturulma:</strong></td>
                                <td><?php echo date('d.m.Y H:i', strtotime($review['created_at'])); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Güncellenme:</strong></td>
                                <td>
                                    <?php if ($review['updated_at']): ?>
                                        <?php echo date('d.m.Y H:i', strtotime($review['updated_at'])); ?>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Hizmet Tarihi:</strong></td>
                                <td>
                                    <?php if ($review['service_date']): ?>
                                        <?php echo date('d.m.Y', strtotime($review['service_date'])); ?>
                                    <?php else: ?>
                                        <span class="text-muted">Belirtilmemiş</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Tavsiye Eder:</strong></td>
                                <td>
                                    <?php if (isset($review['would_recommend'])): ?>
                                        <span class="badge bg-<?php echo $review['would_recommend'] ? 'success' : 'warning'; ?>">
                                            <?php echo $review['would_recommend'] ? 'Evet' : 'Hayır'; ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="text-muted">Belirtilmemiş</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if ($review['comment']): ?>
<div class="row mt-3">
    <div class="col-12">
        <h6><i class="bi bi-chat-quote me-2"></i>Yorum</h6>
        <div class="card">
            <div class="card-body">
                <blockquote class="blockquote mb-0">
                    <p><?php echo nl2br(htmlspecialchars($review['comment'])); ?></p>
                    <footer class="blockquote-footer mt-2">
                        <cite title="<?php echo htmlspecialchars($review['reviewer_name']); ?>">
                            <?php echo htmlspecialchars($review['reviewer_name']); ?>
                        </cite>
                        <small class="text-muted">
                            - <?php echo date('d.m.Y', strtotime($review['created_at'])); ?>
                        </small>
                    </footer>
                </blockquote>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="row mt-4">
    <div class="col-12">
        <div class="d-flex justify-content-between">
            <div>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x me-2"></i>Kapat
                </button>
            </div>
            <div>
                <button type="button" class="btn btn-<?php echo $review['is_active'] ? 'warning' : 'success'; ?>" 
                        onclick="toggleReviewStatus(<?php echo $review['id']; ?>)">
                    <i class="bi bi-<?php echo $review['is_active'] ? 'pause' : 'play'; ?> me-2"></i>
                    <?php echo $review['is_active'] ? 'Pasifleştir' : 'Aktifleştir'; ?>
                </button>
                <button type="button" class="btn btn-danger" onclick="deleteReview(<?php echo $review['id']; ?>)">
                    <i class="bi bi-trash me-2"></i>Sil
                </button>
            </div>
        </div>
    </div>
</div>
