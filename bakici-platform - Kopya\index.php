<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// İstatistikleri al
$stats = [];
try {
    $stats['total_caregivers'] = $db->query("SELECT COUNT(*) FROM users WHERE user_type = 'caregiver' AND is_active = 1")->fetchColumn();
    $stats['total_families'] = $db->query("SELECT COUNT(*) FROM users WHERE user_type = 'family' AND is_active = 1")->fetchColumn();
    $stats['total_jobs'] = $db->query("SELECT COUNT(*) FROM job_listings WHERE status = 'active'")->fetchColumn();
    $stats['total_reviews'] = $db->query("SELECT COUNT(*) FROM reviews WHERE is_approved = 1")->fetchColumn();
} catch (PDOException $e) {
    $stats = ['total_caregivers' => 0, 'total_families' => 0, 'total_jobs' => 0, 'total_reviews' => 0];
}

// Son eklenen bakıcıları al
$recent_caregivers = [];
try {
    $sql = "SELECT u.id, u.full_name, u.city, u.profile_photo, 
                   cp.experience_years, cp.hourly_rate, cp.rating, cp.total_reviews, cp.specializations
            FROM users u 
            LEFT JOIN caregiver_profiles cp ON u.id = cp.user_id 
            WHERE u.user_type = 'caregiver' AND u.is_active = 1 AND u.is_verified = 1
            ORDER BY u.created_at DESC 
            LIMIT 8";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $recent_caregivers = $stmt->fetchAll();
} catch (PDOException $e) {
    $recent_caregivers = [];
}

// Son iş ilanlarını al
$recent_jobs = [];
try {
    $sql = "SELECT jl.id, jl.title, jl.slug, jl.job_type, jl.care_type, jl.location_city, jl.location_district,
                   jl.budget_min, jl.budget_max, jl.budget_type, jl.is_urgent, jl.created_at,
                   u.full_name as employer_name, c.name as category_name, c.icon as category_icon
            FROM job_listings jl
            JOIN users u ON jl.user_id = u.id
            LEFT JOIN categories c ON jl.category_id = c.id
            WHERE jl.status = 'active' AND (jl.expires_at IS NULL OR jl.expires_at > NOW())
            ORDER BY jl.is_urgent DESC, jl.created_at DESC
            LIMIT 6";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $recent_jobs = $stmt->fetchAll();
} catch (PDOException $e) {
    $recent_jobs = [];
}

// Kategorileri al
$categories = getCategories();

$page_title = getSetting('site_name', 'Bakıcı Platform') . ' - ' . getSetting('site_description', 'Güvenilir bakıcı bulmanın en kolay yolu');
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo escape($page_title); ?></title>
    <meta name="description" content="<?php echo escape(getSetting('site_description', 'Güvenilir çocuk bakıcısı, yaşlı bakıcısı, hasta bakıcısı ve ev yardımcısı bulun. Binlerce doğrulanmış bakıcı profili ile güvenli platform.')); ?>">
    <meta name="keywords" content="<?php echo escape(getSetting('site_keywords', 'bakıcı, çocuk bakıcısı, yaşlı bakıcısı, hasta bakıcısı, ev yardımcısı, güvenilir bakıcı')); ?>">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #ff6b6b;
            --primary-dark: #e55555;
            --secondary-color: #4ecdc4;
            --accent-color: #45b7d1;
            --warning-color: #feca57;
            --success-color: #48dbfb;
            --text-dark: #2c3e50;
            --text-light: #7f8c8d;
            --bg-light: #f8f9fa;
            --white: #ffffff;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-dark);
        }

        .hero-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 50%, var(--secondary-color) 100%);
            color: white;
            padding: 120px 0 80px;
            position: relative;
            overflow: hidden;
            min-height: 100vh;
            display: flex;
            align-items: center;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" fill="white" opacity="0.1"><path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25"/><path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5"/><path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z"/></svg>');
            background-size: cover;
            background-position: bottom;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .search-box {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            position: relative;
            z-index: 2;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .feature-card {
            border: none;
            border-radius: 25px;
            padding: 50px 30px;
            height: 100%;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            background: var(--white);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0,0,0,0.2);
        }

        .feature-card:hover::before {
            transform: scaleX(1);
        }

        .stats-section {
            background: linear-gradient(135deg, var(--bg-light) 0%, rgba(78, 205, 196, 0.1) 100%);
            padding: 100px 0;
            position: relative;
        }

        .stat-item {
            text-align: center;
            padding: 40px 20px;
            background: var(--white);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            height: 100%;
        }

        .stat-item:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .stat-number {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 15px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-dark);
            margin: 0;
        }
        
        .caregiver-card {
            border: none;
            border-radius: 25px;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            height: 100%;
            background: var(--white);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            position: relative;
        }

        .caregiver-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1;
        }

        .caregiver-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0,0,0,0.2);
        }

        .caregiver-card:hover::before {
            opacity: 0.05;
        }

        .caregiver-card .card-body {
            position: relative;
            z-index: 2;
        }

        .job-card {
            border: none;
            border-radius: 20px;
            transition: all 0.3s ease;
            background: var(--white);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .job-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
        }

        .job-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .urgent-job::before {
            background: linear-gradient(180deg, var(--warning-color), #ff6b6b) !important;
        }

        .urgent-job {
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.05), var(--white));
        }
        
        .rating-stars {
            color: var(--warning-color);
        }

        .category-card {
            border: none;
            border-radius: 25px;
            padding: 40px 20px;
            text-align: center;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            background: var(--white);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-decoration: none;
            color: inherit;
            position: relative;
            overflow: hidden;
        }

        .category-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .category-card:hover {
            transform: translateY(-10px) scale(1.05);
            box-shadow: 0 25px 50px rgba(0,0,0,0.2);
            color: var(--white);
            text-decoration: none;
        }

        .category-card:hover::before {
            opacity: 1;
        }

        .category-card > * {
            position: relative;
            z-index: 2;
        }

        .category-icon {
            width: 90px;
            height: 90px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 25px;
            font-size: 2.5rem;
            color: var(--white);
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }

        .category-card:hover .category-icon {
            background: var(--white);
            color: var(--primary-color);
            transform: scale(1.1);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: none;
            border-radius: 50px;
            padding: 15px 35px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(255, 107, 107, 0.4);
        }

        .btn-outline-light {
            border: 2px solid rgba(255, 255, 255, 0.8);
            border-radius: 50px;
            padding: 15px 35px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .btn-outline-light:hover {
            background: var(--white);
            color: var(--primary-color);
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(255, 255, 255, 0.3);
        }
        
        .navbar-brand {
            font-weight: 800;
            font-size: 1.8rem;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 5px 25px rgba(0,0,0,0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .nav-link {
            font-weight: 500;
            color: var(--text-dark) !important;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-link:hover::before {
            width: 100%;
        }

        .section-title {
            text-align: center;
            margin-bottom: 80px;
        }

        .section-title h2 {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 25px;
            background: linear-gradient(135deg, var(--text-dark), var(--primary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .section-title p {
            font-size: 1.3rem;
            color: var(--text-light);
            max-width: 600px;
            margin: 0 auto;
        }

        .hero-title {
            font-size: 4rem;
            font-weight: 900;
            line-height: 1.2;
            margin-bottom: 30px;
            text-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .hero-subtitle {
            font-size: 1.4rem;
            font-weight: 400;
            margin-bottom: 40px;
            opacity: 0.9;
            line-height: 1.6;
        }

        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            overflow: hidden;
            pointer-events: none;
        }

        .floating-elements::before,
        .floating-elements::after {
            content: '';
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 6s ease-in-out infinite;
        }

        .floating-elements::before {
            width: 200px;
            height: 200px;
            top: 20%;
            right: 10%;
            animation-delay: -2s;
        }

        .floating-elements::after {
            width: 150px;
            height: 150px;
            bottom: 20%;
            left: 10%;
            animation-delay: -4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        /* Form Styles */
        .form-select, .form-control {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 12px 16px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: var(--white);
        }

        .form-select:focus, .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(255, 107, 107, 0.25);
            transform: translateY(-2px);
        }

        .form-label {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-dark);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.2rem;
            }

            .search-box {
                padding: 30px 20px;
                margin-top: 40px;
            }

            .section-title h2 {
                font-size: 2.2rem;
            }

            .stat-number {
                font-size: 2.5rem;
            }

            .feature-card {
                padding: 30px 20px;
            }

            .category-icon {
                width: 70px;
                height: 70px;
                font-size: 2rem;
            }
        }

        @media (max-width: 576px) {
            .hero-title {
                font-size: 2rem;
            }

            .btn-lg {
                padding: 12px 25px;
                font-size: 1rem;
            }

            .search-box {
                padding: 25px 15px;
            }
        }

        /* Smooth Scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Loading Animation */
        .loading {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        .loading.loaded {
            opacity: 1;
            transform: translateY(0);
        }

        /* Gradient Text */
        .gradient-text {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
        }

        /* CTA Section Styles */
        .cta-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 50%, var(--secondary-color) 100%);
            color: white;
            padding: 120px 0;
            position: relative;
            overflow: hidden;
        }

        .cta-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" fill="white" opacity="0.1"><path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25"/></svg>');
            background-size: cover;
            background-position: bottom;
        }

        .cta-content {
            position: relative;
            z-index: 2;
        }

        .cta-icon {
            font-size: 4rem;
            margin-bottom: 30px;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        .cta-title {
            font-size: 3.5rem;
            font-weight: 900;
            margin-bottom: 30px;
            text-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .cta-subtitle {
            font-size: 1.4rem;
            font-weight: 400;
            opacity: 0.95;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto 50px;
        }

        .cta-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 30px;
            padding: 50px 30px;
            text-align: center;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
            height: 100%;
        }

        .cta-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .cta-card:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow: 0 30px 60px rgba(0,0,0,0.3);
            background: rgba(255, 255, 255, 1);
        }

        .cta-card:hover::before {
            transform: scaleX(1);
        }

        .cta-card-icon {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            font-size: 3rem;
            color: var(--white);
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }

        .cta-card:hover .cta-card-icon {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }

        .cta-card-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 20px;
            color: var(--text-dark);
        }

        .cta-card-text {
            font-size: 1.1rem;
            color: var(--text-light);
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .cta-features {
            list-style: none;
            padding: 0;
            margin: 0 0 40px 0;
        }

        .cta-features li {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            font-size: 1rem;
            color: var(--text-dark);
            font-weight: 500;
        }

        .cta-features li i {
            color: var(--success-color);
            margin-right: 12px;
            font-size: 1.2rem;
        }

        .btn-cta-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: none;
            border-radius: 50px;
            padding: 18px 40px;
            font-weight: 700;
            font-size: 1.1rem;
            color: var(--white);
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.4);
            text-decoration: none;
            display: inline-block;
        }

        .btn-cta-primary:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(255, 107, 107, 0.5);
            color: var(--white);
        }

        .btn-cta-secondary {
            background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
            border: none;
            border-radius: 50px;
            padding: 18px 40px;
            font-weight: 700;
            font-size: 1.1rem;
            color: var(--white);
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(78, 205, 196, 0.4);
            text-decoration: none;
            display: inline-block;
        }

        .btn-cta-secondary:hover {
            background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(78, 205, 196, 0.5);
            color: var(--white);
        }

        .trust-indicators {
            margin-top: 60px;
            padding-top: 40px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        .trust-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 15px 25px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            margin: 10px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .trust-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-3px);
        }

        .trust-item i {
            font-size: 1.5rem;
        }

        .trust-item span {
            font-weight: 600;
            font-size: 0.95rem;
        }

        /* CTA Mobile Responsive */
        @media (max-width: 768px) {
            .cta-section {
                padding: 80px 0;
            }

            .cta-title {
                font-size: 2.5rem;
            }

            .cta-subtitle {
                font-size: 1.2rem;
                margin-bottom: 40px;
            }

            .cta-card {
                padding: 40px 25px;
                margin-bottom: 30px;
            }

            .cta-card-icon {
                width: 80px;
                height: 80px;
                font-size: 2.5rem;
                margin-bottom: 25px;
            }

            .cta-card-title {
                font-size: 1.5rem;
            }

            .cta-card-text {
                font-size: 1rem;
            }

            .trust-indicators {
                margin-top: 40px;
                padding-top: 30px;
            }

            .trust-item {
                margin: 8px 5px;
                padding: 12px 20px;
                font-size: 0.9rem;
            }

            .trust-item i {
                font-size: 1.3rem;
            }
        }

        @media (max-width: 576px) {
            .cta-title {
                font-size: 2rem;
            }

            .cta-subtitle {
                font-size: 1.1rem;
            }

            .cta-card {
                padding: 30px 20px;
            }

            .cta-card-icon {
                width: 70px;
                height: 70px;
                font-size: 2rem;
            }

            .btn-cta-primary,
            .btn-cta-secondary {
                padding: 15px 30px;
                font-size: 1rem;
            }

            .trust-item {
                margin: 5px 3px;
                padding: 10px 15px;
                font-size: 0.85rem;
            }

            .trust-item span {
                font-size: 0.85rem;
            }
        }

        /* Modern Footer Styles */
        .modern-footer {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
            color: var(--white);
            position: relative;
            overflow: hidden;
        }

        .modern-footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" fill="white" opacity="0.05"><path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z"/></svg>');
            background-size: cover;
            background-position: top;
        }

        /* Newsletter Section */
        .newsletter-section {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            padding: 60px 0;
            position: relative;
            z-index: 2;
        }

        .newsletter-title {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 15px;
            color: var(--white);
        }

        .newsletter-subtitle {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 0;
        }

        .newsletter-form {
            max-width: 500px;
            margin-left: auto;
        }

        .newsletter-input {
            border: none;
            border-radius: 50px 0 0 50px;
            padding: 15px 25px;
            font-size: 1.1rem;
            background: var(--white);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .newsletter-input:focus {
            box-shadow: 0 5px 25px rgba(0,0,0,0.2);
            border: none;
        }

        .btn-newsletter {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            border: none;
            border-radius: 0 50px 50px 0;
            padding: 15px 30px;
            font-weight: 600;
            color: var(--white);
            transition: all 0.3s ease;
        }

        .btn-newsletter:hover {
            background: linear-gradient(135deg, #c0392b, #e74c3c);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
            color: var(--white);
        }

        .newsletter-privacy {
            color: rgba(255, 255, 255, 0.8);
            margin-top: 10px;
            display: block;
        }

        /* Footer Main */
        .footer-main {
            padding: 80px 0 40px;
            position: relative;
            z-index: 2;
        }

        .footer-brand {
            margin-bottom: 30px;
        }

        .brand-title {
            font-size: 2rem;
            font-weight: 800;
            margin-bottom: 20px;
        }

        .brand-description {
            font-size: 1.1rem;
            line-height: 1.7;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 30px;
        }

        .download-title, .social-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--white);
        }

        .app-store-btn {
            transition: all 0.3s ease;
            display: inline-block;
        }

        .app-store-btn:hover {
            transform: translateY(-3px);
            filter: brightness(1.1);
        }

        .social-links {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .social-link {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
            font-size: 1.2rem;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .social-link.facebook { background: #3b5998; }
        .social-link.twitter { background: #1da1f2; }
        .social-link.instagram { background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888); }
        .social-link.linkedin { background: #0077b5; }
        .social-link.youtube { background: #ff0000; }
        .social-link.whatsapp { background: #25d366; }

        .social-link:hover {
            transform: translateY(-5px) scale(1.1);
            box-shadow: 0 10px 25px rgba(0,0,0,0.3);
            color: var(--white);
        }

        /* Footer Links */
        .footer-links {
            margin-bottom: 30px;
        }

        .links-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 25px;
            color: var(--white);
            position: relative;
        }

        .links-title::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 0;
            width: 40px;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-radius: 2px;
        }

        .links-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .links-list li {
            margin-bottom: 12px;
        }

        .links-list a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            font-size: 1rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }

        .links-list a i {
            font-size: 0.8rem;
            margin-right: 8px;
            transition: all 0.3s ease;
        }

        .links-list a:hover {
            color: var(--primary-color);
            transform: translateX(5px);
        }

        .links-list a:hover i {
            color: var(--secondary-color);
        }

        /* Footer Contact */
        .footer-contact {
            margin-bottom: 30px;
        }

        .contact-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 25px;
            color: var(--white);
            position: relative;
        }

        .contact-title::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 0;
            width: 40px;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-radius: 2px;
        }

        .contact-info {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .contact-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }

        .contact-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
            font-size: 1.1rem;
            flex-shrink: 0;
        }

        .contact-details {
            display: flex;
            flex-direction: column;
        }

        .contact-details strong {
            color: var(--white);
            font-weight: 600;
            margin-bottom: 5px;
        }

        .contact-details span {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.95rem;
            line-height: 1.4;
        }

        /* Footer Bottom */
        .footer-bottom {
            background: rgba(0, 0, 0, 0.3);
            padding: 30px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            z-index: 2;
        }

        .copyright p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1rem;
            margin: 0;
        }

        .footer-legal {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 15px;
        }

        .legal-links {
            display: flex;
            gap: 20px;
            list-style: none;
            margin: 0;
            padding: 0;
            flex-wrap: wrap;
            justify-content: flex-end;
        }

        .legal-links a {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .legal-links a:hover {
            color: var(--primary-color);
        }

        .security-badges {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .security-badge {
            background: rgba(255, 255, 255, 0.1);
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.85rem;
            color: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .security-badge:hover {
            background: rgba(255, 255, 255, 0.2);
            color: var(--white);
            transform: translateY(-2px);
        }

        /* Mobile Responsive for Footer */
        @media (max-width: 768px) {
            .newsletter-section {
                padding: 40px 0;
                text-align: center;
            }

            .newsletter-title {
                font-size: 2rem;
            }

            .newsletter-subtitle {
                font-size: 1rem;
                margin-bottom: 30px;
            }

            .newsletter-form {
                margin: 0;
            }

            .newsletter-input {
                border-radius: 25px;
                margin-bottom: 15px;
            }

            .btn-newsletter {
                border-radius: 25px;
                width: 100%;
            }

            .footer-main {
                padding: 50px 0 30px;
            }

            .footer-brand {
                text-align: center;
                margin-bottom: 40px;
            }

            .brand-title {
                font-size: 1.8rem;
            }

            .social-links {
                justify-content: center;
            }

            .app-download {
                text-align: center;
            }

            .footer-legal {
                align-items: center;
                text-align: center;
            }

            .legal-links {
                justify-content: center;
                flex-direction: column;
                gap: 10px;
            }

            .security-badges {
                justify-content: center;
            }

            .contact-info {
                gap: 15px;
            }

            .contact-item {
                gap: 12px;
            }

            .contact-icon {
                width: 35px;
                height: 35px;
                font-size: 1rem;
            }
        }

        @media (max-width: 576px) {
            .newsletter-title {
                font-size: 1.8rem;
            }

            .brand-title {
                font-size: 1.5rem;
            }

            .links-title, .contact-title {
                font-size: 1.2rem;
            }

            .social-link {
                width: 40px;
                height: 40px;
                font-size: 1.1rem;
            }

            .footer-bottom {
                padding: 20px 0;
            }

            .legal-links {
                gap: 15px;
            }

            .security-badges {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="index.php">
                <i class="bi bi-heart-fill me-2"></i><?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="caregivers.php">Bakıcı Ara</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="jobs.php">İş İlanları</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">Hakkımızda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">İletişim</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle me-1"></i><?php echo escape(safeArray($_SESSION, 'full_name', 'Kullanıcı')); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="dashboard.php"><i class="bi bi-speedometer2 me-2"></i>Dashboard</a></li>
                                <li><a class="dropdown-item" href="profile.php"><i class="bi bi-person me-2"></i>Profil</a></li>
                                <li><a class="dropdown-item" href="messages.php"><i class="bi bi-chat-dots me-2"></i>Mesajlar</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="auth/logout.php"><i class="bi bi-box-arrow-right me-2"></i>Çıkış</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="auth/login.php">Giriş Yap</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-primary ms-2" href="auth/register.php">Üye Ol</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Alert Messages -->
    <?php if (isset($_GET['success'])): ?>
        <div class="container mt-3">
            <div class="alert alert-success alert-dismissible fade show">
                <i class="bi bi-check-circle me-2"></i>
                <?php
                $success_messages = [
                    'registration_complete' => 'Kayıt işleminiz başarıyla tamamlandı. Hoş geldiniz!',
                    'login_success' => 'Başarıyla giriş yaptınız.',
                    'logout_success' => 'Güvenli bir şekilde çıkış yaptınız.',
                    'contact_sent' => 'Mesajınız başarıyla gönderildi. En kısa sürede dönüş yapacağız.',
                    'password_reset_sent' => 'Şifre sıfırlama bağlantısı e-posta adresinize gönderildi.'
                ];
                echo $success_messages[$_GET['success']] ?? 'İşlem başarıyla tamamlandı.';
                ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    <?php endif; ?>

    <?php if (isset($_GET['error'])): ?>
        <div class="container mt-3">
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="bi bi-exclamation-triangle me-2"></i>
                <?php
                $error_messages = [
                    'login_failed' => 'Giriş bilgileri hatalı. Lütfen tekrar deneyin.',
                    'registration_failed' => 'Kayıt işlemi başarısız. Lütfen bilgilerinizi kontrol edin.',
                    'email_exists' => 'Bu e-posta adresi zaten kullanılıyor.',
                    'invalid_token' => 'Geçersiz veya süresi dolmuş bağlantı.',
                    'access_denied' => 'Bu sayfaya erişim yetkiniz yok.',
                    'session_expired' => 'Oturumunuzun süresi doldu. Lütfen tekrar giriş yapın.'
                ];
                echo $error_messages[$_GET['error']] ?? 'Bir hata oluştu.';
                ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    <?php endif; ?>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="floating-elements"></div>
        <div class="container">
            <div class="row align-items-center min-vh-100">
                <div class="col-lg-6">
                    <div class="hero-content">
                        <h1 class="hero-title">Güvenilir Bakıcı Bulmanın <span style="color: var(--secondary-color);">En Kolay</span> Yolu</h1>
                        <p class="hero-subtitle">Çocuk, yaşlı ve hasta bakımı için doğrulanmış, deneyimli bakıcılarla tanışın. Güvenli platform, kolay iletişim, şeffaf değerlendirmeler ile hayatınızı kolaylaştırın.</p>
                        <div class="d-flex gap-4 flex-wrap mb-5">
                            <a href="caregivers.php" class="btn btn-light btn-lg">
                                <i class="bi bi-search me-2"></i>Bakıcı Ara
                            </a>
                            <a href="job-application.php" class="btn btn-outline-light btn-lg">
                                <i class="bi bi-plus-circle me-2"></i>İlan Ver
                            </a>
                        </div>

                        <!-- Quick Stats -->
                        <div class="row g-3">
                            <div class="col-4">
                                <div class="text-center">
                                    <div class="h4 fw-bold mb-1"><?php echo number_format($stats['total_caregivers']); ?>+</div>
                                    <small class="opacity-75">Doğrulanmış Bakıcı</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-center">
                                    <div class="h4 fw-bold mb-1"><?php echo number_format($stats['total_families']); ?>+</div>
                                    <small class="opacity-75">Mutlu Aile</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-center">
                                    <div class="h4 fw-bold mb-1"><?php echo number_format($stats['total_reviews']); ?>+</div>
                                    <small class="opacity-75">Memnun Kullanıcı</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="search-box">
                        <div class="text-center mb-4">
                            <h4 class="text-dark mb-2"><i class="bi bi-search me-2 text-primary"></i>Hızlı Arama</h4>
                            <p class="text-muted mb-0">İhtiyacınıza uygun bakıcıyı hemen bulun</p>
                        </div>
                        <form action="caregivers.php" method="GET">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label text-dark fw-semibold">
                                        <i class="bi bi-heart me-1 text-primary"></i>Bakım Türü
                                    </label>
                                    <select name="job_type" class="form-select form-select-lg">
                                        <option value="">Tümü</option>
                                        <option value="child_care">👶 Çocuk Bakımı</option>
                                        <option value="elderly_care">👴 Yaşlı Bakımı</option>
                                        <option value="patient_care">🏥 Hasta Bakımı</option>
                                        <option value="house_cleaning">🏠 Ev Temizliği</option>
                                        <option value="companion">🤝 Refakatçi</option>
                                        <option value="other">📋 Diğer</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label text-dark fw-semibold">
                                        <i class="bi bi-geo-alt me-1 text-primary"></i>Şehir
                                    </label>
                                    <select name="city" class="form-select form-select-lg">
                                        <option value="">Tümü</option>
                                        <option value="İstanbul">🏙️ İstanbul</option>
                                        <option value="Ankara">🏛️ Ankara</option>
                                        <option value="İzmir">🌊 İzmir</option>
                                        <option value="Bursa">🌿 Bursa</option>
                                        <option value="Antalya">☀️ Antalya</option>
                                        <option value="Adana">🌾 Adana</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label text-dark fw-semibold">
                                        <i class="bi bi-clock me-1 text-primary"></i>Çalışma Şekli
                                    </label>
                                    <select name="care_type" class="form-select form-select-lg">
                                        <option value="">Tümü</option>
                                        <option value="live_in">🏠 Yatılı</option>
                                        <option value="daily">📅 Günlük</option>
                                        <option value="hourly">⏰ Saatlik</option>
                                        <option value="weekly">📆 Haftalık</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label text-dark fw-semibold">
                                        <i class="bi bi-wallet me-1 text-primary"></i>Bütçe (Aylık)
                                    </label>
                                    <select name="budget" class="form-select form-select-lg">
                                        <option value="">Tümü</option>
                                        <option value="0-3000">💰 3.000 TL'ye kadar</option>
                                        <option value="3000-5000">💰 3.000 - 5.000 TL</option>
                                        <option value="5000-8000">💰 5.000 - 8.000 TL</option>
                                        <option value="8000-12000">💰 8.000 - 12.000 TL</option>
                                        <option value="12000+">💰 12.000 TL üzeri</option>
                                    </select>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary w-100 btn-lg">
                                        <i class="bi bi-search me-2"></i>Bakıcı Ara
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats-section">
        <div class="container">
            <div class="section-title">
                <h2>Rakamlarla Güven</h2>
                <p>Binlerce mutlu kullanıcımızın tercihi</p>
            </div>
            <div class="row g-4">
                <div class="col-md-3 col-sm-6">
                    <div class="stat-item">
                        <div class="mb-3">
                            <i class="bi bi-shield-check" style="font-size: 3rem; color: var(--primary-color);"></i>
                        </div>
                        <div class="stat-number"><?php echo number_format($stats['total_caregivers']); ?>+</div>
                        <p class="stat-label">Doğrulanmış Bakıcı</p>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="stat-item">
                        <div class="mb-3">
                            <i class="bi bi-heart-fill" style="font-size: 3rem; color: var(--secondary-color);"></i>
                        </div>
                        <div class="stat-number"><?php echo number_format($stats['total_families']); ?>+</div>
                        <p class="stat-label">Mutlu Aile</p>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="stat-item">
                        <div class="mb-3">
                            <i class="bi bi-briefcase-fill" style="font-size: 3rem; color: var(--warning-color);"></i>
                        </div>
                        <div class="stat-number"><?php echo number_format($stats['total_jobs']); ?>+</div>
                        <p class="stat-label">Aktif İş İlanı</p>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="stat-item">
                        <div class="mb-3">
                            <i class="bi bi-star-fill" style="font-size: 3rem; color: var(--success-color);"></i>
                        </div>
                        <div class="stat-number"><?php echo number_format($stats['total_reviews']); ?>+</div>
                        <p class="stat-label">Memnuniyet Yorumu</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Categories Section -->
    <?php if (!empty($categories)): ?>
    <section class="py-5">
        <div class="container">
            <div class="section-title">
                <h2>Hizmet Kategorileri</h2>
                <p>İhtiyacınıza uygun bakım hizmetini seçin</p>
            </div>

            <div class="row g-4">
                <?php foreach (array_slice($categories, 0, 6) as $category): ?>
                <div class="col-md-4 col-lg-2">
                    <a href="caregivers.php?category=<?php echo $category['slug']; ?>" class="category-card d-block">
                        <div class="category-icon" style="background-color: <?php echo $category['color']; ?>">
                            <i class="<?php echo $category['icon']; ?>"></i>
                        </div>
                        <h6 class="mb-0"><?php echo escape($category['name']); ?></h6>
                    </a>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Features Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="section-title">
                <h2>Neden <?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?>?</h2>
                <p>Güvenli, hızlı ve kolay bakıcı bulma deneyimi</p>
            </div>

            <div class="row g-4">
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <div class="text-primary mb-4">
                            <i class="bi bi-shield-check" style="font-size: 4rem;"></i>
                        </div>
                        <h5 class="mb-3">Güvenilir Bakıcılar</h5>
                        <p class="text-muted">Tüm bakıcılarımız kimlik ve adli sicil kontrolünden geçer. Referansları doğrulanır ve sürekli değerlendirilir.</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <div class="text-success mb-4">
                            <i class="bi bi-chat-dots" style="font-size: 4rem;"></i>
                        </div>
                        <h5 class="mb-3">Güvenli İletişim</h5>
                        <p class="text-muted">Platform üzerinden güvenli mesajlaşma. İletişim bilgilerine kontrollü erişim ve gizlilik koruması.</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <div class="text-warning mb-4">
                            <i class="bi bi-star-fill" style="font-size: 4rem;"></i>
                        </div>
                        <h5 class="mb-3">Şeffaf Değerlendirme</h5>
                        <p class="text-muted">Gerçek kullanıcı yorumları ve puanları ile doğru seçim yapın. Detaylı profil bilgileri ve referanslar.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Recent Caregivers -->
    <?php if (!empty($recent_caregivers)): ?>
    <section class="py-5">
        <div class="container">
            <div class="row mb-4">
                <div class="col-lg-8">
                    <h3 class="fw-bold">Öne Çıkan Bakıcılar</h3>
                    <p class="text-muted">Deneyimli ve güvenilir bakıcılarımızla tanışın</p>
                </div>
                <div class="col-lg-4 text-end">
                    <a href="caregivers.php" class="btn btn-outline-primary">
                        Tümünü Gör <i class="bi bi-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>

            <div class="row g-4">
                <?php foreach (array_slice($recent_caregivers, 0, 4) as $caregiver): ?>
                <div class="col-md-6 col-lg-3">
                    <div class="caregiver-card">
                        <div class="card-body text-center p-4">
                            <div class="mb-3">
                                <?php if ($caregiver['profile_photo']): ?>
                                    <img src="<?php echo UPLOAD_URL . 'profiles/' . $caregiver['profile_photo']; ?>"
                                         class="rounded-circle" width="100" height="100" style="object-fit: cover;">
                                <?php else: ?>
                                    <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center"
                                         style="width: 100px; height: 100px;">
                                        <i class="bi bi-person-fill text-white" style="font-size: 2.5rem;"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <h6 class="card-title mb-2"><?php echo escape($caregiver['full_name']); ?></h6>
                            <p class="text-muted small mb-2">
                                <i class="bi bi-geo-alt me-1"></i><?php echo escape($caregiver['city']); ?>
                            </p>
                            <?php if ($caregiver['experience_years']): ?>
                                <p class="text-muted small mb-2">
                                    <i class="bi bi-briefcase me-1"></i><?php echo $caregiver['experience_years']; ?> yıl deneyim
                                </p>
                            <?php endif; ?>
                            <?php if ($caregiver['rating'] > 0): ?>
                            <div class="mb-3">
                                <?php echo getRatingStars($caregiver['rating']); ?>
                                <small class="text-muted ms-1">(<?php echo $caregiver['total_reviews']; ?>)</small>
                            </div>
                            <?php endif; ?>
                            <p class="text-primary fw-bold mb-3">
                                <?php echo $caregiver['hourly_rate'] ? formatMoney($caregiver['hourly_rate']) . '/saat' : 'Fiyat Görüşülür'; ?>
                            </p>
                            <a href="caregiver-profile.php?id=<?php echo $caregiver['id']; ?>" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-eye me-1"></i>Profili Gör
                            </a>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Recent Jobs -->
    <?php if (!empty($recent_jobs)): ?>
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row mb-4">
                <div class="col-lg-8">
                    <h3 class="fw-bold">Son İş İlanları</h3>
                    <p class="text-muted">Yeni iş fırsatlarını kaçırmayın</p>
                </div>
                <div class="col-lg-4 text-end">
                    <a href="jobs.php" class="btn btn-outline-primary">
                        Tümünü Gör <i class="bi bi-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>

            <div class="row g-4">
                <?php foreach (array_slice($recent_jobs, 0, 3) as $job): ?>
                <div class="col-md-4">
                    <div class="card job-card <?php echo $job['is_urgent'] ? 'urgent-job' : ''; ?> h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <?php if ($job['is_urgent']): ?>
                                    <span class="badge bg-danger">ACİL</span>
                                <?php endif; ?>
                                <?php if ($job['category_icon']): ?>
                                    <i class="<?php echo $job['category_icon']; ?> text-primary" style="font-size: 1.5rem;"></i>
                                <?php endif; ?>
                            </div>
                            <h6 class="card-title"><?php echo escape($job['title']); ?></h6>
                            <p class="text-muted small mb-2">
                                <i class="bi bi-geo-alt me-1"></i>
                                <?php echo escape($job['location_city']); ?>
                                <?php if ($job['location_district']): ?>
                                    , <?php echo escape($job['location_district']); ?>
                                <?php endif; ?>
                            </p>
                            <p class="text-muted small mb-2">
                                <i class="bi bi-briefcase me-1"></i>
                                <?php echo getJobTypes()[$job['job_type']] ?? $job['job_type']; ?> -
                                <?php echo getCareTypes()[$job['care_type']] ?? $job['care_type']; ?>
                            </p>
                            <?php if ($job['budget_min'] || $job['budget_max']): ?>
                            <p class="text-primary fw-bold mb-2">
                                <i class="bi bi-currency-exchange me-1"></i>
                                <?php if ($job['budget_min'] && $job['budget_max']): ?>
                                    <?php echo formatMoney($job['budget_min']); ?> - <?php echo formatMoney($job['budget_max']); ?>
                                <?php elseif ($job['budget_min']): ?>
                                    <?php echo formatMoney($job['budget_min']); ?>+
                                <?php else: ?>
                                    <?php echo formatMoney($job['budget_max']); ?>'e kadar
                                <?php endif; ?>
                                <?php if ($job['budget_type']): ?>
                                    /<?php echo $job['budget_type'] === 'hourly' ? 'saat' : ($job['budget_type'] === 'daily' ? 'gün' : ($job['budget_type'] === 'weekly' ? 'hafta' : 'ay')); ?>
                                <?php endif; ?>
                            </p>
                            <?php endif; ?>
                            <small class="text-muted">
                                <i class="bi bi-clock me-1"></i><?php echo timeAgo($job['created_at']); ?>
                            </small>
                            <div class="mt-3">
                                <a href="job-details.php?id=<?php echo $job['id']; ?>" class="btn btn-primary btn-sm">
                                    <i class="bi bi-eye me-1"></i>Detayları Gör
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="floating-elements"></div>
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="cta-content text-center">
                        <div class="cta-icon">
                            <i class="bi bi-rocket-takeoff"></i>
                        </div>
                        <h2 class="cta-title">
                            ✨ <span class="gradient-text">Hemen Başlayın!</span> ✨
                        </h2>
                        <p class="cta-subtitle">
                            🎯 Hayalinizdeki bakım hizmetine sadece <strong>3 adımda</strong> ulaşın!
                            <br>💫 <strong>50.000+</strong> doğrulanmış profil ile güvenli platform deneyimi yaşayın.
                        </p>

                        <!-- CTA Cards -->
                        <div class="row g-4 mb-5">
                            <div class="col-lg-6">
                                <div class="cta-card family-card">
                                    <div class="cta-card-icon">
                                        <i class="bi bi-heart-fill"></i>
                                    </div>
                                    <h4 class="cta-card-title">👨‍👩‍👧‍👦 Bakıcı Arıyorum</h4>
                                    <p class="cta-card-text">
                                        🌟 Aileniz için <strong>güvenilir ve deneyimli</strong> bakıcıları keşfedin.
                                        Detaylı profiller ve gerçek yorumlarla doğru seçimi yapın.
                                    </p>
                                    <ul class="cta-features">
                                        <li><i class="bi bi-shield-check-fill"></i> ✅ Kimlik doğrulaması yapılmış</li>
                                        <li><i class="bi bi-award-fill"></i> 🏆 Referans kontrolü tamamlanmış</li>
                                        <li><i class="bi bi-chat-heart-fill"></i> 💬 Güvenli mesajlaşma sistemi</li>
                                        <li><i class="bi bi-headset"></i> 🆘 7/24 canlı destek hizmeti</li>
                                    </ul>
                                    <a href="auth/register.php?type=family" class="btn btn-cta-primary">
                                        <i class="bi bi-search-heart me-2"></i>🚀 Hemen Başla
                                    </a>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="cta-card caregiver-card">
                                    <div class="cta-card-icon">
                                        <i class="bi bi-briefcase-fill"></i>
                                    </div>
                                    <h4 class="cta-card-title">💼 İş Arıyorum</h4>
                                    <p class="cta-card-text">
                                        💰 Bakım sektöründe <strong>esnek çalışma</strong> ile kariyer yapın.
                                        Kendi programınızı belirleyin, adil ücret alın.
                                    </p>
                                    <ul class="cta-features">
                                        <li><i class="bi bi-clock-fill"></i> ⏰ Esnek çalışma saatleri</li>
                                        <li><i class="bi bi-cash-stack"></i> 💵 Rekabetçi ücret garantisi</li>
                                        <li><i class="bi bi-graph-up-arrow"></i> 📈 Kariyer gelişim fırsatları</li>
                                        <li><i class="bi bi-people-fill"></i> 🤝 Profesyonel topluluk</li>
                                    </ul>
                                    <a href="auth/register.php?type=caregiver" class="btn btn-cta-secondary">
                                        <i class="bi bi-briefcase-fill me-2"></i>💪 Hemen Başla
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Trust Indicators -->
                        <div class="trust-indicators">
                            <div class="row align-items-center justify-content-center g-3">
                                <div class="col-auto">
                                    <div class="trust-item">
                                        <i class="bi bi-shield-check text-success"></i>
                                        <span>🔒 SSL Güvenli</span>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <div class="trust-item">
                                        <i class="bi bi-award text-warning"></i>
                                        <span>🏅 Doğrulanmış Platform</span>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <div class="trust-item">
                                        <i class="bi bi-people text-primary"></i>
                                        <span>👥 <?php echo number_format($stats['total_families'] + $stats['total_caregivers']); ?>+ Kullanıcı</span>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <div class="trust-item">
                                        <i class="bi bi-star-fill text-warning"></i>
                                        <span>⭐ 4.9/5 Memnuniyet</span>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <div class="trust-item">
                                        <i class="bi bi-clock text-info"></i>
                                        <span>⚡ 7/24 Destek</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Additional Info -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <p class="text-center mb-0" style="opacity: 0.8; font-size: 0.95rem;">
                                        🎉 <strong>Bu ay <?php echo number_format(rand(150, 300)); ?>+ yeni eşleşme</strong> gerçekleşti!
                                        Sen de aramıza katıl ve farkı hisset. 💫
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="modern-footer">
        <!-- Newsletter Section -->
        <div class="newsletter-section">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-6">
                        <div class="newsletter-content">
                            <h3 class="newsletter-title">📧 Haberdar Olun</h3>
                            <p class="newsletter-subtitle">Yeni bakıcılar, özel fırsatlar ve güncellemelerden ilk siz haberdar olun!</p>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <form class="newsletter-form" action="newsletter-subscribe.php" method="POST">
                            <div class="input-group">
                                <input type="email" class="form-control newsletter-input" placeholder="E-posta adresinizi girin..." required>
                                <button class="btn btn-newsletter" type="submit">
                                    <i class="bi bi-send me-2"></i>Abone Ol
                                </button>
                            </div>
                            <small class="newsletter-privacy">
                                <i class="bi bi-shield-check me-1"></i>
                                E-posta adresiniz güvende, spam göndermiyoruz.
                            </small>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Footer -->
        <div class="footer-main">
            <div class="container">
                <div class="row g-4">
                    <!-- Company Info -->
                    <div class="col-lg-4 col-md-6">
                        <div class="footer-brand">
                            <h4 class="brand-title">
                                <i class="bi bi-heart-fill me-2 text-primary"></i>
                                <span class="gradient-text"><?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?></span>
                            </h4>
                            <p class="brand-description">
                                Güvenilir bakıcı bulmanın en kolay yolu. Doğrulanmış bakıcılar, güvenli platform, şeffaf değerlendirmeler ile ailenizin güvenliği bizim önceliğimiz.
                            </p>

                            <!-- App Download -->
                            <div class="app-download mb-4">
                                <h6 class="download-title">📱 Mobil Uygulamayı İndirin</h6>
                                <div class="d-flex gap-2 flex-wrap">
                                    <a href="#" class="app-store-btn">
                                        <img src="https://developer.apple.com/assets/elements/badges/download-on-the-app-store.svg" alt="App Store" height="40">
                                    </a>
                                    <a href="#" class="app-store-btn">
                                        <img src="https://play.google.com/intl/en_us/badges/static/images/badges/en_badge_web_generic.png" alt="Google Play" height="40">
                                    </a>
                                </div>
                            </div>

                            <!-- Social Media -->
                            <div class="social-media">
                                <h6 class="social-title">🌐 Bizi Takip Edin</h6>
                                <div class="social-links">
                                    <a href="#" class="social-link facebook" title="Facebook">
                                        <i class="bi bi-facebook"></i>
                                    </a>
                                    <a href="#" class="social-link twitter" title="Twitter">
                                        <i class="bi bi-twitter"></i>
                                    </a>
                                    <a href="#" class="social-link instagram" title="Instagram">
                                        <i class="bi bi-instagram"></i>
                                    </a>
                                    <a href="#" class="social-link linkedin" title="LinkedIn">
                                        <i class="bi bi-linkedin"></i>
                                    </a>
                                    <a href="#" class="social-link youtube" title="YouTube">
                                        <i class="bi bi-youtube"></i>
                                    </a>
                                    <a href="#" class="social-link whatsapp" title="WhatsApp">
                                        <i class="bi bi-whatsapp"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div class="col-lg-2 col-md-6">
                        <div class="footer-links">
                            <h5 class="links-title">🔗 Hızlı Linkler</h5>
                            <ul class="links-list">
                                <li><a href="caregivers.php"><i class="bi bi-chevron-right"></i>Bakıcı Ara</a></li>
                                <li><a href="jobs.php"><i class="bi bi-chevron-right"></i>İş İlanları</a></li>
                                <li><a href="job-application.php"><i class="bi bi-chevron-right"></i>İlan Ver</a></li>
                                <li><a href="about.php"><i class="bi bi-chevron-right"></i>Hakkımızda</a></li>
                                <li><a href="contact.php"><i class="bi bi-chevron-right"></i>İletişim</a></li>
                                <li><a href="blog.php"><i class="bi bi-chevron-right"></i>Blog</a></li>
                                <li><a href="testimonials.php"><i class="bi bi-chevron-right"></i>Referanslar</a></li>
                            </ul>
                        </div>
                    </div>

                    <!-- Services -->
                    <div class="col-lg-2 col-md-6">
                        <div class="footer-links">
                            <h5 class="links-title">🏠 Hizmetlerimiz</h5>
                            <ul class="links-list">
                                <li><a href="caregivers.php?type=child_care"><i class="bi bi-chevron-right"></i>Çocuk Bakımı</a></li>
                                <li><a href="caregivers.php?type=elderly_care"><i class="bi bi-chevron-right"></i>Yaşlı Bakımı</a></li>
                                <li><a href="caregivers.php?type=patient_care"><i class="bi bi-chevron-right"></i>Hasta Bakımı</a></li>
                                <li><a href="caregivers.php?type=house_cleaning"><i class="bi bi-chevron-right"></i>Ev Temizliği</a></li>
                                <li><a href="caregivers.php?type=companion"><i class="bi bi-chevron-right"></i>Refakatçi</a></li>
                                <li><a href="caregivers.php?type=pet_care"><i class="bi bi-chevron-right"></i>Evcil Hayvan</a></li>
                                <li><a href="premium.php"><i class="bi bi-chevron-right"></i>Premium Hizmet</a></li>
                            </ul>
                        </div>
                    </div>

                    <!-- Support -->
                    <div class="col-lg-2 col-md-6">
                        <div class="footer-links">
                            <h5 class="links-title">🛟 Destek</h5>
                            <ul class="links-list">
                                <li><a href="help.php"><i class="bi bi-chevron-right"></i>Yardım Merkezi</a></li>
                                <li><a href="faq.php"><i class="bi bi-chevron-right"></i>Sık Sorulan Sorular</a></li>
                                <li><a href="safety.php"><i class="bi bi-chevron-right"></i>Güvenlik</a></li>
                                <li><a href="insurance.php"><i class="bi bi-chevron-right"></i>Sigorta</a></li>
                                <li><a href="complaints.php"><i class="bi bi-chevron-right"></i>Şikayet</a></li>
                                <li><a href="feedback.php"><i class="bi bi-chevron-right"></i>Geri Bildirim</a></li>
                                <li><a href="live-chat.php"><i class="bi bi-chevron-right"></i>Canlı Destek</a></li>
                            </ul>
                        </div>
                    </div>

                    <!-- Contact Info -->
                    <div class="col-lg-2 col-md-6">
                        <div class="footer-contact">
                            <h5 class="contact-title">📞 İletişim</h5>
                            <div class="contact-info">
                                <div class="contact-item">
                                    <div class="contact-icon">
                                        <i class="bi bi-geo-alt-fill"></i>
                                    </div>
                                    <div class="contact-details">
                                        <strong>Adres</strong>
                                        <span>Maslak Mahallesi<br>İstanbul, Türkiye</span>
                                    </div>
                                </div>

                                <div class="contact-item">
                                    <div class="contact-icon">
                                        <i class="bi bi-telephone-fill"></i>
                                    </div>
                                    <div class="contact-details">
                                        <strong>Telefon</strong>
                                        <span>+90 (212) 123 45 67</span>
                                    </div>
                                </div>

                                <div class="contact-item">
                                    <div class="contact-icon">
                                        <i class="bi bi-envelope-fill"></i>
                                    </div>
                                    <div class="contact-details">
                                        <strong>E-posta</strong>
                                        <span><EMAIL></span>
                                    </div>
                                </div>

                                <div class="contact-item">
                                    <div class="contact-icon">
                                        <i class="bi bi-clock-fill"></i>
                                    </div>
                                    <div class="contact-details">
                                        <strong>Çalışma Saatleri</strong>
                                        <span>7/24 Destek</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Bottom -->
        <div class="footer-bottom">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-6">
                        <div class="copyright">
                            <p class="mb-0">
                                &copy; <?php echo date('Y'); ?> <strong><?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?></strong>.
                                Tüm hakları saklıdır. ❤️ ile Türkiye'de geliştirilmiştir.
                            </p>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="footer-legal">
                            <ul class="legal-links">
                                <li><a href="privacy.php">Gizlilik Politikası</a></li>
                                <li><a href="terms.php">Kullanım Şartları</a></li>
                                <li><a href="cookies.php">Çerez Politikası</a></li>
                                <li><a href="gdpr.php">KVKK</a></li>
                            </ul>
                            <div class="security-badges">
                                <span class="security-badge">
                                    <i class="bi bi-shield-check"></i> SSL Güvenli
                                </span>
                                <span class="security-badge">
                                    <i class="bi bi-award"></i> Doğrulanmış
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('shadow-lg');
            } else {
                navbar.classList.remove('shadow-lg');
            }
        });

        // Animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe elements for animation
        document.querySelectorAll('.feature-card, .caregiver-card, .job-card, .category-card').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(30px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });

        // Counter animation for stats
        function animateCounter(element, target) {
            let current = 0;
            const increment = target / 100;
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(current).toLocaleString('tr-TR') + '+';
            }, 20);
        }

        // Animate stats when they come into view
        const statsObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const number = entry.target.querySelector('.stat-number');
                    const target = parseInt(number.textContent.replace(/[^\d]/g, ''));
                    animateCounter(number, target);
                    statsObserver.unobserve(entry.target);
                }
            });
        });

        document.querySelectorAll('.stat-item').forEach(el => {
            statsObserver.observe(el);
        });
    </script>
</body>
</html>
