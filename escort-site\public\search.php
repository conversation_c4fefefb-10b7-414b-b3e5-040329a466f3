<?php
// EscortNews - Ara<PERSON>
require_once 'includes/config.php';

// Arama terimi
$searchQuery = isset($_GET['q']) ? sanitize($_GET['q']) : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$itemsPerPage = 12;

// Sayfa bilgileri
$pageTitle = !empty($searchQuery) ? '"' . htmlspecialchars($searchQuery) . '" Arama Sonuçları - ' . $siteName : 'Arama - ' . $siteName;
$pageDescription = !empty($searchQuery) ? htmlspecialchars($searchQuery) . ' için arama sonuçları.' : 'İlan arama sayfası.';
$pageKeywords = 'arama, ' . htmlspecialchars($searchQuery) . ', escort, ilan';

$searchResults = array();
$totalResults = 0;
$pagination = array();

if (!empty($searchQuery)) {
    // Arama yap
    $searchResults = searchAds($searchQuery, $itemsPerPage, ($page - 1) * $itemsPerPage);
    
    // Toplam sonuç sayısını al
    $pdo = getDbConnection();
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as total
        FROM ads a 
        WHERE a.status = 'active' AND (a.title LIKE ? OR a.description LIKE ?) 
              AND (a.expires_at IS NULL OR a.expires_at > NOW())
    ");
    $searchTerm = '%' . $searchQuery . '%';
    $stmt->execute(array($searchTerm, $searchTerm));
    $totalResults = $stmt->fetch()['total'];
    
    // Sayfalama hesapla
    $pagination = calculatePagination($totalResults, $itemsPerPage, $page);
}

// Header'ı dahil et
include 'includes/header.php';
?>

<!-- Page Header -->
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-12 text-center">
                <h1 class="page-title">
                    <i class="fas fa-search me-2"></i>
                    <?php if (!empty($searchQuery)): ?>
                        "<?php echo htmlspecialchars($searchQuery); ?>" Arama Sonuçları
                    <?php else: ?>
                        İlan Arama
                    <?php endif; ?>
                </h1>
                <?php if (!empty($searchQuery)): ?>
                <p class="page-subtitle">
                    Toplam <?php echo number_format($totalResults); ?> sonuç bulundu
                </p>
                <?php else: ?>
                <p class="page-subtitle">
                    Aradığınız ilanı bulun
                </p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Search Form -->
<div class="container">
    <div class="row justify-content-center mb-5">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <form method="GET" action="search.php">
                        <div class="input-group input-group-lg">
                            <input type="text" name="q" class="form-control" 
                                   placeholder="Şehir, kategori, başlık veya açıklama ara..." 
                                   value="<?php echo htmlspecialchars($searchQuery); ?>"
                                   required>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i> Ara
                            </button>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-lightbulb me-1"></i>
                                İpucu: Şehir adı, kategori veya ilan başlığı ile arama yapabilirsiniz.
                            </small>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Breadcrumb -->
<div class="container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="index.php">Ana Sayfa</a></li>
            <li class="breadcrumb-item"><a href="ads.php">İlanlar</a></li>
            <li class="breadcrumb-item active">Arama</li>
        </ol>
    </nav>
</div>

<!-- Search Results -->
<div class="container">
    <?php if (!empty($searchQuery)): ?>
        <?php if (!empty($searchResults)): ?>
        <!-- Results Found -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h3>
                <i class="fas fa-list me-2 text-primary"></i>
                Arama Sonuçları
            </h3>
            <div class="text-muted">
                Sayfa <?php echo $pagination['current_page']; ?> / <?php echo $pagination['total_pages']; ?>
            </div>
        </div>
        
        <div class="row">
            <?php foreach ($searchResults as $ad): ?>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card ad-card h-100">
                    <?php if ($ad['featured']): ?>
                    <div class="ad-badge featured-badge">
                        <i class="fas fa-crown me-1"></i> Premium
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($ad['primary_photo']): ?>
                    <img src="uploads/ads/<?php echo htmlspecialchars($ad['primary_photo']); ?>" 
                         class="card-img-top" alt="<?php echo htmlspecialchars($ad['title']); ?>">
                    <?php else: ?>
                    <div class="card-img-top d-flex align-items-center justify-content-center" style="background: var(--gradient-primary); height: 200px;">
                        <i class="fas fa-image fa-3x text-white opacity-50"></i>
                    </div>
                    <?php endif; ?>
                    
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <span class="badge" style="background-color: <?php echo $ad['category_color'] ?? '#DC2626'; ?>;">
                                <?php echo htmlspecialchars($ad['category_name']); ?>
                            </span>
                            <small class="text-muted">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                <?php echo htmlspecialchars($ad['city_name']); ?>
                            </small>
                        </div>
                        <h5 class="card-title">
                            <a href="ad-detail.php?slug=<?php echo $ad['slug']; ?>" class="text-decoration-none text-dark">
                                <?php 
                                // Arama terimini vurgula
                                $title = htmlspecialchars($ad['title']);
                                if (!empty($searchQuery)) {
                                    $title = str_ireplace($searchQuery, '<mark>' . htmlspecialchars($searchQuery) . '</mark>', $title);
                                }
                                echo $title;
                                ?>
                            </a>
                        </h5>
                        <p class="card-text text-muted">
                            <?php 
                            // Açıklamada arama terimini vurgula
                            $description = htmlspecialchars(substr($ad['description'], 0, 100)) . '...';
                            if (!empty($searchQuery)) {
                                $description = str_ireplace($searchQuery, '<mark>' . htmlspecialchars($searchQuery) . '</mark>', $description);
                            }
                            echo $description;
                            ?>
                        </p>
                        <div class="d-flex justify-content-between align-items-center">
                            <?php if ($ad['price_per_hour']): ?>
                            <span class="fw-bold text-primary">
                                ₺<?php echo number_format($ad['price_per_hour'], 0, ',', '.'); ?>/saat
                            </span>
                            <?php else: ?>
                            <span class="text-muted">Fiyat Belirtilmemiş</span>
                            <?php endif; ?>
                            <a href="ad-detail.php?slug=<?php echo $ad['slug']; ?>" class="btn btn-primary btn-sm">
                                <i class="fas fa-eye me-1"></i> Detay
                            </a>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                <?php echo date('d.m.Y H:i', strtotime($ad['created_at'])); ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <!-- Pagination -->
        <?php if ($pagination['total_pages'] > 1): ?>
        <nav aria-label="Arama sayfaları">
            <ul class="pagination justify-content-center">
                <?php if ($pagination['has_prev']): ?>
                <li class="page-item">
                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, array('page' => $pagination['prev_page']))); ?>">
                        <i class="fas fa-chevron-left"></i> Önceki
                    </a>
                </li>
                <?php endif; ?>
                
                <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                <li class="page-item <?php echo ($i == $pagination['current_page']) ? 'active' : ''; ?>">
                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, array('page' => $i))); ?>"><?php echo $i; ?></a>
                </li>
                <?php endfor; ?>
                
                <?php if ($pagination['has_next']): ?>
                <li class="page-item">
                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, array('page' => $pagination['next_page']))); ?>">
                        Sonraki <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                <?php endif; ?>
            </ul>
        </nav>
        <?php endif; ?>
        
        <?php else: ?>
        <!-- No Results Found -->
        <div class="text-center py-5">
            <i class="fas fa-search fa-4x text-muted mb-3"></i>
            <h3>Sonuç Bulunamadı</h3>
            <p class="text-muted">
                "<?php echo htmlspecialchars($searchQuery); ?>" için sonuç bulunamadı.<br>
                Farklı anahtar kelimeler deneyebilirsiniz.
            </p>
            <div class="mt-4">
                <a href="ads.php" class="btn btn-primary me-2">Tüm İlanları Gör</a>
                <a href="search.php" class="btn btn-outline-primary">Yeni Arama</a>
            </div>
        </div>
        <?php endif; ?>
    <?php else: ?>
    <!-- No Search Query -->
    <div class="row">
        <!-- Popular Categories -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-tags me-2 text-primary"></i>
                        Popüler Kategoriler
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($categories as $category): ?>
                        <div class="col-6 mb-2">
                            <a href="category.php?slug=<?php echo $category['slug']; ?>" class="btn btn-outline-primary btn-sm w-100">
                                <i class="<?php echo $category['icon'] ?? 'fas fa-tag'; ?> me-1"></i>
                                <?php echo htmlspecialchars($category['name']); ?>
                            </a>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Popular Cities -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-map-marker-alt me-2 text-primary"></i>
                        Popüler Şehirler
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach (array_slice($cities, 0, 8) as $city): ?>
                        <div class="col-6 mb-2">
                            <a href="city.php?slug=<?php echo $city['slug']; ?>" class="btn btn-outline-secondary btn-sm w-100">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                <?php echo htmlspecialchars($city['name']); ?>
                            </a>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Search Tips -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-lightbulb me-2 text-warning"></i>
                Arama İpuçları
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <h6><i class="fas fa-map-marker-alt text-primary me-2"></i>Şehir Araması</h6>
                    <p class="text-muted small">İstanbul, Ankara, İzmir gibi şehir adları ile arama yapın.</p>
                </div>
                <div class="col-md-4">
                    <h6><i class="fas fa-tag text-success me-2"></i>Kategori Araması</h6>
                    <p class="text-muted small">Escort, masaj, VIP gibi kategori adları ile arama yapın.</p>
                </div>
                <div class="col-md-4">
                    <h6><i class="fas fa-search text-info me-2"></i>Genel Arama</h6>
                    <p class="text-muted small">İlan başlığı veya açıklamasında geçen kelimeler ile arama yapın.</p>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php
// Ek CSS
$additionalCSS = "
mark {
    background-color: #fff3cd;
    padding: 0.1em 0.2em;
    border-radius: 0.2em;
}
";

// Footer'ı dahil et
include 'includes/footer.php';
?>
