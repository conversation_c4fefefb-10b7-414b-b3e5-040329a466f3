<?php
// EscortNews - Şehir Sayfası
require_once 'includes/config.php';

// Şehir slug'ını al
$citySlug = isset($_GET['slug']) ? sanitize($_GET['slug']) : '';

if (empty($citySlug)) {
    header('Location: ads.php');
    exit;
}

// Şehir bilgisini al
$pdo = getDbConnection();
$stmt = $pdo->prepare("SELECT * FROM cities WHERE slug = ? AND status = 1");
$stmt->execute(array($citySlug));
$city = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$city) {
    header('Location: ads.php');
    exit;
}

// Sayfa parametreleri
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$itemsPerPage = 12;
$categorySlug = isset($_GET['category']) ? sanitize($_GET['category']) : '';
$sortBy = isset($_GET['sort']) ? sanitize($_GET['sort']) : 'newest';

// Sayfa bilgileri
$pageTitle = htmlspecialchars($city['name']) . ' İlanları - ' . $siteName;
$pageDescription = htmlspecialchars($city['name']) . ' şehrindeki güncel escort ilanları.';
$pageKeywords = htmlspecialchars($city['name']) . ', escort, ilan, ' . htmlspecialchars($city['name']) . ' escort';

// İlanları al
$ads = getAdsByCity($citySlug, $itemsPerPage, ($page - 1) * $itemsPerPage);

// Toplam ilan sayısını al
$stmt = $pdo->prepare("
    SELECT COUNT(*) as total
    FROM ads a 
    LEFT JOIN cities ci ON a.city_id = ci.id 
    WHERE a.status = 'active' AND ci.slug = ? AND (a.expires_at IS NULL OR a.expires_at > NOW())
");
$stmt->execute(array($citySlug));
$totalAds = $stmt->fetch()['total'];

// Sayfalama hesapla
$pagination = calculatePagination($totalAds, $itemsPerPage, $page);

// Şehirdeki kategori dağılımını al
$stmt = $pdo->prepare("
    SELECT c.name, c.slug, c.color, c.icon, COUNT(a.id) as ad_count
    FROM categories c
    LEFT JOIN ads a ON a.category_id = c.id AND a.status = 'active' 
                    AND a.city_id = (SELECT id FROM cities WHERE slug = ?)
                    AND (a.expires_at IS NULL OR a.expires_at > NOW())
    WHERE c.status = 1
    GROUP BY c.id
    ORDER BY ad_count DESC, c.sort_order ASC
");
$stmt->execute(array($citySlug));
$categoryStats = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Header'ı dahil et
include 'includes/header.php';
?>

<!-- Page Header -->
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="fas fa-map-marker-alt me-2"></i>
                    <?php echo htmlspecialchars($city['name']); ?> İlanları
                </h1>
                <p class="page-subtitle">
                    <?php echo htmlspecialchars($city['name']); ?> şehrindeki güncel escort ilanları<br>
                    Toplam <?php echo number_format($totalAds); ?> ilan bulundu
                </p>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="post-ad.php?city=<?php echo $city['slug']; ?>" class="btn btn-light btn-lg">
                    <i class="fas fa-plus me-2"></i> İlan Ver
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Breadcrumb -->
<div class="container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="index.php">Ana Sayfa</a></li>
            <li class="breadcrumb-item"><a href="ads.php">İlanlar</a></li>
            <li class="breadcrumb-item active"><?php echo htmlspecialchars($city['name']); ?></li>
        </ol>
    </nav>
</div>

<!-- City Stats -->
<div class="container mb-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-chart-pie me-2 text-primary"></i>
                        <?php echo htmlspecialchars($city['name']); ?> İstatistikleri
                    </h5>
                    <div class="row">
                        <?php foreach ($categoryStats as $catStat): ?>
                        <div class="col-md-3 col-6 mb-3">
                            <div class="text-center">
                                <i class="<?php echo $catStat['icon'] ?? 'fas fa-tag'; ?> fa-2x mb-2" 
                                   style="color: <?php echo $catStat['color'] ?? '#DC2626'; ?>;"></i>
                                <h4 class="text-primary"><?php echo $catStat['ad_count']; ?></h4>
                                <small class="text-muted"><?php echo htmlspecialchars($catStat['name']); ?></small>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="container mb-4">
    <div class="row">
        <div class="col-md-8">
            <div class="d-flex flex-wrap gap-2">
                <!-- Category Filter -->
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-tags me-1"></i> Kategori
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="city.php?slug=<?php echo $citySlug; ?>">Tüm Kategoriler</a></li>
                        <?php foreach ($categories as $cat): ?>
                        <li><a class="dropdown-item <?php echo ($categorySlug == $cat['slug']) ? 'active' : ''; ?>" 
                               href="city.php?slug=<?php echo $citySlug; ?>&category=<?php echo $cat['slug']; ?>"><?php echo htmlspecialchars($cat['name']); ?></a></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                
                <!-- Featured Filter -->
                <a href="ads.php?city=<?php echo $citySlug; ?>&featured=1" class="btn btn-outline-primary">
                    <i class="fas fa-star me-1"></i> Öne Çıkan
                </a>
            </div>
        </div>
        <div class="col-md-4">
            <div class="d-flex justify-content-end">
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-sort me-1"></i> Sırala
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item <?php echo ($sortBy == 'newest') ? 'active' : ''; ?>" 
                               href="?<?php echo http_build_query(array_merge($_GET, array('sort' => 'newest'))); ?>">En Yeni</a></li>
                        <li><a class="dropdown-item <?php echo ($sortBy == 'oldest') ? 'active' : ''; ?>" 
                               href="?<?php echo http_build_query(array_merge($_GET, array('sort' => 'oldest'))); ?>">En Eski</a></li>
                        <li><a class="dropdown-item <?php echo ($sortBy == 'price_low') ? 'active' : ''; ?>" 
                               href="?<?php echo http_build_query(array_merge($_GET, array('sort' => 'price_low'))); ?>">Fiyat (Düşük)</a></li>
                        <li><a class="dropdown-item <?php echo ($sortBy == 'price_high') ? 'active' : ''; ?>" 
                               href="?<?php echo http_build_query(array_merge($_GET, array('sort' => 'price_high'))); ?>">Fiyat (Yüksek)</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Ads Grid -->
<div class="container">
    <?php if (!empty($ads)): ?>
    <div class="row">
        <?php foreach ($ads as $ad): ?>
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card ad-card h-100">
                <?php if ($ad['featured']): ?>
                <div class="ad-badge featured-badge">
                    <i class="fas fa-crown me-1"></i> Premium
                </div>
                <?php endif; ?>
                
                <?php if ($ad['primary_photo']): ?>
                <img src="uploads/ads/<?php echo htmlspecialchars($ad['primary_photo']); ?>" 
                     class="card-img-top" alt="<?php echo htmlspecialchars($ad['title']); ?>">
                <?php else: ?>
                <div class="card-img-top d-flex align-items-center justify-content-center" style="background: var(--gradient-primary); height: 200px;">
                    <i class="fas fa-image fa-3x text-white opacity-50"></i>
                </div>
                <?php endif; ?>
                
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <span class="badge" style="background-color: <?php echo $ad['category_color'] ?? '#DC2626'; ?>;">
                            <?php echo htmlspecialchars($ad['category_name']); ?>
                        </span>
                        <small class="text-muted">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            <?php echo htmlspecialchars($city['name']); ?>
                        </small>
                    </div>
                    <h5 class="card-title">
                        <a href="ad-detail.php?slug=<?php echo $ad['slug']; ?>" class="text-decoration-none text-dark">
                            <?php echo htmlspecialchars($ad['title']); ?>
                        </a>
                    </h5>
                    <p class="card-text text-muted">
                        <?php echo htmlspecialchars(substr($ad['description'], 0, 100)) . '...'; ?>
                    </p>
                    <div class="d-flex justify-content-between align-items-center">
                        <?php if ($ad['price_per_hour']): ?>
                        <span class="fw-bold text-primary">
                            ₺<?php echo number_format($ad['price_per_hour'], 0, ',', '.'); ?>/saat
                        </span>
                        <?php else: ?>
                        <span class="text-muted">Fiyat Belirtilmemiş</span>
                        <?php endif; ?>
                        <a href="ad-detail.php?slug=<?php echo $ad['slug']; ?>" class="btn btn-primary btn-sm">
                            <i class="fas fa-eye me-1"></i> Detay
                        </a>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            <?php echo date('d.m.Y H:i', strtotime($ad['created_at'])); ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
    
    <!-- Pagination -->
    <?php if ($pagination['total_pages'] > 1): ?>
    <nav aria-label="Şehir sayfaları">
        <ul class="pagination justify-content-center">
            <?php if ($pagination['has_prev']): ?>
            <li class="page-item">
                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, array('page' => $pagination['prev_page']))); ?>">
                    <i class="fas fa-chevron-left"></i> Önceki
                </a>
            </li>
            <?php endif; ?>
            
            <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
            <li class="page-item <?php echo ($i == $pagination['current_page']) ? 'active' : ''; ?>">
                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, array('page' => $i))); ?>"><?php echo $i; ?></a>
            </li>
            <?php endfor; ?>
            
            <?php if ($pagination['has_next']): ?>
            <li class="page-item">
                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, array('page' => $pagination['next_page']))); ?>">
                    Sonraki <i class="fas fa-chevron-right"></i>
                </a>
            </li>
            <?php endif; ?>
        </ul>
    </nav>
    <?php endif; ?>
    
    <?php else: ?>
    <!-- No Ads Found -->
    <div class="text-center py-5">
        <i class="fas fa-search fa-4x text-muted mb-3"></i>
        <h3>İlan Bulunamadı</h3>
        <p class="text-muted">Bu şehirde henüz ilan bulunmuyor.</p>
        <a href="post-ad.php?city=<?php echo $city['slug']; ?>" class="btn btn-primary">İlk İlanı Ver</a>
    </div>
    <?php endif; ?>
</div>

<!-- Other Cities -->
<section class="py-5 bg-light">
    <div class="container">
        <h3 class="mb-4">
            <i class="fas fa-map-marker-alt me-2 text-primary"></i> Diğer Şehirler
        </h3>
        <div class="row">
            <?php foreach ($cities as $otherCity): ?>
                <?php if ($otherCity['slug'] != $citySlug): ?>
                <div class="col-md-3 col-6 mb-3">
                    <a href="city.php?slug=<?php echo $otherCity['slug']; ?>" class="btn btn-outline-primary w-100">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        <?php echo htmlspecialchars($otherCity['name']); ?>
                    </a>
                </div>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<?php
// Footer'ı dahil et
include 'includes/footer.php';
?>
