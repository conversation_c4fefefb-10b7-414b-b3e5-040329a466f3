<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// <PERSON><PERSON>ş kontrolü
requireLogin();

// <PERSON><PERSON>e admin kullanıcıları erişebilir
if ($_SESSION['user_type'] !== 'admin') {
    redirect('../dashboard.php');
}

$error_message = '';
$success_message = '';

// Form gönderildi mi?
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrf_token = safeArray($_POST, 'csrf_token', '');
    
    if (!validateCSRFToken($csrf_token)) {
        $error_message = 'Güvenlik hatası. Lütfen sayfayı yenileyin.';
    } else {
        try {
            // SMTP ayarları
            $smtp_settings = [
                'smtp_host' => trim(safeArray($_POST, 'smtp_host', '')),
                'smtp_port' => safeNumber(safeArray($_POST, 'smtp_port', 587)),
                'smtp_username' => trim(safeArray($_POST, 'smtp_username', '')),
                'smtp_password' => trim(safeArray($_POST, 'smtp_password', '')),
                'smtp_encryption' => safeArray($_POST, 'smtp_encryption', 'tls'),
                'smtp_from_email' => trim(safeArray($_POST, 'smtp_from_email', '')),
                'smtp_from_name' => trim(safeArray($_POST, 'smtp_from_name', '')),
            ];
            
            // Site ayarları
            $site_settings = [
                'site_name' => trim(safeArray($_POST, 'site_name', '')),
                'site_description' => trim(safeArray($_POST, 'site_description', '')),
                'site_email' => trim(safeArray($_POST, 'site_email', '')),
                'site_phone' => trim(safeArray($_POST, 'site_phone', '')),
            ];
            
            // Bildirim ayarları
            $notification_settings = [
                'email_notifications' => safeArray($_POST, 'email_notifications', '0'),
                'new_message_email' => safeArray($_POST, 'new_message_email', '0'),
                'new_application_email' => safeArray($_POST, 'new_application_email', '0'),
            ];

            // PayTR ayarları
            $paytr_settings = [
                'paytr_merchant_id' => trim(safeArray($_POST, 'paytr_merchant_id', '')),
                'paytr_merchant_key' => trim(safeArray($_POST, 'paytr_merchant_key', '')),
                'paytr_merchant_salt' => trim(safeArray($_POST, 'paytr_merchant_salt', '')),
                'paytr_test_mode' => safeArray($_POST, 'paytr_test_mode', '0'),
                'paytr_success_url' => trim(safeArray($_POST, 'paytr_success_url', '')),
                'paytr_fail_url' => trim(safeArray($_POST, 'paytr_fail_url', '')),
            ];

            // Paket ayarları
            $package_settings = [
                'package_basic_price' => safeNumber(safeArray($_POST, 'package_basic_price', 29.00)),
                'package_basic_features' => trim(safeArray($_POST, 'package_basic_features', '')),
                'package_standard_price' => safeNumber(safeArray($_POST, 'package_standard_price', 59.00)),
                'package_standard_features' => trim(safeArray($_POST, 'package_standard_features', '')),
                'package_premium_price' => safeNumber(safeArray($_POST, 'package_premium_price', 99.00)),
                'package_premium_features' => trim(safeArray($_POST, 'package_premium_features', '')),
            ];

            // Tüm ayarları kaydet
            $all_settings = array_merge($smtp_settings, $site_settings, $notification_settings, $paytr_settings, $package_settings);
            
            foreach ($all_settings as $key => $value) {
                $sql = "INSERT INTO system_settings (setting_key, setting_value) VALUES (?, ?) 
                        ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = NOW()";
                $stmt = $db->prepare($sql);
                $stmt->execute([$key, $value]);
            }
            
            // Aktivite kaydı
            logActivity($_SESSION['user_id'], 'settings_update', 'Sistem ayarlarını güncelledi', 'system_settings');
            
            $success_message = 'Ayarlar başarıyla kaydedildi!';
            
        } catch (PDOException $e) {
            $error_message = 'Ayarlar kaydedilirken bir hata oluştu: ' . $e->getMessage();
            error_log("Settings save error: " . $e->getMessage());
        }
    }
}

// Mevcut ayarları getir
try {
    $sql = "SELECT setting_key, setting_value FROM system_settings";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $settings = [];
    while ($row = $stmt->fetch()) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }
} catch (PDOException $e) {
    $settings = [];
}

$page_title = 'Sistem Ayarları';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Admin Panel</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c5aa0;
        }
        
        .admin-sidebar {
            background: linear-gradient(135deg, var(--primary-color), #1e3d72);
            min-height: 100vh;
        }
        
        .form-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .nav-pills .nav-link.active {
            background-color: var(--primary-color);
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- Admin Sidebar -->
            <div class="col-md-3 col-lg-2 admin-sidebar text-white p-0">
                <div class="p-3">
                    <h4 class="fw-bold">
                        <i class="bi bi-gear-fill me-2"></i>Admin Panel
                    </h4>
                </div>
                <nav class="nav flex-column">
                    <a class="nav-link text-white" href="../dashboard.php">
                        <i class="bi bi-speedometer2 me-2"></i>Dashboard
                    </a>
                    <a class="nav-link text-white" href="users.php">
                        <i class="bi bi-people me-2"></i>Kullanıcılar
                    </a>
                    <a class="nav-link text-white" href="jobs.php">
                        <i class="bi bi-briefcase me-2"></i>İş İlanları
                    </a>
                    <a class="nav-link text-white active" href="settings.php">
                        <i class="bi bi-gear me-2"></i>Sistem Ayarları
                    </a>
                    <hr class="text-white">
                    <a class="nav-link text-white" href="../auth/logout.php">
                        <i class="bi bi-box-arrow-right me-2"></i>Çıkış
                    </a>
                </nav>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="p-4">
                    <!-- Sayfa Başlığı -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h2 class="fw-bold">Sistem Ayarları</h2>
                            <p class="text-muted">SMTP, site ve bildirim ayarlarını yönetin</p>
                        </div>
                    </div>

                    <?php if ($error_message): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i><?php echo escape($error_message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success_message): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle me-2"></i><?php echo escape($success_message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <form method="POST">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <!-- Tab Navigation -->
                        <ul class="nav nav-pills mb-4" id="settingsTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="smtp-tab" data-bs-toggle="pill" data-bs-target="#smtp" type="button">
                                    <i class="bi bi-envelope me-2"></i>SMTP Ayarları
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="site-tab" data-bs-toggle="pill" data-bs-target="#site" type="button">
                                    <i class="bi bi-globe me-2"></i>Site Ayarları
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="paytr-tab" data-bs-toggle="pill" data-bs-target="#paytr" type="button">
                                    <i class="bi bi-credit-card me-2"></i>PayTR Ayarları
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="packages-tab" data-bs-toggle="pill" data-bs-target="#packages" type="button">
                                    <i class="bi bi-box me-2"></i>Paket Ayarları
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="notifications-tab" data-bs-toggle="pill" data-bs-target="#notifications" type="button">
                                    <i class="bi bi-bell me-2"></i>Bildirimler
                                </button>
                            </li>
                        </ul>

                        <!-- Tab Content -->
                        <div class="tab-content" id="settingsTabContent">
                            <!-- SMTP Ayarları -->
                            <div class="tab-pane fade show active" id="smtp" role="tabpanel">
                                <div class="form-card card p-4">
                                    <h5 class="fw-bold mb-4">Email (SMTP) Ayarları</h5>
                                    
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="smtp_host" class="form-label">SMTP Sunucu</label>
                                            <input type="text" class="form-control" id="smtp_host" name="smtp_host" 
                                                   value="<?php echo escape($settings['smtp_host'] ?? 'smtp.gmail.com'); ?>"
                                                   placeholder="smtp.gmail.com">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="smtp_port" class="form-label">Port</label>
                                            <input type="number" class="form-control" id="smtp_port" name="smtp_port" 
                                                   value="<?php echo escape($settings['smtp_port'] ?? '587'); ?>"
                                                   placeholder="587">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="smtp_username" class="form-label">Kullanıcı Adı (Email)</label>
                                            <input type="email" class="form-control" id="smtp_username" name="smtp_username" 
                                                   value="<?php echo escape($settings['smtp_username'] ?? ''); ?>"
                                                   placeholder="<EMAIL>">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="smtp_password" class="form-label">Şifre</label>
                                            <input type="password" class="form-control" id="smtp_password" name="smtp_password" 
                                                   value="<?php echo escape($settings['smtp_password'] ?? ''); ?>"
                                                   placeholder="Email şifresi veya app password">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="smtp_encryption" class="form-label">Şifreleme</label>
                                            <select class="form-select" id="smtp_encryption" name="smtp_encryption">
                                                <option value="tls" <?php echo ($settings['smtp_encryption'] ?? 'tls') === 'tls' ? 'selected' : ''; ?>>TLS</option>
                                                <option value="ssl" <?php echo ($settings['smtp_encryption'] ?? 'tls') === 'ssl' ? 'selected' : ''; ?>>SSL</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="smtp_from_email" class="form-label">Gönderen Email</label>
                                            <input type="email" class="form-control" id="smtp_from_email" name="smtp_from_email" 
                                                   value="<?php echo escape($settings['smtp_from_email'] ?? '<EMAIL>'); ?>"
                                                   placeholder="<EMAIL>">
                                        </div>
                                        <div class="col-12">
                                            <label for="smtp_from_name" class="form-label">Gönderen Adı</label>
                                            <input type="text" class="form-control" id="smtp_from_name" name="smtp_from_name" 
                                                   value="<?php echo escape($settings['smtp_from_name'] ?? 'Bakıcı Platform'); ?>"
                                                   placeholder="Bakıcı Platform">
                                        </div>
                                    </div>
                                    
                                    <div class="mt-4">
                                        <button type="button" class="btn btn-outline-primary" onclick="testEmail()">
                                            <i class="bi bi-envelope-check me-2"></i>Test Email Gönder
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Site Ayarları -->
                            <div class="tab-pane fade" id="site" role="tabpanel">
                                <div class="form-card card p-4">
                                    <h5 class="fw-bold mb-4">Site Ayarları</h5>
                                    
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="site_name" class="form-label">Site Adı</label>
                                            <input type="text" class="form-control" id="site_name" name="site_name" 
                                                   value="<?php echo escape($settings['site_name'] ?? 'Bakıcı Platform'); ?>">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="site_email" class="form-label">Site Email</label>
                                            <input type="email" class="form-control" id="site_email" name="site_email" 
                                                   value="<?php echo escape($settings['site_email'] ?? '<EMAIL>'); ?>">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="site_phone" class="form-label">Site Telefon</label>
                                            <input type="text" class="form-control" id="site_phone" name="site_phone" 
                                                   value="<?php echo escape($settings['site_phone'] ?? '+90 ************'); ?>">
                                        </div>
                                        <div class="col-12">
                                            <label for="site_description" class="form-label">Site Açıklaması</label>
                                            <textarea class="form-control" id="site_description" name="site_description" rows="3"><?php echo escape($settings['site_description'] ?? 'Güvenilir bakıcı bulma platformu'); ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- PayTR Ayarları -->
                            <div class="tab-pane fade" id="paytr" role="tabpanel">
                                <div class="form-card card p-4">
                                    <h5 class="fw-bold mb-4">PayTR Ödeme Ayarları</h5>

                                    <div class="alert alert-info">
                                        <i class="bi bi-info-circle me-2"></i>
                                        <strong>PayTR Entegrasyonu:</strong> PayTR hesabınızdan alacağınız bilgileri buraya girin.
                                        <a href="https://dev.paytr.com/" target="_blank" class="alert-link">PayTR Geliştirici Dokümantasyonu</a>
                                    </div>

                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="paytr_merchant_id" class="form-label">Mağaza ID</label>
                                            <input type="text" class="form-control" id="paytr_merchant_id" name="paytr_merchant_id"
                                                   value="<?php echo escape($settings['paytr_merchant_id'] ?? ''); ?>"
                                                   placeholder="PayTR Mağaza ID">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="paytr_merchant_key" class="form-label">Mağaza Anahtarı</label>
                                            <input type="password" class="form-control" id="paytr_merchant_key" name="paytr_merchant_key"
                                                   value="<?php echo escape($settings['paytr_merchant_key'] ?? ''); ?>"
                                                   placeholder="PayTR Mağaza Anahtarı">
                                        </div>
                                        <div class="col-12">
                                            <label for="paytr_merchant_salt" class="form-label">Mağaza Salt</label>
                                            <input type="password" class="form-control" id="paytr_merchant_salt" name="paytr_merchant_salt"
                                                   value="<?php echo escape($settings['paytr_merchant_salt'] ?? ''); ?>"
                                                   placeholder="PayTR Mağaza Salt">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="paytr_success_url" class="form-label">Başarılı Ödeme URL</label>
                                            <input type="url" class="form-control" id="paytr_success_url" name="paytr_success_url"
                                                   value="<?php echo escape($settings['paytr_success_url'] ?? 'http://localhost/bakici-platform/payment-success.php'); ?>"
                                                   placeholder="http://yoursite.com/payment-success.php">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="paytr_fail_url" class="form-label">Başarısız Ödeme URL</label>
                                            <input type="url" class="form-control" id="paytr_fail_url" name="paytr_fail_url"
                                                   value="<?php echo escape($settings['paytr_fail_url'] ?? 'http://localhost/bakici-platform/payment-fail.php'); ?>"
                                                   placeholder="http://yoursite.com/payment-fail.php">
                                        </div>
                                        <div class="col-12">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="paytr_test_mode" name="paytr_test_mode" value="1"
                                                       <?php echo ($settings['paytr_test_mode'] ?? '1') === '1' ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="paytr_test_mode">
                                                    <strong>Test Modu</strong><br>
                                                    <small class="text-muted">Test modunda gerçek ödeme alınmaz</small>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-4">
                                        <button type="button" class="btn btn-outline-primary" onclick="testPayTR()">
                                            <i class="bi bi-credit-card me-2"></i>PayTR Bağlantısını Test Et
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Paket Ayarları -->
                            <div class="tab-pane fade" id="packages" role="tabpanel">
                                <div class="form-card card p-4">
                                    <h5 class="fw-bold mb-4">Üyelik Paket Ayarları</h5>

                                    <div class="alert alert-warning">
                                        <i class="bi bi-exclamation-triangle me-2"></i>
                                        <strong>Dikkat:</strong> Paket fiyatlarını değiştirdiğinizde mevcut kullanıcıların paketleri etkilenmez.
                                    </div>

                                    <!-- Temel Paket -->
                                    <div class="row g-3 mb-4">
                                        <div class="col-12">
                                            <h6 class="fw-bold text-primary">
                                                <i class="bi bi-box me-2"></i>Temel Paket
                                            </h6>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="package_basic_price" class="form-label">Fiyat (₺)</label>
                                            <input type="number" step="0.01" class="form-control" id="package_basic_price" name="package_basic_price"
                                                   value="<?php echo escape($settings['package_basic_price'] ?? '29.00'); ?>">
                                        </div>
                                        <div class="col-md-9">
                                            <label for="package_basic_features" class="form-label">Özellikler (virgülle ayırın)</label>
                                            <textarea class="form-control" id="package_basic_features" name="package_basic_features" rows="3"><?php echo escape($settings['package_basic_features'] ?? 'Temel özellikler,5 iş ilanı,Email desteği'); ?></textarea>
                                        </div>
                                    </div>

                                    <!-- Standart Paket -->
                                    <div class="row g-3 mb-4">
                                        <div class="col-12">
                                            <h6 class="fw-bold text-success">
                                                <i class="bi bi-box me-2"></i>Standart Paket
                                            </h6>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="package_standard_price" class="form-label">Fiyat (₺)</label>
                                            <input type="number" step="0.01" class="form-control" id="package_standard_price" name="package_standard_price"
                                                   value="<?php echo escape($settings['package_standard_price'] ?? '59.00'); ?>">
                                        </div>
                                        <div class="col-md-9">
                                            <label for="package_standard_features" class="form-label">Özellikler (virgülle ayırın)</label>
                                            <textarea class="form-control" id="package_standard_features" name="package_standard_features" rows="3"><?php echo escape($settings['package_standard_features'] ?? 'Tüm temel özellikler,15 iş ilanı,Öncelikli destek,Mesajlaşma sistemi'); ?></textarea>
                                        </div>
                                    </div>

                                    <!-- Premium Paket -->
                                    <div class="row g-3 mb-4">
                                        <div class="col-12">
                                            <h6 class="fw-bold text-warning">
                                                <i class="bi bi-box me-2"></i>Premium Paket
                                            </h6>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="package_premium_price" class="form-label">Fiyat (₺)</label>
                                            <input type="number" step="0.01" class="form-control" id="package_premium_price" name="package_premium_price"
                                                   value="<?php echo escape($settings['package_premium_price'] ?? '99.00'); ?>">
                                        </div>
                                        <div class="col-md-9">
                                            <label for="package_premium_features" class="form-label">Özellikler (virgülle ayırın)</label>
                                            <textarea class="form-control" id="package_premium_features" name="package_premium_features" rows="3"><?php echo escape($settings['package_premium_features'] ?? 'Tüm özellikler,Sınırsız iş ilanı,7/24 destek,Özel danışman,Analitik raporlar'); ?></textarea>
                                        </div>
                                    </div>

                                    <div class="mt-4">
                                        <button type="button" class="btn btn-outline-info" onclick="previewPackages()">
                                            <i class="bi bi-eye me-2"></i>Paket Önizlemesi
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Bildirim Ayarları -->
                            <div class="tab-pane fade" id="notifications" role="tabpanel">
                                <div class="form-card card p-4">
                                    <h5 class="fw-bold mb-4">Bildirim Ayarları</h5>

                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="email_notifications" name="email_notifications" value="1"
                                               <?php echo ($settings['email_notifications'] ?? '1') === '1' ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="email_notifications">
                                            <strong>Email Bildirimleri</strong><br>
                                            <small class="text-muted">Genel email bildirimlerini aktif/pasif yapar</small>
                                        </label>
                                    </div>

                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="new_message_email" name="new_message_email" value="1"
                                               <?php echo ($settings['new_message_email'] ?? '1') === '1' ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="new_message_email">
                                            <strong>Yeni Mesaj Email Bildirimi</strong><br>
                                            <small class="text-muted">Yeni mesaj geldiğinde email gönderir</small>
                                        </label>
                                    </div>

                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="new_application_email" name="new_application_email" value="1"
                                               <?php echo ($settings['new_application_email'] ?? '1') === '1' ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="new_application_email">
                                            <strong>Yeni Başvuru Email Bildirimi</strong><br>
                                            <small class="text-muted">İş ilanına yeni başvuru geldiğinde email gönderir</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Kaydet Butonu -->
                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="bi bi-check-circle me-2"></i>Ayarları Kaydet
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function testEmail() {
            alert('Test email özelliği yakında aktif olacak.');
        }

        function testPayTR() {
            const merchantId = document.getElementById('paytr_merchant_id').value;
            const merchantKey = document.getElementById('paytr_merchant_key').value;
            const merchantSalt = document.getElementById('paytr_merchant_salt').value;

            if (!merchantId || !merchantKey || !merchantSalt) {
                alert('PayTR bilgilerini tamamlayın.');
                return;
            }

            alert('PayTR test özelliği yakında aktif olacak.');
        }

        function previewPackages() {
            const basicPrice = document.getElementById('package_basic_price').value;
            const standardPrice = document.getElementById('package_standard_price').value;
            const premiumPrice = document.getElementById('package_premium_price').value;

            const preview = `Paket Fiyatları:\n\nTemel: ₺${basicPrice}\nStandart: ₺${standardPrice}\nPremium: ₺${premiumPrice}`;
            alert(preview);
        }
    </script>
</body>
</html>
