<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Giriş kontrolü
if (!isLoggedIn()) {
    header('Location: auth/login.php');
    exit;
}

$application_id = intval($_GET['id'] ?? 0);
if (!$application_id) {
    header('Location: dashboard.php?error=invalid_application');
    exit;
}

// Başvuru bilgilerini getir
try {
    $sql = "SELECT ja.*, 
                   jl.title as job_title, jl.description as job_description, jl.user_id as employer_id,
                   employer.full_name as employer_name
            FROM job_applications ja
            JOIN job_listings jl ON ja.job_id = jl.id
            JOIN users employer ON jl.user_id = employer.id
            WHERE ja.id = ? AND ja.caregiver_id = ?";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([$application_id, $_SESSION['user_id']]);
    $application = $stmt->fetch();
    
    if (!$application) {
        header('Location: dashboard.php?error=application_not_found');
        exit;
    }
    
    // Sadece pending ve viewed durumlarında düzenlenebilir
    if (!in_array($application['status'], ['pending', 'viewed'])) {
        header('Location: dashboard.php?error=cannot_edit_application');
        exit;
    }
    
} catch (PDOException $e) {
    header('Location: dashboard.php?error=database_error');
    exit;
}

// Form gönderildi
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $message = trim($_POST['message'] ?? '');
    
    $errors = [];
    
    // Validasyon
    if (empty($message)) {
        $errors[] = 'Başvuru mesajı gereklidir.';
    } elseif (strlen($message) < 50) {
        $errors[] = 'Başvuru mesajı en az 50 karakter olmalıdır.';
    } elseif (strlen($message) > 1000) {
        $errors[] = 'Başvuru mesajı en fazla 1000 karakter olabilir.';
    }
    
    // Güncelleme
    if (empty($errors)) {
        try {
            $update_sql = "UPDATE job_applications SET 
                          message = ?, 
                          updated_at = NOW() 
                          WHERE id = ? AND caregiver_id = ?";
            $update_stmt = $db->prepare($update_sql);
            $update_stmt->execute([$message, $application_id, $_SESSION['user_id']]);
            
            header('Location: dashboard.php?success=application_updated');
            exit;
            
        } catch (PDOException $e) {
            $errors[] = 'Güncelleme sırasında hata oluştu: ' . $e->getMessage();
        }
    }
}

$page_title = 'Başvurumu Düzenle';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bakıcı Platform</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c5aa0;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
        }
        
        .char-counter {
            font-size: 0.875rem;
            color: #6c757d;
        }
        
        .char-counter.warning {
            color: #fd7e14;
        }
        
        .char-counter.danger {
            color: #dc3545;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="index.php">
                <i class="bi bi-heart-fill me-2"></i>Bakıcı Platform
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.php">Dashboard</a>
                <a class="nav-link" href="auth/logout.php">Çıkış</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="bi bi-pencil me-2"></i>Başvurumu Düzenle</h4>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0">
                                <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                                <li class="breadcrumb-item active">Başvuru Düzenle</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="card-body">
                        <!-- Errors -->
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <!-- Başvuru Bilgileri -->
                        <div class="row mb-4">
                            <div class="col-md-8">
                                <h6>Başvuru Bilgileri</h6>
                                <div class="bg-light p-3 rounded">
                                    <h5 class="text-primary mb-2"><?php echo htmlspecialchars($application['job_title']); ?></h5>
                                    <p class="mb-2"><strong>İşveren:</strong> <?php echo htmlspecialchars($application['employer_name']); ?></p>
                                    <p class="mb-2"><strong>Başvuru Tarihi:</strong> <?php echo date('d.m.Y H:i', strtotime($application['applied_at'])); ?></p>
                                    <p class="mb-0">
                                        <strong>Durum:</strong> 
                                        <span class="badge bg-<?php echo $application['status'] === 'pending' ? 'warning' : 'info'; ?>">
                                            <?php echo $application['status'] === 'pending' ? 'Bekliyor' : 'Görüldü'; ?>
                                        </span>
                                    </p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="alert alert-info">
                                    <h6><i class="bi bi-info-circle me-2"></i>Bilgi</h6>
                                    <p class="small mb-0">Başvurunuz sadece "Bekliyor" veya "Görüldü" durumundayken düzenlenebilir.</p>
                                </div>
                            </div>
                        </div>

                        <!-- Düzenleme Formu -->
                        <form method="POST">
                            <div class="mb-3">
                                <label for="message" class="form-label">Başvuru Mesajınız *</label>
                                <textarea class="form-control" id="message" name="message" rows="8" 
                                          placeholder="Neden bu iş için uygun olduğunuzu, deneyimlerinizi ve motivasyonunuzu açıklayın..." 
                                          required maxlength="1000"><?php echo htmlspecialchars($application['message']); ?></textarea>
                                <div class="d-flex justify-content-between">
                                    <div class="form-text">En az 50, en fazla 1000 karakter</div>
                                    <div class="char-counter" id="charCounter">
                                        <span id="charCount"><?php echo strlen($application['message']); ?></span>/1000
                                    </div>
                                </div>
                            </div>
                            
                            <div class="alert alert-warning">
                                <h6><i class="bi bi-exclamation-triangle me-2"></i>Önemli</h6>
                                <ul class="mb-0">
                                    <li>Başvuru mesajınızı dikkatli bir şekilde yazın</li>
                                    <li>Deneyimlerinizi ve yeteneklerinizi vurgulayın</li>
                                    <li>Neden bu iş için uygun olduğunuzu açıklayın</li>
                                    <li>Profesyonel bir dil kullanın</li>
                                </ul>
                            </div>
                            
                            <div class="d-flex justify-content-between mt-4">
                                <a href="dashboard.php" class="btn btn-secondary">
                                    <i class="bi bi-arrow-left me-2"></i>Geri Dön
                                </a>
                                <div>
                                    <a href="application-details.php?id=<?php echo $application['id']; ?>" class="btn btn-outline-primary me-2">
                                        <i class="bi bi-eye me-2"></i>Detayları Görüntüle
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check me-2"></i>Güncelle
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Karakter sayacı
        const messageTextarea = document.getElementById('message');
        const charCount = document.getElementById('charCount');
        const charCounter = document.getElementById('charCounter');
        
        function updateCharCount() {
            const length = messageTextarea.value.length;
            charCount.textContent = length;
            
            // Renk değiştir
            charCounter.className = 'char-counter';
            if (length > 900) {
                charCounter.classList.add('danger');
            } else if (length > 800) {
                charCounter.classList.add('warning');
            }
            
            // Minimum karakter kontrolü
            if (length < 50) {
                messageTextarea.setCustomValidity('En az 50 karakter yazmalısınız.');
            } else {
                messageTextarea.setCustomValidity('');
            }
        }
        
        messageTextarea.addEventListener('input', updateCharCount);
        updateCharCount(); // İlk yükleme
        
        // Form submit kontrolü
        document.querySelector('form').addEventListener('submit', function(e) {
            const length = messageTextarea.value.length;
            if (length < 50) {
                e.preventDefault();
                alert('Başvuru mesajı en az 50 karakter olmalıdır.');
                messageTextarea.focus();
            }
        });
    </script>
</body>
</html>
