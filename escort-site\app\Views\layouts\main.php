<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    
    <!-- SEO Meta Tags -->
    <title><?= $title ?? 'Escort İlan Sitesi' ?></title>
    <meta name="description" content="<?= $description ?? 'Türkiye\'nin en güvenilir escort ilan sitesi' ?>">
    <meta name="keywords" content="<?= $keywords ?? 'escort, ilan, türkiye, güvenilir' ?>">
    <meta name="author" content="Escort İlan Sitesi">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?= $title ?? 'Escort İlan Sitesi' ?>">
    <meta property="og:description" content="<?= $description ?? 'Türkiye\'nin en güvenilir escort ilan sitesi' ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?= current_url() ?>">
    <meta property="og:site_name" content="<?= $settings['site_name'] ?? 'Escort İlan Sitesi' ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= base_url('assets/images/favicon.ico') ?>">
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: <?= $settings['site_theme_color'] ?? '#E91E63' ?>;
            --secondary-color: <?= $settings['site_secondary_color'] ?? '#9C27B0' ?>;
            --gradient-bg: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            --dark-color: #1A1A1A;
            --light-color: #F8F9FA;
            --accent-color: #FF4081;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: var(--light-color);
        }
        
        /* Header Styles */
        .navbar {
            background: var(--gradient-bg) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem 0;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: white !important;
        }
        
        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }
        
        .navbar-nav .nav-link:hover {
            color: white !important;
            transform: translateY(-2px);
        }
        
        /* Button Styles */
        .btn-primary {
            background: var(--gradient-bg);
            border: none;
            border-radius: 25px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(233, 30, 99, 0.4);
        }
        
        .btn-outline-primary {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            border-radius: 25px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-outline-primary:hover {
            background: var(--gradient-bg);
            border-color: transparent;
            transform: translateY(-2px);
        }
        
        /* Card Styles */
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }
        
        /* Hero Section */
        .hero-section {
            background: var(--gradient-bg);
            color: white;
            padding: 4rem 0;
            text-align: center;
        }
        
        .hero-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        
        .hero-subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        /* Footer Styles */
        .footer {
            background: var(--dark-color);
            color: white;
            padding: 3rem 0 1rem;
            margin-top: 4rem;
        }
        
        .footer h5 {
            color: var(--primary-color);
            margin-bottom: 1rem;
        }
        
        .footer a {
            color: rgba(255,255,255,0.7);
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .footer a:hover {
            color: var(--primary-color);
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }
            
            .hero-subtitle {
                font-size: 1rem;
            }
        }
        
        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
    
    <?= $this->renderSection('styles') ?>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="<?= base_url() ?>">
                <i class="fas fa-heart me-2"></i>
                <?= $settings['site_name'] ?? 'Escort İlan Sitesi' ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url() ?>">
                            <i class="fas fa-home me-1"></i> Ana Sayfa
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= base_url('ads') ?>">
                            <i class="fas fa-list me-1"></i> İlanlar
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-map-marker-alt me-1"></i> Şehirler
                        </a>
                        <ul class="dropdown-menu">
                            <?php if (isset($cities)): ?>
                                <?php foreach ($cities as $city): ?>
                                    <li><a class="dropdown-item" href="<?= base_url('ads/city/' . $city['slug']) ?>"><?= esc($city['name']) ?></a></li>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </ul>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <?php if (session()->get('isLoggedIn')): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i> <?= esc(session()->get('username')) ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="<?= base_url('dashboard') ?>"><i class="fas fa-tachometer-alt me-2"></i> Panel</a></li>
                                <li><a class="dropdown-item" href="<?= base_url('dashboard/my-ads') ?>"><i class="fas fa-list me-2"></i> İlanlarım</a></li>
                                <li><a class="dropdown-item" href="<?= base_url('dashboard/create-ad') ?>"><i class="fas fa-plus me-2"></i> İlan Ver</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?= base_url('auth/logout') ?>"><i class="fas fa-sign-out-alt me-2"></i> Çıkış</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="<?= base_url('auth/login') ?>">
                                <i class="fas fa-sign-in-alt me-1"></i> Giriş
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?= base_url('auth/register') ?>">
                                <i class="fas fa-user-plus me-1"></i> Kayıt
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        <?= $this->renderSection('content') ?>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-4 mb-4">
                    <h5><i class="fas fa-heart me-2"></i><?= $settings['site_name'] ?? 'Escort İlan Sitesi' ?></h5>
                    <p><?= $settings['site_description'] ?? 'Türkiye\'nin en güvenilir escort ilan sitesi' ?></p>
                </div>
                <div class="col-md-2 mb-4">
                    <h5>Hızlı Linkler</h5>
                    <ul class="list-unstyled">
                        <li><a href="<?= base_url() ?>">Ana Sayfa</a></li>
                        <li><a href="<?= base_url('ads') ?>">İlanlar</a></li>
                        <li><a href="<?= base_url('about') ?>">Hakkımızda</a></li>
                        <li><a href="<?= base_url('contact') ?>">İletişim</a></li>
                    </ul>
                </div>
                <div class="col-md-2 mb-4">
                    <h5>Kategoriler</h5>
                    <ul class="list-unstyled">
                        <?php if (isset($categories)): ?>
                            <?php foreach (array_slice($categories, 0, 4) as $category): ?>
                                <li><a href="<?= base_url('ads/category/' . $category['slug']) ?>"><?= esc($category['name']) ?></a></li>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </ul>
                </div>
                <div class="col-md-2 mb-4">
                    <h5>Hesap</h5>
                    <ul class="list-unstyled">
                        <?php if (session()->get('isLoggedIn')): ?>
                            <li><a href="<?= base_url('dashboard') ?>">Panel</a></li>
                            <li><a href="<?= base_url('dashboard/my-ads') ?>">İlanlarım</a></li>
                            <li><a href="<?= base_url('dashboard/create-ad') ?>">İlan Ver</a></li>
                        <?php else: ?>
                            <li><a href="<?= base_url('auth/login') ?>">Giriş Yap</a></li>
                            <li><a href="<?= base_url('auth/register') ?>">Kayıt Ol</a></li>
                        <?php endif; ?>
                    </ul>
                </div>
                <div class="col-md-2 mb-4">
                    <h5>Yasal</h5>
                    <ul class="list-unstyled">
                        <li><a href="<?= base_url('privacy') ?>">Gizlilik</a></li>
                        <li><a href="<?= base_url('terms') ?>">Şartlar</a></li>
                    </ul>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; <?= date('Y') ?> <?= $settings['site_name'] ?? 'Escort İlan Sitesi' ?>. Tüm hakları saklıdır.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <i class="fas fa-code text-primary"></i> 
                        CodeIgniter 4 ile geliştirilmiştir
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // CSRF Token for AJAX requests
        const csrfToken = '<?= csrf_token() ?>';
        const csrfHash = '<?= csrf_hash() ?>';
        
        // Base URL
        const baseUrl = '<?= base_url() ?>';
        
        // Common functions
        function showLoading(element) {
            element.html('<span class="loading"></span> Yükleniyor...');
            element.prop('disabled', true);
        }
        
        function hideLoading(element, text) {
            element.html(text);
            element.prop('disabled', false);
        }
        
        function showAlert(message, type = 'success') {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            if ($('.alert-container').length) {
                $('.alert-container').html(alertHtml);
            } else {
                $('main').prepend('<div class="container mt-3"><div class="alert-container">' + alertHtml + '</div></div>');
            }
            
            // Auto hide after 5 seconds
            setTimeout(() => {
                $('.alert').fadeOut();
            }, 5000);
        }
    </script>
    
    <?= $this->renderSection('scripts') ?>
</body>
</html>
