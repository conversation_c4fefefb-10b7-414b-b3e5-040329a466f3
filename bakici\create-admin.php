<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

try {
    // Admin kull<PERSON> var mı kontrol et
    $check_sql = "SELECT id FROM users WHERE email = '<EMAIL>'";
    $check_stmt = $db->prepare($check_sql);
    $check_stmt->execute();
    
    if ($check_stmt->fetch()) {
        echo "<p>✅ Admin kullanıcı<PERSON>ı zaten mevcut.</p>";
        echo "<p><strong>Email:</strong> <EMAIL></p>";
        echo "<p><strong>Şifre:</strong> admin123</p>";
    } else {
        // Admin kullanıcısı oluştur
        $admin_data = [
            'full_name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => password_hash('admin123', PASSWORD_DEFAULT),
            'phone' => '+90 ************',
            'user_type' => 'admin',
            'status' => 'active',
            'email_verified' => 1
        ];
        
        $sql = "INSERT INTO users (full_name, email, password, phone, user_type, status, email_verified, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
        $stmt = $db->prepare($sql);
        $stmt->execute([
            $admin_data['full_name'],
            $admin_data['email'],
            $admin_data['password'],
            $admin_data['phone'],
            $admin_data['user_type'],
            $admin_data['status'],
            $admin_data['email_verified']
        ]);
        
        echo "<h2>✅ Admin Kullanıcısı Oluşturuldu!</h2>";
        echo "<p><strong>Email:</strong> <EMAIL></p>";
        echo "<p><strong>Şifre:</strong> admin123</p>";
        echo "<p><strong>Kullanıcı Tipi:</strong> Admin</p>";
    }
    
    echo "<hr>";
    echo "<p><a href='auth/login.php'>Giriş Sayfasına Git</a></p>";
    echo "<p><a href='admin/settings.php'>Admin Panel - Ayarlar</a></p>";
    
} catch (PDOException $e) {
    echo "<p>❌ Hata: " . $e->getMessage() . "</p>";
}
?>
