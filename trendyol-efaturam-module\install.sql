-- Trendyol E-Faturam Module Installation SQL
-- OpenCart 3.0.3.2 Compatible

-- Create invoices table
CREATE TABLE IF NOT EXISTS `oc_trendyol_efaturam_invoices` (
    `invoice_id` int(11) NOT NULL AUTO_INCREMENT,
    `order_id` int(11) NOT NULL,
    `invoice_number` varchar(50) NOT NULL,
    `invoice_uuid` varchar(100) DEFAULT NULL,
    `invoice_status` varchar(20) DEFAULT 'pending',
    `invoice_data` text,
    `response_data` text,
    `error_message` text,
    `date_created` datetime NOT NULL,
    `date_modified` datetime DEFAULT NULL,
    PRIMARY KEY (`invoice_id`),
    KEY `order_id` (`order_id`),
    KEY `invoice_number` (`invoice_number`),
    KEY `invoice_status` (`invoice_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create logs table
CREATE TABLE IF NOT EXISTS `oc_trendyol_efaturam_logs` (
    `log_id` int(11) NOT NULL AUTO_INCREMENT,
    `order_id` int(11) DEFAULT NULL,
    `invoice_id` int(11) DEFAULT NULL,
    `action` varchar(50) NOT NULL,
    `message` text NOT NULL,
    `request_data` text,
    `response_data` text,
    `date_created` datetime NOT NULL,
    PRIMARY KEY (`log_id`),
    KEY `order_id` (`order_id`),
    KEY `invoice_id` (`invoice_id`),
    KEY `action` (`action`),
    KEY `date_created` (`date_created`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create settings table
CREATE TABLE IF NOT EXISTS `oc_trendyol_efaturam_settings` (
    `setting_id` int(11) NOT NULL AUTO_INCREMENT,
    `key` varchar(100) NOT NULL,
    `value` text,
    `date_created` datetime NOT NULL,
    `date_modified` datetime DEFAULT NULL,
    PRIMARY KEY (`setting_id`),
    UNIQUE KEY `key` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default settings
INSERT IGNORE INTO `oc_trendyol_efaturam_settings` (`key`, `value`, `date_created`) VALUES
('api_url', 'https://efatura-test.trendyol.com/api/v1', NOW()),
('test_mode', '1', NOW()),
('auto_send', '1', NOW()),
('invoice_prefix', 'TY', NOW()),
('timeout', '30', NOW());

-- Add extension to extensions table
INSERT IGNORE INTO `oc_extension` (`type`, `code`) VALUES ('module', 'trendyol_efaturam');

-- Add user permissions for order integration
UPDATE `oc_user_group` SET
permission = JSON_SET(
    permission,
    '$.access[999]', 'extension/module/trendyol_efaturam',
    '$.modify[999]', 'extension/module/trendyol_efaturam',
    '$.access[1000]', 'extension/module/trendyol_efaturam_order',
    '$.modify[1000]', 'extension/module/trendyol_efaturam_order'
)
WHERE user_group_id = 1;
