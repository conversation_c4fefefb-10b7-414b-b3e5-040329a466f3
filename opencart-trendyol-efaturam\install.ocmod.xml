<?xml version="1.0" encoding="utf-8"?>
<modification>
    <name>Trendyol E-Faturam</name>
    <code>trendyol_efaturam</code>
    <version>1.0.0</version>
    <author>Your Name</author>
    <link>https://www.yourwebsite.com</link>
    
    <file path="admin/controller/extension/module/trendyol_efaturam.php">
        <operation>
            <search><![CDATA[public function install() {]]></search>
            <add position="after"><![CDATA[
        $this->load->model('extension/module/trendyol_efaturam');
        $this->model_extension_module_trendyol_efaturam->install();
            ]]></add>
        </operation>
        
        <operation>
            <search><![CDATA[public function uninstall() {]]></search>
            <add position="after"><![CDATA[
        $this->load->model('extension/module/trendyol_efaturam');
        $this->model_extension_module_trendyol_efaturam->uninstall();
            ]]></add>
        </operation>
    </file>
    
    <file path="admin/view/template/common/column_left.twig">
        <operation>
            <search><![CDATA[<li><a href="{{ extension }}">{{ text_extension }}</a></li>]]></search>
            <add position="after"><![CDATA[
            {% if trendyol_efaturam_access %}
            <li class="dropdown">
                <a href="#" class="dropdown-toggle" data-toggle="dropdown">Trendyol E-Faturam <span class="caret"></span></a>
                <ul class="dropdown-menu">
                    <li><a href="{{ trendyol_efaturam_settings }}">Settings</a></li>
                    <li><a href="{{ trendyol_efaturam_invoices }}">Invoices</a></li>
                    <li><a href="{{ trendyol_efaturam_logs }}">Logs</a></li>
                    <li><a href="{{ trendyol_efaturam_test }}">Test</a></li>
                </ul>
            </li>
            {% endif %}
            ]]></add>
        </operation>
    </file>
    
    <file path="admin/controller/common/column_left.php">
        <operation>
            <search><![CDATA[$data['extension'] = $this->url->link('marketplace/extension', 'user_token=' . $this->session->data['user_token'], true);]]></search>
            <add position="after"><![CDATA[
        
        // Trendyol E-Faturam menu
        if ($this->user->hasPermission('access', 'extension/module/trendyol_efaturam')) {
            $data['trendyol_efaturam_access'] = true;
            $data['trendyol_efaturam_settings'] = $this->url->link('extension/module/trendyol_efaturam', 'user_token=' . $this->session->data['user_token'], true);
            $data['trendyol_efaturam_invoices'] = $this->url->link('extension/module/trendyol_efaturam_invoices', 'user_token=' . $this->session->data['user_token'], true);
            $data['trendyol_efaturam_logs'] = $this->url->link('extension/module/trendyol_efaturam_logs', 'user_token=' . $this->session->data['user_token'], true);
            $data['trendyol_efaturam_test'] = $this->url->link('extension/module/trendyol_efaturam_test', 'user_token=' . $this->session->data['user_token'], true);
        } else {
            $data['trendyol_efaturam_access'] = false;
        }
            ]]></add>
        </operation>
    </file>
</modification>
