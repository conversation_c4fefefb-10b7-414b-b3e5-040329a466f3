<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$error_message = '';
$success_message = '';

// Form gönderildi mi?
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrf_token = safeArray($_POST, 'csrf_token', '');
    
    if (!validateCSRFToken($csrf_token)) {
        $error_message = 'Güvenlik hatası. Lütfen sayfayı yenileyin.';
    } else {
        $name = trim(safeArray($_POST, 'name', ''));
        $email = trim(safeArray($_POST, 'email', ''));
        $phone = trim(safeArray($_POST, 'phone', ''));
        $subject = trim(safeArray($_POST, 'subject', ''));
        $message = trim(safeArray($_POST, 'message', ''));
        
        // Validasyon
        if (empty($name)) {
            $error_message = 'Ad soyad gereklidir.';
        } elseif (empty($email)) {
            $error_message = 'E-posta adresi gereklidir.';
        } elseif (!validateEmail($email)) {
            $error_message = 'Geçerli bir e-posta adresi girin.';
        } elseif (empty($subject)) {
            $error_message = 'Konu gereklidir.';
        } elseif (empty($message)) {
            $error_message = 'Mesaj gereklidir.';
        } else {
            try {
                // İletişim mesajını kaydet
                $sql = "INSERT INTO contact_messages (name, email, phone, subject, message, ip_address, user_agent, created_at) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
                $stmt = $db->prepare($sql);
                $stmt->execute([
                    $name, $email, $phone, $subject, $message, 
                    $_SERVER['REMOTE_ADDR'], $_SERVER['HTTP_USER_AGENT']
                ]);
                
                $success_message = 'Mesajınız başarıyla gönderildi. En kısa sürede size dönüş yapacağız.';
                
                // Form verilerini temizle
                $_POST = [];
                
            } catch (PDOException $e) {
                $error_message = 'Mesaj gönderilirken bir hata oluştu. Lütfen tekrar deneyin.';
            }
        }
    }
}

$page_title = 'İletişim';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #ff6b6b;
            --primary-dark: #e55555;
            --secondary-color: #4ecdc4;
            --accent-color: #45b7d1;
            --warning-color: #feca57;
            --success-color: #48dbfb;
            --text-dark: #2c3e50;
            --text-light: #7f8c8d;
            --bg-light: #f8f9fa;
            --white: #ffffff;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-dark);
            background: linear-gradient(135deg, var(--bg-light) 0%, rgba(78, 205, 196, 0.1) 100%);
        }

        .contact-card {
            border: none;
            border-radius: 25px;
            padding: 40px 30px;
            height: 100%;
            background: var(--white);
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .contact-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .contact-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }

        .contact-card:hover::before {
            transform: scaleX(1);
        }

        .contact-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            margin: 0 auto 25px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }

        .contact-card:hover .contact-icon {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 5px 25px rgba(0,0,0,0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .navbar-brand {
            font-weight: 800;
            font-size: 1.8rem;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-link {
            font-weight: 500;
            color: var(--text-dark) !important;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-link:hover::before,
        .nav-link.active::before {
            width: 100%;
        }

        .form-control {
            border: 2px solid rgba(255, 107, 107, 0.1);
            border-radius: 15px;
            padding: 15px 20px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(255, 107, 107, 0.25);
            transform: translateY(-2px);
        }

        .form-select {
            border: 2px solid rgba(255, 107, 107, 0.1);
            border-radius: 15px;
            padding: 15px 20px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(255, 107, 107, 0.25);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: none;
            border-radius: 50px;
            padding: 15px 35px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(255, 107, 107, 0.4);
        }

        .map-container {
            border-radius: 20px;
            overflow: hidden;
            height: 300px;
            background: linear-gradient(135deg, var(--bg-light), rgba(78, 205, 196, 0.1));
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid rgba(255, 107, 107, 0.1);
        }

        .page-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 50%, var(--secondary-color) 100%);
            color: white;
            padding: 80px 0 60px;
            margin-bottom: 50px;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" fill="white" opacity="0.1"><path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25"/></svg>');
            background-size: cover;
            background-position: bottom;
        }

        .page-header .container {
            position: relative;
            z-index: 2;
        }

        .gradient-text {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .alert {
            border: none;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .alert-success {
            background: linear-gradient(135deg, rgba(72, 219, 251, 0.1), rgba(78, 205, 196, 0.1));
            border-left: 4px solid var(--success-color);
            color: var(--text-dark);
        }

        .alert-danger {
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(229, 85, 85, 0.1));
            border-left: 4px solid var(--primary-color);
            color: var(--text-dark);
        }

        .accordion-item {
            border: none;
            margin-bottom: 15px;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .accordion-button {
            background: var(--white);
            border: none;
            font-weight: 600;
            color: var(--text-dark);
            padding: 20px 25px;
        }

        .accordion-button:not(.collapsed) {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: var(--white);
            box-shadow: none;
        }

        .accordion-button:focus {
            box-shadow: none;
            border: none;
        }

        .accordion-body {
            padding: 25px;
            background: var(--white);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .page-header {
                padding: 60px 0 40px;
            }

            .contact-card {
                padding: 30px 20px;
                margin-bottom: 30px;
            }

            .contact-icon {
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
            }

            .form-control,
            .form-select {
                padding: 12px 15px;
            }

            .btn-primary {
                padding: 12px 25px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="index.php">
                <i class="bi bi-heart-fill me-2"></i><?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="caregivers.php">Bakıcı Ara</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="jobs.php">İş İlanları</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">Hakkımızda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="contact.php">İletişim</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle me-1"></i><?php echo escape(safeArray($_SESSION, 'full_name', 'Kullanıcı')); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="dashboard.php"><i class="bi bi-speedometer2 me-2"></i>Dashboard</a></li>
                                <li><a class="dropdown-item" href="profile.php"><i class="bi bi-person me-2"></i>Profil</a></li>
                                <li><a class="dropdown-item" href="messages.php"><i class="bi bi-chat-dots me-2"></i>Mesajlar</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="auth/logout.php"><i class="bi bi-box-arrow-right me-2"></i>Çıkış</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="auth/login.php">Giriş Yap</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-primary ms-2" href="auth/register.php">Üye Ol</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Sayfa Başlığı -->
    <div class="page-header">
        <div class="container">
            <div class="row justify-content-center text-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-4">
                        💬 <span class="text-warning">İletişime Geçin</span> 📞
                    </h1>
                    <p class="lead mb-4" style="font-size: 1.3rem;">
                        🤝 Size nasıl yardımcı olabiliriz? <br>
                        💫 <strong>7/24 destek</strong> ekibimiz yanınızda!
                    </p>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="d-flex align-items-center justify-content-center">
                                <i class="bi bi-telephone-fill me-2" style="font-size: 1.5rem;"></i>
                                <span class="fw-semibold">📞 Telefon Desteği</span>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="d-flex align-items-center justify-content-center">
                                <i class="bi bi-envelope-fill me-2" style="font-size: 1.5rem;"></i>
                                <span class="fw-semibold">✉️ E-posta Desteği</span>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="d-flex align-items-center justify-content-center">
                                <i class="bi bi-chat-dots-fill me-2" style="font-size: 1.5rem;"></i>
                                <span class="fw-semibold">💬 Canlı Destek</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container mb-5">

        <div class="row g-5">
            <!-- İletişim Bilgileri -->
            <div class="col-lg-4">
                <div class="contact-card text-center">
                    <div class="contact-icon">
                        <i class="bi bi-telephone-fill"></i>
                    </div>
                    <h5 class="fw-bold mb-3 gradient-text">📞 Telefon Desteği</h5>
                    <p class="fw-semibold mb-2" style="font-size: 1.2rem; color: var(--primary-color);">
                        <?php echo escape(getSetting('contact_phone', '+90 ************')); ?>
                    </p>
                    <p class="text-muted mb-3">
                        ⏰ <strong>Pazartesi - Cuma:</strong> 09:00 - 18:00<br>
                        📅 <strong>Cumartesi:</strong> 10:00 - 16:00
                    </p>
                    <div class="mt-3">
                        <span class="badge bg-success">✅ Aktif</span>
                        <span class="badge bg-primary">🆘 Acil Destek</span>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="contact-card text-center">
                    <div class="contact-icon">
                        <i class="bi bi-envelope-fill"></i>
                    </div>
                    <h5 class="fw-bold mb-3 gradient-text">✉️ E-posta Desteği</h5>
                    <p class="fw-semibold mb-2" style="font-size: 1.2rem; color: var(--primary-color);">
                        <?php echo escape(getSetting('contact_email', '<EMAIL>')); ?>
                    </p>
                    <p class="text-muted mb-3">
                        ⚡ <strong>24 saat içinde yanıt</strong><br>
                        📧 Detaylı sorularınız için ideal
                    </p>
                    <div class="mt-3">
                        <span class="badge bg-info">📧 E-posta</span>
                        <span class="badge bg-warning">⚡ Hızlı Yanıt</span>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="contact-card text-center">
                    <div class="contact-icon">
                        <i class="bi bi-geo-alt-fill"></i>
                    </div>
                    <h5 class="fw-bold mb-3 gradient-text">📍 Ofis Adresi</h5>
                    <p class="text-muted mb-2" style="line-height: 1.8;">
                        🏢 <strong>Maslak Mahallesi</strong><br>
                        🛣️ Büyükdere Caddesi No:123<br>
                        📮 34485 Sarıyer/İstanbul
                    </p>
                    <p class="text-muted mb-3">
                        🕘 <strong>Ziyaret saatleri:</strong> 09:00 - 17:00<br>
                        🚇 Metro: Maslak İstasyonu
                    </p>
                    <div class="mt-3">
                        <span class="badge bg-secondary">🏢 Ofis</span>
                        <span class="badge bg-info">🚇 Metro Yakını</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-5">
            <!-- İletişim Formu -->
            <div class="col-lg-8">
                <div class="contact-card">
                    <h4 class="fw-bold mb-4">Mesaj Gönder</h4>
                    
                    <?php if ($error_message): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i><?php echo escape($error_message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success_message): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle me-2"></i><?php echo escape($success_message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <form method="POST" id="contactForm">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <div class="row g-3 mb-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">Ad Soyad *</label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?php echo escape(safeArray($_POST, 'name', '')); ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="email" class="form-label">E-posta *</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo escape(safeArray($_POST, 'email', '')); ?>" required>
                            </div>
                        </div>
                        
                        <div class="row g-3 mb-3">
                            <div class="col-md-6">
                                <label for="phone" class="form-label">Telefon</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?php echo escape(safeArray($_POST, 'phone', '')); ?>">
                            </div>
                            <div class="col-md-6">
                                <label for="subject" class="form-label">Konu *</label>
                                <select class="form-select" id="subject" name="subject" required>
                                    <option value="">Konu seçin</option>
                                    <option value="Genel Bilgi" <?php echo safeArray($_POST, 'subject', '') === 'Genel Bilgi' ? 'selected' : ''; ?>>Genel Bilgi</option>
                                    <option value="Teknik Destek" <?php echo safeArray($_POST, 'subject', '') === 'Teknik Destek' ? 'selected' : ''; ?>>Teknik Destek</option>
                                    <option value="Bakıcı Başvurusu" <?php echo safeArray($_POST, 'subject', '') === 'Bakıcı Başvurusu' ? 'selected' : ''; ?>>Bakıcı Başvurusu</option>
                                    <option value="Şikayet" <?php echo safeArray($_POST, 'subject', '') === 'Şikayet' ? 'selected' : ''; ?>>Şikayet</option>
                                    <option value="Öneri" <?php echo safeArray($_POST, 'subject', '') === 'Öneri' ? 'selected' : ''; ?>>Öneri</option>
                                    <option value="İş Birliği" <?php echo safeArray($_POST, 'subject', '') === 'İş Birliği' ? 'selected' : ''; ?>>İş Birliği</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label for="message" class="form-label">Mesajınız *</label>
                            <textarea class="form-control" id="message" name="message" rows="6" 
                                      placeholder="Mesajınızı buraya yazın..." required><?php echo escape(safeArray($_POST, 'message', '')); ?></textarea>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="bi bi-send me-2"></i>Mesaj Gönder
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Harita ve Ek Bilgiler -->
            <div class="col-lg-4">
                <div class="contact-card">
                    <h5 class="fw-bold mb-4">Ofis Konumu</h5>
                    <div class="map-container mb-4">
                        <div class="text-center text-muted">
                            <i class="bi bi-geo-alt" style="font-size: 3rem;"></i>
                            <p class="mt-2">Harita Yükleniyor...</p>
                        </div>
                    </div>
                    
                    <h6 class="fw-bold mb-3">Çalışma Saatleri</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <strong>Pazartesi - Cuma:</strong><br>
                            <span class="text-muted">09:00 - 18:00</span>
                        </li>
                        <li class="mb-2">
                            <strong>Cumartesi:</strong><br>
                            <span class="text-muted">10:00 - 16:00</span>
                        </li>
                        <li class="mb-2">
                            <strong>Pazar:</strong><br>
                            <span class="text-muted">Kapalı</span>
                        </li>
                    </ul>
                    
                    <hr>
                    
                    <h6 class="fw-bold mb-3">Acil Durum</h6>
                    <p class="text-muted small">
                        Acil durumlar için 7/24 destek hattımızı arayabilirsiniz:
                    </p>
                    <p class="fw-bold text-danger">+90 212 555 0001</p>
                </div>
            </div>
        </div>

        <!-- SSS -->
        <div class="row mt-5">
            <div class="col">
                <div class="contact-card">
                    <h4 class="fw-bold mb-4">Sık Sorulan Sorular</h4>
                    
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                    Bakıcı nasıl bulabilirim?
                                </button>
                            </h2>
                            <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Sitemize üye olduktan sonra "Bakıcı Ara" bölümünden ihtiyaçlarınıza uygun bakıcıları filtreleyebilir, 
                                    profillerini inceleyebilir ve doğrudan iletişime geçebilirsiniz.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                    Bakıcılar güvenilir mi?
                                </button>
                            </h2>
                            <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Tüm bakıcılarımız kimlik doğrulama, referans kontrolü ve güvenlik taramasından geçer. 
                                    Ayrıca kullanıcı değerlendirmeleri ile şeffaf bir sistem sunuyoruz.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                    Ücretler nasıl belirleniyor?
                                </button>
                            </h2>
                            <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Ücretler bakıcıların deneyimi, uzmanlık alanları ve hizmet türüne göre değişir. 
                                    Bakıcılar kendi ücretlerini belirler ve siz de bütçenize uygun olanları seçebilirsiniz.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Form validasyonu
        document.getElementById('contactForm').addEventListener('submit', function(e) {
            const name = document.getElementById('name').value.trim();
            const email = document.getElementById('email').value.trim();
            const subject = document.getElementById('subject').value;
            const message = document.getElementById('message').value.trim();
            
            if (!name || !email || !subject || !message) {
                e.preventDefault();
                alert('Lütfen tüm zorunlu alanları doldurun.');
                return;
            }
            
            // E-posta formatı kontrolü
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                e.preventDefault();
                alert('Geçerli bir e-posta adresi girin.');
                return;
            }
            
            // Loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Gönderiliyor...';
            submitBtn.disabled = true;
            
            // Form gönderilirken hata olursa butonu eski haline getir
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 5000);
        });
    </script>
</body>
</html>
