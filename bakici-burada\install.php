<?php
// <PERSON><PERSON>lum sayfası - Veritabanını oluştur ve demo verileri ekle
require_once 'config/database.php';

$installation_status = [];
$errors = [];

try {
    // Veritabanı bağlantısını test et
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        $installation_status[] = "✅ Veritabanı bağlantısı başarılı";
        $installation_status[] = "✅ Veritabanı 'bakici_burada' oluşturuldu";
        
        // Tabloları oluştur
        $tables_created = 0;
        
        // Users tablosu
        try {
            $db->exec("CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_type ENUM('family', 'caregiver', 'admin') NOT NULL DEFAULT 'family',
                email VARCHAR(255) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                full_name VARCHAR(255) NOT NULL,
                phone VARCHAR(20),
                city VARCHAR(100),
                district VARCHAR(100),
                address TEXT,
                profile_photo VARCHAR(255),
                is_verified BOOLEAN DEFAULT FALSE,
                is_active BOOLEAN DEFAULT TRUE,
                email_verified_at DATETIME NULL,
                email_verification_token VARCHAR(255),
                remember_token VARCHAR(255),
                last_login DATETIME NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
            $tables_created++;
        } catch (PDOException $e) {
            $errors[] = "Users tablosu oluşturulamadı: " . $e->getMessage();
        }
        
        // Caregiver profiles tablosu
        try {
            $db->exec("CREATE TABLE IF NOT EXISTS caregiver_profiles (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                birth_date DATE,
                gender ENUM('male', 'female', 'other') DEFAULT 'female',
                experience_years INT DEFAULT 0,
                education_level VARCHAR(100),
                languages TEXT,
                services TEXT,
                hourly_rate DECIMAL(10,2),
                daily_rate DECIMAL(10,2),
                monthly_rate DECIMAL(10,2),
                availability TEXT,
                bio TEXT,
                skills TEXT,
                certificates TEXT,
                reference_info TEXT,
                background_check BOOLEAN DEFAULT FALSE,
                background_check_file VARCHAR(255),
                cv_file VARCHAR(255),
                rating DECIMAL(3,2) DEFAULT 0.00,
                total_reviews INT DEFAULT 0,
                total_jobs INT DEFAULT 0,
                is_premium BOOLEAN DEFAULT FALSE,
                premium_until DATE NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
            $tables_created++;
        } catch (PDOException $e) {
            $errors[] = "Caregiver profiles tablosu oluşturulamadı: " . $e->getMessage();
        }
        
        // Job listings tablosu
        try {
            $db->exec("CREATE TABLE IF NOT EXISTS job_listings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                title VARCHAR(255) NOT NULL,
                description TEXT NOT NULL,
                job_type ENUM('child_care', 'elderly_care', 'patient_care', 'house_help', 'pet_care') NOT NULL,
                care_type ENUM('live_in', 'live_out', 'hourly', 'daily', 'weekly') NOT NULL,
                location_city VARCHAR(100) NOT NULL,
                location_district VARCHAR(100),
                location_address TEXT,
                budget_min DECIMAL(10,2),
                budget_max DECIMAL(10,2),
                budget_type ENUM('hourly', 'daily', 'weekly', 'monthly') DEFAULT 'monthly',
                start_date DATE,
                requirements TEXT,
                preferred_gender ENUM('male', 'female', 'no_preference') DEFAULT 'no_preference',
                preferred_age_min INT,
                preferred_age_max INT,
                required_experience INT DEFAULT 0,
                required_languages TEXT,
                contact_phone VARCHAR(20),
                contact_email VARCHAR(255),
                is_urgent BOOLEAN DEFAULT FALSE,
                is_featured BOOLEAN DEFAULT FALSE,
                is_premium BOOLEAN DEFAULT FALSE,
                status ENUM('active', 'paused', 'closed', 'expired') DEFAULT 'active',
                expires_at DATETIME NULL,
                views_count INT DEFAULT 0,
                applications_count INT DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
            $tables_created++;
        } catch (PDOException $e) {
            $errors[] = "Job listings tablosu oluşturulamadı: " . $e->getMessage();
        }
        
        // Diğer tabloları da oluştur
        $other_tables = [
            "job_applications" => "CREATE TABLE IF NOT EXISTS job_applications (
                id INT AUTO_INCREMENT PRIMARY KEY,
                job_id INT NOT NULL,
                caregiver_id INT NOT NULL,
                message TEXT,
                proposed_rate DECIMAL(10,2),
                status ENUM('pending', 'accepted', 'rejected', 'withdrawn') DEFAULT 'pending',
                employer_notes TEXT,
                applied_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (job_id) REFERENCES job_listings(id) ON DELETE CASCADE,
                FOREIGN KEY (caregiver_id) REFERENCES users(id) ON DELETE CASCADE,
                UNIQUE KEY unique_application (job_id, caregiver_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            "messages" => "CREATE TABLE IF NOT EXISTS messages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                sender_id INT NOT NULL,
                receiver_id INT NOT NULL,
                job_id INT NULL,
                subject VARCHAR(255),
                message TEXT NOT NULL,
                is_read BOOLEAN DEFAULT FALSE,
                is_deleted_by_sender BOOLEAN DEFAULT FALSE,
                is_deleted_by_receiver BOOLEAN DEFAULT FALSE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (job_id) REFERENCES job_listings(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            "reviews" => "CREATE TABLE IF NOT EXISTS reviews (
                id INT AUTO_INCREMENT PRIMARY KEY,
                reviewer_id INT NOT NULL,
                reviewed_id INT NOT NULL,
                job_id INT NULL,
                rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
                title VARCHAR(255),
                comment TEXT,
                is_anonymous BOOLEAN DEFAULT FALSE,
                is_approved BOOLEAN DEFAULT TRUE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (reviewer_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (reviewed_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (job_id) REFERENCES job_listings(id) ON DELETE SET NULL,
                UNIQUE KEY unique_review (reviewer_id, reviewed_id, job_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            "packages" => "CREATE TABLE IF NOT EXISTS packages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                user_type ENUM('family', 'caregiver') NOT NULL,
                price DECIMAL(10,2) NOT NULL,
                duration_days INT NOT NULL,
                features TEXT,
                max_job_listings INT DEFAULT 0,
                max_applications INT DEFAULT 0,
                max_messages INT DEFAULT 0,
                can_see_contact_info BOOLEAN DEFAULT FALSE,
                is_featured BOOLEAN DEFAULT FALSE,
                is_active BOOLEAN DEFAULT TRUE,
                sort_order INT DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            "notifications" => "CREATE TABLE IF NOT EXISTS notifications (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                type VARCHAR(50) NOT NULL,
                title VARCHAR(255) NOT NULL,
                message TEXT NOT NULL,
                data JSON NULL,
                is_read BOOLEAN DEFAULT FALSE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
        ];
        
        foreach ($other_tables as $table_name => $sql) {
            try {
                $db->exec($sql);
                $tables_created++;
            } catch (PDOException $e) {
                $errors[] = "$table_name tablosu oluşturulamadı: " . $e->getMessage();
            }
        }
        
        $installation_status[] = "✅ $tables_created tablo başarıyla oluşturuldu";
        
        // Admin kullanıcısı oluştur
        try {
            $admin_check = $db->prepare("SELECT id FROM users WHERE user_type = 'admin' LIMIT 1");
            $admin_check->execute();
            
            if (!$admin_check->fetch()) {
                $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
                $admin_sql = "INSERT INTO users (user_type, email, password, full_name, is_verified, is_active) 
                              VALUES ('admin', '<EMAIL>', ?, 'Sistem Yöneticisi', 1, 1)";
                $admin_stmt = $db->prepare($admin_sql);
                $admin_stmt->execute([$admin_password]);
                $installation_status[] = "✅ Admin kullanıcısı oluşturuldu (<EMAIL> / admin123)";
            } else {
                $installation_status[] = "ℹ️ Admin kullanıcısı zaten mevcut";
            }
        } catch (PDOException $e) {
            $errors[] = "Admin kullanıcısı oluşturulamadı: " . $e->getMessage();
        }
        
        // Demo verileri oluştur
        $demo_created = false;
        try {
            // Demo kullanıcı kontrolü
            $demo_check = $db->prepare("SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1");
            $demo_check->execute();
            
            if (!$demo_check->fetch()) {
                // Demo verileri oluştur (database.php'deki kodu çalıştır)
                include_once 'install-demo-data.php';
                $installation_status[] = "✅ Demo veriler oluşturuldu";
                $demo_created = true;
            } else {
                $installation_status[] = "ℹ️ Demo veriler zaten mevcut";
            }
        } catch (Exception $e) {
            $errors[] = "Demo veriler oluşturulamadı: " . $e->getMessage();
            $errors[] = "Hata dosyası: " . $e->getFile() . " satır: " . $e->getLine();
        }
        
    } else {
        $errors[] = "❌ Veritabanı bağlantısı kurulamadı";
    }
    
} catch (Exception $e) {
    $errors[] = "❌ Genel hata: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kurulum - Bakıcı Burada</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3d72 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .install-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .install-header {
            background: #2c5aa0;
            color: white;
            padding: 2rem;
            text-align: center;
            border-radius: 20px 20px 0 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="install-card">
            <div class="install-header">
                <h2><i class="bi bi-heart-fill me-2"></i>Bakıcı Burada</h2>
                <p class="mb-0">Sistem Kurulumu</p>
            </div>
            
            <div class="p-4">
                <h5 class="mb-4">Kurulum Durumu</h5>
                
                <?php if (!empty($installation_status)): ?>
                    <div class="mb-4">
                        <?php foreach ($installation_status as $status): ?>
                            <div class="alert alert-success py-2">
                                <?php echo $status; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($errors)): ?>
                    <div class="mb-4">
                        <?php foreach ($errors as $error): ?>
                            <div class="alert alert-danger py-2">
                                <?php echo $error; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
                
                <?php if (empty($errors)): ?>
                    <div class="alert alert-success">
                        <h6><i class="bi bi-check-circle me-2"></i>Kurulum Tamamlandı!</h6>
                        <p class="mb-0">Sistem başarıyla kuruldu ve demo veriler eklendi.</p>
                    </div>
                    
                    <div class="card mt-4">
                        <div class="card-header">
                            <h6 class="mb-0">Demo Hesaplar</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Admin</h6>
                                    <p class="small mb-1"><strong>E-posta:</strong> <EMAIL></p>
                                    <p class="small"><strong>Şifre:</strong> admin123</p>
                                </div>
                                <div class="col-md-6">
                                    <h6>Demo Aile</h6>
                                    <p class="small mb-1"><strong>E-posta:</strong> <EMAIL></p>
                                    <p class="small"><strong>Şifre:</strong> demo123</p>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Demo Bakıcı</h6>
                                    <p class="small mb-1"><strong>E-posta:</strong> <EMAIL></p>
                                    <p class="small"><strong>Şifre:</strong> demo123</p>
                                </div>
                                <div class="col-md-6">
                                    <h6>Diğer Demo Hesaplar</h6>
                                    <p class="small">Tüm demo hesapların şifresi: <strong>demo123</strong></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 mt-4">
                        <a href="index.php" class="btn btn-primary btn-lg">
                            <i class="bi bi-house me-2"></i>Ana Sayfaya Git
                        </a>
                        <a href="auth/login.php" class="btn btn-outline-primary">
                            <i class="bi bi-box-arrow-in-right me-2"></i>Giriş Yap
                        </a>
                    </div>
                <?php else: ?>
                    <div class="d-grid gap-2 mt-4">
                        <a href="install.php" class="btn btn-warning">
                            <i class="bi bi-arrow-clockwise me-2"></i>Tekrar Dene
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
