<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$caregiver_id = safeNumber(safeArray($_GET, 'id', 0));

if (!$caregiver_id) {
    redirect('caregivers.php');
}

try {
    // Bakıcı bilgilerini getir
    $sql = "SELECT u.*, cp.* 
            FROM users u 
            LEFT JOIN caregiver_profiles cp ON u.id = cp.user_id 
            WHERE u.id = ? AND u.user_type = 'caregiver' AND u.is_active = 1";
    $stmt = $db->prepare($sql);
    $stmt->execute([$caregiver_id]);
    $caregiver = $stmt->fetch();
    
    if (!$caregiver) {
        redirect('caregivers.php');
    }
} catch (PDOException $e) {
    redirect('caregivers.php');
}

$page_title = escape($caregiver['full_name']) . ' - Bakıcı Profili';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #ff6b6b;
            --primary-dark: #e55555;
            --secondary-color: #4ecdc4;
            --accent-color: #45b7d1;
            --warning-color: #feca57;
            --success-color: #48dbfb;
            --text-dark: #2c3e50;
            --text-light: #7f8c8d;
            --bg-light: #f8f9fa;
            --white: #ffffff;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-dark);
        }

        .profile-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 50%, var(--secondary-color) 100%);
            color: white;
            padding: 100px 0 80px;
            position: relative;
            overflow: hidden;
        }

        .profile-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" fill="white" opacity="0.1"><path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25"/></svg>');
            background-size: cover;
            background-position: bottom;
        }

        .profile-header .container {
            position: relative;
            z-index: 2;
        }

        .profile-card {
            border: none;
            border-radius: 30px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            margin-top: -80px;
            background: var(--white);
            position: relative;
            z-index: 10;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .rating-stars {
            color: var(--warning-color);
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 5px 25px rgba(0,0,0,0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .navbar-brand {
            font-weight: 800;
            font-size: 1.8rem;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-link {
            font-weight: 500;
            color: var(--text-dark) !important;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-link:hover::before {
            width: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: none;
            border-radius: 50px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(255, 107, 107, 0.4);
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color), #f39c12);
            border: none;
            border-radius: 50px;
            padding: 12px 30px;
            font-weight: 600;
            color: var(--white);
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(254, 202, 87, 0.3);
        }

        .btn-warning:hover {
            background: linear-gradient(135deg, #f39c12, var(--warning-color));
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(254, 202, 87, 0.4);
            color: var(--white);
        }

        .btn-outline-success {
            border: 2px solid var(--success-color);
            color: var(--success-color);
            border-radius: 50px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-outline-success:hover {
            background: var(--success-color);
            border-color: var(--success-color);
            color: var(--white);
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(72, 219, 251, 0.4);
        }

        .card {
            border: none;
            border-radius: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            background: var(--white);
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .breadcrumb-item a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .breadcrumb-item a:hover {
            color: var(--white);
        }

        .profile-photo {
            border: 5px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }

        .profile-photo:hover {
            transform: scale(1.05);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }

        .stat-card {
            background: linear-gradient(135deg, var(--white), var(--bg-light));
            border-radius: 20px;
            padding: 30px 20px;
            text-align: center;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0,0,0,0.2);
        }

        .stat-card:hover::before {
            transform: scaleX(1);
        }

        .review-card {
            background: var(--white);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border-left: 4px solid var(--primary-color);
        }

        .review-card:hover {
            transform: translateX(5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.12);
        }

        .reviewer-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
            font-weight: 700;
            font-size: 1.1rem;
        }

        .badge {
            border-radius: 15px;
            padding: 8px 15px;
            font-weight: 600;
        }

        .badge.bg-success {
            background: linear-gradient(135deg, var(--success-color), #00d2d3) !important;
        }

        .badge.bg-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)) !important;
        }

        .alert {
            border: none;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .alert-success {
            background: linear-gradient(135deg, rgba(72, 219, 251, 0.1), rgba(78, 205, 196, 0.1));
            border-left: 4px solid var(--success-color);
            color: var(--text-dark);
        }

        .alert-danger {
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(229, 85, 85, 0.1));
            border-left: 4px solid var(--primary-color);
            color: var(--text-dark);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .profile-header {
                padding: 60px 0 40px;
            }

            .profile-card {
                margin-top: -40px;
                padding: 30px 20px !important;
            }

            .navbar-brand {
                font-size: 1.5rem;
            }

            .btn {
                padding: 10px 20px;
                font-size: 0.9rem;
            }

            .stat-card {
                padding: 20px 15px;
                margin-bottom: 20px;
            }

            .review-card {
                padding: 20px;
            }

            .reviewer-avatar {
                width: 40px;
                height: 40px;
                font-size: 1rem;
            }
        }

        /* Gradient Text */
        .gradient-text {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Floating Animation */
        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            overflow: hidden;
            pointer-events: none;
        }

        .floating-elements::before,
        .floating-elements::after {
            content: '';
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 6s ease-in-out infinite;
        }

        .floating-elements::before {
            width: 150px;
            height: 150px;
            top: 20%;
            right: 10%;
            animation-delay: -2s;
        }

        .floating-elements::after {
            width: 100px;
            height: 100px;
            bottom: 20%;
            left: 10%;
            animation-delay: -4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="index.php">
                <i class="bi bi-heart-fill me-2"></i><?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="caregivers.php">Bakıcı Ara</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="jobs.php">İş İlanları</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">Hakkımızda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">İletişim</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle me-1"></i><?php echo escape(safeArray($_SESSION, 'full_name', 'Kullanıcı')); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="dashboard.php"><i class="bi bi-speedometer2 me-2"></i>Dashboard</a></li>
                                <li><a class="dropdown-item" href="profile.php"><i class="bi bi-person me-2"></i>Profil</a></li>
                                <li><a class="dropdown-item" href="messages.php"><i class="bi bi-chat-dots me-2"></i>Mesajlar</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="auth/logout.php"><i class="bi bi-box-arrow-right me-2"></i>Çıkış</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="auth/login.php">Giriş Yap</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-primary ms-2" href="auth/register.php">Üye Ol</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Profile Header -->
    <section class="profile-header">
        <div class="floating-elements"></div>
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="index.php">🏠 Ana Sayfa</a></li>
                            <li class="breadcrumb-item"><a href="caregivers.php">👥 Bakıcı Ara</a></li>
                            <li class="breadcrumb-item active text-white">👤 <?php echo escape($caregiver['full_name']); ?></li>
                        </ol>
                    </nav>
                    <h1 class="display-4 fw-bold mb-3">
                        ✨ <?php echo escape($caregiver['full_name']); ?>
                        <?php if ($caregiver['is_verified']): ?>
                            <span class="badge bg-success ms-2">
                                <i class="bi bi-patch-check-fill"></i> Doğrulanmış
                            </span>
                        <?php endif; ?>
                    </h1>
                    <p class="lead mb-3">
                        <i class="bi bi-geo-alt-fill me-2"></i>📍 <?php echo escape($caregiver['city']); ?>
                        <?php if ($caregiver['district']): ?>
                            , <?php echo escape($caregiver['district']); ?>
                        <?php endif; ?>
                    </p>
                    <?php if ($caregiver['rating'] > 0): ?>
                    <div class="d-flex align-items-center mb-3">
                        <div class="rating-stars me-2">
                            <?php echo getRatingStars($caregiver['rating']); ?>
                        </div>
                        <span class="fw-bold me-2"><?php echo formatRating($caregiver['rating']); ?></span>
                        <span class="text-white-50">(<?php echo $caregiver['total_reviews']; ?> değerlendirme)</span>
                    </div>
                    <?php endif; ?>
                    <div class="d-flex flex-wrap gap-2">
                        <?php if ($caregiver['experience_years']): ?>
                            <span class="badge bg-light text-dark">
                                <i class="bi bi-award me-1"></i><?php echo $caregiver['experience_years']; ?> yıl deneyim
                            </span>
                        <?php endif; ?>
                        <?php if ($caregiver['specializations']): ?>
                            <span class="badge bg-light text-dark">
                                <i class="bi bi-star me-1"></i>Uzman
                            </span>
                        <?php endif; ?>
                        <span class="badge bg-light text-dark">
                            <i class="bi bi-calendar me-1"></i><?php echo date('Y', strtotime($caregiver['created_at'])); ?>'den beri üye
                        </span>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <?php if (isLoggedIn() && $_SESSION['user_type'] === 'family'): ?>
                        <div class="d-grid gap-2">
                            <a href="send-message.php?to=<?php echo $caregiver['id']; ?>" class="btn btn-light btn-lg">
                                <i class="bi bi-chat-heart-fill me-2"></i>💬 Mesaj Gönder
                            </a>
                            <div class="text-center mt-2">
                                <small class="text-white-50">
                                    <i class="bi bi-shield-check me-1"></i>Güvenli mesajlaşma
                                </small>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>

    <div class="container">
        <!-- Alert Messages -->
        <?php if (isset($_GET['success'])): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="bi bi-check-circle me-2"></i>
                <?php
                $success_messages = [
                    'review_added' => 'Değerlendirmeniz başarıyla kaydedildi. Teşekkür ederiz!'
                ];
                echo $success_messages[$_GET['success']] ?? 'İşlem başarıyla tamamlandı.';
                ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_GET['error'])): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="bi bi-exclamation-triangle me-2"></i>
                <?php
                $error_messages = [
                    'already_reviewed' => 'Bu bakıcıyı daha önce değerlendirmişsiniz.',
                    'permission_denied' => 'Bu işlem için yetkiniz yok.',
                    'caregiver_not_found' => 'Bakıcı bulunamadı.'
                ];
                echo $error_messages[$_GET['error']] ?? 'Bir hata oluştu.';
                ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Profile Card -->
        <div class="profile-card p-5">
            <div class="row">
                <div class="col-md-4 text-center">
                    <div class="position-relative d-inline-block mb-4">
                        <?php if ($caregiver['profile_photo']): ?>
                            <img src="<?php echo UPLOAD_URL . 'profiles/' . $caregiver['profile_photo']; ?>"
                                 class="rounded-circle profile-photo" width="200" height="200" style="object-fit: cover;">
                        <?php else: ?>
                            <div class="rounded-circle d-inline-flex align-items-center justify-content-center profile-photo"
                                 style="width: 200px; height: 200px; background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));">
                                <i class="bi bi-person-fill text-white" style="font-size: 5rem;"></i>
                            </div>
                        <?php endif; ?>
                        <?php if ($caregiver['is_verified']): ?>
                            <div class="position-absolute bottom-0 end-0 bg-success rounded-circle d-flex align-items-center justify-content-center"
                                 style="width: 50px; height: 50px; border: 3px solid white;">
                                <i class="bi bi-patch-check-fill text-white" style="font-size: 1.5rem;"></i>
                            </div>
                        <?php endif; ?>
                    </div>

                    <?php if ($caregiver['rating'] > 0): ?>
                    <div class="mb-4">
                        <div class="rating-stars mb-2">
                            <?php echo getRatingStars($caregiver['rating']); ?>
                        </div>
                        <div>
                            <span class="h5 fw-bold gradient-text"><?php echo formatRating($caregiver['rating']); ?></span>
                            <small class="text-muted ms-1">(<?php echo $caregiver['total_reviews']; ?> değerlendirme)</small>
                        </div>
                    </div>
                    <?php endif; ?>

                    <div class="mb-4">
                        <div class="p-3 rounded-3" style="background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(78, 205, 196, 0.1)); border: 2px solid rgba(255, 107, 107, 0.2);">
                            <h4 class="gradient-text fw-bold mb-1">
                                💰 <?php if ($caregiver['monthly_rate']): ?>
                                    <?php echo formatMoney($caregiver['monthly_rate']); ?>/ay
                                <?php elseif ($caregiver['daily_rate']): ?>
                                    <?php echo formatMoney($caregiver['daily_rate']); ?>/gün
                                <?php elseif ($caregiver['hourly_rate']): ?>
                                    <?php echo formatMoney($caregiver['hourly_rate']); ?>/saat
                                <?php else: ?>
                                    Fiyat Görüşülür
                                <?php endif; ?>
                            </h4>
                            <small class="text-muted">Başlangıç fiyatı</small>
                        </div>
                    </div>

                    <?php if (isLoggedIn() && $_SESSION['user_type'] === 'family' && $_SESSION['user_id'] != $caregiver['id']): ?>
                        <div class="d-grid gap-3">
                            <a href="send-message.php?to=<?php echo $caregiver['id']; ?>" class="btn btn-primary">
                                <i class="bi bi-chat-heart-fill me-2"></i>💬 Mesaj Gönder
                            </a>
                            <a href="add-review.php?caregiver_id=<?php echo $caregiver['id']; ?>" class="btn btn-warning">
                                <i class="bi bi-star-fill me-2"></i>⭐ Değerlendir
                            </a>
                            <?php if ($caregiver['phone']): ?>
                                <a href="tel:<?php echo $caregiver['phone']; ?>" class="btn btn-outline-success">
                                    <i class="bi bi-telephone-fill me-2"></i>📞 Hemen Ara
                                </a>
                            <?php endif; ?>
                            <div class="text-center mt-2">
                                <small class="text-muted">
                                    <i class="bi bi-shield-check me-1"></i>Güvenli iletişim garantisi
                                </small>
                            </div>
                        </div>
                    <?php elseif (isLoggedIn() && $_SESSION['user_id'] == $caregiver['id']): ?>
                        <div class="d-grid gap-3">
                            <a href="edit-caregiver-profile.php" class="btn btn-primary">
                                <i class="bi bi-pencil-square me-2"></i>✏️ Profili Düzenle
                            </a>
                            <a href="profile.php" class="btn btn-outline-secondary">
                                <i class="bi bi-gear-fill me-2"></i>⚙️ Hesap Ayarları
                            </a>
                            <div class="text-center mt-2">
                                <small class="text-muted">
                                    <i class="bi bi-info-circle me-1"></i>Profilinizi güncel tutun
                                </small>
                            </div>
                        </div>
                    <?php elseif (!isLoggedIn()): ?>
                        <div class="d-grid">
                            <a href="auth/login.php" class="btn btn-primary">
                                <i class="bi bi-box-arrow-in-right me-2"></i>🔐 Giriş Yapın
                            </a>
                            <div class="text-center mt-2">
                                <small class="text-muted">
                                    Mesaj göndermek için giriş yapmanız gerekiyor
                                </small>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
                
                <div class="col-md-8">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <div class="info-card p-3 rounded-3" style="background: linear-gradient(135deg, rgba(255, 107, 107, 0.05), rgba(78, 205, 196, 0.05)); border-left: 4px solid var(--primary-color);">
                                <h6 class="fw-bold gradient-text mb-2">
                                    <i class="bi bi-award-fill me-2"></i>🏆 Deneyim
                                </h6>
                                <p class="mb-0 fw-semibold"><?php echo $caregiver['experience_years'] ?? 0; ?> yıl profesyonel deneyim</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-card p-3 rounded-3" style="background: linear-gradient(135deg, rgba(78, 205, 196, 0.05), rgba(69, 183, 209, 0.05)); border-left: 4px solid var(--secondary-color);">
                                <h6 class="fw-bold gradient-text mb-2">
                                    <i class="bi bi-mortarboard-fill me-2"></i>🎓 Eğitim
                                </h6>
                                <p class="mb-0 fw-semibold"><?php echo escape($caregiver['education_level'] ?? 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-card p-3 rounded-3" style="background: linear-gradient(135deg, rgba(254, 202, 87, 0.05), rgba(243, 156, 18, 0.05)); border-left: 4px solid var(--warning-color);">
                                <h6 class="fw-bold gradient-text mb-2">
                                    <i class="bi bi-person-fill me-2"></i><?php echo $caregiver['gender'] === 'female' ? '👩' : ($caregiver['gender'] === 'male' ? '👨' : '👤'); ?> Cinsiyet
                                </h6>
                                <p class="mb-0 fw-semibold"><?php echo $caregiver['gender'] === 'female' ? 'Kadın' : ($caregiver['gender'] === 'male' ? 'Erkek' : 'Belirtilmemiş'); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-card p-3 rounded-3" style="background: linear-gradient(135deg, rgba(72, 219, 251, 0.05), rgba(0, 210, 211, 0.05)); border-left: 4px solid var(--success-color);">
                                <h6 class="fw-bold gradient-text mb-2">
                                    <i class="bi bi-translate me-2"></i>🌍 Diller
                                </h6>
                                <p class="mb-0 fw-semibold"><?php echo escape($caregiver['languages'] ?? 'Türkçe'); ?></p>
                            </div>
                        </div>
                        <?php if ($caregiver['specializations']): ?>
                        <div class="col-12">
                            <div class="info-card p-4 rounded-3" style="background: linear-gradient(135deg, rgba(255, 107, 107, 0.08), rgba(78, 205, 196, 0.08)); border: 2px solid rgba(255, 107, 107, 0.2);">
                                <h6 class="fw-bold gradient-text mb-3">
                                    <i class="bi bi-star-fill me-2"></i>⭐ Uzmanlık Alanları
                                </h6>
                                <p class="mb-0 fw-semibold"><?php echo escape($caregiver['specializations']); ?></p>
                            </div>
                        </div>
                        <?php endif; ?>
                        <?php if ($caregiver['bio']): ?>
                        <div class="col-12">
                            <div class="info-card p-4 rounded-3" style="background: linear-gradient(135deg, rgba(78, 205, 196, 0.08), rgba(69, 183, 209, 0.08)); border: 2px solid rgba(78, 205, 196, 0.2);">
                                <h6 class="fw-bold gradient-text mb-3">
                                    <i class="bi bi-person-heart me-2"></i>💝 Hakkında
                                </h6>
                                <p class="mb-0 lh-lg"><?php echo nl2br(escape($caregiver['bio'])); ?></p>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Değerlendirmeler -->
        <?php
        // Değerlendirmeleri getir
        try {
            $reviews_sql = "SELECT r.*, u.full_name as reviewer_name
                           FROM reviews r
                           JOIN users u ON r.reviewer_id = u.id
                           WHERE r.reviewed_id = ? AND r.is_active = 1 AND r.is_approved = 1
                           ORDER BY r.created_at DESC LIMIT 10";
            $reviews_stmt = $db->prepare($reviews_sql);
            $reviews_stmt->execute([$caregiver['id']]);
            $reviews = $reviews_stmt->fetchAll();
        } catch (PDOException $e) {
            $reviews = [];
        }
        ?>

        <?php if (!empty($reviews)): ?>
        <div class="card mt-5">
            <div class="card-header" style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); color: white; border-radius: 25px 25px 0 0;">
                <h5 class="mb-0 fw-bold">
                    <i class="bi bi-star-fill me-2"></i>⭐ Müşteri Değerlendirmeleri
                    <span class="badge bg-light text-dark ms-2"><?php echo count($reviews); ?> yorum</span>
                </h5>
            </div>
            <div class="card-body p-4">
                <?php foreach ($reviews as $review): ?>
                    <div class="review-card">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <div class="d-flex align-items-center">
                                <div class="reviewer-avatar me-3">
                                    <?php echo strtoupper(substr($review['reviewer_name'], 0, 2)); ?>
                                </div>
                                <div>
                                    <h6 class="fw-bold mb-1"><?php echo htmlspecialchars($review['reviewer_name']); ?></h6>
                                    <div class="rating-stars mb-1">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <i class="bi bi-star<?php echo $i <= $review['rating'] ? '-fill' : ''; ?>"></i>
                                        <?php endfor; ?>
                                        <span class="text-muted ms-2 fw-semibold"><?php echo $review['rating']; ?>/5</span>
                                    </div>
                                </div>
                            </div>
                            <div class="text-end">
                                <small class="text-muted fw-semibold">
                                    <i class="bi bi-calendar3 me-1"></i><?php echo date('d.m.Y', strtotime($review['created_at'])); ?>
                                </small>
                                <?php if ($review['service_type']): ?>
                                    <br>
                                    <span class="badge bg-primary mt-1">
                                        <?php
                                        $service_types = [
                                            'child_care' => '👶 Çocuk Bakımı',
                                            'elderly_care' => '👴 Yaşlı Bakımı',
                                            'patient_care' => '🏥 Hasta Bakımı',
                                            'house_cleaning' => '🏠 Ev Temizliği',
                                            'companion' => '🤝 Refakatçi',
                                            'other' => '📋 Diğer'
                                        ];
                                        echo $service_types[$review['service_type']] ?? $review['service_type'];
                                        ?>
                                    </span>
                                <?php endif; ?>
                                <?php if (isset($review['would_recommend']) && $review['would_recommend']): ?>
                                    <br>
                                    <span class="badge bg-success mt-1">
                                        <i class="bi bi-hand-thumbs-up-fill me-1"></i>👍 Tavsiye Ediyor
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="review-content">
                            <p class="mb-0 lh-lg" style="font-style: italic; color: var(--text-dark);">
                                "<?php echo nl2br(htmlspecialchars($review['comment'])); ?>"
                            </p>
                        </div>
                    </div>
                <?php endforeach; ?>

                <div class="text-center mt-4">
                    <a href="reviews.php?caregiver_id=<?php echo $caregiver['id']; ?>" class="btn btn-outline-primary">
                        <i class="bi bi-eye me-2"></i>Tüm Değerlendirmeleri Gör
                    </a>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- İstatistikler -->
        <div class="row g-4 mt-5 mb-5">
            <div class="col-12 text-center mb-4">
                <h3 class="fw-bold gradient-text">📊 Performans İstatistikleri</h3>
                <p class="text-muted">Güvenilirlik ve deneyim göstergeleri</p>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="stat-card">
                    <div class="mb-3" style="color: var(--primary-color);">
                        <i class="bi bi-briefcase-fill" style="font-size: 3rem;"></i>
                    </div>
                    <h3 class="fw-bold gradient-text mb-2"><?php echo $caregiver['total_jobs'] ?? 0; ?></h3>
                    <p class="text-muted mb-0 fw-semibold">💼 Tamamlanan İş</p>
                    <small class="text-muted">Başarılı projeler</small>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="stat-card">
                    <div class="mb-3" style="color: var(--warning-color);">
                        <i class="bi bi-star-fill" style="font-size: 3rem;"></i>
                    </div>
                    <h3 class="fw-bold gradient-text mb-2"><?php echo $caregiver['total_reviews'] ?? 0; ?></h3>
                    <p class="text-muted mb-0 fw-semibold">⭐ Değerlendirme</p>
                    <small class="text-muted">Müşteri yorumu</small>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="stat-card">
                    <div class="mb-3" style="color: var(--secondary-color);">
                        <i class="bi bi-clock-fill" style="font-size: 3rem;"></i>
                    </div>
                    <h3 class="fw-bold gradient-text mb-2"><?php echo $caregiver['experience_years'] ?? 0; ?></h3>
                    <p class="text-muted mb-0 fw-semibold">⏰ Yıl Deneyim</p>
                    <small class="text-muted">Profesyonel tecrübe</small>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="stat-card">
                    <div class="mb-3" style="color: var(--success-color);">
                        <i class="bi bi-calendar-check-fill" style="font-size: 3rem;"></i>
                    </div>
                    <h3 class="fw-bold gradient-text mb-2"><?php echo date('Y', strtotime($caregiver['created_at'])); ?></h3>
                    <p class="text-muted mb-0 fw-semibold">📅 Üyelik Yılı</p>
                    <small class="text-muted">Platform deneyimi</small>
                </div>
            </div>
        </div>

        <!-- Güven Göstergeleri -->
        <div class="row mt-5 mb-5">
            <div class="col-12">
                <div class="card" style="background: linear-gradient(135deg, rgba(255, 107, 107, 0.05), rgba(78, 205, 196, 0.05)); border: 2px solid rgba(255, 107, 107, 0.1);">
                    <div class="card-body p-4">
                        <h5 class="fw-bold gradient-text text-center mb-4">
                            <i class="bi bi-shield-check-fill me-2"></i>🛡️ Güven ve Güvenlik
                        </h5>
                        <div class="row g-3">
                            <div class="col-md-3 col-sm-6 text-center">
                                <div class="p-3">
                                    <i class="bi bi-patch-check-fill text-success" style="font-size: 2rem;"></i>
                                    <p class="mb-0 mt-2 fw-semibold">✅ Kimlik Doğrulandı</p>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6 text-center">
                                <div class="p-3">
                                    <i class="bi bi-shield-fill-check text-primary" style="font-size: 2rem;"></i>
                                    <p class="mb-0 mt-2 fw-semibold">🔒 Güvenlik Kontrolü</p>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6 text-center">
                                <div class="p-3">
                                    <i class="bi bi-telephone-fill text-warning" style="font-size: 2rem;"></i>
                                    <p class="mb-0 mt-2 fw-semibold">📞 İletişim Doğrulandı</p>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6 text-center">
                                <div class="p-3">
                                    <i class="bi bi-award-fill text-info" style="font-size: 2rem;"></i>
                                    <p class="mb-0 mt-2 fw-semibold">🏆 Referans Onaylandı</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
