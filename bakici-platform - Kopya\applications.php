<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// G<PERSON>ş kontrolü
requireLogin();

// Sad<PERSON>e a<PERSON> k<PERSON>anıcıları erişebilir
if ($_SESSION['user_type'] !== 'family') {
    redirect('dashboard.php');
}

$user_id = $_SESSION['user_id'];

// Başvuruları getir
try {
    $sql = "SELECT ja.*, jl.title as job_title, jl.slug as job_slug, 
                   u.full_name as caregiver_name, u.phone as caregiver_phone, u.email as caregiver_email,
                   cp.rating, cp.experience_years, cp.hourly_rate, cp.daily_rate, cp.monthly_rate
            FROM job_applications ja
            JOIN job_listings jl ON ja.job_id = jl.id
            JOIN users u ON ja.caregiver_id = u.id
            LEFT JOIN caregiver_profiles cp ON u.id = cp.user_id
            WHERE jl.user_id = ?
            ORDER BY ja.applied_at DESC";
    $stmt = $db->prepare($sql);
    $stmt->execute([$user_id]);
    $applications = $stmt->fetchAll();
} catch (PDOException $e) {
    $applications = [];
}

$page_title = 'Başvurular';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c5aa0;
        }
        
        .application-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }
        
        .application-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }
        
        .status-pending { border-left: 4px solid #ffc107; }
        .status-accepted { border-left: 4px solid #28a745; }
        .status-rejected { border-left: 4px solid #dc3545; }
        .status-hired { border-left: 4px solid #17a2b8; }
        
        .navbar {
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="index.php">
                <i class="bi bi-heart-fill me-2"></i><?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="caregivers.php">Bakıcı Ara</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="jobs.php">İş İlanları</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">Hakkımızda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">İletişim</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i><?php echo escape($_SESSION['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="dashboard.php"><i class="bi bi-speedometer2 me-2"></i>Dashboard</a></li>
                            <li><a class="dropdown-item" href="profile.php"><i class="bi bi-person me-2"></i>Profil</a></li>
                            <li><a class="dropdown-item" href="messages.php"><i class="bi bi-chat-dots me-2"></i>Mesajlar</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="auth/logout.php"><i class="bi bi-box-arrow-right me-2"></i>Çıkış</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Sayfa Başlığı -->
        <div class="row mb-4">
            <div class="col">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                        <li class="breadcrumb-item active">Başvurular</li>
                    </ol>
                </nav>
                <h2 class="fw-bold">İlanlarıma Gelen Başvurular</h2>
                <p class="text-muted">İş ilanlarınıza gelen başvuruları yönetin</p>
            </div>
        </div>

        <!-- Filtreler -->
        <div class="row mb-4">
            <div class="col-md-6">
                <select class="form-select" id="statusFilter">
                    <option value="">Tüm Durumlar</option>
                    <option value="pending">Bekleyen</option>
                    <option value="accepted">Kabul Edilen</option>
                    <option value="rejected">Reddedilen</option>
                    <option value="hired">İşe Alınan</option>
                </select>
            </div>
            <div class="col-md-6">
                <input type="text" class="form-control" id="searchFilter" placeholder="Bakıcı adı veya iş ilanı ara...">
            </div>
        </div>

        <!-- Başvurular -->
        <div class="row">
            <?php if (!empty($applications)): ?>
                <?php foreach ($applications as $app): ?>
                <div class="col-12">
                    <div class="application-card status-<?php echo $app['status']; ?> card">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-3">
                                    <h6 class="fw-bold mb-1"><?php echo escape($app['caregiver_name']); ?></h6>
                                    <p class="text-muted small mb-1">
                                        <i class="bi bi-briefcase me-1"></i><?php echo $app['experience_years'] ?? 0; ?> yıl deneyim
                                    </p>
                                    <?php if ($app['rating'] > 0): ?>
                                        <div class="mb-2">
                                            <?php echo getRatingStars($app['rating']); ?>
                                            <small class="text-muted ms-1">(<?php echo formatRating($app['rating']); ?>)</small>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-3">
                                    <p class="mb-1"><strong>İlan:</strong></p>
                                    <a href="job-detail.php?slug=<?php echo $app['job_slug']; ?>" class="text-decoration-none">
                                        <?php echo escape($app['job_title']); ?>
                                    </a>
                                </div>
                                <div class="col-md-2">
                                    <p class="mb-1"><strong>Teklif:</strong></p>
                                    <p class="text-primary fw-bold mb-0">
                                        <?php echo $app['proposed_rate'] ? formatMoney($app['proposed_rate']) . '/saat' : 'Belirtilmemiş'; ?>
                                    </p>
                                </div>
                                <div class="col-md-2">
                                    <span class="badge bg-<?php 
                                        echo $app['status'] === 'pending' ? 'warning' : 
                                            ($app['status'] === 'accepted' ? 'success' : 
                                            ($app['status'] === 'rejected' ? 'danger' : 'info')); 
                                    ?>">
                                        <?php echo getApplicationStatuses()[$app['status']] ?? $app['status']; ?>
                                    </span>
                                    <br>
                                    <small class="text-muted"><?php echo timeAgo($app['applied_at']); ?></small>
                                </div>
                                <div class="col-md-2 text-end">
                                    <div class="btn-group-vertical" role="group">
                                        <button class="btn btn-sm btn-outline-primary" onclick="viewApplication(<?php echo $app['id']; ?>)">
                                            <i class="bi bi-eye"></i> Görüntüle
                                        </button>
                                        <?php if ($app['status'] === 'pending'): ?>
                                            <button class="btn btn-sm btn-success" onclick="updateStatus(<?php echo $app['id']; ?>, 'accepted')">
                                                <i class="bi bi-check"></i> Kabul Et
                                            </button>
                                            <button class="btn btn-sm btn-danger" onclick="updateStatus(<?php echo $app['id']; ?>, 'rejected')">
                                                <i class="bi bi-x"></i> Reddet
                                            </button>
                                        <?php elseif ($app['status'] === 'accepted'): ?>
                                            <button class="btn btn-sm btn-info" onclick="updateStatus(<?php echo $app['id']; ?>, 'hired')">
                                                <i class="bi bi-person-check"></i> İşe Al
                                            </button>
                                        <?php endif; ?>
                                        <a href="send-message.php?to=<?php echo $app['caregiver_id']; ?>&job_id=<?php echo $app['job_id']; ?>" class="btn btn-sm btn-outline-secondary">
                                            <i class="bi bi-chat-dots"></i> Mesaj Gönder
                                        </a>
                                    </div>
                                </div>
                            </div>
                            
                            <?php if ($app['cover_letter']): ?>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <div class="bg-light p-3 rounded">
                                        <h6 class="fw-bold mb-2">Başvuru Mektubu:</h6>
                                        <p class="mb-0"><?php echo nl2br(escape($app['cover_letter'])); ?></p>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="bi bi-person-check text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">Henüz başvuru yok</h4>
                        <p class="text-muted">İş ilanlarınıza gelen başvurular burada görünecek.</p>
                        <a href="jobs/create.php" class="btn btn-primary">Yeni İlan Ver</a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Application Detail Modal -->
    <div class="modal fade" id="applicationModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Başvuru Detayları</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="applicationDetails">
                    <!-- Ajax ile yüklenecek -->
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Başvuru detaylarını görüntüle
        function viewApplication(applicationId) {
            // Modal açılacak ve detaylar yüklenecek
            const modal = new bootstrap.Modal(document.getElementById('applicationModal'));
            document.getElementById('applicationDetails').innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div></div>';
            modal.show();
            
            // Ajax ile detayları yükle (şimdilik placeholder)
            setTimeout(() => {
                document.getElementById('applicationDetails').innerHTML = '<p>Başvuru detayları yükleniyor...</p>';
            }, 1000);
        }
        
        // Başvuru durumunu güncelle
        function updateStatus(applicationId, status) {
            if (confirm('Bu işlemi yapmak istediğinizden emin misiniz?')) {
                // Ajax ile durum güncellenecek
                alert('Durum güncelleme özelliği yakında aktif olacak.');
            }
        }
        
        // Filtreleme
        document.getElementById('statusFilter').addEventListener('change', function() {
            filterApplications();
        });
        
        document.getElementById('searchFilter').addEventListener('input', function() {
            filterApplications();
        });
        
        function filterApplications() {
            const statusFilter = document.getElementById('statusFilter').value;
            const searchFilter = document.getElementById('searchFilter').value.toLowerCase();
            const cards = document.querySelectorAll('.application-card');
            
            cards.forEach(card => {
                const status = card.className.includes('status-' + statusFilter) || statusFilter === '';
                const text = card.textContent.toLowerCase().includes(searchFilter) || searchFilter === '';
                
                if (status && text) {
                    card.parentElement.style.display = 'block';
                } else {
                    card.parentElement.style.display = 'none';
                }
            });
        }
    </script>
</body>
</html>
