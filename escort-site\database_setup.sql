-- Escort İlan Sitesi Veritabanı Kurulum Scripti
-- PHP CodeIgniter 4 ile uyumlu
-- K<PERSON>rm<PERSON>zı-<PERSON>r temalı escort ilan sitesi

-- Veritabanı oluştur
CREATE DATABASE IF NOT EXISTS `escort_site` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `escort_site`;

-- 1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tablosu
CREATE TABLE `users` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `username` varchar(50) NOT NULL UNIQUE,
    `email` varchar(100) NOT NULL UNIQUE,
    `password` varchar(255) NOT NULL,
    `first_name` varchar(50) DEFAULT NULL,
    `last_name` varchar(50) DEFAULT NULL,
    `phone` varchar(20) DEFAULT NULL,
    `role` enum('admin','moderator','premium','user') DEFAULT 'user',
    `status` enum('active','inactive','banned') DEFAULT 'active',
    `email_verified` tinyint(1) DEFAULT 0,
    `verification_token` varchar(100) DEFAULT NULL,
    `reset_token` varchar(100) DEFAULT NULL,
    `last_login` datetime DEFAULT NULL,
    `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_email` (`email`),
    KEY `idx_username` (`username`),
    KEY `idx_role` (`role`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. Şehirler tablosu
CREATE TABLE `cities` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `slug` varchar(100) NOT NULL UNIQUE,
    `plate_code` varchar(3) DEFAULT NULL,
    `status` tinyint(1) DEFAULT 1,
    `sort_order` int(11) DEFAULT 0,
    PRIMARY KEY (`id`),
    KEY `idx_slug` (`slug`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. İlçeler tablosu
CREATE TABLE `districts` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `city_id` int(11) NOT NULL,
    `name` varchar(100) NOT NULL,
    `slug` varchar(100) NOT NULL,
    `status` tinyint(1) DEFAULT 1,
    `sort_order` int(11) DEFAULT 0,
    PRIMARY KEY (`id`),
    KEY `idx_city_id` (`city_id`),
    KEY `idx_slug` (`slug`),
    KEY `idx_status` (`status`),
    FOREIGN KEY (`city_id`) REFERENCES `cities`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. Kategoriler tablosu
CREATE TABLE `categories` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `slug` varchar(100) NOT NULL UNIQUE,
    `description` text DEFAULT NULL,
    `icon` varchar(50) DEFAULT NULL,
    `color` varchar(7) DEFAULT '#E91E63',
    `status` tinyint(1) DEFAULT 1,
    `sort_order` int(11) DEFAULT 0,
    `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_slug` (`slug`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 5. Üyelik paketleri tablosu
CREATE TABLE `packages` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(50) NOT NULL,
    `slug` varchar(50) NOT NULL UNIQUE,
    `description` text DEFAULT NULL,
    `price` decimal(10,2) NOT NULL,
    `duration_days` int(11) NOT NULL,
    `max_ads` int(11) DEFAULT 1,
    `max_photos` int(11) DEFAULT 5,
    `featured` tinyint(1) DEFAULT 0,
    `priority` tinyint(1) DEFAULT 0,
    `color` varchar(7) DEFAULT '#E91E63',
    `features` json DEFAULT NULL,
    `status` tinyint(1) DEFAULT 1,
    `sort_order` int(11) DEFAULT 0,
    `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_slug` (`slug`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 6. Escort profilleri tablosu
CREATE TABLE `escorts` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `stage_name` varchar(100) NOT NULL,
    `age` int(3) DEFAULT NULL,
    `height` int(3) DEFAULT NULL,
    `weight` int(3) DEFAULT NULL,
    `bust` varchar(10) DEFAULT NULL,
    `waist` varchar(10) DEFAULT NULL,
    `hip` varchar(10) DEFAULT NULL,
    `hair_color` varchar(20) DEFAULT NULL,
    `eye_color` varchar(20) DEFAULT NULL,
    `body_type` varchar(20) DEFAULT NULL,
    `ethnicity` varchar(30) DEFAULT NULL,
    `languages` json DEFAULT NULL,
    `services` json DEFAULT NULL,
    `rates` json DEFAULT NULL,
    `availability` json DEFAULT NULL,
    `about` text DEFAULT NULL,
    `verification_status` enum('pending','verified','rejected') DEFAULT 'pending',
    `verification_date` datetime DEFAULT NULL,
    `profile_views` int(11) DEFAULT 0,
    `status` tinyint(1) DEFAULT 1,
    `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_verification_status` (`verification_status`),
    KEY `idx_status` (`status`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 7. İlanlar tablosu
CREATE TABLE `ads` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `escort_id` int(11) DEFAULT NULL,
    `category_id` int(11) NOT NULL,
    `city_id` int(11) NOT NULL,
    `district_id` int(11) DEFAULT NULL,
    `package_id` int(11) DEFAULT NULL,
    `title` varchar(200) NOT NULL,
    `slug` varchar(200) NOT NULL,
    `description` text NOT NULL,
    `price_per_hour` decimal(10,2) DEFAULT NULL,
    `price_per_night` decimal(10,2) DEFAULT NULL,
    `contact_phone` varchar(20) DEFAULT NULL,
    `contact_whatsapp` varchar(20) DEFAULT NULL,
    `contact_telegram` varchar(50) DEFAULT NULL,
    `location_details` text DEFAULT NULL,
    `services_offered` json DEFAULT NULL,
    `working_hours` json DEFAULT NULL,
    `featured` tinyint(1) DEFAULT 0,
    `priority` tinyint(1) DEFAULT 0,
    `status` enum('draft','pending','active','rejected','expired') DEFAULT 'pending',
    `rejection_reason` text DEFAULT NULL,
    `views` int(11) DEFAULT 0,
    `clicks` int(11) DEFAULT 0,
    `expires_at` datetime DEFAULT NULL,
    `published_at` datetime DEFAULT NULL,
    `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_escort_id` (`escort_id`),
    KEY `idx_category_id` (`category_id`),
    KEY `idx_city_id` (`city_id`),
    KEY `idx_district_id` (`district_id`),
    KEY `idx_package_id` (`package_id`),
    KEY `idx_slug` (`slug`),
    KEY `idx_status` (`status`),
    KEY `idx_featured` (`featured`),
    KEY `idx_expires_at` (`expires_at`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`escort_id`) REFERENCES `escorts`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`category_id`) REFERENCES `categories`(`id`) ON DELETE RESTRICT,
    FOREIGN KEY (`city_id`) REFERENCES `cities`(`id`) ON DELETE RESTRICT,
    FOREIGN KEY (`district_id`) REFERENCES `districts`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`package_id`) REFERENCES `packages`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 8. İlan fotoğrafları tablosu
CREATE TABLE `ad_photos` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `ad_id` int(11) NOT NULL,
    `filename` varchar(255) NOT NULL,
    `original_name` varchar(255) DEFAULT NULL,
    `file_size` int(11) DEFAULT NULL,
    `mime_type` varchar(50) DEFAULT NULL,
    `width` int(11) DEFAULT NULL,
    `height` int(11) DEFAULT NULL,
    `is_primary` tinyint(1) DEFAULT 0,
    `sort_order` int(11) DEFAULT 0,
    `status` tinyint(1) DEFAULT 1,
    `uploaded_at` datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_ad_id` (`ad_id`),
    KEY `idx_is_primary` (`is_primary`),
    KEY `idx_status` (`status`),
    FOREIGN KEY (`ad_id`) REFERENCES `ads`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 9. Ödemeler tablosu
CREATE TABLE `payments` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `package_id` int(11) DEFAULT NULL,
    `ad_id` int(11) DEFAULT NULL,
    `payment_type` enum('package','ad_boost','ad_feature','ad_renewal') NOT NULL,
    `amount` decimal(10,2) NOT NULL,
    `currency` varchar(3) DEFAULT 'TRY',
    `payment_method` varchar(50) DEFAULT 'paytr',
    `transaction_id` varchar(100) DEFAULT NULL,
    `paytr_token` varchar(255) DEFAULT NULL,
    `status` enum('pending','completed','failed','cancelled','refunded') DEFAULT 'pending',
    `payment_data` json DEFAULT NULL,
    `paid_at` datetime DEFAULT NULL,
    `expires_at` datetime DEFAULT NULL,
    `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_package_id` (`package_id`),
    KEY `idx_ad_id` (`ad_id`),
    KEY `idx_transaction_id` (`transaction_id`),
    KEY `idx_status` (`status`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`package_id`) REFERENCES `packages`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`ad_id`) REFERENCES `ads`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 10. Değerlendirmeler tablosu
CREATE TABLE `reviews` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `ad_id` int(11) NOT NULL,
    `user_id` int(11) DEFAULT NULL,
    `reviewer_name` varchar(50) DEFAULT NULL,
    `rating` tinyint(1) NOT NULL CHECK (`rating` >= 1 AND `rating` <= 5),
    `comment` text DEFAULT NULL,
    `is_verified` tinyint(1) DEFAULT 0,
    `status` enum('pending','approved','rejected') DEFAULT 'pending',
    `ip_address` varchar(45) DEFAULT NULL,
    `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_ad_id` (`ad_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_status` (`status`),
    FOREIGN KEY (`ad_id`) REFERENCES `ads`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 11. Favoriler tablosu
CREATE TABLE `favorites` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `ad_id` int(11) NOT NULL,
    `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_favorite` (`user_id`, `ad_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_ad_id` (`ad_id`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`ad_id`) REFERENCES `ads`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 12. Mesajlar tablosu
CREATE TABLE `messages` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `sender_id` int(11) DEFAULT NULL,
    `receiver_id` int(11) NOT NULL,
    `ad_id` int(11) DEFAULT NULL,
    `subject` varchar(200) DEFAULT NULL,
    `message` text NOT NULL,
    `is_read` tinyint(1) DEFAULT 0,
    `sender_deleted` tinyint(1) DEFAULT 0,
    `receiver_deleted` tinyint(1) DEFAULT 0,
    `ip_address` varchar(45) DEFAULT NULL,
    `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_sender_id` (`sender_id`),
    KEY `idx_receiver_id` (`receiver_id`),
    KEY `idx_ad_id` (`ad_id`),
    KEY `idx_is_read` (`is_read`),
    FOREIGN KEY (`sender_id`) REFERENCES `users`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`receiver_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`ad_id`) REFERENCES `ads`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 13. Şikayetler tablosu
CREATE TABLE `reports` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `reporter_id` int(11) DEFAULT NULL,
    `ad_id` int(11) DEFAULT NULL,
    `user_id` int(11) DEFAULT NULL,
    `type` enum('spam','fake','inappropriate','scam','other') NOT NULL,
    `reason` text NOT NULL,
    `status` enum('pending','investigating','resolved','dismissed') DEFAULT 'pending',
    `admin_notes` text DEFAULT NULL,
    `ip_address` varchar(45) DEFAULT NULL,
    `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_reporter_id` (`reporter_id`),
    KEY `idx_ad_id` (`ad_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_status` (`status`),
    FOREIGN KEY (`reporter_id`) REFERENCES `users`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`ad_id`) REFERENCES `ads`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 14. Site ayarları tablosu
CREATE TABLE `settings` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `key` varchar(100) NOT NULL UNIQUE,
    `value` text DEFAULT NULL,
    `type` enum('text','number','boolean','json','file') DEFAULT 'text',
    `group` varchar(50) DEFAULT 'general',
    `description` text DEFAULT NULL,
    `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_key` (`key`),
    KEY `idx_group` (`group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 15. Sistem logları tablosu
CREATE TABLE `logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) DEFAULT NULL,
    `action` varchar(100) NOT NULL,
    `table_name` varchar(50) DEFAULT NULL,
    `record_id` int(11) DEFAULT NULL,
    `old_values` json DEFAULT NULL,
    `new_values` json DEFAULT NULL,
    `ip_address` varchar(45) DEFAULT NULL,
    `user_agent` text DEFAULT NULL,
    `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_action` (`action`),
    KEY `idx_table_name` (`table_name`),
    KEY `idx_created_at` (`created_at`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- ÖRNEK VERİLER
-- ============================================================================

-- Admin kullanıcısı ekleme (şifre: admin123)
INSERT INTO `users` (`username`, `email`, `password`, `first_name`, `last_name`, `role`, `status`, `email_verified`) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'User', 'admin', 'active', 1);

-- Türkiye şehirleri ekleme (örnekler)
INSERT INTO `cities` (`name`, `slug`, `plate_code`, `status`, `sort_order`) VALUES
('İstanbul', 'istanbul', '34', 1, 1),
('Ankara', 'ankara', '06', 1, 2),
('İzmir', 'izmir', '35', 1, 3),
('Antalya', 'antalya', '07', 1, 4),
('Bursa', 'bursa', '16', 1, 5),
('Adana', 'adana', '01', 1, 6),
('Gaziantep', 'gaziantep', '27', 1, 7),
('Konya', 'konya', '42', 1, 8);

-- İstanbul ilçeleri ekleme
INSERT INTO `districts` (`city_id`, `name`, `slug`, `status`, `sort_order`) VALUES
(1, 'Kadıköy', 'kadikoy', 1, 1),
(1, 'Beşiktaş', 'besiktas', 1, 2),
(1, 'Şişli', 'sisli', 1, 3),
(1, 'Beyoğlu', 'beyoglu', 1, 4),
(1, 'Fatih', 'fatih', 1, 5),
(1, 'Bakırköy', 'bakirkoy', 1, 6),
(1, 'Ataşehir', 'atasehir', 1, 7),
(1, 'Maltepe', 'maltepe', 1, 8);

-- Kategoriler ekleme
INSERT INTO `categories` (`name`, `slug`, `description`, `icon`, `color`, `status`, `sort_order`) VALUES
('Escort', 'escort', 'Profesyonel escort hizmetleri', 'fas fa-heart', '#E91E63', 1, 1),
('Masaj', 'masaj', 'Rahatlatıcı masaj hizmetleri', 'fas fa-spa', '#9C27B0', 1, 2),
('VIP Escort', 'vip-escort', 'Premium VIP escort hizmetleri', 'fas fa-crown', '#FF4081', 1, 3),
('Çift Escort', 'cift-escort', 'Çiftler için özel hizmetler', 'fas fa-users', '#E91E63', 1, 4);

-- Üyelik paketleri ekleme
INSERT INTO `packages` (`name`, `slug`, `description`, `price`, `duration_days`, `max_ads`, `max_photos`, `featured`, `priority`, `color`, `features`, `status`, `sort_order`) VALUES
('Bronz', 'bronz', 'Temel üyelik paketi', 50.00, 30, 1, 5, 0, 0, '#CD7F32', '["Temel ilan", "5 fotoğraf", "30 gün süre"]', 1, 1),
('Gümüş', 'gumus', 'Gelişmiş üyelik paketi', 80.00, 60, 2, 10, 1, 0, '#C0C0C0', '["2 ilan", "10 fotoğraf", "Öne çıkarma", "60 gün süre"]', 1, 2),
('Altın', 'altin', 'Premium üyelik paketi', 120.00, 90, 3, 15, 1, 1, '#FFD700', '["3 ilan", "15 fotoğraf", "VIP listeleme", "Öncelik", "90 gün süre"]', 1, 3),
('Platinum', 'platinum', 'En üst seviye paket', 200.00, 180, 5, 25, 1, 1, '#E5E4E2', '["5 ilan", "25 fotoğraf", "Süper VIP", "En üst öncelik", "180 gün süre"]', 1, 4);

-- Site ayarları ekleme
INSERT INTO `settings` (`key`, `value`, `type`, `group`, `description`) VALUES
('site_name', 'Escort İlan Sitesi', 'text', 'general', 'Site adı'),
('site_description', 'Türkiye\'nin en güvenilir escort ilan sitesi', 'text', 'general', 'Site açıklaması'),
('site_keywords', 'escort, ilan, türkiye, güvenilir', 'text', 'seo', 'Site anahtar kelimeleri'),
('admin_email', '<EMAIL>', 'text', 'general', 'Admin e-posta adresi'),
('paytr_merchant_id', '', 'text', 'payment', 'PayTR Merchant ID'),
('paytr_merchant_key', '', 'text', 'payment', 'PayTR Merchant Key'),
('paytr_merchant_salt', '', 'text', 'payment', 'PayTR Merchant Salt'),
('paytr_test_mode', '1', 'boolean', 'payment', 'PayTR test modu'),
('max_photo_size', '5', 'number', 'upload', 'Maksimum fotoğraf boyutu (MB)'),
('allowed_photo_types', 'jpg,jpeg,png,gif', 'text', 'upload', 'İzin verilen fotoğraf türleri'),
('site_theme_color', '#E91E63', 'text', 'design', 'Site ana rengi'),
('site_secondary_color', '#9C27B0', 'text', 'design', 'Site ikincil rengi');

-- ============================================================================
-- İNDEXLER VE OPTİMİZASYONLAR
-- ============================================================================

-- Performans için ek indexler
CREATE INDEX idx_ads_city_category ON ads(city_id, category_id, status);
CREATE INDEX idx_ads_featured_priority ON ads(featured, priority, status);
CREATE INDEX idx_ads_expires_status ON ads(expires_at, status);
CREATE INDEX idx_users_role_status ON users(role, status);
CREATE INDEX idx_payments_status_created ON payments(status, created_at);

-- Veritabanı kurulumu tamamlandı
SELECT 'Escort İlan Sitesi veritabanı başarıyla kuruldu!' as message;
