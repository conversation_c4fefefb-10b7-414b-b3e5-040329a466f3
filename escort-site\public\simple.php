<?php
// Basit PHP test sayfası
echo "<!DOCTYPE html>";
echo "<html lang='tr'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>Escort İlan Sitesi - Test</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { background: linear-gradient(45deg, #E91E63, #9C27B0); color: white; font-family: Arial, sans-serif; }";
echo ".container { margin-top: 50px; }";
echo ".card { background: rgba(255,255,255,0.1); border: none; backdrop-filter: blur(10px); }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<div class='row justify-content-center'>";
echo "<div class='col-md-8'>";
echo "<div class='card p-4'>";
echo "<h1 class='text-center mb-4'>🌹 Escort İlan Sitesi</h1>";
echo "<p class='text-center'>PHP " . phpversion() . " ile çalışıyor</p>";

// Veritabanı bağlantısı test et
try {
    $host = 'localhost';
    $dbname = 'escort_site';
    $username = 'root';
    $password = '';
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    echo "<div class='alert alert-success'>✅ Veritabanı bağlantısı başarılı!</div>";
    
    // Kategorileri listele
    $stmt = $pdo->query("SELECT * FROM categories WHERE status = 1 ORDER BY sort_order ASC");
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Kategoriler:</h3>";
    echo "<div class='row'>";
    foreach ($categories as $category) {
        echo "<div class='col-md-6 mb-3'>";
        echo "<div class='card p-3'>";
        echo "<h5>" . htmlspecialchars($category['name']) . "</h5>";
        echo "<p class='small'>" . htmlspecialchars($category['description']) . "</p>";
        echo "</div>";
        echo "</div>";
    }
    echo "</div>";
    
    // Şehirleri listele
    $stmt = $pdo->query("SELECT * FROM cities WHERE status = 1 ORDER BY sort_order ASC LIMIT 8");
    $cities = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Şehirler:</h3>";
    echo "<div class='row'>";
    foreach ($cities as $city) {
        echo "<div class='col-md-3 mb-2'>";
        echo "<span class='badge bg-light text-dark'>" . htmlspecialchars($city['name']) . "</span>";
        echo "</div>";
    }
    echo "</div>";
    
    // İstatistikler
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM categories WHERE status = 1");
    $categoryCount = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM cities WHERE status = 1");
    $cityCount = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM settings");
    $settingCount = $stmt->fetch()['count'];
    
    echo "<h3>İstatistikler:</h3>";
    echo "<div class='row text-center'>";
    echo "<div class='col-md-4'>";
    echo "<h2>" . $categoryCount . "</h2>";
    echo "<p>Kategori</p>";
    echo "</div>";
    echo "<div class='col-md-4'>";
    echo "<h2>" . $cityCount . "</h2>";
    echo "<p>Şehir</p>";
    echo "</div>";
    echo "<div class='col-md-4'>";
    echo "<h2>" . $settingCount . "</h2>";
    echo "<p>Ayar</p>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ Veritabanı hatası: " . $e->getMessage() . "</div>";
}

echo "<div class='text-center mt-4'>";
echo "<a href='index.php' class='btn btn-light me-2'>CodeIgniter Dene</a>";
echo "<a href='debug.php' class='btn btn-outline-light'>Debug Sayfası</a>";
echo "</div>";

echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "</body>";
echo "</html>";
?>
