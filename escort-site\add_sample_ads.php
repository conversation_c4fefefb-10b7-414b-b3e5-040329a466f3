<?php
// Örnek İlanlar Ekleme Scripti
// PHP 7.1 Uyumlu

try {
    $pdo = new PDO("mysql:host=localhost;dbname=escort_site;charset=utf8mb4", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Örnek İlanlar Ekleniyor...</h2>";
    
    // Önce admin kullanı<PERSON>ı<PERSON>ını al
    $stmt = $pdo->query("SELECT id FROM users WHERE role = 'admin' LIMIT 1");
    $admin = $stmt->fetch();
    $adminId = $admin ? $admin['id'] : 1;
    
    // Kategorileri al
    $stmt = $pdo->query("SELECT id, name FROM categories WHERE status = 1");
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Şehirleri al
    $stmt = $pdo->query("SELECT id, name FROM cities WHERE status = 1");
    $cities = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Örnek ilan verileri
    $sampleAds = array(
        array(
            'title' => 'Elit Escort Hizmetleri - İstanbul',
            'description' => 'Profesyonel ve kaliteli escort hizmetleri sunuyoruz. Deneyimli ve güvenilir kadromuzla size en iyi hizmeti vermeyi hedefliyoruz. 7/24 hizmet vermekteyiz.',
            'price_per_hour' => 500,
            'contact_phone' => '0555 123 4567',
            'featured' => 1,
            'priority' => 1
        ),
        array(
            'title' => 'VIP Escort Ankara - Premium Hizmet',
            'description' => 'Ankara\'da VIP escort hizmetleri. Kaliteli ve güvenilir hizmet anlayışımızla müşteri memnuniyetini ön planda tutuyoruz.',
            'price_per_hour' => 400,
            'contact_phone' => '0555 234 5678',
            'featured' => 1,
            'priority' => 2
        ),
        array(
            'title' => 'Masaj ve Wellness Hizmetleri',
            'description' => 'Rahatlatıcı masaj hizmetleri ve wellness uygulamaları. Stres atmanız ve rahatlmanız için ideal ortam sunuyoruz.',
            'price_per_hour' => 300,
            'contact_phone' => '0555 345 6789',
            'featured' => 0,
            'priority' => 0
        ),
        array(
            'title' => 'İzmir Escort - Kaliteli Hizmet',
            'description' => 'İzmir bölgesinde kaliteli escort hizmetleri. Müşteri memnuniyeti odaklı çalışma prensibimizle hizmet vermekteyiz.',
            'price_per_hour' => 350,
            'contact_phone' => '0555 456 7890',
            'featured' => 1,
            'priority' => 3
        ),
        array(
            'title' => 'Çift Escort Hizmetleri - Özel',
            'description' => 'Çiftler için özel tasarlanmış escort hizmetleri. Gizlilik ve kalite önceliğimizdir. Randevu için iletişime geçiniz.',
            'price_per_hour' => 600,
            'contact_phone' => '0555 567 8901',
            'featured' => 0,
            'priority' => 0
        ),
        array(
            'title' => 'Antalya VIP Escort - Lüks Hizmet',
            'description' => 'Antalya\'da lüks VIP escort hizmetleri. Tatil bölgesinde kaliteli ve güvenilir hizmet sunuyoruz.',
            'price_per_hour' => 450,
            'contact_phone' => '0555 678 9012',
            'featured' => 1,
            'priority' => 4
        ),
        array(
            'title' => 'Bursa Escort - Güvenilir Hizmet',
            'description' => 'Bursa bölgesinde güvenilir escort hizmetleri. Kaliteli hizmet anlayışımızla müşterilerimize hizmet vermekteyiz.',
            'price_per_hour' => 320,
            'contact_phone' => '0555 789 0123',
            'featured' => 0,
            'priority' => 0
        ),
        array(
            'title' => 'Premium Masaj Merkezi',
            'description' => 'Premium masaj hizmetleri ve spa uygulamaları. Uzman masörlerimizle size en iyi hizmeti sunuyoruz.',
            'price_per_hour' => 280,
            'contact_phone' => '0555 890 1234',
            'featured' => 0,
            'priority' => 0
        )
    );
    
    // İlanları ekle
    $insertCount = 0;
    foreach ($sampleAds as $index => $adData) {
        // Rastgele kategori ve şehir seç
        $category = $categories[array_rand($categories)];
        $city = $cities[array_rand($cities)];
        
        // Slug oluştur
        $slug = strtolower(str_replace(' ', '-', $adData['title']));
        $slug = preg_replace('/[^a-z0-9\-]/', '', $slug);
        
        // Son kullanma tarihi (30 gün sonra)
        $expiresAt = date('Y-m-d H:i:s', strtotime('+30 days'));
        
        $stmt = $pdo->prepare("
            INSERT INTO ads (
                user_id, category_id, city_id, title, slug, description, 
                price_per_hour, contact_phone, featured, priority, status, 
                expires_at, published_at, created_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, NOW(), NOW()
            )
        ");
        
        $result = $stmt->execute(array(
            $adminId,
            $category['id'],
            $city['id'],
            $adData['title'],
            $slug . '-' . ($index + 1),
            $adData['description'],
            $adData['price_per_hour'],
            $adData['contact_phone'],
            $adData['featured'],
            $adData['priority'],
            $expiresAt
        ));
        
        if ($result) {
            $insertCount++;
            echo "<p>✅ İlan eklendi: " . htmlspecialchars($adData['title']) . " (" . $category['name'] . " - " . $city['name'] . ")</p>";
        }
    }
    
    echo "<h3>✅ Toplam $insertCount ilan başarıyla eklendi!</h3>";
    echo "<p><a href='index.php'>Ana Sayfaya Dön</a></p>";
    
} catch (PDOException $e) {
    echo "<h3>❌ Hata: " . $e->getMessage() . "</h3>";
}
?>
