<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Giriş kontrolü
requireLogin();

// Sadece aile kullanıcıları erişebilir
if ($_SESSION['user_type'] !== 'family') {
    redirect('../dashboard.php');
}

$job_id = safeNumber(safeArray($_GET, 'id', 0));
$user_id = $_SESSION['user_id'];

if (!$job_id) {
    redirect('my-jobs.php');
}

// İş ilanını getir ve sahiplik kontrolü yap
try {
    $sql = "SELECT * FROM job_listings WHERE id = ? AND user_id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$job_id, $user_id]);
    $job = $stmt->fetch();
    
    if (!$job) {
        redirect('my-jobs.php');
    }
} catch (PDOException $e) {
    redirect('my-jobs.php');
}

$error_message = '';
$success_message = '';

// Form gönderildi mi?
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrf_token = safeArray($_POST, 'csrf_token', '');
    
    if (!validateCSRFToken($csrf_token)) {
        $error_message = 'Güvenlik hatası. Lütfen sayfayı yenileyin.';
    } else {
        $title = trim(safeArray($_POST, 'title', ''));
        $job_type = safeArray($_POST, 'job_type', '');
        $care_type = safeArray($_POST, 'care_type', '');
        $description = trim(safeArray($_POST, 'description', ''));
        $location_city = safeArray($_POST, 'location_city', '');
        $location_district = trim(safeArray($_POST, 'location_district', ''));
        $budget_type = safeArray($_POST, 'budget_type', 'monthly');
        $budget_min = safeNumber(safeArray($_POST, 'budget_min', 0));
        $budget_max = safeNumber(safeArray($_POST, 'budget_max', 0));
        $start_date = safeArray($_POST, 'start_date', '');
        $expires_at = safeArray($_POST, 'expires_at', '');
        $requirements = trim(safeArray($_POST, 'requirements', ''));
        $benefits = trim(safeArray($_POST, 'benefits', ''));
        $is_urgent = safeArray($_POST, 'is_urgent', false) ? 1 : 0;
        $status = safeArray($_POST, 'status', 'active');
        
        // Validasyon
        if (empty($title)) {
            $error_message = 'İş ilanı başlığı gereklidir.';
        } elseif (!validateLength($title, 5, 200)) {
            $error_message = 'İş ilanı başlığı 5-200 karakter arasında olmalıdır.';
        } elseif (empty($job_type)) {
            $error_message = 'İş tipi seçmelisiniz.';
        } elseif (empty($care_type)) {
            $error_message = 'Bakım tipi seçmelisiniz.';
        } elseif (empty($description)) {
            $error_message = 'İş açıklaması gereklidir.';
        } elseif (!validateLength($description, 20, 2000)) {
            $error_message = 'İş açıklaması 20-2000 karakter arasında olmalıdır.';
        } elseif (empty($location_city)) {
            $error_message = 'Şehir seçmelisiniz.';
        } elseif ($budget_min < 0 || $budget_max < 0) {
            $error_message = 'Bütçe değerleri negatif olamaz.';
        } elseif ($budget_min > 0 && $budget_max > 0 && $budget_min > $budget_max) {
            $error_message = 'Minimum bütçe maksimum bütçeden büyük olamaz.';
        } else {
            try {
                // Başlık değiştiyse yeni slug oluştur
                $slug = $job['slug'];
                if ($title !== $job['title']) {
                    $slug = generateUniqueSlug('job_listings', $title, $job_id);
                }
                
                // İş ilanını güncelle
                $sql = "UPDATE job_listings SET 
                        title = ?, slug = ?, job_type = ?, care_type = ?, description = ?, 
                        location_city = ?, location_district = ?, budget_min = ?, budget_max = ?, budget_type = ?, 
                        start_date = ?, expires_at = ?, requirements = ?, benefits = ?, is_urgent = ?, status = ?,
                        updated_at = NOW()
                        WHERE id = ? AND user_id = ?";
                
                $stmt = $db->prepare($sql);
                $stmt->execute([
                    $title, $slug, $job_type, $care_type, $description, $location_city, $location_district, 
                    $budget_min, $budget_max, $budget_type, $start_date ?: null, $expires_at, 
                    $requirements, $benefits, $is_urgent, $status, $job_id, $user_id
                ]);
                
                // Aktivite kaydı
                logActivity($user_id, 'job_update', 'İş ilanını güncelledi: ' . $title, 'job_listings', $job_id);
                
                // Başarı mesajı
                $success_message = 'İş ilanınız başarıyla güncellendi!';
                
                // Güncellenmiş veriyi tekrar getir
                $stmt = $db->prepare("SELECT * FROM job_listings WHERE id = ? AND user_id = ?");
                $stmt->execute([$job_id, $user_id]);
                $job = $stmt->fetch();
                
            } catch (PDOException $e) {
                $error_message = 'İlan güncellenirken bir hata oluştu. Lütfen tekrar deneyin.';
                error_log("Job update error: " . $e->getMessage());
            }
        }
    }
}

$page_title = 'İş İlanı Düzenle';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c5aa0;
        }
        
        .form-card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            background: white;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
        }
        
        .navbar {
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="../index.php">
                <i class="bi bi-heart-fill me-2"></i><?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../caregivers.php">Bakıcı Ara</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../jobs.php">İş İlanları</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../about.php">Hakkımızda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../contact.php">İletişim</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i><?php echo escape($_SESSION['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../dashboard.php"><i class="bi bi-speedometer2 me-2"></i>Dashboard</a></li>
                            <li><a class="dropdown-item" href="../profile.php"><i class="bi bi-person me-2"></i>Profil</a></li>
                            <li><a class="dropdown-item" href="../messages.php"><i class="bi bi-chat-dots me-2"></i>Mesajlar</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../auth/logout.php"><i class="bi bi-box-arrow-right me-2"></i>Çıkış</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4 mb-5">
        <!-- Sayfa Başlığı -->
        <div class="row mb-4">
            <div class="col">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="../dashboard.php">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="my-jobs.php">İlanlarım</a></li>
                        <li class="breadcrumb-item active">Düzenle</li>
                    </ol>
                </nav>
                <h2 class="fw-bold">İş İlanı Düzenle</h2>
                <p class="text-muted">İş ilanınızı güncelleyin</p>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="form-card p-5">
                    <?php if ($error_message): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i><?php echo escape($error_message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success_message): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle me-2"></i><?php echo escape($success_message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <form id="jobForm" method="POST">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <!-- Temel Bilgiler -->
                        <div class="mb-5">
                            <h4 class="fw-bold mb-4">Temel Bilgiler</h4>
                            
                            <div class="mb-3">
                                <label for="title" class="form-label">İş İlanı Başlığı *</label>
                                <input type="text" class="form-control" id="title" name="title" 
                                       value="<?php echo escape($job['title']); ?>"
                                       placeholder="Örn: Deneyimli Çocuk Bakıcısı Aranıyor" required>
                            </div>
                            
                            <div class="row g-3 mb-3">
                                <div class="col-md-6">
                                    <label for="job_type" class="form-label">İş Tipi *</label>
                                    <select class="form-select" id="job_type" name="job_type" required>
                                        <option value="">Seçin</option>
                                        <?php foreach (getJobTypes() as $key => $value): ?>
                                            <option value="<?php echo $key; ?>" <?php echo $job['job_type'] === $key ? 'selected' : ''; ?>>
                                                <?php echo $value; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="care_type" class="form-label">Bakım Tipi *</label>
                                    <select class="form-select" id="care_type" name="care_type" required>
                                        <option value="">Seçin</option>
                                        <?php foreach (getCareTypes() as $key => $value): ?>
                                            <option value="<?php echo $key; ?>" <?php echo $job['care_type'] === $key ? 'selected' : ''; ?>>
                                                <?php echo $value; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">İş Açıklaması *</label>
                                <textarea class="form-control" id="description" name="description" rows="5" 
                                          placeholder="İş detaylarını, beklentilerinizi ve özel durumları açıklayın..." required><?php echo escape($job['description']); ?></textarea>
                            </div>
                        </div>
                        
                        <!-- Lokasyon -->
                        <div class="mb-5">
                            <h4 class="fw-bold mb-4">Lokasyon</h4>
                            
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="location_city" class="form-label">Şehir *</label>
                                    <select class="form-select" id="location_city" name="location_city" required>
                                        <option value="">Şehir seçin</option>
                                        <?php foreach (getCities() as $city): ?>
                                            <option value="<?php echo $city; ?>" <?php echo $job['location_city'] === $city ? 'selected' : ''; ?>>
                                                <?php echo $city; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="location_district" class="form-label">İlçe</label>
                                    <input type="text" class="form-control" id="location_district" name="location_district" 
                                           value="<?php echo escape($job['location_district']); ?>"
                                           placeholder="İlçe adı">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Bütçe -->
                        <div class="mb-5">
                            <h4 class="fw-bold mb-4">Bütçe</h4>
                            
                            <div class="row g-3 mb-3">
                                <div class="col-md-4">
                                    <label for="budget_type" class="form-label">Ödeme Tipi</label>
                                    <select class="form-select" id="budget_type" name="budget_type">
                                        <option value="hourly" <?php echo $job['budget_type'] === 'hourly' ? 'selected' : ''; ?>>Saatlik</option>
                                        <option value="daily" <?php echo $job['budget_type'] === 'daily' ? 'selected' : ''; ?>>Günlük</option>
                                        <option value="weekly" <?php echo $job['budget_type'] === 'weekly' ? 'selected' : ''; ?>>Haftalık</option>
                                        <option value="monthly" <?php echo $job['budget_type'] === 'monthly' ? 'selected' : ''; ?>>Aylık</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="budget_min" class="form-label">Minimum Bütçe (TL)</label>
                                    <input type="number" class="form-control" id="budget_min" name="budget_min" 
                                           value="<?php echo $job['budget_min']; ?>"
                                           placeholder="0" min="0">
                                </div>
                                <div class="col-md-4">
                                    <label for="budget_max" class="form-label">Maksimum Bütçe (TL)</label>
                                    <input type="number" class="form-control" id="budget_max" name="budget_max" 
                                           value="<?php echo $job['budget_max']; ?>"
                                           placeholder="0" min="0">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Tarih ve Süre -->
                        <div class="mb-5">
                            <h4 class="fw-bold mb-4">Tarih ve Süre</h4>
                            
                            <div class="row g-3 mb-3">
                                <div class="col-md-6">
                                    <label for="start_date" class="form-label">Başlangıç Tarihi</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date"
                                           value="<?php echo $job['start_date']; ?>">
                                </div>
                                <div class="col-md-6">
                                    <label for="expires_at" class="form-label">İlan Bitiş Tarihi</label>
                                    <input type="date" class="form-control" id="expires_at" name="expires_at"
                                           value="<?php echo date('Y-m-d', strtotime($job['expires_at'])); ?>">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Ek Bilgiler -->
                        <div class="mb-5">
                            <h4 class="fw-bold mb-4">Ek Bilgiler</h4>
                            
                            <div class="mb-3">
                                <label for="requirements" class="form-label">Aranan Özellikler</label>
                                <textarea class="form-control" id="requirements" name="requirements" rows="3" 
                                          placeholder="Deneyim, eğitim, sertifika vb. gereksinimler..."><?php echo escape($job['requirements']); ?></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="benefits" class="form-label">Sunulan İmkanlar</label>
                                <textarea class="form-control" id="benefits" name="benefits" rows="3" 
                                          placeholder="Yemek, ulaşım, sigorta vb. imkanlar..."><?php echo escape($job['benefits']); ?></textarea>
                            </div>
                            
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="is_urgent" name="is_urgent" value="1"
                                       <?php echo $job['is_urgent'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="is_urgent">
                                    <strong>Acil İlan</strong> - İlanınız öne çıkarılsın
                                </label>
                            </div>
                        </div>
                        
                        <!-- Durum -->
                        <div class="mb-5">
                            <h4 class="fw-bold mb-4">İlan Durumu</h4>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="status" class="form-label">Durum</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="active" <?php echo $job['status'] === 'active' ? 'selected' : ''; ?>>Aktif</option>
                                        <option value="paused" <?php echo $job['status'] === 'paused' ? 'selected' : ''; ?>>Duraklatılmış</option>
                                        <option value="closed" <?php echo $job['status'] === 'closed' ? 'selected' : ''; ?>>Kapatılmış</option>
                                        <option value="draft" <?php echo $job['status'] === 'draft' ? 'selected' : ''; ?>>Taslak</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Butonlar -->
                        <div class="d-flex gap-3">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="bi bi-check-circle me-2"></i>Değişiklikleri Kaydet
                            </button>
                            <a href="../job-detail.php?slug=<?php echo $job['slug']; ?>" class="btn btn-outline-primary btn-lg">
                                <i class="bi bi-eye me-2"></i>İlanı Görüntüle
                            </a>
                            <a href="my-jobs.php" class="btn btn-outline-secondary btn-lg">
                                <i class="bi bi-arrow-left me-2"></i>İlanlarıma Dön
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Form validasyonu
        document.getElementById('jobForm').addEventListener('submit', function(e) {
            const title = document.getElementById('title').value.trim();
            const jobType = document.getElementById('job_type').value;
            const careType = document.getElementById('care_type').value;
            const description = document.getElementById('description').value.trim();
            const city = document.getElementById('location_city').value;
            
            if (!title || !jobType || !careType || !description || !city) {
                e.preventDefault();
                alert('Lütfen tüm zorunlu alanları doldurun.');
                return;
            }
            
            // Loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Güncelleniyor...';
            submitBtn.disabled = true;
        });
        
        // Bugünden önceki tarihleri engelle
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('start_date').min = today;
        document.getElementById('expires_at').min = today;
    </script>
</body>
</html>
