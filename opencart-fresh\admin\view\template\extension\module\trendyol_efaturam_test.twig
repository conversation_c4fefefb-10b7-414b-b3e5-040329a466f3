{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ back }}" data-toggle="tooltip" title="Geri" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }} - Test</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-check-circle"></i> API Bağlantı Testi</h3>
      </div>
      <div class="panel-body">
        {% if test_result %}
          {% if test_result.success %}
            <div class="alert alert-success">
              <i class="fa fa-check-circle"></i> <strong>Başarılı!</strong> {{ test_result.message }}
            </div>
            {% if test_result.data %}
              <h4>API Yanıtı:</h4>
              <pre class="well">{{ test_result.data|json_encode(constant('JSON_PRETTY_PRINT')) }}</pre>
            {% endif %}
          {% else %}
            <div class="alert alert-danger">
              <i class="fa fa-exclamation-circle"></i> <strong>Hata!</strong> {{ test_result.message }}
            </div>
            {% if test_result.data %}
              <h4>Hata Detayları:</h4>
              <pre class="well">{{ test_result.data|json_encode(constant('JSON_PRETTY_PRINT')) }}</pre>
            {% endif %}
          {% endif %}
        {% else %}
          <div class="alert alert-info">
            <i class="fa fa-info-circle"></i> Test henüz çalıştırılmadı.
          </div>
        {% endif %}
        
        <div class="row">
          <div class="col-sm-12">
            <h4>Test Bilgileri:</h4>
            <table class="table table-bordered">
              <tr>
                <td><strong>API URL:</strong></td>
                <td>{{ api_url|default('Yapılandırılmamış') }}</td>
              </tr>
              <tr>
                <td><strong>Kullanıcı Adı:</strong></td>
                <td>{{ username|default('Yapılandırılmamış') }}</td>
              </tr>
              <tr>
                <td><strong>Test Modu:</strong></td>
                <td>{{ test_mode ? 'Etkin' : 'Devre Dışı' }}</td>
              </tr>
              <tr>
                <td><strong>Test Zamanı:</strong></td>
                <td>{{ "now"|date("d/m/Y H:i:s") }}</td>
              </tr>
            </table>
          </div>
        </div>
        
        <div class="row">
          <div class="col-sm-12">
            <a href="{{ test_url }}" class="btn btn-primary">
              <i class="fa fa-refresh"></i> Testi Tekrar Çalıştır
            </a>
          </div>
        </div>
      </div>
    </div>
    
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-info-circle"></i> Test Hakkında</h3>
      </div>
      <div class="panel-body">
        <p>Bu test, Trendyol E-Faturam API'sine bağlantınızı kontrol eder. Test başarılı olursa:</p>
        <ul>
          <li>API URL'niz doğru yapılandırılmış</li>
          <li>Kimlik bilgileriniz geçerli</li>
          <li>Ağ bağlantınız çalışıyor</li>
          <li>Modül fatura göndermeye hazır</li>
        </ul>
        
        <p>Test başarısız olursa, lütfen aşağıdakileri kontrol edin:</p>
        <ul>
          <li>API URL'nin doğru olduğundan emin olun</li>
          <li>Kullanıcı adı ve şifrenizi kontrol edin</li>
          <li>İnternet bağlantınızı kontrol edin</li>
          <li>Firewall ayarlarınızı kontrol edin</li>
        </ul>
      </div>
    </div>
  </div>
</div>
{{ footer }}
