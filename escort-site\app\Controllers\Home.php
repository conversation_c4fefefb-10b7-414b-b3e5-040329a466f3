<?php

namespace App\Controllers;

use App\Models\AdModel;
use App\Models\CategoryModel;
use App\Models\CityModel;
use App\Models\SettingModel;

class Home extends BaseController
{
    protected $adModel;
    protected $categoryModel;
    protected $cityModel;
    protected $settingModel;

    public function __construct()
    {
        $this->adModel = new AdModel();
        $this->categoryModel = new CategoryModel();
        $this->cityModel = new CityModel();
        $this->settingModel = new SettingModel();
    }

    public function index()
    {
        // Basit veritabanı sorguları ile veri al
        $db = \Config\Database::connect();

        try {
            // Site ayarlarını al
            $settingsQuery = $db->query("SELECT `key`, `value` FROM settings");
            $settingsResult = $settingsQuery->getResult();
            $settings = [];
            foreach ($settingsResult as $setting) {
                $settings[$setting->key] = $setting->value;
            }

            // Kategorileri al
            $categoriesQuery = $db->query("SELECT * FROM categories WHERE status = 1 ORDER BY sort_order ASC");
            $categories = $categoriesQuery->getResult('array');

            // Şehirleri al
            $citiesQuery = $db->query("SELECT * FROM cities WHERE status = 1 ORDER BY sort_order ASC");
            $cities = $citiesQuery->getResult('array');

            // İstatistikleri al
            $totalAdsQuery = $db->query("SELECT COUNT(*) as count FROM ads WHERE status = 'active' AND expires_at > NOW()");
            $totalCitiesQuery = $db->query("SELECT COUNT(*) as count FROM cities WHERE status = 1");
            $totalCategoriesQuery = $db->query("SELECT COUNT(*) as count FROM categories WHERE status = 1");
            $todayAdsQuery = $db->query("SELECT COUNT(*) as count FROM ads WHERE status = 'active' AND DATE(created_at) = CURDATE()");

            $stats = [
                'total_ads' => $totalAdsQuery->getRow()->count,
                'total_cities' => $totalCitiesQuery->getRow()->count,
                'total_categories' => $totalCategoriesQuery->getRow()->count,
                'today_ads' => $todayAdsQuery->getRow()->count
            ];

        } catch (\Exception $e) {
            // Hata durumunda varsayılan değerler
            $settings = [
                'site_name' => 'Escort İlan Sitesi',
                'site_description' => 'Türkiye\'nin en güvenilir escort ilan sitesi',
                'site_keywords' => 'escort, ilan, türkiye'
            ];

            $categories = [];
            $cities = [];
            $stats = [
                'total_ads' => 0,
                'total_cities' => 8,
                'total_categories' => 4,
                'today_ads' => 0
            ];
        }

        $data = [
            'title' => $settings['site_name'] ?? 'Escort İlan Sitesi',
            'description' => $settings['site_description'] ?? 'Türkiye\'nin en güvenilir escort ilan sitesi',
            'keywords' => $settings['site_keywords'] ?? 'escort, ilan, türkiye',
            'settings' => $settings,
            'categories' => $categories,
            'cities' => $cities,
            'stats' => $stats
        ];

        return view('home/index', $data);
    }

    public function about()
    {
        $settings = $this->settingModel->getSettings();

        $data = [
            'title' => 'Hakkımızda - ' . ($settings['site_name'] ?? 'Escort İlan Sitesi'),
            'settings' => $settings
        ];

        return view('home/about', $data);
    }

    public function contact()
    {
        $settings = $this->settingModel->getSettings();

        $data = [
            'title' => 'İletişim - ' . ($settings['site_name'] ?? 'Escort İlan Sitesi'),
            'settings' => $settings
        ];

        return view('home/contact', $data);
    }

    public function privacy()
    {
        $settings = $this->settingModel->getSettings();

        $data = [
            'title' => 'Gizlilik Politikası - ' . ($settings['site_name'] ?? 'Escort İlan Sitesi'),
            'settings' => $settings
        ];

        return view('home/privacy', $data);
    }

    public function terms()
    {
        $settings = $this->settingModel->getSettings();

        $data = [
            'title' => 'Kullanım Şartları - ' . ($settings['site_name'] ?? 'Escort İlan Sitesi'),
            'settings' => $settings
        ];

        return view('home/terms', $data);
    }
}
