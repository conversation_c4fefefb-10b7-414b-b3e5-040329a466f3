<?php

namespace App\Controllers;

use App\Models\AdModel;
use App\Models\CategoryModel;
use App\Models\CityModel;
use App\Models\SettingModel;

class Home extends BaseController
{
    protected $adModel;
    protected $categoryModel;
    protected $cityModel;
    protected $settingModel;

    public function __construct()
    {
        $this->adModel = new AdModel();
        $this->categoryModel = new CategoryModel();
        $this->cityModel = new CityModel();
        $this->settingModel = new SettingModel();
    }

    public function index()
    {
        try {
            // Site ayarlarını al
            $settings = $this->settingModel->getSettings();

            // Kategorileri al
            $categories = $this->categoryModel->getActiveCategories();

            // Şehirleri al
            $cities = $this->cityModel->getActiveCities();

            // İstatistikleri al
            $stats = [
                'total_ads' => $this->adModel->getTotalActiveAds(),
                'total_cities' => $this->cityModel->getTotalActiveCities(),
                'total_categories' => $this->categoryModel->getTotalActiveCategories(),
                'today_ads' => $this->adModel->getTodayAds()
            ];
        } catch (\Exception $e) {
            // Hata durumunda varsayılan değerler
            $settings = [
                'site_name' => 'Escort İlan Sitesi',
                'site_description' => 'Türkiye\'nin en güvenilir escort ilan sitesi',
                'site_keywords' => 'escort, ilan, türkiye'
            ];

            $categories = [];
            $cities = [];
            $stats = [
                'total_ads' => 0,
                'total_cities' => 8,
                'total_categories' => 4,
                'today_ads' => 0
            ];
        }

        $data = [
            'title' => $settings['site_name'] ?? 'Escort İlan Sitesi',
            'description' => $settings['site_description'] ?? 'Türkiye\'nin en güvenilir escort ilan sitesi',
            'keywords' => $settings['site_keywords'] ?? 'escort, ilan, türkiye',
            'settings' => $settings,
            'categories' => $categories,
            'cities' => $cities,
            'stats' => $stats
        ];

        return view('home/index', $data);
    }

    public function about()
    {
        $settings = $this->settingModel->getSettings();

        $data = [
            'title' => 'Hakkımızda - ' . ($settings['site_name'] ?? 'Escort İlan Sitesi'),
            'settings' => $settings
        ];

        return view('home/about', $data);
    }

    public function contact()
    {
        $settings = $this->settingModel->getSettings();

        $data = [
            'title' => 'İletişim - ' . ($settings['site_name'] ?? 'Escort İlan Sitesi'),
            'settings' => $settings
        ];

        return view('home/contact', $data);
    }

    public function privacy()
    {
        $settings = $this->settingModel->getSettings();

        $data = [
            'title' => 'Gizlilik Politikası - ' . ($settings['site_name'] ?? 'Escort İlan Sitesi'),
            'settings' => $settings
        ];

        return view('home/privacy', $data);
    }

    public function terms()
    {
        $settings = $this->settingModel->getSettings();

        $data = [
            'title' => 'Kullanım Şartları - ' . ($settings['site_name'] ?? 'Escort İlan Sitesi'),
            'settings' => $settings
        ];

        return view('home/terms', $data);
    }
}
