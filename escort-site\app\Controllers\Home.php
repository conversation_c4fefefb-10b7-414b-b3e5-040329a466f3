<?php

namespace App\Controllers;

use App\Models\AdModel;
use App\Models\CategoryModel;
use App\Models\CityModel;
use App\Models\SettingModel;

class Home extends BaseController
{
    protected $adModel;
    protected $categoryModel;
    protected $cityModel;
    protected $settingModel;

    public function __construct()
    {
        $this->adModel = new AdModel();
        $this->categoryModel = new CategoryModel();
        $this->cityModel = new CityModel();
        $this->settingModel = new SettingModel();
    }

    public function index()
    {
        // Site ayarlarını al
        $settings = $this->settingModel->getSettings();

        // Öne çıkan ilanları al
        $featuredAds = $this->adModel->getFeaturedAds(8);

        // Son eklenen ilanları al
        $latestAds = $this->adModel->getLatestAds(12);

        // Kategorileri al
        $categories = $this->categoryModel->getActiveCategories();

        // Şehirleri al
        $cities = $this->cityModel->getActiveCities();

        // İstatistikleri al
        $stats = [
            'total_ads' => $this->adModel->getTotalActiveAds(),
            'total_cities' => $this->cityModel->getTotalActiveCities(),
            'total_categories' => $this->categoryModel->getTotalActiveCategories(),
            'today_ads' => $this->adModel->getTodayAds()
        ];

        $data = [
            'title' => $settings['site_name'] ?? 'Escort İlan Sitesi',
            'description' => $settings['site_description'] ?? 'Türkiye\'nin en güvenilir escort ilan sitesi',
            'keywords' => $settings['site_keywords'] ?? 'escort, ilan, türkiye',
            'settings' => $settings,
            'featuredAds' => $featuredAds,
            'latestAds' => $latestAds,
            'categories' => $categories,
            'cities' => $cities,
            'stats' => $stats
        ];

        return view('home/index', $data);
    }

    public function about()
    {
        $settings = $this->settingModel->getSettings();

        $data = [
            'title' => 'Hakkımızda - ' . ($settings['site_name'] ?? 'Escort İlan Sitesi'),
            'settings' => $settings
        ];

        return view('home/about', $data);
    }

    public function contact()
    {
        $settings = $this->settingModel->getSettings();

        $data = [
            'title' => 'İletişim - ' . ($settings['site_name'] ?? 'Escort İlan Sitesi'),
            'settings' => $settings
        ];

        return view('home/contact', $data);
    }

    public function privacy()
    {
        $settings = $this->settingModel->getSettings();

        $data = [
            'title' => 'Gizlilik Politikası - ' . ($settings['site_name'] ?? 'Escort İlan Sitesi'),
            'settings' => $settings
        ];

        return view('home/privacy', $data);
    }

    public function terms()
    {
        $settings = $this->settingModel->getSettings();

        $data = [
            'title' => 'Kullanım Şartları - ' . ($settings['site_name'] ?? 'Escort İlan Sitesi'),
            'settings' => $settings
        ];

        return view('home/terms', $data);
    }
}
