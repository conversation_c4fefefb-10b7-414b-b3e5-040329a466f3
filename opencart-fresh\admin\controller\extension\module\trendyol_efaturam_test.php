<?php
/**
 * Trendyol E-Faturam OpenCart 3.0.3.2 Extension
 * Test Controller - For testing module functionality
 * 
 * <AUTHOR> Name
 * @version 1.0.0
 * @license MIT
 */

class ControllerExtensionModuleTrendyolEfaturamTest extends Controller {
    
    private $error = array();
    
    public function index() {
        $this->load->language('extension/module/trendyol_efaturam');
        
        $this->document->setTitle($this->language->get('heading_title_test'));
        
        $data['heading_title'] = $this->language->get('heading_title_test');
        
        $data['breadcrumbs'] = array();
        
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_extension'),
            'href' => $this->url->link('marketplace/extension', 'user_token=' . $this->session->data['user_token'] . '&type=module', true)
        );
        
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title_test'),
            'href' => $this->url->link('extension/module/trendyol_efaturam_test', 'user_token=' . $this->session->data['user_token'], true)
        );
        
        // Test results
        $data['tests'] = array();
        
        // Test 1: Module Configuration
        $data['tests'][] = $this->testModuleConfiguration();
        
        // Test 2: Database Tables
        $data['tests'][] = $this->testDatabaseTables();
        
        // Test 3: API Connection
        $data['tests'][] = $this->testApiConnection();
        
        // Test 4: File Permissions
        $data['tests'][] = $this->testFilePermissions();
        
        // Test 5: PHP Extensions
        $data['tests'][] = $this->testPhpExtensions();
        
        // Test 6: Sample Invoice Data
        $data['tests'][] = $this->testSampleInvoiceData();
        
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        
        $this->response->setOutput($this->load->view('extension/module/trendyol_efaturam_test', $data));
    }
    
    /**
     * Test module configuration
     */
    private function testModuleConfiguration() {
        $test = array(
            'name' => 'Module Configuration',
            'description' => 'Check if module is properly configured',
            'status' => 'success',
            'message' => 'Module configuration is valid',
            'details' => array()
        );
        
        // Check if module is enabled
        if (!$this->config->get('module_trendyol_efaturam_status')) {
            $test['status'] = 'warning';
            $test['message'] = 'Module is not enabled';
            $test['details'][] = 'Enable the module in Extensions > Modules';
        }
        
        // Check API URL
        if (!$this->config->get('module_trendyol_efaturam_api_url')) {
            $test['status'] = 'error';
            $test['message'] = 'API URL is not configured';
            $test['details'][] = 'Set API URL in module settings';
        }
        
        // Check credentials
        if (!$this->config->get('module_trendyol_efaturam_username') || !$this->config->get('module_trendyol_efaturam_password')) {
            $test['status'] = 'error';
            $test['message'] = 'API credentials are not configured';
            $test['details'][] = 'Set username and password in module settings';
        }
        
        // Check company information
        if (!$this->config->get('module_trendyol_efaturam_company_name') || !$this->config->get('module_trendyol_efaturam_company_tax_number')) {
            $test['status'] = 'warning';
            $test['message'] = 'Company information is incomplete';
            $test['details'][] = 'Set company name and tax number for proper invoice generation';
        }
        
        return $test;
    }
    
    /**
     * Test database tables
     */
    private function testDatabaseTables() {
        $test = array(
            'name' => 'Database Tables',
            'description' => 'Check if required database tables exist',
            'status' => 'success',
            'message' => 'All required tables exist',
            'details' => array()
        );
        
        $required_tables = array(
            'trendyol_efaturam_invoices',
            'trendyol_efaturam_logs',
            'trendyol_efaturam_settings'
        );
        
        foreach ($required_tables as $table) {
            $query = $this->db->query("SHOW TABLES LIKE '" . DB_PREFIX . $table . "'");
            if ($query->num_rows == 0) {
                $test['status'] = 'error';
                $test['message'] = 'Missing database tables';
                $test['details'][] = 'Table ' . DB_PREFIX . $table . ' does not exist';
            } else {
                $test['details'][] = 'Table ' . DB_PREFIX . $table . ' exists';
            }
        }
        
        return $test;
    }
    
    /**
     * Test API connection
     */
    private function testApiConnection() {
        $test = array(
            'name' => 'API Connection',
            'description' => 'Test connection to Trendyol E-Faturam API',
            'status' => 'success',
            'message' => 'API connection successful',
            'details' => array()
        );
        
        try {
            $this->load->library('trendyol_efaturam_api');
            
            $api = new TrendyolEfaturamApi(
                $this->config->get('module_trendyol_efaturam_api_url'),
                $this->config->get('module_trendyol_efaturam_username'),
                $this->config->get('module_trendyol_efaturam_password'),
                $this->config->get('module_trendyol_efaturam_mode') == 'test'
            );
            
            $result = $api->testConnection();
            
            if ($result['success']) {
                $test['details'][] = 'API connection successful';
                $test['details'][] = 'Response: ' . json_encode($result['data']);
            } else {
                $test['status'] = 'error';
                $test['message'] = 'API connection failed';
                $test['details'][] = 'Error: ' . $result['error'];
            }
        } catch (Exception $e) {
            $test['status'] = 'error';
            $test['message'] = 'API connection failed';
            $test['details'][] = 'Exception: ' . $e->getMessage();
        }
        
        return $test;
    }
    
    /**
     * Test file permissions
     */
    private function testFilePermissions() {
        $test = array(
            'name' => 'File Permissions',
            'description' => 'Check if log directory is writable',
            'status' => 'success',
            'message' => 'File permissions are correct',
            'details' => array()
        );
        
        // Check log directory
        if (!is_writable(DIR_LOGS)) {
            $test['status'] = 'error';
            $test['message'] = 'Log directory is not writable';
            $test['details'][] = 'Make ' . DIR_LOGS . ' writable (chmod 755 or 777)';
        } else {
            $test['details'][] = 'Log directory is writable';
        }
        
        // Test log file creation
        $test_file = DIR_LOGS . 'trendyol_efaturam_test.log';
        if (file_put_contents($test_file, 'Test log entry') === false) {
            $test['status'] = 'error';
            $test['message'] = 'Cannot create log files';
            $test['details'][] = 'Unable to create test log file';
        } else {
            $test['details'][] = 'Log file creation successful';
            unlink($test_file); // Clean up test file
        }
        
        return $test;
    }
    
    /**
     * Test PHP extensions
     */
    private function testPhpExtensions() {
        $test = array(
            'name' => 'PHP Extensions',
            'description' => 'Check if required PHP extensions are loaded',
            'status' => 'success',
            'message' => 'All required extensions are loaded',
            'details' => array()
        );
        
        $required_extensions = array(
            'curl' => 'cURL extension for API communication',
            'json' => 'JSON extension for data processing',
            'openssl' => 'OpenSSL extension for secure connections'
        );
        
        foreach ($required_extensions as $extension => $description) {
            if (!extension_loaded($extension)) {
                $test['status'] = 'error';
                $test['message'] = 'Missing required PHP extensions';
                $test['details'][] = 'Missing: ' . $extension . ' - ' . $description;
            } else {
                $test['details'][] = 'Loaded: ' . $extension . ' - ' . $description;
            }
        }
        
        return $test;
    }
    
    /**
     * Test sample invoice data preparation
     */
    private function testSampleInvoiceData() {
        $test = array(
            'name' => 'Sample Invoice Data',
            'description' => 'Test invoice data preparation with sample order',
            'status' => 'success',
            'message' => 'Invoice data preparation successful',
            'details' => array()
        );
        
        try {
            // Create sample order data
            $sample_order = array(
                'order_id' => 12345,
                'date_added' => date('Y-m-d H:i:s'),
                'currency_code' => 'TRY',
                'currency_value' => 1,
                'payment_company' => 'Test Company Ltd.',
                'payment_firstname' => 'John',
                'payment_lastname' => 'Doe',
                'payment_address_1' => 'Test Address 123',
                'payment_address_2' => '',
                'payment_city' => 'Istanbul',
                'payment_zone' => 'Kadikoy',
                'payment_postcode' => '34000',
                'payment_country' => 'Turkey'
            );
            
            // Test data preparation logic
            $invoice_number = 'TY' . date('Y') . str_pad($sample_order['order_id'], 6, '0', STR_PAD_LEFT);
            $test['details'][] = 'Generated invoice number: ' . $invoice_number;
            
            // Test corporate customer detection
            $is_corporate = !empty($sample_order['payment_company']);
            $test['details'][] = 'Customer type: ' . ($is_corporate ? 'Corporate' : 'Individual');
            
            // Test currency handling
            $test['details'][] = 'Currency: ' . $sample_order['currency_code'] . ' (Rate: ' . $sample_order['currency_value'] . ')';
            
            $test['details'][] = 'Sample invoice data preparation completed successfully';
            
        } catch (Exception $e) {
            $test['status'] = 'error';
            $test['message'] = 'Invoice data preparation failed';
            $test['details'][] = 'Error: ' . $e->getMessage();
        }
        
        return $test;
    }
    
    /**
     * Run all tests via AJAX
     */
    public function runTests() {
        $json = array();
        
        try {
            $tests = array();
            
            $tests[] = $this->testModuleConfiguration();
            $tests[] = $this->testDatabaseTables();
            $tests[] = $this->testApiConnection();
            $tests[] = $this->testFilePermissions();
            $tests[] = $this->testPhpExtensions();
            $tests[] = $this->testSampleInvoiceData();
            
            $json['success'] = true;
            $json['tests'] = $tests;
            
            // Calculate overall status
            $overall_status = 'success';
            foreach ($tests as $test) {
                if ($test['status'] == 'error') {
                    $overall_status = 'error';
                    break;
                } elseif ($test['status'] == 'warning' && $overall_status != 'error') {
                    $overall_status = 'warning';
                }
            }
            
            $json['overall_status'] = $overall_status;
            
        } catch (Exception $e) {
            $json['success'] = false;
            $json['error'] = $e->getMessage();
        }
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }
}
