<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// <PERSON><PERSON>ş kontrolü
if (!isLoggedIn()) {
    header('Location: ../auth/login.php');
    exit;
}

// Sadece admin kullanıcıları erişebilir
if ($_SESSION['user_type'] !== 'admin') {
    header('Location: ../dashboard.php');
    exit;
}

// Mesaj durumu güncelleme
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $message_id = intval($_POST['message_id']);
    $action = $_POST['action'];
    
    try {
        if ($action === 'mark_read') {
            $sql = "UPDATE contact_messages SET status = 'read', updated_at = NOW() WHERE id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$message_id]);
            $success = 'Mesaj okundu olarak işaretlendi.';
        } elseif ($action === 'mark_replied') {
            $sql = "UPDATE contact_messages SET status = 'replied', updated_at = NOW() WHERE id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$message_id]);
            $success = 'Mesaj yanıtlandı olarak işaretlendi.';
        } elseif ($action === 'close') {
            $sql = "UPDATE contact_messages SET status = 'closed', updated_at = NOW() WHERE id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$message_id]);
            $success = 'Mesaj kapatıldı.';
        } elseif ($action === 'add_note') {
            $note = trim($_POST['admin_note']);
            $sql = "UPDATE contact_messages SET admin_notes = ?, updated_at = NOW() WHERE id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$note, $message_id]);
            $success = 'Admin notu eklendi.';
        }
    } catch (PDOException $e) {
        $error = 'İşlem sırasında hata oluştu: ' . $e->getMessage();
    }
}

// Mesajları getir
try {
    $filter = $_GET['filter'] ?? 'all';
    $search = $_GET['search'] ?? '';
    
    $sql = "SELECT * FROM contact_messages WHERE 1=1";
    $params = [];
    
    if ($filter !== 'all') {
        $sql .= " AND status = ?";
        $params[] = $filter;
    }
    
    if (!empty($search)) {
        $sql .= " AND (name LIKE ? OR email LIKE ? OR subject LIKE ? OR message LIKE ?)";
        $search_term = '%' . $search . '%';
        $params = array_merge($params, [$search_term, $search_term, $search_term, $search_term]);
    }
    
    $sql .= " ORDER BY created_at DESC";
    
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    $messages = $stmt->fetchAll();
    
    // İstatistikler
    $stats = [
        'total' => $db->query("SELECT COUNT(*) FROM contact_messages")->fetchColumn(),
        'new' => $db->query("SELECT COUNT(*) FROM contact_messages WHERE status = 'new'")->fetchColumn(),
        'read' => $db->query("SELECT COUNT(*) FROM contact_messages WHERE status = 'read'")->fetchColumn(),
        'replied' => $db->query("SELECT COUNT(*) FROM contact_messages WHERE status = 'replied'")->fetchColumn(),
        'closed' => $db->query("SELECT COUNT(*) FROM contact_messages WHERE status = 'closed'")->fetchColumn()
    ];
    
} catch (PDOException $e) {
    $messages = [];
    $stats = ['total' => 0, 'new' => 0, 'read' => 0, 'replied' => 0, 'closed' => 0];
    $error = 'Mesajlar yüklenirken hata oluştu: ' . $e->getMessage();
}

$page_title = 'İletişim Mesajları';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Admin Panel</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c5aa0;
        }
        
        .sidebar {
            background: linear-gradient(135deg, var(--primary-color), #1e3d72);
            min-height: 100vh;
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 2px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: rgba(255,255,255,0.1);
            transform: translateX(5px);
        }
        
        .main-content {
            background: #f8f9fa;
            min-height: 100vh;
        }
        
        .stats-card {
            border: none;
            border-radius: 15px;
            padding: 20px;
            height: 100%;
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .message-card {
            border: none;
            border-radius: 10px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        
        .message-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .status-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="p-3">
                    <h4 class="text-white mb-4">
                        <i class="bi bi-shield-check me-2"></i>Admin Panel
                    </h4>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link" href="../dashboard.php">
                            <i class="bi bi-speedometer2 me-2"></i>Dashboard
                        </a>
                        <a class="nav-link" href="users.php">
                            <i class="bi bi-people me-2"></i>Kullanıcılar
                        </a>
                        <a class="nav-link" href="jobs.php">
                            <i class="bi bi-briefcase me-2"></i>İş İlanları
                        </a>
                        <a class="nav-link" href="applications.php">
                            <i class="bi bi-file-earmark-text me-2"></i>Başvurular
                        </a>
                        <a class="nav-link" href="reviews.php">
                            <i class="bi bi-star me-2"></i>Değerlendirmeler
                        </a>
                        <a class="nav-link" href="payments.php">
                            <i class="bi bi-credit-card me-2"></i>Ödemeler
                        </a>
                        <a class="nav-link active" href="contact-messages.php">
                            <i class="bi bi-envelope me-2"></i>İletişim Mesajları
                        </a>
                        <a class="nav-link" href="settings.php">
                            <i class="bi bi-gear me-2"></i>Ayarlar
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h2 class="fw-bold mb-1"><?php echo $page_title; ?></h2>
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb">
                                    <li class="breadcrumb-item"><a href="../dashboard.php">Dashboard</a></li>
                                    <li class="breadcrumb-item active">İletişim Mesajları</li>
                                </ol>
                            </nav>
                        </div>
                    </div>

                    <!-- Alerts -->
                    <?php if (isset($success)): ?>
                        <div class="alert alert-success alert-dismissible fade show">
                            <i class="bi bi-check-circle me-2"></i><?php echo $success; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($error)): ?>
                        <div class="alert alert-danger alert-dismissible fade show">
                            <i class="bi bi-exclamation-triangle me-2"></i><?php echo $error; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- İstatistikler -->
                    <div class="row g-3 mb-4">
                        <div class="col-md-3">
                            <div class="stats-card card bg-primary text-white">
                                <h3><?php echo $stats['total']; ?></h3>
                                <small>Toplam Mesaj</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card card bg-danger text-white">
                                <h3><?php echo $stats['new']; ?></h3>
                                <small>Yeni Mesaj</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card card bg-warning text-white">
                                <h3><?php echo $stats['read']; ?></h3>
                                <small>Okunmuş</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card card bg-success text-white">
                                <h3><?php echo $stats['replied']; ?></h3>
                                <small>Yanıtlanmış</small>
                            </div>
                        </div>
                    </div>

                    <!-- Filtreler -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <form method="GET" class="row g-3">
                                <div class="col-md-4">
                                    <label class="form-label">Durum Filtresi</label>
                                    <select name="filter" class="form-select">
                                        <option value="all" <?php echo $filter === 'all' ? 'selected' : ''; ?>>Tümü</option>
                                        <option value="new" <?php echo $filter === 'new' ? 'selected' : ''; ?>>Yeni</option>
                                        <option value="read" <?php echo $filter === 'read' ? 'selected' : ''; ?>>Okunmuş</option>
                                        <option value="replied" <?php echo $filter === 'replied' ? 'selected' : ''; ?>>Yanıtlanmış</option>
                                        <option value="closed" <?php echo $filter === 'closed' ? 'selected' : ''; ?>>Kapatılmış</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Arama</label>
                                    <input type="text" name="search" class="form-control" 
                                           placeholder="Ad, email, konu veya mesaj içeriğinde ara..." 
                                           value="<?php echo htmlspecialchars($search); ?>">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="bi bi-search me-2"></i>Filtrele
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Mesaj Listesi -->
                    <div class="row">
                        <?php foreach ($messages as $message): ?>
                            <div class="col-12">
                                <div class="message-card card">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-8">
                                                <div class="d-flex align-items-start">
                                                    <div class="flex-grow-1">
                                                        <h6 class="mb-1">
                                                            <?php echo htmlspecialchars($message['subject']); ?>
                                                            <?php if ($message['status'] === 'new'): ?>
                                                                <span class="status-badge badge bg-danger ms-2">Yeni</span>
                                                            <?php elseif ($message['status'] === 'read'): ?>
                                                                <span class="status-badge badge bg-warning ms-2">Okundu</span>
                                                            <?php elseif ($message['status'] === 'replied'): ?>
                                                                <span class="status-badge badge bg-success ms-2">Yanıtlandı</span>
                                                            <?php elseif ($message['status'] === 'closed'): ?>
                                                                <span class="status-badge badge bg-secondary ms-2">Kapatıldı</span>
                                                            <?php endif; ?>
                                                        </h6>
                                                        <p class="text-muted small mb-2">
                                                            <strong><?php echo htmlspecialchars($message['name']); ?></strong> 
                                                            (<?php echo htmlspecialchars($message['email']); ?>)
                                                            <?php if ($message['phone']): ?>
                                                                - <?php echo htmlspecialchars($message['phone']); ?>
                                                            <?php endif; ?>
                                                        </p>
                                                        <p class="mb-2"><?php echo nl2br(htmlspecialchars(substr($message['message'], 0, 200))); ?>
                                                            <?php if (strlen($message['message']) > 200): ?>...<?php endif; ?>
                                                        </p>
                                                        <small class="text-muted">
                                                            <i class="bi bi-clock me-1"></i>
                                                            <?php echo date('d.m.Y H:i', strtotime($message['created_at'])); ?>
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="btn-group-vertical w-100" role="group">
                                                    <button class="btn btn-outline-primary btn-sm" onclick="viewMessage(<?php echo $message['id']; ?>)">
                                                        <i class="bi bi-eye me-1"></i>Detay
                                                    </button>
                                                    <?php if ($message['status'] === 'new'): ?>
                                                        <button class="btn btn-outline-warning btn-sm" onclick="markAsRead(<?php echo $message['id']; ?>)">
                                                            <i class="bi bi-check me-1"></i>Okundu İşaretle
                                                        </button>
                                                    <?php endif; ?>
                                                    <?php if ($message['status'] !== 'replied'): ?>
                                                        <button class="btn btn-outline-success btn-sm" onclick="markAsReplied(<?php echo $message['id']; ?>)">
                                                            <i class="bi bi-reply me-1"></i>Yanıtlandı İşaretle
                                                        </button>
                                                    <?php endif; ?>
                                                    <?php if ($message['status'] !== 'closed'): ?>
                                                        <button class="btn btn-outline-secondary btn-sm" onclick="closeMessage(<?php echo $message['id']; ?>)">
                                                            <i class="bi bi-x me-1"></i>Kapat
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <?php if ($message['admin_notes']): ?>
                                            <div class="mt-3 p-2 bg-light rounded">
                                                <small class="text-muted"><strong>Admin Notu:</strong></small>
                                                <p class="small mb-0"><?php echo nl2br(htmlspecialchars($message['admin_notes'])); ?></p>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        
                        <?php if (empty($messages)): ?>
                            <div class="col-12">
                                <div class="text-center py-5">
                                    <i class="bi bi-envelope text-muted" style="font-size: 3rem;"></i>
                                    <h5 class="text-muted mt-3">Mesaj bulunamadı</h5>
                                    <p class="text-muted">Seçilen kriterlere uygun mesaj bulunmuyor.</p>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function viewMessage(messageId) {
            // Modal ile mesaj detaylarını göster
            fetch(`message-details.php?id=${messageId}`)
                .then(response => response.text())
                .then(data => {
                    const modal = new bootstrap.Modal(document.getElementById('messageModal') || createMessageModal());
                    document.getElementById('messageModalContent').innerHTML = data;
                    modal.show();
                })
                .catch(error => {
                    alert('Mesaj bilgileri yüklenirken hata oluştu.');
                });
        }

        function createMessageModal() {
            const modalHtml = `
                <div class="modal fade" id="messageModal" tabindex="-1">
                    <div class="modal-dialog modal-xl">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Mesaj Detayları</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body" id="messageModalContent">
                                <div class="text-center">
                                    <div class="spinner-border" role="status">
                                        <span class="visually-hidden">Yükleniyor...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            return document.getElementById('messageModal');
        }
        
        function markAsRead(messageId) {
            if (confirm('Bu mesajı okundu olarak işaretlemek istediğinizden emin misiniz?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="mark_read">
                    <input type="hidden" name="message_id" value="${messageId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        function markAsReplied(messageId) {
            if (confirm('Bu mesajı yanıtlandı olarak işaretlemek istediğinizden emin misiniz?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="mark_replied">
                    <input type="hidden" name="message_id" value="${messageId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        function closeMessage(messageId) {
            if (confirm('Bu mesajı kapatmak istediğinizden emin misiniz?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="close">
                    <input type="hidden" name="message_id" value="${messageId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
