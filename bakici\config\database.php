<?php
// Veritabanı sabitleri
define('DB_HOST', 'localhost');
define('DB_NAME', 'bakici_platform');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// Site sabitleri
define('SITE_URL', 'http://localhost/bakici-platform');
define('SITE_NAME', 'Bakıcı Platform');
define('UPLOAD_PATH', __DIR__ . '/../uploads/');
define('UPLOAD_URL', SITE_URL . '/uploads/');

class Database {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $charset = DB_CHARSET;
    public $conn;

    public function getConnection() {
        $this->conn = null;
        try {
            // Önce veritabanı olmadan bağlan
            $dsn = "mysql:host=" . $this->host . ";charset=" . $this->charset;
            $this->conn = new PDO($dsn, $this->username, $this->password);
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

            // Veritabanını oluştur (yoksa)
            $this->conn->exec("CREATE DATABASE IF NOT EXISTS `" . $this->db_name . "` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");

            // Şimdi veritabanına bağlan
            $this->conn->exec("USE `" . $this->db_name . "`");

        } catch(PDOException $exception) {
            error_log("Veritabanı bağlantı hatası: " . $exception->getMessage());
            return null;
        }
        return $this->conn;
    }
}

// Global veritabanı bağlantısı
$database = new Database();
$db = $database->getConnection();

// Veritabanı tablolarını oluştur
if ($db) {
    try {
        // Kullanıcılar tablosu
        $db->exec("CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_type ENUM('family', 'caregiver', 'admin') NOT NULL DEFAULT 'family',
            email VARCHAR(255) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            full_name VARCHAR(255) NOT NULL,
            phone VARCHAR(20),
            city VARCHAR(100),
            district VARCHAR(100),
            address TEXT,
            profile_photo VARCHAR(255),
            is_verified BOOLEAN DEFAULT FALSE,
            is_active BOOLEAN DEFAULT TRUE,
            is_premium BOOLEAN DEFAULT FALSE,
            premium_until DATETIME NULL,
            email_verified_at DATETIME NULL,
            email_verification_token VARCHAR(255),
            password_reset_token VARCHAR(255),
            password_reset_expires DATETIME NULL,
            remember_token VARCHAR(255),
            last_login DATETIME NULL,
            login_count INT DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_email (email),
            INDEX idx_user_type (user_type),
            INDEX idx_city (city),
            INDEX idx_is_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

        // Kategoriler tablosu
        $db->exec("CREATE TABLE IF NOT EXISTS categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            slug VARCHAR(255) UNIQUE NOT NULL,
            description TEXT,
            icon VARCHAR(100),
            color VARCHAR(7) DEFAULT '#007bff',
            is_active BOOLEAN DEFAULT TRUE,
            sort_order INT DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_slug (slug),
            INDEX idx_is_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

        // Lokasyonlar tablosu
        $db->exec("CREATE TABLE IF NOT EXISTS locations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            city VARCHAR(100) NOT NULL,
            district VARCHAR(100),
            latitude DECIMAL(10, 8),
            longitude DECIMAL(11, 8),
            is_active BOOLEAN DEFAULT TRUE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_city (city),
            INDEX idx_district (district)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

        // Aile profilleri tablosu
        $db->exec("CREATE TABLE IF NOT EXISTS family_profiles (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            family_size INT DEFAULT 1,
            children_count INT DEFAULT 0,
            children_ages TEXT,
            pets BOOLEAN DEFAULT FALSE,
            pet_details TEXT,
            home_type ENUM('apartment', 'house', 'villa') DEFAULT 'apartment',
            special_needs TEXT,
            preferred_languages TEXT,
            budget_range VARCHAR(50),
            additional_info TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

        // Bakıcı profilleri tablosu
        $db->exec("CREATE TABLE IF NOT EXISTS caregiver_profiles (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            birth_date DATE,
            gender ENUM('male', 'female', 'other') DEFAULT 'female',
            nationality VARCHAR(100) DEFAULT 'Türk',
            marital_status ENUM('single', 'married', 'divorced', 'widowed'),
            education_level VARCHAR(100),
            languages TEXT,
            experience_years INT DEFAULT 0,
            specializations TEXT,
            services TEXT,
            hourly_rate DECIMAL(10,2),
            daily_rate DECIMAL(10,2),
            weekly_rate DECIMAL(10,2),
            monthly_rate DECIMAL(10,2),
            availability TEXT,
            work_schedule TEXT,
            transportation BOOLEAN DEFAULT FALSE,
            can_travel BOOLEAN DEFAULT FALSE,
            max_travel_distance INT DEFAULT 0,
            bio TEXT,
            skills TEXT,
            certificates TEXT,
            reference_info TEXT,
            background_check BOOLEAN DEFAULT FALSE,
            background_check_file VARCHAR(255),
            cv_file VARCHAR(255),
            portfolio_images TEXT,
            rating DECIMAL(3,2) DEFAULT 0.00,
            total_reviews INT DEFAULT 0,
            total_jobs INT DEFAULT 0,
            response_rate DECIMAL(5,2) DEFAULT 0.00,
            response_time INT DEFAULT 0,
            is_featured BOOLEAN DEFAULT FALSE,
            featured_until DATETIME NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_rating (rating),
            INDEX idx_hourly_rate (hourly_rate),
            INDEX idx_experience (experience_years)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

        // İş ilanları tablosu
        $db->exec("DROP TABLE IF EXISTS job_listings");
        $db->exec("CREATE TABLE job_listings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            category_id INT,
            title VARCHAR(255) NOT NULL,
            slug VARCHAR(255) UNIQUE NOT NULL,
            description TEXT NOT NULL,
            job_type ENUM('child_care', 'elderly_care', 'patient_care', 'house_help', 'pet_care', 'companion_care') NOT NULL,
            care_type ENUM('live_in', 'live_out', 'hourly', 'daily', 'weekly', 'monthly') NOT NULL,
            location_city VARCHAR(100) NOT NULL,
            location_district VARCHAR(100),
            location_address TEXT,
            latitude DECIMAL(10, 8),
            longitude DECIMAL(11, 8),
            budget_min DECIMAL(10,2),
            budget_max DECIMAL(10,2),
            budget_type ENUM('hourly', 'daily', 'weekly', 'monthly') DEFAULT 'monthly',
            budget_negotiable BOOLEAN DEFAULT FALSE,
            start_date DATE,
            end_date DATE,
            duration_months INT,
            requirements TEXT,
            benefits TEXT,
            preferred_gender ENUM('male', 'female', 'no_preference') DEFAULT 'no_preference',
            preferred_age_min INT,
            preferred_age_max INT,
            required_experience INT DEFAULT 0,
            required_languages TEXT,
            required_skills TEXT,
            work_schedule TEXT,
            special_requirements TEXT,
            contact_phone VARCHAR(20),
            contact_email VARCHAR(255),
            contact_whatsapp VARCHAR(20),
            is_urgent BOOLEAN DEFAULT FALSE,
            is_featured BOOLEAN DEFAULT FALSE,
            is_premium BOOLEAN DEFAULT FALSE,
            featured_until DATETIME NULL,
            status ENUM('draft', 'active', 'paused', 'closed', 'expired') DEFAULT 'active',
            expires_at DATETIME NULL,
            views_count INT DEFAULT 0,
            applications_count INT DEFAULT 0,
            favorites_count INT DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
            INDEX idx_slug (slug),
            INDEX idx_job_type (job_type),
            INDEX idx_location (location_city, location_district),
            INDEX idx_status (status),
            INDEX idx_budget (budget_min, budget_max),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

        // Başvurular tablosu
        $db->exec("CREATE TABLE IF NOT EXISTS job_applications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            job_id INT NOT NULL,
            caregiver_id INT NOT NULL,
            cover_letter TEXT,
            proposed_rate DECIMAL(10,2),
            availability_start DATE,
            availability_end DATE,
            status ENUM('pending', 'viewed', 'shortlisted', 'interview', 'accepted', 'rejected', 'withdrawn', 'hired') DEFAULT 'pending',
            employer_notes TEXT,
            interview_date DATETIME NULL,
            interview_location TEXT,
            interview_notes TEXT,
            rejection_reason TEXT,
            applied_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (job_id) REFERENCES job_listings(id) ON DELETE CASCADE,
            FOREIGN KEY (caregiver_id) REFERENCES users(id) ON DELETE CASCADE,
            UNIQUE KEY unique_application (job_id, caregiver_id),
            INDEX idx_status (status),
            INDEX idx_applied_at (applied_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

        // Favoriler tablosu
        $db->exec("CREATE TABLE IF NOT EXISTS favorites (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            target_type ENUM('job', 'caregiver', 'family') NOT NULL,
            target_id INT NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            UNIQUE KEY unique_favorite (user_id, target_type, target_id),
            INDEX idx_target (target_type, target_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

        // Mesajlaşma tablosu
        $db->exec("CREATE TABLE IF NOT EXISTS messages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            conversation_id VARCHAR(100) NOT NULL,
            sender_id INT NOT NULL,
            receiver_id INT NOT NULL,
            job_id INT NULL,
            parent_id INT NULL,
            subject VARCHAR(255),
            message TEXT NOT NULL,
            message_type ENUM('text', 'image', 'file', 'system') DEFAULT 'text',
            attachment_path VARCHAR(255),
            attachment_name VARCHAR(255),
            attachment_size INT,
            is_read BOOLEAN DEFAULT FALSE,
            read_at DATETIME NULL,
            is_deleted_by_sender BOOLEAN DEFAULT FALSE,
            is_deleted_by_receiver BOOLEAN DEFAULT FALSE,
            is_important BOOLEAN DEFAULT FALSE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (job_id) REFERENCES job_listings(id) ON DELETE SET NULL,
            FOREIGN KEY (parent_id) REFERENCES messages(id) ON DELETE SET NULL,
            INDEX idx_conversation (conversation_id),
            INDEX idx_sender (sender_id),
            INDEX idx_receiver (receiver_id),
            INDEX idx_is_read (is_read),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

        // Değerlendirmeler tablosu
        $db->exec("CREATE TABLE IF NOT EXISTS reviews (
            id INT AUTO_INCREMENT PRIMARY KEY,
            reviewer_id INT NOT NULL,
            reviewed_id INT NOT NULL,
            job_id INT NULL,
            application_id INT NULL,
            rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
            title VARCHAR(255),
            comment TEXT,
            pros TEXT,
            cons TEXT,
            would_recommend BOOLEAN DEFAULT TRUE,
            is_anonymous BOOLEAN DEFAULT FALSE,
            is_approved BOOLEAN DEFAULT TRUE,
            is_featured BOOLEAN DEFAULT FALSE,
            admin_notes TEXT,
            helpful_count INT DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (reviewer_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (reviewed_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (job_id) REFERENCES job_listings(id) ON DELETE SET NULL,
            FOREIGN KEY (application_id) REFERENCES job_applications(id) ON DELETE SET NULL,
            UNIQUE KEY unique_review (reviewer_id, reviewed_id, job_id),
            INDEX idx_rating (rating),
            INDEX idx_reviewed (reviewed_id),
            INDEX idx_is_approved (is_approved)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

        // Değerlendirme yardımcı oyları
        $db->exec("CREATE TABLE IF NOT EXISTS review_votes (
            id INT AUTO_INCREMENT PRIMARY KEY,
            review_id INT NOT NULL,
            user_id INT NOT NULL,
            vote_type ENUM('helpful', 'not_helpful') NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (review_id) REFERENCES reviews(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            UNIQUE KEY unique_vote (review_id, user_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

        // Paketler tablosu
        $db->exec("CREATE TABLE IF NOT EXISTS packages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            slug VARCHAR(255) UNIQUE NOT NULL,
            description TEXT,
            features TEXT,
            user_type ENUM('family', 'caregiver', 'both') NOT NULL,
            price DECIMAL(10,2) NOT NULL,
            original_price DECIMAL(10,2),
            currency VARCHAR(3) DEFAULT 'TRY',
            duration_days INT NOT NULL,
            max_job_listings INT DEFAULT 0,
            max_applications INT DEFAULT 0,
            max_messages INT DEFAULT 0,
            max_featured_days INT DEFAULT 0,
            can_see_contact_info BOOLEAN DEFAULT FALSE,
            can_use_advanced_search BOOLEAN DEFAULT FALSE,
            priority_support BOOLEAN DEFAULT FALSE,
            is_featured BOOLEAN DEFAULT FALSE,
            is_popular BOOLEAN DEFAULT FALSE,
            is_active BOOLEAN DEFAULT TRUE,
            sort_order INT DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_slug (slug),
            INDEX idx_user_type (user_type),
            INDEX idx_is_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

        // Kullanıcı paket satın alımları
        $db->exec("CREATE TABLE IF NOT EXISTS user_packages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            package_id INT NOT NULL,
            payment_id VARCHAR(255),
            amount DECIMAL(10,2) NOT NULL,
            currency VARCHAR(3) DEFAULT 'TRY',
            starts_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            expires_at DATETIME NOT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            auto_renew BOOLEAN DEFAULT FALSE,
            usage_stats JSON,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (package_id) REFERENCES packages(id) ON DELETE CASCADE,
            INDEX idx_user_expires (user_id, expires_at),
            INDEX idx_is_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

        // Ödemeler tablosu
        $db->exec("CREATE TABLE IF NOT EXISTS payments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            package_id INT NULL,
            payment_method VARCHAR(50) DEFAULT 'paytr',
            payment_type ENUM('package', 'featured', 'premium', 'other') DEFAULT 'package',
            amount DECIMAL(10,2) NOT NULL,
            currency VARCHAR(3) DEFAULT 'TRY',
            status ENUM('pending', 'processing', 'success', 'failed', 'cancelled', 'refunded') DEFAULT 'pending',
            transaction_id VARCHAR(255),
            merchant_oid VARCHAR(255),
            paytr_token VARCHAR(255),
            paytr_response TEXT,
            callback_data JSON,
            refund_amount DECIMAL(10,2) DEFAULT 0,
            refund_reason TEXT,
            notes TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (package_id) REFERENCES packages(id) ON DELETE SET NULL,
            INDEX idx_user (user_id),
            INDEX idx_status (status),
            INDEX idx_transaction (transaction_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

        // Bildirimler tablosu
        $db->exec("CREATE TABLE IF NOT EXISTS notifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            type VARCHAR(50) NOT NULL,
            title VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            action_url VARCHAR(500),
            icon VARCHAR(100),
            data JSON NULL,
            is_read BOOLEAN DEFAULT FALSE,
            read_at DATETIME NULL,
            is_important BOOLEAN DEFAULT FALSE,
            expires_at DATETIME NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_user_read (user_id, is_read),
            INDEX idx_type (type),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

        // Aktivite logları tablosu
        $db->exec("CREATE TABLE IF NOT EXISTS activity_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            action VARCHAR(50) NOT NULL,
            description TEXT,
            table_name VARCHAR(50),
            record_id INT,
            old_values JSON,
            new_values JSON,
            ip_address VARCHAR(45),
            user_agent TEXT,
            session_id VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
            INDEX idx_user (user_id),
            INDEX idx_action (action),
            INDEX idx_table_record (table_name, record_id),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

        // Sistem ayarları tablosu
        $db->exec("CREATE TABLE IF NOT EXISTS settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT,
            setting_type ENUM('text', 'number', 'boolean', 'json', 'email', 'url') DEFAULT 'text',
            category VARCHAR(50) DEFAULT 'general',
            description TEXT,
            is_public BOOLEAN DEFAULT FALSE,
            is_editable BOOLEAN DEFAULT TRUE,
            validation_rules TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_category (category),
            INDEX idx_is_public (is_public)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

        // Dosyalar tablosu
        $db->exec("CREATE TABLE IF NOT EXISTS files (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            original_name VARCHAR(255) NOT NULL,
            file_name VARCHAR(255) NOT NULL,
            file_path VARCHAR(500) NOT NULL,
            file_size INT NOT NULL,
            mime_type VARCHAR(100),
            file_type ENUM('image', 'document', 'video', 'audio', 'other') DEFAULT 'other',
            related_type VARCHAR(50),
            related_id INT,
            is_public BOOLEAN DEFAULT FALSE,
            download_count INT DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
            INDEX idx_user (user_id),
            INDEX idx_related (related_type, related_id),
            INDEX idx_file_type (file_type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

        // Varsayılan kategorileri oluştur
        $category_check = $db->prepare("SELECT id FROM categories LIMIT 1");
        $category_check->execute();

        if (!$category_check->fetch()) {
            $categories = [
                ['Çocuk Bakımı', 'cocuk-bakimi', 'Bebek ve çocuk bakım hizmetleri', 'bi-heart', '#e74c3c'],
                ['Yaşlı Bakımı', 'yasli-bakimi', 'Yaşlı ve hasta bakım hizmetleri', 'bi-person-heart', '#3498db'],
                ['Hasta Bakımı', 'hasta-bakimi', 'Medikal bakım ve hasta bakım hizmetleri', 'bi-hospital', '#2ecc71'],
                ['Ev Yardımcısı', 'ev-yardimcisi', 'Ev temizliği ve günlük işler', 'bi-house', '#f39c12'],
                ['Evcil Hayvan Bakımı', 'evcil-hayvan-bakimi', 'Pet bakım ve yürüyüş hizmetleri', 'bi-heart-fill', '#9b59b6'],
                ['Refakatçi', 'refakatci', 'Hastane ve evde refakatçi hizmetleri', 'bi-person-check', '#1abc9c']
            ];

            foreach ($categories as $category) {
                $sql = "INSERT INTO categories (name, slug, description, icon, color) VALUES (?, ?, ?, ?, ?)";
                $stmt = $db->prepare($sql);
                $stmt->execute($category);
            }
        }

        // Varsayılan lokasyonları oluştur
        $location_check = $db->prepare("SELECT id FROM locations LIMIT 1");
        $location_check->execute();

        if (!$location_check->fetch()) {
            $cities = [
                'İstanbul', 'Ankara', 'İzmir', 'Bursa', 'Antalya', 'Adana', 'Konya', 'Gaziantep',
                'Mersin', 'Diyarbakır', 'Kayseri', 'Eskişehir', 'Urfa', 'Malatya', 'Erzurum',
                'Van', 'Batman', 'Elazığ', 'İzmit', 'Manisa', 'Sivas', 'Gebze', 'Balıkesir',
                'Tarsus', 'Kahramanmaraş', 'Erzincan', 'Ordu', 'Trabzon', 'Aydın', 'Çorum'
            ];

            foreach ($cities as $city) {
                $sql = "INSERT INTO locations (city) VALUES (?)";
                $stmt = $db->prepare($sql);
                $stmt->execute([$city]);
            }
        }

        // Varsayılan admin kullanıcısı oluştur
        $admin_check = $db->prepare("SELECT id FROM users WHERE user_type = 'admin' LIMIT 1");
        $admin_check->execute();

        if (!$admin_check->fetch()) {
            $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
            $admin_sql = "INSERT INTO users (user_type, email, password, full_name, is_verified, is_active)
                          VALUES ('admin', '<EMAIL>', ?, 'Sistem Yöneticisi', 1, 1)";
            $admin_stmt = $db->prepare($admin_sql);
            $admin_stmt->execute([$admin_password]);
        }

        // Varsayılan paketleri oluştur
        $package_check = $db->prepare("SELECT id FROM packages LIMIT 1");
        $package_check->execute();

        if (!$package_check->fetch()) {
            $packages = [
                // Aile paketleri
                ['Temel Aile', 'temel-aile', 'Temel özelliklerle bakıcı bulun', 'İlan verme|Mesajlaşma|Profil görüntüleme', 'family', 29.90, 39.90, 'TRY', 30, 2, 20, 50, 0, 0, 0, 0, 0, 0, 1, 1],
                ['Standart Aile', 'standart-aile', 'Gelişmiş özelliklerle hızlı bakıcı bulun', 'İlan verme|Mesajlaşma|İletişim bilgileri|Gelişmiş arama', 'family', 59.90, 79.90, 'TRY', 30, 5, 50, 150, 7, 1, 1, 0, 0, 1, 1, 2],
                ['Premium Aile', 'premium-aile', 'Tüm özelliklerle VIP hizmet', 'Sınırsız ilan|Sınırsız mesaj|Öncelikli destek|Öne çıkan ilanlar', 'family', 99.90, 129.90, 'TRY', 30, -1, -1, -1, 30, 1, 1, 1, 1, 1, 1, 3],

                // Bakıcı paketleri
                ['Temel Bakıcı', 'temel-bakici', 'İş fırsatlarına başvurun', 'Profil oluşturma|Başvuru yapma|Mesajlaşma', 'caregiver', 19.90, 29.90, 'TRY', 30, 0, 15, 30, 0, 0, 0, 0, 0, 0, 1, 1],
                ['Standart Bakıcı', 'standart-bakici', 'Daha fazla görünürlük', 'Gelişmiş profil|Daha fazla başvuru|İletişim bilgileri', 'caregiver', 39.90, 59.90, 'TRY', 30, 0, 50, 100, 7, 1, 1, 0, 0, 1, 1, 2],
                ['Premium Bakıcı', 'premium-bakici', 'Öne çıkan profil ile maksimum fırsat', 'Öne çıkan profil|Sınırsız başvuru|Öncelikli destek', 'caregiver', 69.90, 99.90, 'TRY', 30, 0, -1, -1, 30, 1, 1, 1, 1, 1, 1, 3]
            ];

            foreach ($packages as $package) {
                $sql = "INSERT INTO packages (name, slug, description, features, user_type, price, original_price, currency, duration_days, max_job_listings, max_applications, max_messages, max_featured_days, can_see_contact_info, can_use_advanced_search, priority_support, is_featured, is_popular, is_active, sort_order)
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $stmt = $db->prepare($sql);
                $stmt->execute($package);
            }
        }

        // Varsayılan sistem ayarları
        $settings_check = $db->prepare("SELECT id FROM settings LIMIT 1");
        $settings_check->execute();

        if (!$settings_check->fetch()) {
            $settings = [
                ['site_name', 'Bakıcı Platform', 'text', 'general', 'Site adı', 1, 1],
                ['site_description', 'Güvenilir bakıcı bulmanın en kolay yolu', 'text', 'general', 'Site açıklaması', 1, 1],
                ['site_keywords', 'bakıcı, çocuk bakıcısı, yaşlı bakımı, hasta bakımı', 'text', 'seo', 'Site anahtar kelimeleri', 1, 1],
                ['contact_email', '<EMAIL>', 'email', 'contact', 'İletişim e-postası', 1, 1],
                ['contact_phone', '+90 ************', 'text', 'contact', 'İletişim telefonu', 1, 1],
                ['paytr_merchant_id', '', 'text', 'payment', 'PayTR Merchant ID', 0, 1],
                ['paytr_merchant_key', '', 'text', 'payment', 'PayTR Merchant Key', 0, 1],
                ['paytr_merchant_salt', '', 'text', 'payment', 'PayTR Merchant Salt', 0, 1],
                ['email_verification_required', '1', 'boolean', 'user', 'E-posta doğrulama zorunlu', 0, 1],
                ['admin_approval_required', '0', 'boolean', 'user', 'Admin onayı gerekli', 0, 1],
                ['max_file_size', '10485760', 'number', 'upload', 'Maksimum dosya boyutu (byte)', 0, 1],
                ['allowed_file_types', 'jpg,jpeg,png,pdf,doc,docx', 'text', 'upload', 'İzin verilen dosya türleri', 0, 1]
            ];

            foreach ($settings as $setting) {
                $sql = "INSERT INTO settings (setting_key, setting_value, setting_type, category, description, is_public, is_editable) VALUES (?, ?, ?, ?, ?, ?, ?)";
                $stmt = $db->prepare($sql);
                $stmt->execute($setting);
            }
        }

        // Contact messages tablosu
        $db->exec("CREATE TABLE IF NOT EXISTS contact_messages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(100) NOT NULL,
            phone VARCHAR(20),
            subject VARCHAR(200) NOT NULL,
            message TEXT NOT NULL,
            ip_address VARCHAR(45),
            user_agent TEXT,
            is_read BOOLEAN DEFAULT FALSE,
            replied_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_created_at (created_at),
            INDEX idx_is_read (is_read)
        )");

        // Activity logs tablosu
        $db->exec("DROP TABLE IF EXISTS activity_logs");
        $db->exec("CREATE TABLE activity_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            action VARCHAR(100) NOT NULL,
            description TEXT,
            table_name VARCHAR(100),
            record_id INT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_action (action),
            INDEX idx_created_at (created_at),
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
        )");

        // Notifications tablosu
        $db->exec("CREATE TABLE IF NOT EXISTS notifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            type VARCHAR(50) NOT NULL,
            title VARCHAR(200) NOT NULL,
            message TEXT NOT NULL,
            action_url VARCHAR(500),
            is_read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_is_read (is_read),
            INDEX idx_created_at (created_at),
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )");

        // Messages tablosu
        $db->exec("CREATE TABLE IF NOT EXISTS messages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            sender_id INT NOT NULL,
            receiver_id INT NOT NULL,
            subject VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            is_read BOOLEAN DEFAULT FALSE,
            replied_to INT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_sender (sender_id),
            INDEX idx_receiver (receiver_id),
            INDEX idx_created_at (created_at),
            INDEX idx_is_read (is_read),
            FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (replied_to) REFERENCES messages(id) ON DELETE SET NULL
        )");

        // System settings tablosu
        $db->exec("CREATE TABLE IF NOT EXISTS system_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT,
            setting_type ENUM('text', 'number', 'boolean', 'email', 'password', 'textarea') DEFAULT 'text',
            category VARCHAR(50) DEFAULT 'general',
            description TEXT,
            is_public BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_category (category),
            INDEX idx_key (setting_key)
        )");

        // Eksik alanları users tablosuna ekle
        try {
            // email_verified alanı var mı kontrol et
            $check_column = $db->query("SHOW COLUMNS FROM users LIKE 'email_verified'");
            if (!$check_column->fetch()) {
                $db->exec("ALTER TABLE users ADD COLUMN email_verified BOOLEAN DEFAULT FALSE AFTER email");
            }

            // status alanı var mı kontrol et
            $check_column = $db->query("SHOW COLUMNS FROM users LIKE 'status'");
            if (!$check_column->fetch()) {
                $db->exec("ALTER TABLE users ADD COLUMN status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' AFTER user_type");
            }

            // updated_at alanı var mı kontrol et
            $check_column = $db->query("SHOW COLUMNS FROM users LIKE 'updated_at'");
            if (!$check_column->fetch()) {
                $db->exec("ALTER TABLE users ADD COLUMN updated_at TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP AFTER created_at");
            }

            // Mevcut kullanıcıları aktif yap
            $db->exec("UPDATE users SET status = 'active', email_verified = 1 WHERE status IS NULL OR email_verified IS NULL");

        } catch (PDOException $e) {
            // Hata durumunda devam et
        }

        // Varsayılan sistem ayarlarını ekle
        $default_settings = [
            // SMTP Ayarları
            ['smtp_host', 'smtp.gmail.com', 'text', 'email', 'SMTP sunucu adresi'],
            ['smtp_port', '587', 'number', 'email', 'SMTP port numarası'],
            ['smtp_username', '', 'email', 'email', 'SMTP kullanıcı adı (email)'],
            ['smtp_password', '', 'password', 'email', 'SMTP şifresi'],
            ['smtp_encryption', 'tls', 'text', 'email', 'SMTP şifreleme (tls/ssl)'],
            ['smtp_from_email', '<EMAIL>', 'email', 'email', 'Gönderen email adresi'],
            ['smtp_from_name', 'Bakıcı Platform', 'text', 'email', 'Gönderen adı'],

            // Site Ayarları
            ['site_name', 'Bakıcı Platform', 'text', 'general', 'Site adı'],
            ['site_description', 'Güvenilir bakıcı bulma platformu', 'textarea', 'general', 'Site açıklaması'],
            ['site_email', '<EMAIL>', 'email', 'general', 'Site iletişim emaili'],
            ['site_phone', '+90 ************', 'text', 'general', 'Site telefon numarası'],

            // Bildirim Ayarları
            ['email_notifications', '1', 'boolean', 'notifications', 'Email bildirimleri aktif'],
            ['new_message_email', '1', 'boolean', 'notifications', 'Yeni mesaj email bildirimi'],
            ['new_application_email', '1', 'boolean', 'notifications', 'Yeni başvuru email bildirimi']
        ];

        foreach ($default_settings as $setting) {
            $check_sql = "SELECT id FROM system_settings WHERE setting_key = ?";
            $check_stmt = $db->prepare($check_sql);
            $check_stmt->execute([$setting[0]]);

            if (!$check_stmt->fetch()) {
                $insert_sql = "INSERT INTO system_settings (setting_key, setting_value, setting_type, category, description) VALUES (?, ?, ?, ?, ?)";
                $insert_stmt = $db->prepare($insert_sql);
                $insert_stmt->execute($setting);
            }
        }

        // Veritabanı kurulumu tamamlandı

    } catch(PDOException $e) {
        // Hata durumunda log kaydı yapılabilir
        error_log("Veritabanı kurulum hatası: " . $e->getMessage());
    }
}
?>
