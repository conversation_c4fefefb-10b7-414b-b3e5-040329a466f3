<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// G<PERSON>ş kontrolü
requireLogin();

$user_id = safeArray($_SESSION, 'user_id', 0);
$user_type = safeArray($_SESSION, 'user_type', 'family');

// Kullanıcı istatistikleri
$stats = [];

if ($user_type === 'family') {
    // Aile istatistikleri
    try {
        $stats['my_jobs'] = $db->prepare("SELECT COUNT(*) FROM job_listings WHERE user_id = ?");
        $stats['my_jobs']->execute([$user_id]);
        $stats['my_jobs'] = $stats['my_jobs']->fetchColumn();

        $stats['active_jobs'] = $db->prepare("SELECT COUNT(*) FROM job_listings WHERE user_id = ? AND status = 'active'");
        $stats['active_jobs']->execute([$user_id]);
        $stats['active_jobs'] = $stats['active_jobs']->fetchColumn();

        $stats['total_applications'] = $db->prepare("SELECT COUNT(*) FROM job_applications ja JOIN job_listings jl ON ja.job_id = jl.id WHERE jl.user_id = ?");
        $stats['total_applications']->execute([$user_id]);
        $stats['total_applications'] = $stats['total_applications']->fetchColumn();

        $stats['unread_messages'] = $db->prepare("SELECT COUNT(*) FROM messages WHERE receiver_id = ? AND is_read = 0");
        $stats['unread_messages']->execute([$user_id]);
        $stats['unread_messages'] = $stats['unread_messages']->fetchColumn();

        // Son başvurular
        $recent_applications_sql = "SELECT ja.*, jl.title as job_title, u.full_name as caregiver_name, cp.rating, cp.experience_years
                                   FROM job_applications ja
                                   JOIN job_listings jl ON ja.job_id = jl.id
                                   JOIN users u ON ja.caregiver_id = u.id
                                   LEFT JOIN caregiver_profiles cp ON u.id = cp.user_id
                                   WHERE jl.user_id = ?
                                   ORDER BY ja.applied_at DESC LIMIT 5";
        $stmt = $db->prepare($recent_applications_sql);
        $stmt->execute([$user_id]);
        $recent_applications = $stmt->fetchAll();

        // Aktif ilanlar
        $active_jobs_sql = "SELECT jl.*, COUNT(ja.id) as application_count
                           FROM job_listings jl
                           LEFT JOIN job_applications ja ON jl.id = ja.job_id
                           WHERE jl.user_id = ? AND jl.status = 'active'
                           GROUP BY jl.id
                           ORDER BY jl.created_at DESC LIMIT 5";
        $stmt = $db->prepare($active_jobs_sql);
        $stmt->execute([$user_id]);
        $active_jobs = $stmt->fetchAll();

    } catch (PDOException $e) {
        $stats = ['my_jobs' => 0, 'active_jobs' => 0, 'total_applications' => 0, 'unread_messages' => 0];
        $recent_applications = [];
        $active_jobs = [];
    }

} elseif ($user_type === 'caregiver') {
    // Bakıcı istatistikleri
    try {
        $stats['my_applications'] = $db->prepare("SELECT COUNT(*) FROM job_applications WHERE caregiver_id = ?");
        $stats['my_applications']->execute([$user_id]);
        $stats['my_applications'] = $stats['my_applications']->fetchColumn();

        $stats['pending_applications'] = $db->prepare("SELECT COUNT(*) FROM job_applications WHERE caregiver_id = ? AND status = 'pending'");
        $stats['pending_applications']->execute([$user_id]);
        $stats['pending_applications'] = $stats['pending_applications']->fetchColumn();

        $stats['accepted_applications'] = $db->prepare("SELECT COUNT(*) FROM job_applications WHERE caregiver_id = ? AND status = 'accepted'");
        $stats['accepted_applications']->execute([$user_id]);
        $stats['accepted_applications'] = $stats['accepted_applications']->fetchColumn();

        $stats['unread_messages'] = $db->prepare("SELECT COUNT(*) FROM messages WHERE receiver_id = ? AND is_read = 0");
        $stats['unread_messages']->execute([$user_id]);
        $stats['unread_messages'] = $stats['unread_messages']->fetchColumn();

        // Profil tamamlanma oranı
        $profile_sql = "SELECT * FROM caregiver_profiles WHERE user_id = ?";
        $stmt = $db->prepare($profile_sql);
        $stmt->execute([$user_id]);
        $profile = $stmt->fetch();

        $profile_completion = 0;
        if ($profile) {
            $fields = ['birth_date', 'experience_years', 'education_level', 'services', 'bio', 'hourly_rate'];
            $completed_fields = 0;
            foreach ($fields as $field) {
                if (!empty($profile[$field])) $completed_fields++;
            }
            $profile_completion = round(($completed_fields / count($fields)) * 100);
        }
        $stats['profile_completion'] = $profile_completion;

        // Son başvurularım
        $my_applications_sql = "SELECT ja.*, jl.title as job_title, jl.location_city, jl.budget_min, jl.budget_max, jl.budget_type, u.full_name as employer_name
                               FROM job_applications ja
                               JOIN job_listings jl ON ja.job_id = jl.id
                               JOIN users u ON jl.user_id = u.id
                               WHERE ja.caregiver_id = ?
                               ORDER BY ja.applied_at DESC LIMIT 5";
        $stmt = $db->prepare($my_applications_sql);
        $stmt->execute([$user_id]);
        $my_applications = $stmt->fetchAll();

        // Uygun iş ilanları
        $suitable_jobs_sql = "SELECT jl.*, u.full_name as employer_name
                             FROM job_listings jl
                             JOIN users u ON jl.user_id = u.id
                             WHERE jl.status = 'active'
                             AND jl.id NOT IN (SELECT job_id FROM job_applications WHERE caregiver_id = ?)
                             ORDER BY jl.created_at DESC LIMIT 5";
        $stmt = $db->prepare($suitable_jobs_sql);
        $stmt->execute([$user_id]);
        $suitable_jobs = $stmt->fetchAll();

    } catch (PDOException $e) {
        $stats = ['my_applications' => 0, 'pending_applications' => 0, 'accepted_applications' => 0, 'unread_messages' => 0, 'profile_completion' => 0];
        $my_applications = [];
        $suitable_jobs = [];
    }
}

// Son mesajlar
try {
    $messages_sql = "SELECT m.*, u.full_name as sender_name
                     FROM messages m
                     JOIN users u ON m.sender_id = u.id
                     WHERE m.receiver_id = ?
                     ORDER BY m.created_at DESC LIMIT 5";
    $stmt = $db->prepare($messages_sql);
    $stmt->execute([$user_id]);
    $recent_messages = $stmt->fetchAll();
} catch (PDOException $e) {
    $recent_messages = [];
}

// Bildirimler
try {
    $notifications_sql = "SELECT * FROM notifications WHERE user_id = ? ORDER BY created_at DESC LIMIT 5";
    $stmt = $db->prepare($notifications_sql);
    $stmt->execute([$user_id]);
    $notifications = $stmt->fetchAll();
} catch (PDOException $e) {
    $notifications = [];
}

$page_title = 'Dashboard';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bakıcı Burada</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c5aa0;
            --secondary-color: #f8f9fa;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
        }

        .sidebar {
            background: linear-gradient(135deg, var(--primary-color) 0%, #1e3d72 100%);
            min-height: 100vh;
            color: white;
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            border-radius: 8px;
            margin: 0.25rem 0;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: rgba(255,255,255,0.1);
        }

        .stat-card {
            border: none;
            border-radius: 15px;
            padding: 1.5rem;
            height: 100%;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .progress-circle {
            width: 80px;
            height: 80px;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(44, 90, 160, 0.05);
        }

        .notification-item {
            border-left: 4px solid var(--primary-color);
            background: rgba(44, 90, 160, 0.05);
        }

        .message-item {
            border-left: 4px solid var(--success-color);
            background: rgba(40, 167, 69, 0.05);
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="sidebar">
                    <div class="p-3">
                        <h4 class="mb-0">
                            <i class="bi bi-heart-fill me-2"></i>Bakıcı Burada
                        </h4>
                        <small class="text-light">Hoş geldiniz, <?php echo escape(safeArray($_SESSION, 'full_name', 'Kullanıcı')); ?></small>
                    </div>

                    <nav class="nav flex-column px-3">
                        <a class="nav-link active" href="dashboard.php">
                            <i class="bi bi-speedometer2 me-2"></i>Dashboard
                        </a>

                        <?php if ($user_type === 'family'): ?>
                            <a class="nav-link" href="jobs/my-jobs.php">
                                <i class="bi bi-briefcase me-2"></i>İlanlarım
                            </a>
                            <a class="nav-link" href="jobs/create.php">
                                <i class="bi bi-plus-circle me-2"></i>İlan Ver
                            </a>
                            <a class="nav-link" href="caregivers.php">
                                <i class="bi bi-search me-2"></i>Bakıcı Ara
                            </a>
                            <a class="nav-link" href="applications.php">
                                <i class="bi bi-person-check me-2"></i>Başvurular
                            </a>
                        <?php elseif ($user_type === 'caregiver'): ?>
                            <a class="nav-link" href="profile/caregiver.php">
                                <i class="bi bi-person me-2"></i>Profilim
                            </a>
                            <a class="nav-link" href="jobs.php">
                                <i class="bi bi-search me-2"></i>İş Ara
                            </a>
                            <a class="nav-link" href="applications/my-applications.php">
                                <i class="bi bi-file-text me-2"></i>Başvurularım
                            </a>
                        <?php endif; ?>

                        <a class="nav-link" href="messages.php">
                            <i class="bi bi-chat-dots me-2"></i>Mesajlar
                            <?php if (safeArray($stats, 'unread_messages', 0) > 0): ?>
                                <span class="badge bg-danger ms-auto"><?php echo safeArray($stats, 'unread_messages', 0); ?></span>
                            <?php endif; ?>
                        </a>
                        <a class="nav-link" href="notifications.php">
                            <i class="bi bi-bell me-2"></i>Bildirimler
                        </a>
                        <a class="nav-link" href="packages.php">
                            <i class="bi bi-gift me-2"></i>Paketler
                        </a>

                        <hr class="my-3">

                        <a class="nav-link" href="profile.php">
                            <i class="bi bi-gear me-2"></i>Ayarlar
                        </a>
                        <a class="nav-link" href="help.php">
                            <i class="bi bi-question-circle me-2"></i>Yardım
                        </a>
                        <a class="nav-link" href="auth/logout.php">
                            <i class="bi bi-box-arrow-right me-2"></i>Çıkış
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h2 class="mb-0">Dashboard</h2>
                            <p class="text-muted mb-0">
                                <?php if ($user_type === 'family'): ?>
                                    İlanlarınızı yönetin ve bakıcı bulun
                                <?php else: ?>
                                    İş fırsatlarını keşfedin ve başvuru yapın
                                <?php endif; ?>
                            </p>
                        </div>
                        <div>
                            <?php if ($user_type === 'family'): ?>
                                <a href="jobs/create.php" class="btn btn-primary">
                                    <i class="bi bi-plus-circle me-2"></i>Yeni İlan
                                </a>
                            <?php else: ?>
                                <a href="jobs.php" class="btn btn-primary">
                                    <i class="bi bi-search me-2"></i>İş Ara
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- İstatistik Kartları -->
                    <div class="row g-4 mb-4">
                        <?php if ($user_type === 'family'): ?>
                            <div class="col-md-3">
                                <div class="card stat-card bg-primary text-white">
                                    <div class="d-flex align-items-center">
                                        <div class="stat-icon bg-white bg-opacity-25">
                                            <i class="bi bi-briefcase"></i>
                                        </div>
                                        <div class="ms-3">
                                            <h3 class="mb-0"><?php echo safeArray($stats, 'my_jobs', 0); ?></h3>
                                            <small>Toplam İlanım</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card stat-card bg-success text-white">
                                    <div class="d-flex align-items-center">
                                        <div class="stat-icon bg-white bg-opacity-25">
                                            <i class="bi bi-check-circle"></i>
                                        </div>
                                        <div class="ms-3">
                                            <h3 class="mb-0"><?php echo safeArray($stats, 'active_jobs', 0); ?></h3>
                                            <small>Aktif İlan</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card stat-card bg-warning text-white">
                                    <div class="d-flex align-items-center">
                                        <div class="stat-icon bg-white bg-opacity-25">
                                            <i class="bi bi-person-check"></i>
                                        </div>
                                        <div class="ms-3">
                                            <h3 class="mb-0"><?php echo safeArray($stats, 'total_applications', 0); ?></h3>
                                            <small>Toplam Başvuru</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card stat-card bg-info text-white">
                                    <div class="d-flex align-items-center">
                                        <div class="stat-icon bg-white bg-opacity-25">
                                            <i class="bi bi-chat-dots"></i>
                                        </div>
                                        <div class="ms-3">
                                            <h3 class="mb-0"><?php echo safeArray($stats, 'unread_messages', 0); ?></h3>
                                            <small>Okunmamış Mesaj</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="col-md-3">
                                <div class="card stat-card bg-primary text-white">
                                    <div class="d-flex align-items-center">
                                        <div class="stat-icon bg-white bg-opacity-25">
                                            <i class="bi bi-file-text"></i>
                                        </div>
                                        <div class="ms-3">
                                            <h3 class="mb-0"><?php echo safeArray($stats, 'my_applications', 0); ?></h3>
                                            <small>Başvurularım</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card stat-card bg-warning text-white">
                                    <div class="d-flex align-items-center">
                                        <div class="stat-icon bg-white bg-opacity-25">
                                            <i class="bi bi-clock"></i>
                                        </div>
                                        <div class="ms-3">
                                            <h3 class="mb-0"><?php echo safeArray($stats, 'pending_applications', 0); ?></h3>
                                            <small>Bekleyen</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card stat-card bg-success text-white">
                                    <div class="d-flex align-items-center">
                                        <div class="stat-icon bg-white bg-opacity-25">
                                            <i class="bi bi-check-circle"></i>
                                        </div>
                                        <div class="ms-3">
                                            <h3 class="mb-0"><?php echo safeArray($stats, 'accepted_applications', 0); ?></h3>
                                            <small>Kabul Edilen</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card stat-card bg-info text-white">
                                    <div class="d-flex align-items-center">
                                        <div class="progress-circle">
                                            <svg viewBox="0 0 36 36" class="circular-chart">
                                                <path class="circle-bg" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                                                      fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
                                                <path class="circle" stroke-dasharray="<?php echo safeArray($stats, 'profile_completion', 0); ?>, 100"
                                                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                                                      fill="none" stroke="white" stroke-width="2"/>
                                                <text x="18" y="20.35" class="percentage" fill="white" font-size="6"><?php echo safeArray($stats, 'profile_completion', 0); ?>%</text>
                                            </svg>
                                        </div>
                                        <div class="ms-3">
                                            <small>Profil Tamamlanma</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Ana İçerik -->
                    <div class="row g-4">
                        <!-- Sol Kolon -->
                        <div class="col-lg-8">
                            <?php if ($user_type === 'family'): ?>
                                <!-- Son Başvurular -->
                                <div class="card mb-4">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0">Son Başvurular</h5>
                                        <a href="applications.php" class="btn btn-sm btn-outline-primary">Tümünü Gör</a>
                                    </div>
                                    <div class="card-body">
                                        <?php if (!empty($recent_applications)): ?>
                                            <div class="table-responsive">
                                                <table class="table table-hover">
                                                    <thead>
                                                        <tr>
                                                            <th>Bakıcı</th>
                                                            <th>İlan</th>
                                                            <th>Deneyim</th>
                                                            <th>Puan</th>
                                                            <th>Tarih</th>
                                                            <th>İşlem</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php foreach ($recent_applications as $app): ?>
                                                        <tr>
                                                            <td>
                                                                <strong><?php echo escape($app['caregiver_name']); ?></strong>
                                                            </td>
                                                            <td><?php echo escape($app['job_title']); ?></td>
                                                            <td><?php echo $app['experience_years']; ?> yıl</td>
                                                            <td>
                                                                <?php if ($app['rating'] > 0): ?>
                                                                    <span class="text-warning">
                                                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                                                            <i class="bi bi-star<?php echo $i <= $app['rating'] ? '-fill' : ''; ?>"></i>
                                                                        <?php endfor; ?>
                                                                    </span>
                                                                    <small>(<?php echo formatRating($app['rating']); ?>)</small>
                                                                <?php else: ?>
                                                                    <small class="text-muted">Henüz puan yok</small>
                                                                <?php endif; ?>
                                                            </td>
                                                            <td><?php echo formatDate($app['applied_at']); ?></td>
                                                            <td>
                                                                <a href="application.php?id=<?php echo $app['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                                    Görüntüle
                                                                </a>
                                                            </td>
                                                        </tr>
                                                        <?php endforeach; ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        <?php else: ?>
                                            <div class="text-center py-4">
                                                <i class="bi bi-inbox text-muted" style="font-size: 3rem;"></i>
                                                <p class="text-muted mt-2">Henüz başvuru yok</p>
                                                <a href="jobs/create.php" class="btn btn-primary">İlan Ver</a>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <!-- Aktif İlanlar -->
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0">Aktif İlanlarım</h5>
                                        <a href="jobs/my-jobs.php" class="btn btn-sm btn-outline-primary">Tümünü Gör</a>
                                    </div>
                                    <div class="card-body">
                                        <?php if (!empty($active_jobs)): ?>
                                            <?php foreach ($active_jobs as $job): ?>
                                            <div class="border rounded p-3 mb-3">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div>
                                                        <h6 class="mb-1"><?php echo escape($job['title']); ?></h6>
                                                        <p class="text-muted small mb-2">
                                                            <i class="bi bi-geo-alt me-1"></i><?php echo escape($job['location_city']); ?>
                                                            <?php if ($job['location_district']): ?>
                                                                , <?php echo escape($job['location_district']); ?>
                                                            <?php endif; ?>
                                                        </p>
                                                        <p class="text-muted small mb-0">
                                                            <i class="bi bi-person-check me-1"></i><?php echo $job['application_count']; ?> başvuru
                                                        </p>
                                                    </div>
                                                    <div class="text-end">
                                                        <?php if ($job['budget_min'] || $job['budget_max']): ?>
                                                        <p class="text-primary fw-bold mb-1">
                                                            <?php if ($job['budget_min'] && $job['budget_max']): ?>
                                                                <?php echo formatMoney($job['budget_min']); ?> - <?php echo formatMoney($job['budget_max']); ?>
                                                            <?php elseif ($job['budget_min']): ?>
                                                                <?php echo formatMoney($job['budget_min']); ?>+
                                                            <?php else: ?>
                                                                <?php echo formatMoney($job['budget_max']); ?>'e kadar
                                                            <?php endif; ?>
                                                        </p>
                                                        <?php endif; ?>
                                                        <a href="job.php?id=<?php echo $job['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                            Görüntüle
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <div class="text-center py-4">
                                                <i class="bi bi-briefcase text-muted" style="font-size: 3rem;"></i>
                                                <p class="text-muted mt-2">Aktif ilanınız yok</p>
                                                <a href="jobs/create.php" class="btn btn-primary">İlk İlanınızı Verin</a>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>

                            <?php else: ?>
                                <!-- Bakıcı için başvurular -->
                                <div class="card mb-4">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0">Son Başvurularım</h5>
                                        <a href="applications/my-applications.php" class="btn btn-sm btn-outline-primary">Tümünü Gör</a>
                                    </div>
                                    <div class="card-body">
                                        <?php if (!empty($my_applications)): ?>
                                            <?php foreach ($my_applications as $app): ?>
                                            <div class="border rounded p-3 mb-3">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div>
                                                        <h6 class="mb-1"><?php echo escape($app['job_title']); ?></h6>
                                                        <p class="text-muted small mb-1">
                                                            <i class="bi bi-person me-1"></i><?php echo escape($app['employer_name']); ?>
                                                        </p>
                                                        <p class="text-muted small mb-2">
                                                            <i class="bi bi-geo-alt me-1"></i><?php echo escape($app['location_city']); ?>
                                                        </p>
                                                        <small class="text-muted">
                                                            Başvuru: <?php echo formatDate($app['applied_at']); ?>
                                                        </small>
                                                    </div>
                                                    <div class="text-end">
                                                        <span class="badge bg-<?php
                                                            echo $app['status'] === 'pending' ? 'warning' :
                                                                ($app['status'] === 'accepted' ? 'success' : 'danger');
                                                        ?>">
                                                            <?php
                                                            echo $app['status'] === 'pending' ? 'Bekliyor' :
                                                                ($app['status'] === 'accepted' ? 'Kabul Edildi' : 'Reddedildi');
                                                            ?>
                                                        </span>
                                                        <div class="mt-2">
                                                            <a href="application.php?id=<?php echo $app['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                                Detay
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <div class="text-center py-4">
                                                <i class="bi bi-file-text text-muted" style="font-size: 3rem;"></i>
                                                <p class="text-muted mt-2">Henüz başvuru yapmadınız</p>
                                                <a href="jobs.php" class="btn btn-primary">İş İlanlarına Göz Atın</a>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <!-- Uygun İş İlanları -->
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0">Size Uygun İş İlanları</h5>
                                        <a href="jobs.php" class="btn btn-sm btn-outline-primary">Tümünü Gör</a>
                                    </div>
                                    <div class="card-body">
                                        <?php if (!empty($suitable_jobs)): ?>
                                            <?php foreach ($suitable_jobs as $job): ?>
                                            <div class="border rounded p-3 mb-3">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div>
                                                        <h6 class="mb-1"><?php echo escape($job['title']); ?></h6>
                                                        <p class="text-muted small mb-1">
                                                            <i class="bi bi-person me-1"></i><?php echo escape($job['employer_name']); ?>
                                                        </p>
                                                        <p class="text-muted small mb-2">
                                                            <i class="bi bi-geo-alt me-1"></i><?php echo escape($job['location_city']); ?>
                                                            <?php if ($job['location_district']): ?>
                                                                , <?php echo escape($job['location_district']); ?>
                                                            <?php endif; ?>
                                                        </p>
                                                        <small class="text-muted">
                                                            İlan: <?php echo formatDate($job['created_at']); ?>
                                                        </small>
                                                    </div>
                                                    <div class="text-end">
                                                        <?php if ($job['budget_min'] || $job['budget_max']): ?>
                                                        <p class="text-primary fw-bold mb-2">
                                                            <?php if ($job['budget_min'] && $job['budget_max']): ?>
                                                                <?php echo formatMoney($job['budget_min']); ?> - <?php echo formatMoney($job['budget_max']); ?>
                                                            <?php elseif ($job['budget_min']): ?>
                                                                <?php echo formatMoney($job['budget_min']); ?>+
                                                            <?php else: ?>
                                                                <?php echo formatMoney($job['budget_max']); ?>'e kadar
                                                            <?php endif; ?>
                                                        </p>
                                                        <?php endif; ?>
                                                        <a href="job.php?id=<?php echo $job['id']; ?>" class="btn btn-sm btn-primary">
                                                            Başvur
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <div class="text-center py-4">
                                                <i class="bi bi-search text-muted" style="font-size: 3rem;"></i>
                                                <p class="text-muted mt-2">Şu anda uygun iş ilanı yok</p>
                                                <a href="jobs.php" class="btn btn-primary">Tüm İlanları Görüntüle</a>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Sağ Kolon -->
                        <div class="col-lg-4">
                            <!-- Profil Tamamlama (Sadece Bakıcı) -->
                            <?php if ($user_type === 'caregiver' && safeArray($stats, 'profile_completion', 0) < 100): ?>
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">Profilinizi Tamamlayın</h6>
                                </div>
                                <div class="card-body">
                                    <div class="progress mb-3">
                                        <div class="progress-bar" role="progressbar" style="width: <?php echo safeArray($stats, 'profile_completion', 0); ?>%">
                                            <?php echo safeArray($stats, 'profile_completion', 0); ?>%
                                        </div>
                                    </div>
                                    <p class="small text-muted mb-3">
                                        Tam profil daha fazla iş fırsatı demektir!
                                    </p>
                                    <a href="profile/caregiver.php" class="btn btn-primary btn-sm w-100">
                                        Profili Tamamla
                                    </a>
                                </div>
                            </div>
                            <?php endif; ?>

                            <!-- Son Mesajlar -->
                            <div class="card mb-4">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">Son Mesajlar</h6>
                                    <a href="messages.php" class="btn btn-sm btn-outline-primary">Tümü</a>
                                </div>
                                <div class="card-body">
                                    <?php if (!empty($recent_messages)): ?>
                                        <?php foreach (array_slice($recent_messages, 0, 3) as $message): ?>
                                        <div class="message-item rounded p-2 mb-2">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <strong class="small"><?php echo escape($message['sender_name']); ?></strong>
                                                    <p class="small mb-1"><?php echo escape(substr($message['subject'], 0, 30)); ?>...</p>
                                                    <small class="text-muted"><?php echo formatDateTime($message['created_at'], 'd.m H:i'); ?></small>
                                                </div>
                                                <?php if (!$message['is_read']): ?>
                                                    <span class="badge bg-primary">Yeni</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <div class="text-center py-3">
                                            <i class="bi bi-chat-dots text-muted" style="font-size: 2rem;"></i>
                                            <p class="text-muted small mt-2">Henüz mesaj yok</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Bildirimler -->
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">Son Bildirimler</h6>
                                    <a href="notifications.php" class="btn btn-sm btn-outline-primary">Tümü</a>
                                </div>
                                <div class="card-body">
                                    <?php if (!empty($notifications)): ?>
                                        <?php foreach (array_slice($notifications, 0, 3) as $notification): ?>
                                        <div class="notification-item rounded p-2 mb-2">
                                            <strong class="small"><?php echo escape($notification['title']); ?></strong>
                                            <p class="small mb-1"><?php echo escape($notification['message']); ?></p>
                                            <small class="text-muted"><?php echo formatDateTime($notification['created_at'], 'd.m H:i'); ?></small>
                                        </div>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <div class="text-center py-3">
                                            <i class="bi bi-bell text-muted" style="font-size: 2rem;"></i>
                                            <p class="text-muted small mt-2">Henüz bildirim yok</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Otomatik yenileme (5 dakikada bir)
        setTimeout(function() {
            location.reload();
        }, 300000);

        // Bildirim sayısını güncelle
        function updateNotificationCount() {
            fetch('api/notification-count.php')
                .then(response => response.json())
                .then(data => {
                    if (data.count > 0) {
                        document.querySelector('.nav-link[href="messages.php"] .badge').textContent = data.count;
                    }
                });
        }

        // Her 30 saniyede bir bildirim sayısını kontrol et
        setInterval(updateNotificationCount, 30000);
    </script>
</body>
</html>