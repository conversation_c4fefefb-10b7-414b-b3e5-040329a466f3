<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Kullanıcı giriş yapmışsa çıkış işlemi yap
if (isset($_SESSION['user_id'])) {
    $user_id = $_SESSION['user_id'];
    
    // Aktivite kaydı
    logActivity($user_id, 'logout', 'Kullanıcı çıkış yaptı', 'users', $user_id);
    
    // Remember me token'ını temizle
    if (isset($_COOKIE['remember_token'])) {
        try {
            $sql = "UPDATE users SET remember_token = NULL WHERE id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$user_id]);
        } catch (PDOException $e) {
            // Hata durumunda sessizce devam et
        }
        
        // <PERSON>ie'yi sil
        setcookie('remember_token', '', time() - 3600, '/');
    }
}

// Session'ı temizle
session_destroy();

// Ana sayfaya yönlendir
header('Location: ../index.php?logout=1');
exit;
?>
