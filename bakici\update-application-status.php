<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Giriş kontrolü
if (!isLoggedIn()) {
    header('Location: auth/login.php');
    exit;
}

// POST kontrolü
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: dashboard.php');
    exit;
}

$application_id = intval($_POST['application_id'] ?? 0);
$status = $_POST['status'] ?? '';
$redirect = $_POST['redirect'] ?? 'dashboard.php';

// Geçerli durumlar
$valid_statuses = ['pending', 'viewed', 'shortlisted', 'interview', 'accepted', 'rejected', 'withdrawn', 'hired'];

if (!$application_id || !in_array($status, $valid_statuses)) {
    header('Location: ' . $redirect . '?error=invalid_data');
    exit;
}

try {
    // Başvuru bilgilerini getir ve yetki kontrolü yap
    $check_sql = "SELECT ja.*, jl.user_id as employer_id 
                  FROM job_applications ja 
                  JOIN job_listings jl ON ja.job_id = jl.id 
                  WHERE ja.id = ?";
    $check_stmt = $db->prepare($check_sql);
    $check_stmt->execute([$application_id]);
    $application = $check_stmt->fetch();
    
    if (!$application) {
        header('Location: ' . $redirect . '?error=application_not_found');
        exit;
    }
    
    $user_id = $_SESSION['user_id'];
    $user_type = $_SESSION['user_type'];
    
    // Yetki kontrolü
    $can_update = false;
    
    if ($user_type === 'admin') {
        $can_update = true;
    } elseif ($user_id == $application['employer_id']) {
        // İşveren sadece belirli durumları değiştirebilir
        $employer_allowed = ['viewed', 'shortlisted', 'interview', 'accepted', 'rejected', 'hired'];
        $can_update = in_array($status, $employer_allowed);
    } elseif ($user_id == $application['caregiver_id']) {
        // Bakıcı sadece geri çekebilir
        $can_update = ($status === 'withdrawn' && in_array($application['status'], ['pending', 'viewed']));
    }
    
    if (!$can_update) {
        header('Location: ' . $redirect . '?error=permission_denied');
        exit;
    }
    
    // Durum güncelleme
    $update_sql = "UPDATE job_applications SET status = ?, updated_at = NOW() WHERE id = ?";
    $update_stmt = $db->prepare($update_sql);
    $update_stmt->execute([$status, $application_id]);
    
    // Başarı mesajı
    $status_names = [
        'pending' => 'Bekliyor',
        'viewed' => 'Görüldü',
        'shortlisted' => 'Kısa Listeye Alındı',
        'interview' => 'Görüşmeye Çağrıldı',
        'accepted' => 'Kabul Edildi',
        'rejected' => 'Reddedildi',
        'withdrawn' => 'Geri Çekildi',
        'hired' => 'İşe Alındı'
    ];
    
    $success_message = 'Başvuru durumu "' . $status_names[$status] . '" olarak güncellendi.';
    
    // Bildirim gönder (opsiyonel)
    try {
        if ($status === 'accepted') {
            // Kabul edildi bildirimi
            $notification_sql = "INSERT INTO notifications (user_id, title, message, type, created_at) 
                                VALUES (?, ?, ?, ?, NOW())";
            $notification_stmt = $db->prepare($notification_sql);
            $notification_stmt->execute([
                $application['caregiver_id'],
                'Başvurunuz Kabul Edildi!',
                'Başvurunuz kabul edildi. İşveren ile iletişime geçebilirsiniz.',
                'application_accepted'
            ]);
        } elseif ($status === 'rejected') {
            // Reddedildi bildirimi
            $notification_sql = "INSERT INTO notifications (user_id, title, message, type, created_at) 
                                VALUES (?, ?, ?, ?, NOW())";
            $notification_stmt = $db->prepare($notification_sql);
            $notification_stmt->execute([
                $application['caregiver_id'],
                'Başvuru Durumu Güncellendi',
                'Başvurunuzla ilgili bir güncelleme var.',
                'application_updated'
            ]);
        } elseif ($status === 'hired') {
            // İşe alındı bildirimi
            $notification_sql = "INSERT INTO notifications (user_id, title, message, type, created_at) 
                                VALUES (?, ?, ?, ?, NOW())";
            $notification_stmt = $db->prepare($notification_sql);
            $notification_stmt->execute([
                $application['caregiver_id'],
                'Tebrikler! İşe Alındınız!',
                'İşe alındınız. İşveren ile detayları konuşabilirsiniz.',
                'hired'
            ]);
        }
    } catch (PDOException $e) {
        // Bildirim hatası önemli değil, devam et
    }
    
    header('Location: ' . $redirect . '?success=' . urlencode($success_message));
    exit;
    
} catch (PDOException $e) {
    error_log("Application status update error: " . $e->getMessage());
    header('Location: ' . $redirect . '?error=database_error');
    exit;
}
?>
