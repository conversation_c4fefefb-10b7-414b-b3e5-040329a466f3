<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Giriş kontrolü
if (!isLoggedIn()) {
    echo '<div class="alert alert-danger"><PERSON><PERSON><PERSON> yapmanız gerekiyor.</div>';
    exit;
}

// Sadece admin kullanıcıları erişebilir
if ($_SESSION['user_type'] !== 'admin') {
    echo '<div class="alert alert-danger">Bu sayfaya erişim yetkiniz yok.</div>';
    exit;
}

$user_id = intval($_GET['id'] ?? 0);
if (!$user_id) {
    echo '<div class="alert alert-danger">Geçersiz kullanıcı ID.</div>';
    exit;
}

// Kullanıcı bilgilerini getir
try {
    $sql = "SELECT * FROM users WHERE id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$user_id]);
    $user = $stmt->fetch();
    
    if (!$user) {
        echo '<div class="alert alert-danger">Kullanıcı bulunamadı.</div>';
        exit;
    }
    
    // İstatistikleri getir
    $stats = [];
    
    if ($user['user_type'] === 'family') {
        // Aile için iş ilanları
        $job_sql = "SELECT COUNT(*) as total, 
                           SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active,
                           SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as closed
                    FROM job_listings WHERE user_id = ?";
        $job_stmt = $db->prepare($job_sql);
        $job_stmt->execute([$user_id]);
        $stats['jobs'] = $job_stmt->fetch();
        
        // Son iş ilanları
        $recent_jobs_sql = "SELECT * FROM job_listings WHERE user_id = ? ORDER BY created_at DESC LIMIT 5";
        $recent_jobs_stmt = $db->prepare($recent_jobs_sql);
        $recent_jobs_stmt->execute([$user_id]);
        $stats['recent_jobs'] = $recent_jobs_stmt->fetchAll();
        
    } elseif ($user['user_type'] === 'caregiver') {
        // Bakıcı için başvurular
        $app_sql = "SELECT COUNT(*) as total,
                           SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                           SUM(CASE WHEN status = 'accepted' THEN 1 ELSE 0 END) as accepted
                    FROM job_applications WHERE caregiver_id = ?";
        $app_stmt = $db->prepare($app_sql);
        $app_stmt->execute([$user_id]);
        $stats['applications'] = $app_stmt->fetch();
        
        // Bakıcı profili
        $profile_sql = "SELECT * FROM caregiver_profiles WHERE user_id = ?";
        $profile_stmt = $db->prepare($profile_sql);
        $profile_stmt->execute([$user_id]);
        $stats['profile'] = $profile_stmt->fetch();
        
        // Son başvurular
        $recent_apps_sql = "SELECT ja.*, jl.title, jl.location 
                           FROM job_applications ja 
                           JOIN job_listings jl ON ja.job_id = jl.id 
                           WHERE ja.caregiver_id = ? 
                           ORDER BY ja.applied_at DESC LIMIT 5";
        $recent_apps_stmt = $db->prepare($recent_apps_sql);
        $recent_apps_stmt->execute([$user_id]);
        $stats['recent_applications'] = $recent_apps_stmt->fetchAll();
    }
    
    // Mesajlar
    $message_sql = "SELECT COUNT(*) as total,
                           SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread
                    FROM messages WHERE sender_id = ? OR receiver_id = ?";
    $message_stmt = $db->prepare($message_sql);
    $message_stmt->execute([$user_id, $user_id]);
    $stats['messages'] = $message_stmt->fetch();
    
} catch (PDOException $e) {
    echo '<div class="alert alert-danger">Veri yüklenirken hata oluştu.</div>';
    exit;
}
?>

<div class="row">
    <div class="col-md-4">
        <!-- Kullanıcı Bilgileri -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">Kullanıcı Bilgileri</h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" 
                         style="width: 80px; height: 80px; font-size: 2rem;">
                        <i class="bi bi-person"></i>
                    </div>
                    <h5 class="mt-2 mb-1"><?php echo htmlspecialchars($user['full_name']); ?></h5>
                    <span class="badge bg-<?php echo $user['user_type'] === 'admin' ? 'danger' : ($user['user_type'] === 'family' ? 'success' : 'info'); ?>">
                        <?php echo $user['user_type'] === 'family' ? 'Aile' : ($user['user_type'] === 'caregiver' ? 'Bakıcı' : 'Admin'); ?>
                    </span>
                </div>
                
                <table class="table table-sm">
                    <tr>
                        <td><strong>Email:</strong></td>
                        <td><?php echo htmlspecialchars($user['email']); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Telefon:</strong></td>
                        <td><?php echo htmlspecialchars($user['phone'] ?? 'Belirtilmemiş'); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Şehir:</strong></td>
                        <td><?php echo htmlspecialchars($user['city'] ?? 'Belirtilmemiş'); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Durum:</strong></td>
                        <td>
                            <span class="badge bg-<?php echo ($user['status'] ?? 'active') === 'active' ? 'success' : 'secondary'; ?>">
                                <?php echo ($user['status'] ?? 'active') === 'active' ? 'Aktif' : 'Pasif'; ?>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Doğrulama:</strong></td>
                        <td>
                            <?php if ($user['is_verified'] ?? 0): ?>
                                <span class="badge bg-success">Doğrulandı</span>
                            <?php else: ?>
                                <span class="badge bg-warning">Bekliyor</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Kayıt:</strong></td>
                        <td><?php echo date('d.m.Y', strtotime($user['created_at'])); ?></td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- Mesaj İstatistikleri -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Mesaj İstatistikleri</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary"><?php echo $stats['messages']['total'] ?? 0; ?></h4>
                        <small>Toplam Mesaj</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-warning"><?php echo $stats['messages']['unread'] ?? 0; ?></h4>
                        <small>Okunmamış</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <?php if ($user['user_type'] === 'family'): ?>
            <!-- Aile İstatistikleri -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0">İş İlanı İstatistikleri</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4">
                            <h4 class="text-primary"><?php echo $stats['jobs']['total'] ?? 0; ?></h4>
                            <small>Toplam İlan</small>
                        </div>
                        <div class="col-4">
                            <h4 class="text-success"><?php echo $stats['jobs']['active'] ?? 0; ?></h4>
                            <small>Aktif İlan</small>
                        </div>
                        <div class="col-4">
                            <h4 class="text-secondary"><?php echo $stats['jobs']['closed'] ?? 0; ?></h4>
                            <small>Kapalı İlan</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Son İş İlanları -->
            <?php if (!empty($stats['recent_jobs'])): ?>
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Son İş İlanları</h6>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush">
                            <?php foreach ($stats['recent_jobs'] as $job): ?>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($job['title']); ?></h6>
                                        <span class="badge bg-<?php echo $job['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                            <?php echo ucfirst($job['status']); ?>
                                        </span>
                                    </div>
                                    <p class="mb-1"><?php echo htmlspecialchars($job['location']); ?></p>
                                    <small><?php echo date('d.m.Y', strtotime($job['created_at'])); ?></small>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            
        <?php elseif ($user['user_type'] === 'caregiver'): ?>
            <!-- Bakıcı İstatistikleri -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0">Başvuru İstatistikleri</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4">
                            <h4 class="text-primary"><?php echo $stats['applications']['total'] ?? 0; ?></h4>
                            <small>Toplam Başvuru</small>
                        </div>
                        <div class="col-4">
                            <h4 class="text-warning"><?php echo $stats['applications']['pending'] ?? 0; ?></h4>
                            <small>Bekleyen</small>
                        </div>
                        <div class="col-4">
                            <h4 class="text-success"><?php echo $stats['applications']['accepted'] ?? 0; ?></h4>
                            <small>Kabul Edilen</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Bakıcı Profili -->
            <?php if ($stats['profile']): ?>
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">Bakıcı Profili</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Deneyim:</strong> <?php echo $stats['profile']['experience_years']; ?> yıl</p>
                                <p><strong>Puan:</strong> <?php echo $stats['profile']['rating']; ?>★</p>
                                <p><strong>Toplam İş:</strong> <?php echo $stats['profile']['total_jobs']; ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Saatlik Ücret:</strong> <?php echo formatMoney($stats['profile']['hourly_rate']); ?></p>
                                <p><strong>Aylık Ücret:</strong> <?php echo formatMoney($stats['profile']['monthly_rate']); ?></p>
                                <p><strong>Müsaitlik:</strong>
                                    <span class="badge bg-<?php echo isset($stats['profile']['is_available']) && $stats['profile']['is_available'] ? 'success' : 'secondary'; ?>">
                                        <?php echo isset($stats['profile']['is_available']) && $stats['profile']['is_available'] ? 'Müsait' : 'Müsait Değil'; ?>
                                    </span>
                                </p>
                            </div>
                        </div>
                        <?php if ($stats['profile']['specializations']): ?>
                            <p><strong>Uzmanlık Alanları:</strong> <?php echo htmlspecialchars($stats['profile']['specializations']); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Son Başvurular -->
            <?php if (!empty($stats['recent_applications'])): ?>
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Son Başvurular</h6>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush">
                            <?php foreach ($stats['recent_applications'] as $app): ?>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($app['title']); ?></h6>
                                        <span class="badge bg-<?php echo $app['status'] === 'accepted' ? 'success' : ($app['status'] === 'pending' ? 'warning' : 'secondary'); ?>">
                                            <?php echo ucfirst($app['status']); ?>
                                        </span>
                                    </div>
                                    <p class="mb-1"><?php echo htmlspecialchars($app['location']); ?></p>
                                    <small><?php echo date('d.m.Y', strtotime($app['applied_at'])); ?></small>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<div class="mt-3">
    <a href="user-edit.php?id=<?php echo $user['id']; ?>" class="btn btn-primary">
        <i class="bi bi-pencil me-2"></i>Düzenle
    </a>
    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Kapat</button>
</div>
