<?php
/**
 * Trendyol E-Faturam API Library
 * 
 * <AUTHOR> Name
 * @version 1.0.0
 * @license MIT
 */

class TrendyolEfaturamApi {
    
    private $api_url;
    private $username;
    private $password;
    private $test_mode;
    private $timeout = 30;
    
    // API Endpoints
    const ENDPOINT_TEST_CONNECTION = '/api/test';
    const ENDPOINT_SEND_INVOICE = '/api/invoice/send';
    const ENDPOINT_SEND_ARCHIVE = '/api/archive/send';
    const ENDPOINT_GET_INVOICE_STATUS = '/api/invoice/status';
    const ENDPOINT_GET_INVOICE_PDF = '/api/invoice/pdf';
    
    public function __construct($api_url, $username, $password, $test_mode = true) {
        $this->api_url = rtrim($api_url, '/');
        $this->username = $username;
        $this->password = $password;
        $this->test_mode = $test_mode;
    }
    
    /**
     * Test API connection
     */
    public function testConnection() {
        try {
            $response = $this->makeRequest('GET', self::ENDPOINT_TEST_CONNECTION);
            
            if ($response['http_code'] == 200) {
                return array('success' => true, 'data' => $response['data']);
            } else {
                return array('success' => false, 'error' => 'HTTP ' . $response['http_code'] . ': ' . $response['data']);
            }
        } catch (Exception $e) {
            return array('success' => false, 'error' => $e->getMessage());
        }
    }
    
    /**
     * Send E-Invoice (for corporate customers)
     */
    public function sendInvoice($invoice_data) {
        try {
            $data = $this->prepareInvoiceData($invoice_data);
            $response = $this->makeRequest('POST', self::ENDPOINT_SEND_INVOICE, $data);
            
            if ($response['http_code'] == 200 || $response['http_code'] == 201) {
                $result = json_decode($response['data'], true);
                return array('success' => true, 'data' => $result);
            } else {
                return array('success' => false, 'error' => 'HTTP ' . $response['http_code'] . ': ' . $response['data']);
            }
        } catch (Exception $e) {
            return array('success' => false, 'error' => $e->getMessage());
        }
    }
    
    /**
     * Send E-Archive Invoice (for individual customers)
     */
    public function sendArchiveInvoice($invoice_data) {
        try {
            $data = $this->prepareArchiveInvoiceData($invoice_data);
            $response = $this->makeRequest('POST', self::ENDPOINT_SEND_ARCHIVE, $data);
            
            if ($response['http_code'] == 200 || $response['http_code'] == 201) {
                $result = json_decode($response['data'], true);
                return array('success' => true, 'data' => $result);
            } else {
                return array('success' => false, 'error' => 'HTTP ' . $response['http_code'] . ': ' . $response['data']);
            }
        } catch (Exception $e) {
            return array('success' => false, 'error' => $e->getMessage());
        }
    }
    
    /**
     * Get invoice status
     */
    public function getInvoiceStatus($invoice_id) {
        try {
            $endpoint = self::ENDPOINT_GET_INVOICE_STATUS . '/' . $invoice_id;
            $response = $this->makeRequest('GET', $endpoint);
            
            if ($response['http_code'] == 200) {
                $result = json_decode($response['data'], true);
                return array('success' => true, 'data' => $result);
            } else {
                return array('success' => false, 'error' => 'HTTP ' . $response['http_code'] . ': ' . $response['data']);
            }
        } catch (Exception $e) {
            return array('success' => false, 'error' => $e->getMessage());
        }
    }
    
    /**
     * Get invoice PDF
     */
    public function getInvoicePdf($invoice_id) {
        try {
            $endpoint = self::ENDPOINT_GET_INVOICE_PDF . '/' . $invoice_id;
            $response = $this->makeRequest('GET', $endpoint);
            
            if ($response['http_code'] == 200) {
                return array('success' => true, 'data' => $response['data']);
            } else {
                return array('success' => false, 'error' => 'HTTP ' . $response['http_code'] . ': ' . $response['data']);
            }
        } catch (Exception $e) {
            return array('success' => false, 'error' => $e->getMessage());
        }
    }
    
    /**
     * Prepare invoice data for E-Invoice
     */
    private function prepareInvoiceData($invoice_data) {
        $data = array(
            'invoiceType' => 'SATIS',
            'documentType' => 'FATURA',
            'invoiceSeriesOrNumber' => $invoice_data['invoice_number'],
            'issueDate' => date('Y-m-d', strtotime($invoice_data['date_added'])),
            'issueTime' => date('H:i:s', strtotime($invoice_data['date_added'])),
            'invoiceCurrencyCode' => $invoice_data['currency_code'],
            'exchangeRate' => isset($invoice_data['exchange_rate']) ? $invoice_data['exchange_rate'] : 1,
            'wildCard1' => $invoice_data['order_id'],
            
            // Seller information
            'seller' => array(
                'vkn' => $invoice_data['seller']['tax_number'],
                'alias' => $invoice_data['seller']['alias'],
                'title' => $invoice_data['seller']['company_name'],
                'taxOffice' => $invoice_data['seller']['tax_office'],
                'address' => array(
                    'streetName' => $invoice_data['seller']['address'],
                    'citySubdivisionName' => $invoice_data['seller']['district'],
                    'cityName' => $invoice_data['seller']['city'],
                    'postalZone' => $invoice_data['seller']['postcode'],
                    'country' => $invoice_data['seller']['country']
                )
            ),
            
            // Buyer information
            'buyer' => array(
                'vkn' => $invoice_data['buyer']['tax_number'],
                'title' => $invoice_data['buyer']['company_name'],
                'taxOffice' => $invoice_data['buyer']['tax_office'],
                'address' => array(
                    'streetName' => $invoice_data['buyer']['address'],
                    'citySubdivisionName' => $invoice_data['buyer']['district'],
                    'cityName' => $invoice_data['buyer']['city'],
                    'postalZone' => $invoice_data['buyer']['postcode'],
                    'country' => $invoice_data['buyer']['country']
                )
            ),
            
            // Invoice lines
            'invoiceLines' => array()
        );
        
        // Add invoice lines
        foreach ($invoice_data['products'] as $index => $product) {
            $data['invoiceLines'][] = array(
                'lineNumber' => $index + 1,
                'deliveredQuantity' => $product['quantity'],
                'unitCode' => 'C62', // Piece
                'unitPrice' => $product['price'],
                'lineExtensionAmount' => $product['total'],
                'taxLines' => array(
                    array(
                        'taxAmount' => $product['tax'],
                        'taxRate' => $product['tax_rate'],
                        'taxTypeCode' => '0015' // KDV
                    )
                ),
                'item' => array(
                    'name' => $product['name']
                )
            );
        }
        
        return $data;
    }
    
    /**
     * Prepare invoice data for E-Archive Invoice
     */
    private function prepareArchiveInvoiceData($invoice_data) {
        $data = $this->prepareInvoiceData($invoice_data);
        
        // E-Archive specific modifications
        $data['documentType'] = 'EARSIVFATURA';
        $data['buyer']['identifier'] = $invoice_data['buyer']['tc_number']; // TC number for individuals
        unset($data['buyer']['vkn']); // Remove VKN for individuals
        
        return $data;
    }
    
    /**
     * Make HTTP request to API
     */
    private function makeRequest($method, $endpoint, $data = null) {
        $url = $this->api_url . $endpoint;
        
        $ch = curl_init();
        
        curl_setopt_array($ch, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $this->timeout,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'Accept: application/json',
                'Authorization: Basic ' . base64_encode($this->username . ':' . $this->password)
            ),
            CURLOPT_SSL_VERIFYPEER => !$this->test_mode,
            CURLOPT_SSL_VERIFYHOST => !$this->test_mode ? 2 : 0
        ));
        
        if ($data && ($method == 'POST' || $method == 'PUT')) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        if ($error) {
            throw new Exception('cURL Error: ' . $error);
        }
        
        return array(
            'http_code' => $http_code,
            'data' => $response
        );
    }
    
    /**
     * Log API requests and responses
     */
    private function log($message, $data = null) {
        $log_entry = date('Y-m-d H:i:s') . ' - ' . $message;
        if ($data) {
            $log_entry .= ' - Data: ' . json_encode($data);
        }
        $log_entry .= PHP_EOL;
        
        // Write to log file
        $log_file = DIR_LOGS . 'trendyol_efaturam.log';
        file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
    }
}
