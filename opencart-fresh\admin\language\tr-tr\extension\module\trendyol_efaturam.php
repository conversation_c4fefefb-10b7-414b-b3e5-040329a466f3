<?php
/**
 * Trendyol E-Faturam OpenCart 3.0.3.2 Extension
 * Turkish Language File
 */

// Heading
$_['heading_title']    = 'Trendyol E-Faturam';

// Text
$_['text_extension']   = 'Eklentiler';
$_['text_success']     = 'Başarılı: Trendyol E-Faturam modülü güncellendi!';
$_['text_edit']        = 'Trendyol E-Faturam Modülünü Düzenle';
$_['text_enabled']     = 'Etkin';
$_['text_disabled']    = 'Devre Dışı';
$_['text_test_mode']   = 'Test Modu';
$_['text_production_mode'] = 'Canlı Mod';
$_['text_connection_success'] = 'API bağlantısı başarılı!';
$_['text_connection_failed'] = 'API bağlantısı başarısız';

// Entry
$_['entry_status']     = 'Durum';
$_['entry_mode']       = 'Mod';
$_['entry_api_url']    = 'API URL';
$_['entry_username']   = 'Kullanıcı Adı';
$_['entry_password']   = 'Şifre';
$_['entry_company_name'] = 'Şirket Adı';
$_['entry_company_tax_number'] = 'Vergi Numarası';
$_['entry_company_tax_office'] = 'Vergi Dairesi';
$_['entry_auto_send']  = 'Otomatik Gönderim';

// Button
$_['button_test_connection'] = 'Bağlantıyı Test Et';

// Error
$_['error_permission'] = 'Uyarı: Bu modülü değiştirme yetkiniz yok!';
$_['error_api_url']    = 'API URL gerekli!';
$_['error_username']   = 'Kullanıcı adı gerekli!';
$_['error_password']   = 'Şifre gerekli!';

// Help
$_['help_api_url']     = 'Trendyol E-Faturam API endpoint URL\'si';
$_['help_username']    = 'Trendyol E-Faturam kullanıcı adınız';
$_['help_password']    = 'Trendyol E-Faturam şifreniz';
$_['help_auto_send']   = 'Sipariş tamamlandığında otomatik olarak fatura gönderilsin mi?';
$_['help_mode']        = 'Test modu geliştirme için, Canlı mod üretim için kullanılır';
