<?php
/**
 * Trendyol E-Faturam OpenCart 3.0.3.2 Extension
 * Invoice Management Controller
 * 
 * <AUTHOR> Name
 * @version 1.0.0
 * @license MIT
 */

class ControllerExtensionModuleTrendyolEfaturamInvoices extends Controller {
    
    private $error = array();
    
    public function index() {
        $this->load->language('extension/module/trendyol_efaturam');
        
        $this->document->setTitle($this->language->get('heading_title_invoices'));
        
        $this->load->model('extension/module/trendyol_efaturam');
        
        $this->getList();
    }
    
    public function getList() {
        if (isset($this->request->get['filter_order_id'])) {
            $filter_order_id = $this->request->get['filter_order_id'];
        } else {
            $filter_order_id = '';
        }
        
        if (isset($this->request->get['filter_invoice_number'])) {
            $filter_invoice_number = $this->request->get['filter_invoice_number'];
        } else {
            $filter_invoice_number = '';
        }
        
        if (isset($this->request->get['filter_status'])) {
            $filter_status = $this->request->get['filter_status'];
        } else {
            $filter_status = '';
        }
        
        if (isset($this->request->get['filter_type'])) {
            $filter_type = $this->request->get['filter_type'];
        } else {
            $filter_type = '';
        }
        
        if (isset($this->request->get['filter_date_from'])) {
            $filter_date_from = $this->request->get['filter_date_from'];
        } else {
            $filter_date_from = '';
        }
        
        if (isset($this->request->get['filter_date_to'])) {
            $filter_date_to = $this->request->get['filter_date_to'];
        } else {
            $filter_date_to = '';
        }
        
        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }
        
        $url = '';
        
        if (isset($this->request->get['filter_order_id'])) {
            $url .= '&filter_order_id=' . urlencode(html_entity_decode($this->request->get['filter_order_id'], ENT_QUOTES, 'UTF-8'));
        }
        
        if (isset($this->request->get['filter_invoice_number'])) {
            $url .= '&filter_invoice_number=' . urlencode(html_entity_decode($this->request->get['filter_invoice_number'], ENT_QUOTES, 'UTF-8'));
        }
        
        if (isset($this->request->get['filter_status'])) {
            $url .= '&filter_status=' . urlencode(html_entity_decode($this->request->get['filter_status'], ENT_QUOTES, 'UTF-8'));
        }
        
        if (isset($this->request->get['filter_type'])) {
            $url .= '&filter_type=' . urlencode(html_entity_decode($this->request->get['filter_type'], ENT_QUOTES, 'UTF-8'));
        }
        
        if (isset($this->request->get['filter_date_from'])) {
            $url .= '&filter_date_from=' . urlencode(html_entity_decode($this->request->get['filter_date_from'], ENT_QUOTES, 'UTF-8'));
        }
        
        if (isset($this->request->get['filter_date_to'])) {
            $url .= '&filter_date_to=' . urlencode(html_entity_decode($this->request->get['filter_date_to'], ENT_QUOTES, 'UTF-8'));
        }
        
        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }
        
        $data['breadcrumbs'] = array();
        
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_extension'),
            'href' => $this->url->link('marketplace/extension', 'user_token=' . $this->session->data['user_token'] . '&type=module', true)
        );
        
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title_invoices'),
            'href' => $this->url->link('extension/module/trendyol_efaturam_invoices', 'user_token=' . $this->session->data['user_token'] . $url, true)
        );
        
        $data['invoices'] = array();
        
        $filter_data = array(
            'filter_order_id'       => $filter_order_id,
            'filter_invoice_number' => $filter_invoice_number,
            'filter_status'         => $filter_status,
            'filter_type'           => $filter_type,
            'filter_date_from'      => $filter_date_from,
            'filter_date_to'        => $filter_date_to,
            'start'                 => ($page - 1) * $this->config->get('config_limit_admin'),
            'limit'                 => $this->config->get('config_limit_admin')
        );
        
        $invoice_total = $this->model_extension_module_trendyol_efaturam->getTotalInvoices($filter_data);
        
        $results = $this->model_extension_module_trendyol_efaturam->getInvoices($filter_data);
        
        foreach ($results as $result) {
            $data['invoices'][] = array(
                'invoice_id'     => $result['invoice_id'],
                'order_id'       => $result['order_id'],
                'invoice_number' => $result['invoice_number'],
                'invoice_type'   => $result['invoice_type'],
                'status'         => $result['status'],
                'customer'       => $result['firstname'] . ' ' . $result['lastname'],
                'total'          => $result['total'],
                'date_created'   => date($this->language->get('date_format_short'), strtotime($result['date_created'])),
                'retry_count'    => $result['retry_count'],
                'view'           => $this->url->link('extension/module/trendyol_efaturam_invoices/view', 'user_token=' . $this->session->data['user_token'] . '&invoice_id=' . $result['invoice_id'] . $url, true),
                'resend'         => $this->url->link('extension/module/trendyol_efaturam_invoices/resend', 'user_token=' . $this->session->data['user_token'] . '&invoice_id=' . $result['invoice_id'] . $url, true)
            );
        }
        
        $data['heading_title'] = $this->language->get('heading_title_invoices');
        
        $data['text_list'] = $this->language->get('text_list');
        $data['text_no_results'] = $this->language->get('text_no_results');
        $data['text_confirm'] = $this->language->get('text_confirm');
        
        $data['column_order_id'] = $this->language->get('column_order_id');
        $data['column_invoice_number'] = $this->language->get('column_invoice_number');
        $data['column_customer'] = $this->language->get('column_customer');
        $data['column_type'] = $this->language->get('column_type');
        $data['column_status'] = $this->language->get('column_status');
        $data['column_total'] = $this->language->get('column_total');
        $data['column_date_created'] = $this->language->get('column_date_created');
        $data['column_action'] = $this->language->get('column_action');
        
        $data['entry_order_id'] = $this->language->get('entry_order_id');
        $data['entry_invoice_number'] = $this->language->get('entry_invoice_number');
        $data['entry_status'] = $this->language->get('entry_status');
        $data['entry_type'] = $this->language->get('entry_type');
        $data['entry_date_from'] = $this->language->get('entry_date_from');
        $data['entry_date_to'] = $this->language->get('entry_date_to');
        
        $data['button_filter'] = $this->language->get('button_filter');
        $data['button_view'] = $this->language->get('button_view');
        $data['button_resend'] = $this->language->get('button_resend');
        
        if (isset($this->error['warning'])) {
            $data['error_warning'] = $this->error['warning'];
        } else {
            $data['error_warning'] = '';
        }
        
        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];
            
            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }
        
        $data['filter_order_id'] = $filter_order_id;
        $data['filter_invoice_number'] = $filter_invoice_number;
        $data['filter_status'] = $filter_status;
        $data['filter_type'] = $filter_type;
        $data['filter_date_from'] = $filter_date_from;
        $data['filter_date_to'] = $filter_date_to;
        
        $pagination = new Pagination();
        $pagination->total = $invoice_total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit_admin');
        $pagination->url = $this->url->link('extension/module/trendyol_efaturam_invoices', 'user_token=' . $this->session->data['user_token'] . $url . '&page={page}', true);
        
        $data['pagination'] = $pagination->render();
        
        $data['results'] = sprintf($this->language->get('text_pagination'), ($invoice_total) ? (($page - 1) * $this->config->get('config_limit_admin')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit_admin')) > ($invoice_total - $this->config->get('config_limit_admin'))) ? $invoice_total : ((($page - 1) * $this->config->get('config_limit_admin')) + $this->config->get('config_limit_admin')), $invoice_total, ceil($invoice_total / $this->config->get('config_limit_admin')));
        
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        
        $this->response->setOutput($this->load->view('extension/module/trendyol_efaturam_invoices', $data));
    }
    
    public function view() {
        $this->load->language('extension/module/trendyol_efaturam');
        
        if (isset($this->request->get['invoice_id'])) {
            $invoice_id = $this->request->get['invoice_id'];
        } else {
            $invoice_id = 0;
        }
        
        $this->load->model('extension/module/trendyol_efaturam');
        
        $invoice_info = $this->model_extension_module_trendyol_efaturam->getInvoice($invoice_id);
        
        if ($invoice_info) {
            $this->document->setTitle($this->language->get('heading_title_invoice_view'));
            
            $data['heading_title'] = $this->language->get('heading_title_invoice_view');
            
            $data['invoice'] = $invoice_info;
            $data['api_response'] = json_decode($invoice_info['api_response'], true);
            
            $data['header'] = $this->load->controller('common/header');
            $data['column_left'] = $this->load->controller('common/column_left');
            $data['footer'] = $this->load->controller('common/footer');
            
            $this->response->setOutput($this->load->view('extension/module/trendyol_efaturam_invoice_view', $data));
        } else {
            $this->response->redirect($this->url->link('extension/module/trendyol_efaturam_invoices', 'user_token=' . $this->session->data['user_token'], true));
        }
    }
    
    public function resend() {
        $this->load->language('extension/module/trendyol_efaturam');
        
        if (isset($this->request->get['invoice_id'])) {
            $invoice_id = $this->request->get['invoice_id'];
        } else {
            $invoice_id = 0;
        }
        
        $this->load->model('extension/module/trendyol_efaturam');
        
        $invoice_info = $this->model_extension_module_trendyol_efaturam->getInvoice($invoice_id);
        
        if ($invoice_info) {
            try {
                // Load order information
                $this->load->model('checkout/order');
                $order_info = $this->model_checkout_order->getOrder($invoice_info['order_id']);
                
                if ($order_info) {
                    // Resend invoice logic here
                    $this->load->controller('catalog/controller/extension/module/trendyol_efaturam');
                    
                    $this->session->data['success'] = $this->language->get('text_invoice_resent');
                } else {
                    $this->session->data['error'] = $this->language->get('error_order_not_found');
                }
            } catch (Exception $e) {
                $this->session->data['error'] = $e->getMessage();
            }
        } else {
            $this->session->data['error'] = $this->language->get('error_invoice_not_found');
        }
        
        $this->response->redirect($this->url->link('extension/module/trendyol_efaturam_invoices', 'user_token=' . $this->session->data['user_token'], true));
    }
}
