# Değişiklik Günlüğü

Bu dosya, Trendyol E-Faturam OpenCart modülündeki tüm önemli değişiklikleri içerir.

Format [Keep a Changelog](https://keepachangelog.com/en/1.0.0/) standardına dayanır ve bu proje [Semantic Versioning](https://semver.org/spec/v2.0.0.html) kullanır.

## [Unreleased]

### Planlanmış Özellikler
- E-İrsaliye desteği
- Toplu fatura gönderimi
- Fatura şablonu özelleştirme
- Multi-store desteği
- Webhook desteği

## [1.0.0] - 2025-01-XX

### Eklenen
- İlk sürüm yayınlandı
- E-Fatura gönderim desteği
- E-Arşiv fatura gönderim desteği
- Otomatik fatura gönderim sistemi
- Admin panel arayüzü
- API bağlantı testi
- Detaylı loglama sistemi
- Hata yönetimi ve yeniden deneme mekaniz<PERSON>ı
- Test modu desteği
- Fatura durumu takibi
- Manuel fatura yeniden gönderimi
- Sistem test araçları
- Türkçe dil desteği
- OCMOD kurulum desteği
- Veritabanı tablo yönetimi
- Güvenlik önlemleri
- Kapsamlı dokümantasyon

### Teknik Özellikler
- OpenCart 3.0.3.2+ uyumluluğu
- PHP 7.1+ desteği
- MySQL 5.6+ desteği
- cURL tabanlı API iletişimi
- JSON veri formatı
- SSL/TLS güvenlik desteği
- Event-driven otomatik gönderim
- Modüler yapı
- Hata toleransı
- Performans optimizasyonu

### Güvenlik
- API kimlik doğrulama
- Güvenli veri saklama
- SQL injection koruması
- XSS koruması
- CSRF koruması
- Dosya izin kontrolü

### Veritabanı
- `oc_trendyol_efaturam_invoices` tablosu
- `oc_trendyol_efaturam_logs` tablosu  
- `oc_trendyol_efaturam_settings` tablosu
- İndeks optimizasyonu
- Veri bütünlüğü kontrolü

### API Entegrasyonu
- Trendyol E-Faturam API v1 desteği
- RESTful API iletişimi
- JSON request/response
- HTTP status kod yönetimi
- Timeout yönetimi
- Retry mekanizması

### Kullanıcı Arayüzü
- Bootstrap 3 uyumlu tasarım
- Responsive arayüz
- AJAX tabanlı işlemler
- Filtreleme ve arama
- Sayfalama desteği
- Kullanıcı dostu mesajlar

### Loglama
- Detaylı API log kayıtları
- Hata log kayıtları
- Sistem olayları logu
- Log filtreleme
- Log temizleme
- Debug modu

### Test Araçları
- Sistem gereksinim kontrolü
- API bağlantı testi
- Veritabanı kontrolü
- Dosya izin kontrolü
- PHP extension kontrolü
- Örnek veri testi

### Dokümantasyon
- README.md - Genel bilgiler
- INSTALLATION.md - Kurulum kılavuzu
- CHANGELOG.md - Değişiklik günlüğü
- Kod içi dokümantasyon
- API dokümantasyonu
- Sorun giderme kılavuzu

## Gelecek Sürümler

### [1.1.0] - Planlanıyor
- E-İrsaliye entegrasyonu
- Gelişmiş raporlama
- Fatura şablonu editörü
- Webhook desteği

### [1.2.0] - Planlanıyor
- Multi-store desteği
- Toplu işlem araçları
- API v2 desteği
- Performans iyileştirmeleri

### [2.0.0] - Planlanıyor
- OpenCart 4.x desteği
- Yeni UI/UX tasarımı
- Mikroservis mimarisi
- GraphQL API desteği

## Destek ve Katkı

### Hata Bildirimi
Hata bildirmek için GitHub Issues kullanın:
- Detaylı hata açıklaması
- Adım adım tekrar etme yöntemi
- Sistem bilgileri
- Log dosyaları

### Özellik İsteği
Yeni özellik önerileri için:
- Özellik açıklaması
- Kullanım senaryoları
- Fayda analizi
- Teknik gereksinimler

### Katkıda Bulunma
1. Fork edin
2. Feature branch oluşturun
3. Testlerinizi yazın
4. Commit edin
5. Pull request gönderin

## Lisans

Bu proje MIT lisansı altında dağıtılmaktadır. Detaylar için LICENSE dosyasına bakın.

## İletişim

- **Email**: <EMAIL>
- **Website**: https://www.example.com
- **GitHub**: https://github.com/username/opencart-trendyol-efaturam
- **Dokümantasyon**: https://docs.example.com

## Teşekkürler

Bu projeye katkıda bulunan herkese teşekkürler:
- OpenCart topluluğu
- Trendyol E-Faturam ekibi
- Beta test kullanıcıları
- Katkıda bulunan geliştiriciler
