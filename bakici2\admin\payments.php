<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Giriş kontrolü
if (!isset($_SESSION['user_id'])) {
    header('Location: ../auth/login.php');
    exit;
}

// Sadece admin kullanıcıları erişebilir
if ($_SESSION['user_type'] !== 'admin') {
    header('Location: ../dashboard.php');
    exit;
}

// Ödemeleri getir (şimdilik demo veriler)
try {
    // Demo ödeme verileri oluştur
    $payments = [
        [
            'id' => 1,
            'user_name' => 'Demo Aile Kullanıcısı',
            'user_email' => '<EMAIL>',
            'package_name' => 'Standart Paket',
            'amount' => 59.00,
            'status' => 'completed',
            'payment_method' => 'credit_card',
            'transaction_id' => 'TXN_' . date('Ymd') . '_001',
            'created_at' => date('Y-m-d H:i:s', strtotime('-2 days'))
        ],
        [
            'id' => 2,
            'user_name' => 'Mehmet Kaya',
            'user_email' => '<EMAIL>',
            'package_name' => 'Premium Paket',
            'amount' => 99.00,
            'status' => 'pending',
            'payment_method' => 'bank_transfer',
            'transaction_id' => 'TXN_' . date('Ymd') . '_002',
            'created_at' => date('Y-m-d H:i:s', strtotime('-1 day'))
        ],
        [
            'id' => 3,
            'user_name' => 'Fatma Yılmaz',
            'user_email' => '<EMAIL>',
            'package_name' => 'Temel Paket',
            'amount' => 29.00,
            'status' => 'failed',
            'payment_method' => 'credit_card',
            'transaction_id' => 'TXN_' . date('Ymd') . '_003',
            'created_at' => date('Y-m-d H:i:s', strtotime('-3 hours'))
        ]
    ];

    // İstatistikler
    $stats = [
        'total' => count($payments),
        'completed' => count(array_filter($payments, function($p) { return $p['status'] === 'completed'; })),
        'pending' => count(array_filter($payments, function($p) { return $p['status'] === 'pending'; })),
        'failed' => count(array_filter($payments, function($p) { return $p['status'] === 'failed'; }))
    ];

    // Toplam gelir
    $total_revenue = array_sum(array_map(function($p) {
        return $p['status'] === 'completed' ? $p['amount'] : 0;
    }, $payments));

} catch (Exception $e) {
    $payments = [];
    $stats = ['total' => 0, 'completed' => 0, 'pending' => 0, 'failed' => 0];
    $total_revenue = 0;
    error_log("Admin payments query error: " . $e->getMessage());
}

$page_title = 'Ödeme Yönetimi';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Admin Panel</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="../index.php">
                <i class="bi bi-heart-fill me-2"></i>Bakıcı Platform - Admin
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../dashboard.php">Dashboard</a>
                <a class="nav-link" href="settings.php">Ayarlar</a>
                <a class="nav-link" href="../auth/logout.php">Çıkış</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="bi bi-credit-card me-2"></i>Ödeme Yönetimi</h4>
                    </div>
                    <div class="card-body">
                        <!-- İstatistikler -->
                        <div class="row g-3 mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h3><?php echo $stats['total']; ?></h3>
                                        <small>Toplam İşlem</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h3><?php echo $stats['completed']; ?></h3>
                                        <small>Başarılı</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <h3><?php echo $stats['pending']; ?></h3>
                                        <small>Bekleyen</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h3>₺<?php echo number_format($total_revenue, 2); ?></h3>
                                        <small>Toplam Gelir</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Ödeme Listesi -->
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Kullanıcı</th>
                                        <th>Paket</th>
                                        <th>Tutar</th>
                                        <th>Ödeme Yöntemi</th>
                                        <th>Durum</th>
                                        <th>İşlem ID</th>
                                        <th>Tarih</th>
                                        <th>İşlemler</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($payments as $payment): ?>
                                    <tr>
                                        <td><?php echo $payment['id']; ?></td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($payment['user_name']); ?></strong>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($payment['user_email']); ?></small>
                                        </td>
                                        <td><?php echo htmlspecialchars($payment['package_name']); ?></td>
                                        <td>
                                            <strong>₺<?php echo number_format($payment['amount'], 2); ?></strong>
                                        </td>
                                        <td>
                                            <?php if ($payment['payment_method'] === 'credit_card'): ?>
                                                <i class="bi bi-credit-card me-1"></i>Kredi Kartı
                                            <?php elseif ($payment['payment_method'] === 'bank_transfer'): ?>
                                                <i class="bi bi-bank me-1"></i>Havale/EFT
                                            <?php else: ?>
                                                <i class="bi bi-wallet me-1"></i>Diğer
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($payment['status'] === 'completed'): ?>
                                                <span class="badge bg-success">Tamamlandı</span>
                                            <?php elseif ($payment['status'] === 'pending'): ?>
                                                <span class="badge bg-warning">Bekleyen</span>
                                            <?php elseif ($payment['status'] === 'failed'): ?>
                                                <span class="badge bg-danger">Başarısız</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Bilinmiyor</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <code><?php echo htmlspecialchars($payment['transaction_id']); ?></code>
                                        </td>
                                        <td>
                                            <small><?php echo date('d.m.Y H:i', strtotime($payment['created_at'])); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="viewPayment(<?php echo $payment['id']; ?>)">
                                                    <i class="bi bi-eye"></i>
                                                </button>
                                                <?php if ($payment['status'] === 'pending'): ?>
                                                    <button class="btn btn-outline-success" onclick="approvePayment(<?php echo $payment['id']; ?>)">
                                                        <i class="bi bi-check"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger" onclick="rejectPayment(<?php echo $payment['id']; ?>)">
                                                        <i class="bi bi-x"></i>
                                                    </button>
                                                <?php endif; ?>
                                                <button class="btn btn-outline-info" onclick="refundPayment(<?php echo $payment['id']; ?>)">
                                                    <i class="bi bi-arrow-counterclockwise"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6><i class="bi bi-info-circle me-2"></i>Demo Veriler</h6>
                                    <p class="mb-0">Bu sayfada gösterilen veriler demo amaçlıdır. Gerçek ödeme sistemi PayTR entegrasyonu ile aktif olacaktır.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function viewPayment(paymentId) {
            alert('Ödeme detay sayfası yakında aktif olacak. ID: ' + paymentId);
        }

        function approvePayment(paymentId) {
            if (confirm('Bu ödemeyi onaylamak istediğinizden emin misiniz?')) {
                alert('Ödeme onaylama özelliği yakında aktif olacak. ID: ' + paymentId);
            }
        }

        function rejectPayment(paymentId) {
            if (confirm('Bu ödemeyi reddetmek istediğinizden emin misiniz?')) {
                alert('Ödeme reddetme özelliği yakında aktif olacak. ID: ' + paymentId);
            }
        }

        function refundPayment(paymentId) {
            if (confirm('Bu ödeme için iade işlemi başlatmak istediğinizden emin misiniz?')) {
                alert('Ödeme iade özelliği yakında aktif olacak. ID: ' + paymentId);
            }
        }
    </script>
</body>
</html>
