<?php
/**
 * Trendyol E-Faturam OpenCart 3.0.3.2 Extension
 * English Language File
 */

// Heading
$_['heading_title']    = 'Trendyol E-Faturam';

// Text
$_['text_extension']   = 'Extensions';
$_['text_success']     = 'Success: You have modified Trendyol E-Faturam module!';
$_['text_edit']        = 'Edit Trendyol E-Faturam Module';
$_['text_enabled']     = 'Enabled';
$_['text_disabled']    = 'Disabled';
$_['text_yes']         = 'Yes';
$_['text_no']          = 'No';
$_['text_actions']     = 'Actions';

// Entry
$_['entry_status']     = 'Status';
$_['entry_api_url']    = 'API URL';
$_['entry_username']   = 'Username';
$_['entry_password']   = 'Password';
$_['entry_test_mode']  = 'Test Mode';

// Help
$_['help_api_url']     = 'Trendyol E-Faturam API URL address';
$_['help_test_mode']   = 'In test mode, invoices are not sent to the real system';

// Button
$_['button_save']              = 'Save';
$_['button_cancel']            = 'Cancel';
$_['button_test_connection']   = 'Test Connection';
$_['button_view_invoices']     = 'View Invoices';

// Error
$_['error_permission'] = 'Warning: You do not have permission to modify this module!';
$_['error_api_url']    = 'API URL required!';
$_['error_username']   = 'Username required!';
$_['error_password']   = 'Password required!';
