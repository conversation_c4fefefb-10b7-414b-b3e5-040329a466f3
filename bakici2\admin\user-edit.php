<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Giriş kontrolü
if (!isLoggedIn()) {
    header('Location: ../auth/login.php');
    exit;
}

// Sadece admin kullanıcıları erişebilir
if ($_SESSION['user_type'] !== 'admin') {
    header('Location: ../dashboard.php');
    exit;
}

$user_id = intval($_GET['id'] ?? 0);
if (!$user_id) {
    header('Location: users.php');
    exit;
}

// Kullanıcı bilgilerini getir
try {
    $sql = "SELECT * FROM users WHERE id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$user_id]);
    $user = $stmt->fetch();
    
    if (!$user) {
        header('Location: users.php?error=user_not_found');
        exit;
    }
} catch (PDOException $e) {
    header('Location: users.php?error=database_error');
    exit;
}

// Form gönderildi
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $full_name = trim($_POST['full_name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $city = trim($_POST['city'] ?? '');
    $user_type = $_POST['user_type'] ?? '';
    $status = $_POST['status'] ?? '';
    $is_verified = isset($_POST['is_verified']) ? 1 : 0;
    
    $errors = [];
    
    // Validasyon
    if (empty($full_name)) {
        $errors[] = 'Ad soyad gereklidir.';
    }
    
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Geçerli bir email adresi gereklidir.';
    }
    
    if (!in_array($user_type, ['family', 'caregiver', 'admin'])) {
        $errors[] = 'Geçerli bir kullanıcı tipi seçiniz.';
    }
    
    if (!in_array($status, ['active', 'inactive', 'deleted'])) {
        $errors[] = 'Geçerli bir durum seçiniz.';
    }
    
    // Email benzersizlik kontrolü
    if (empty($errors)) {
        $email_check_sql = "SELECT id FROM users WHERE email = ? AND id != ?";
        $email_check_stmt = $db->prepare($email_check_sql);
        $email_check_stmt->execute([$email, $user_id]);
        if ($email_check_stmt->fetch()) {
            $errors[] = 'Bu email adresi başka bir kullanıcı tarafından kullanılıyor.';
        }
    }
    
    // Güncelleme
    if (empty($errors)) {
        try {
            $update_sql = "UPDATE users SET 
                          full_name = ?, 
                          email = ?, 
                          phone = ?, 
                          city = ?, 
                          user_type = ?, 
                          status = ?, 
                          is_verified = ?, 
                          updated_at = NOW() 
                          WHERE id = ?";
            $update_stmt = $db->prepare($update_sql);
            $update_stmt->execute([
                $full_name, $email, $phone, $city, 
                $user_type, $status, $is_verified, $user_id
            ]);
            
            header('Location: users.php?success=user_updated');
            exit;
            
        } catch (PDOException $e) {
            $errors[] = 'Güncelleme sırasında hata oluştu: ' . $e->getMessage();
        }
    }
}

$page_title = 'Kullanıcı Düzenle';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Admin Panel</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="../index.php">
                <i class="bi bi-heart-fill me-2"></i>Bakıcı Platform - Admin
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../dashboard.php">Dashboard</a>
                <a class="nav-link" href="users.php">Kullanıcılar</a>
                <a class="nav-link" href="../auth/logout.php">Çıkış</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="bi bi-pencil me-2"></i>Kullanıcı Düzenle</h4>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0">
                                <li class="breadcrumb-item"><a href="../dashboard.php">Dashboard</a></li>
                                <li class="breadcrumb-item"><a href="users.php">Kullanıcılar</a></li>
                                <li class="breadcrumb-item active">Düzenle</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="card-body">
                        <!-- Errors -->
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <form method="POST">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="full_name" class="form-label">Ad Soyad *</label>
                                    <input type="text" class="form-control" id="full_name" name="full_name" 
                                           value="<?php echo htmlspecialchars($user['full_name']); ?>" required>
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="email" class="form-label">Email *</label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?php echo htmlspecialchars($user['email']); ?>" required>
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="phone" class="form-label">Telefon</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" 
                                           value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>">
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="city" class="form-label">Şehir</label>
                                    <select class="form-select" id="city" name="city">
                                        <option value="">Şehir Seçin</option>
                                        <?php foreach (getCities() as $city): ?>
                                            <option value="<?php echo $city; ?>" 
                                                    <?php echo ($user['city'] ?? '') === $city ? 'selected' : ''; ?>>
                                                <?php echo $city; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-4">
                                    <label for="user_type" class="form-label">Kullanıcı Tipi *</label>
                                    <select class="form-select" id="user_type" name="user_type" required>
                                        <option value="family" <?php echo $user['user_type'] === 'family' ? 'selected' : ''; ?>>Aile</option>
                                        <option value="caregiver" <?php echo $user['user_type'] === 'caregiver' ? 'selected' : ''; ?>>Bakıcı</option>
                                        <option value="admin" <?php echo $user['user_type'] === 'admin' ? 'selected' : ''; ?>>Admin</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-4">
                                    <label for="status" class="form-label">Durum *</label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="active" <?php echo ($user['status'] ?? 'active') === 'active' ? 'selected' : ''; ?>>Aktif</option>
                                        <option value="inactive" <?php echo ($user['status'] ?? '') === 'inactive' ? 'selected' : ''; ?>>Pasif</option>
                                        <option value="deleted" <?php echo ($user['status'] ?? '') === 'deleted' ? 'selected' : ''; ?>>Silinmiş</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-4">
                                    <label class="form-label">Doğrulama</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_verified" name="is_verified" 
                                               <?php echo ($user['is_verified'] ?? 0) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="is_verified">
                                            Email Doğrulandı
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="col-12">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <small class="text-muted">
                                                <strong>Kayıt Tarihi:</strong> 
                                                <?php echo date('d.m.Y H:i', strtotime($user['created_at'])); ?>
                                            </small>
                                        </div>
                                        <div class="col-md-6">
                                            <small class="text-muted">
                                                <strong>Son Güncelleme:</strong> 
                                                <?php echo $user['updated_at'] ? date('d.m.Y H:i', strtotime($user['updated_at'])) : 'Hiç'; ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-between mt-4">
                                <a href="users.php" class="btn btn-secondary">
                                    <i class="bi bi-arrow-left me-2"></i>Geri Dön
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check me-2"></i>Güncelle
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
