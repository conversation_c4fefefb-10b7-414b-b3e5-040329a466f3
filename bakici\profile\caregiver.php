<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Giriş kontrolü
requireLogin();

// Sadece bakıcı kullanıcıları erişebilir
if ($_SESSION['user_type'] !== 'caregiver') {
    redirect('../dashboard.php');
}

$page_title = 'Profil Düzenle';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c5aa0;
        }
        
        .form-card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            background: white;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
        }
        
        .navbar {
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .profile-photo {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            object-fit: cover;
            border: 5px solid white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="../index.php">
                <i class="bi bi-heart-fill me-2"></i><?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../caregivers.php">Bakıcı Ara</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../jobs.php">İş İlanları</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../about.php">Hakkımızda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../contact.php">İletişim</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i><?php echo escape($_SESSION['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../dashboard.php"><i class="bi bi-speedometer2 me-2"></i>Dashboard</a></li>
                            <li><a class="dropdown-item" href="../profile.php"><i class="bi bi-person me-2"></i>Profil</a></li>
                            <li><a class="dropdown-item" href="../messages.php"><i class="bi bi-chat-dots me-2"></i>Mesajlar</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../auth/logout.php"><i class="bi bi-box-arrow-right me-2"></i>Çıkış</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4 mb-5">
        <!-- Sayfa Başlığı -->
        <div class="row mb-4">
            <div class="col">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="../dashboard.php">Dashboard</a></li>
                        <li class="breadcrumb-item active">Profil Düzenle</li>
                    </ol>
                </nav>
                <h2 class="fw-bold">Bakıcı Profilimi Düzenle</h2>
                <p class="text-muted">Profilinizi güncel tutarak daha fazla iş fırsatı yakalayın</p>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <div class="form-card p-5">
                    <form id="profileForm" method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <!-- Profil Fotoğrafı -->
                        <div class="text-center mb-5">
                            <div class="position-relative d-inline-block">
                                <img src="../assets/images/default-avatar.png" alt="Profil Fotoğrafı" class="profile-photo" id="profileImage">
                                <label for="profile_photo" class="btn btn-primary btn-sm position-absolute bottom-0 end-0 rounded-circle" style="width: 40px; height: 40px;">
                                    <i class="bi bi-camera"></i>
                                </label>
                                <input type="file" id="profile_photo" name="profile_photo" class="d-none" accept="image/*">
                            </div>
                            <p class="text-muted mt-2">Profil fotoğrafınızı değiştirmek için tıklayın</p>
                        </div>
                        
                        <!-- Kişisel Bilgiler -->
                        <div class="mb-5">
                            <h4 class="fw-bold mb-4">Kişisel Bilgiler</h4>
                            
                            <div class="row g-3 mb-3">
                                <div class="col-md-6">
                                    <label for="birth_date" class="form-label">Doğum Tarihi</label>
                                    <input type="date" class="form-control" id="birth_date" name="birth_date">
                                </div>
                                <div class="col-md-6">
                                    <label for="gender" class="form-label">Cinsiyet</label>
                                    <select class="form-select" id="gender" name="gender">
                                        <option value="">Seçin</option>
                                        <option value="female">Kadın</option>
                                        <option value="male">Erkek</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="row g-3 mb-3">
                                <div class="col-md-6">
                                    <label for="education_level" class="form-label">Eğitim Seviyesi</label>
                                    <select class="form-select" id="education_level" name="education_level">
                                        <option value="">Seçin</option>
                                        <option value="İlkokul">İlkokul</option>
                                        <option value="Ortaokul">Ortaokul</option>
                                        <option value="Lise">Lise</option>
                                        <option value="Ön Lisans">Ön Lisans</option>
                                        <option value="Lisans">Lisans</option>
                                        <option value="Yüksek Lisans">Yüksek Lisans</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="languages" class="form-label">Konuştuğu Diller</label>
                                    <input type="text" class="form-control" id="languages" name="languages" 
                                           placeholder="Türkçe, İngilizce, Almanca">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Deneyim ve Uzmanlık -->
                        <div class="mb-5">
                            <h4 class="fw-bold mb-4">Deneyim ve Uzmanlık</h4>
                            
                            <div class="row g-3 mb-3">
                                <div class="col-md-6">
                                    <label for="experience_years" class="form-label">Deneyim (Yıl)</label>
                                    <input type="number" class="form-control" id="experience_years" name="experience_years" 
                                           min="0" max="50" placeholder="0">
                                </div>
                                <div class="col-md-6">
                                    <label for="certifications" class="form-label">Sertifikalar</label>
                                    <input type="text" class="form-control" id="certifications" name="certifications" 
                                           placeholder="İlk yardım, CPR, vb.">
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="specializations" class="form-label">Uzmanlık Alanları</label>
                                <textarea class="form-control" id="specializations" name="specializations" rows="3" 
                                          placeholder="Çocuk bakımı, yaşlı bakımı, hasta bakımı, ev temizliği, vb."></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="bio" class="form-label">Hakkımda</label>
                                <textarea class="form-control" id="bio" name="bio" rows="4" 
                                          placeholder="Kendinizi tanıtın, deneyimlerinizi ve yaklaşımınızı açıklayın..."></textarea>
                            </div>
                        </div>
                        
                        <!-- Ücret Bilgileri -->
                        <div class="mb-5">
                            <h4 class="fw-bold mb-4">Ücret Bilgileri</h4>
                            
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <label for="hourly_rate" class="form-label">Saatlik Ücret (TL)</label>
                                    <input type="number" class="form-control" id="hourly_rate" name="hourly_rate" 
                                           min="0" step="0.01" placeholder="0.00">
                                </div>
                                <div class="col-md-4">
                                    <label for="daily_rate" class="form-label">Günlük Ücret (TL)</label>
                                    <input type="number" class="form-control" id="daily_rate" name="daily_rate" 
                                           min="0" step="0.01" placeholder="0.00">
                                </div>
                                <div class="col-md-4">
                                    <label for="monthly_rate" class="form-label">Aylık Ücret (TL)</label>
                                    <input type="number" class="form-control" id="monthly_rate" name="monthly_rate" 
                                           min="0" step="0.01" placeholder="0.00">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Çalışma Tercihleri -->
                        <div class="mb-5">
                            <h4 class="fw-bold mb-4">Çalışma Tercihleri</h4>
                            
                            <div class="row g-3 mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">Çalışma Şekli</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="live_in" name="work_types[]" value="live_in">
                                        <label class="form-check-label" for="live_in">Yatılı</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="live_out" name="work_types[]" value="live_out">
                                        <label class="form-check-label" for="live_out">Gündüzlü</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="hourly" name="work_types[]" value="hourly">
                                        <label class="form-check-label" for="hourly">Saatlik</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Hizmet Türleri</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="child_care" name="service_types[]" value="child_care">
                                        <label class="form-check-label" for="child_care">Çocuk Bakımı</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="elderly_care" name="service_types[]" value="elderly_care">
                                        <label class="form-check-label" for="elderly_care">Yaşlı Bakımı</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="patient_care" name="service_types[]" value="patient_care">
                                        <label class="form-check-label" for="patient_care">Hasta Bakımı</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="house_help" name="service_types[]" value="house_help">
                                        <label class="form-check-label" for="house_help">Ev Yardımcısı</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Butonlar -->
                        <div class="d-flex gap-3">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="bi bi-check-circle me-2"></i>Profili Kaydet
                            </button>
                            <a href="../dashboard.php" class="btn btn-outline-secondary btn-lg">
                                <i class="bi bi-arrow-left me-2"></i>Dashboard'a Dön
                            </a>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Profil Tamamlama</h6>
                    </div>
                    <div class="card-body">
                        <div class="progress mb-3" style="height: 10px;">
                            <div class="progress-bar bg-success" style="width: 45%"></div>
                        </div>
                        <p class="small text-muted">Profiliniz %45 tamamlanmış</p>
                        
                        <h6 class="fw-bold mb-3">Eksik Bilgiler:</h6>
                        <ul class="list-unstyled small">
                            <li class="mb-2">
                                <i class="bi bi-circle text-warning me-2"></i>Profil fotoğrafı
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-circle text-warning me-2"></i>Doğum tarihi
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-circle text-warning me-2"></i>Uzmanlık alanları
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-circle text-warning me-2"></i>Ücret bilgileri
                            </li>
                        </ul>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">Profil İpuçları</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled small">
                            <li class="mb-3">
                                <i class="bi bi-lightbulb text-warning me-2"></i>
                                <strong>Fotoğraf:</strong> Güler yüzlü ve profesyonel bir fotoğraf kullanın.
                            </li>
                            <li class="mb-3">
                                <i class="bi bi-lightbulb text-warning me-2"></i>
                                <strong>Açıklama:</strong> Deneyimlerinizi ve yaklaşımınızı detaylı anlatın.
                            </li>
                            <li class="mb-0">
                                <i class="bi bi-lightbulb text-warning me-2"></i>
                                <strong>Sertifikalar:</strong> Sahip olduğunuz sertifikaları belirtin.
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Profil fotoğrafı önizleme
        document.getElementById('profile_photo').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('profileImage').src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        });
        
        // Form gönderimi
        document.getElementById('profileForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Kaydediliyor...';
            submitBtn.disabled = true;
            
            // Gerçek form gönderimi için şimdilik alert
            alert('Profil güncelleme özelliği yakında aktif olacak.');
            
            // Butonu eski haline getir
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    </script>
</body>
</html>
