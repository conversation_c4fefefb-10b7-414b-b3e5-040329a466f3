# Trendyol E-Faturam OpenCart ******* Modülü

Bu modül, OpenCart ******* e-ticaret platformu ile Trendyol E-Faturam sistemi arasında entegrasyon sağlar.

## Özellikler

- ✅ OpenCart ******* tam uyumluluğu
- ✅ Trendyol E-Faturam API entegrasyonu
- ✅ Otomatik fatura oluşturma
- ✅ Test modu desteği
- ✅ Detaylı loglama sistemi
- ✅ Fatura durumu takibi
- ✅ Türkçe ve İngilizce dil desteği
- ✅ Admin panel entegrasyonu

## Kurulum

### 1. FTP ile Kurulum

1. `upload` klasörünün içeriğini OpenCart ana dizininize yükleyin
2. Veritabanınızda `install.sql` dosyasını çalıştırın
3. Admin panelden Extensions > Modules bölümüne gidin
4. "Trendyol E-Faturam" modülünü bulun ve Install edin

### 2. <PERSON>

1. Dosyaları aşağıdaki konumlara kopyalayın:
   ```
   upload/admin/controller/extension/module/trendyol_efaturam.php
   → admin/controller/extension/module/trendyol_efaturam.php
   
   upload/admin/model/extension/module/trendyol_efaturam.php
   → admin/model/extension/module/trendyol_efaturam.php
   
   upload/admin/view/template/extension/module/trendyol_efaturam.twig
   → admin/view/template/extension/module/trendyol_efaturam.twig
   
   upload/admin/view/template/extension/module/trendyol_efaturam_test.twig
   → admin/view/template/extension/module/trendyol_efaturam_test.twig
   
   upload/admin/language/en-gb/extension/module/trendyol_efaturam.php
   → admin/language/en-gb/extension/module/trendyol_efaturam.php
   
   upload/admin/language/tr-tr/extension/module/trendyol_efaturam.php
   → admin/language/tr-tr/extension/module/trendyol_efaturam.php
   
   upload/system/library/trendyol_efaturam_api.php
   → system/library/trendyol_efaturam_api.php
   ```

2. Veritabanınızda `install.sql` dosyasını çalıştırın

## Yapılandırma

1. Admin panelden Extensions > Modules > Trendyol E-Faturam bölümüne gidin
2. Modülü Install edin
3. Edit butonuna tıklayarak ayarları açın
4. API bilgilerini girin:
   - **API URL**: `https://efatura-test.trendyol.com/api/v1` (test için)
   - **Username**: Trendyol E-Faturam kullanıcı adınız
   - **Password**: Trendyol E-Faturam şifreniz
   - **Test Mode**: Test ortamı için Etkin
5. Save butonuna tıklayın
6. Test Connection ile bağlantıyı test edin

## Kullanım

Modül kurulup yapılandırıldıktan sonra:

### Sipariş Detayında Fatura İşlemleri:
1. **Sales > Orders** menüsünden sipariş listesine gidin
2. Herhangi bir siparişin **View** butonuna tıklayın
3. Sipariş detay sayfasında **"Trendyol E-Faturam"** panelini göreceksiniz
4. **"Fatura Oluştur"** butonuna tıklayarak fatura kesin
5. **"Durum Kontrol Et"** ile fatura durumunu takip edin

### Sipariş Listesinde Fatura Durumları:
1. **Sales > Orders** sayfasında her siparişin **"E-Fatura"** sütununda durumu görürsünüz
2. Durumlar: **Fatura Yok**, **Beklemede**, **Gönderildi**, **Onaylandı**, **Başarısız**

### Otomatik İşlemler:
1. Siparişler manuel olarak faturaya dönüştürülür
2. Fatura durumları otomatik takip edilir
3. Hata durumları loglanır
4. Admin panelden fatura geçmişi görüntülenebilir

## Test

1. Admin panelden modül ayarlarına gidin
2. "Test Connection" butonuna tıklayın
3. API bağlantısının başarılı olduğunu doğrulayın
4. Test siparişi oluşturun
5. Fatura oluşturma işlemini test edin

## Sorun Giderme

### Modül Görünmüyor
```sql
INSERT IGNORE INTO oc_extension (type, code) VALUES ('module', 'trendyol_efaturam');
```

### Permission Hatası
```sql
UPDATE oc_user_group SET 
permission = JSON_SET(
    permission, 
    '$.access[999]', 'extension/module/trendyol_efaturam',
    '$.modify[999]', 'extension/module/trendyol_efaturam'
) 
WHERE user_group_id = 1;
```

### Cache Temizleme
```bash
rm -rf system/storage/cache/*
rm -rf system/storage/session/*
```

## Sistem Gereksinimleri

- OpenCart *******
- PHP 7.1+
- MySQL 5.6+
- cURL extension
- JSON extension

## Destek

Herhangi bir sorun yaşarsanız:
1. Log dosyalarını kontrol edin
2. Test modunu etkinleştirin
3. API bağlantısını test edin
4. Veritabanı tablolarının oluşturulduğunu doğrulayın

## Lisans

MIT License

## Versiyon

v1.0.0 - İlk sürüm
