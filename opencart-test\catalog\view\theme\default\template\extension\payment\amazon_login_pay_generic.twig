{% if column_left and column_right %}
{% set class = 'col-sm-6' %}
{% elseif column_left or column_right %}
{% set class = 'col-sm-9' %}
{% else %}
{% set class = 'col-sm-12' %}
{% endif %}
{{ header }}
<style type="text/css">
    .breadcrumb {
        padding: 0;
        overflow: hidden;
    }

    .breadcrumb > li {
        height: 38px;
        line-height: 38px;
    }

    .breadcrumb > li:after {
        top: 6px;
    }

    .breadcrumb > li.current {
        text-decoration: underline;
    }

    .breadcrumb > li.disabled {
        cursor: not-allowed;
        color: #aaa;
    }
</style>
<div class="container">
    {% if breadcrumbs %}
    <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
            {% if breadcrumb.href and not breadcrumb.current %}
            <li><a href="{{ breadcrumb.href }}">
                {{ breadcrumb.text }}
            </a></li>
            {% else %}
                <li class="text-muted {{ breadcrumb.current ? 'current' : 'disabled' }}">{{ breadcrumb.text }}</li>
            {% endif %}
        {% endfor %}
    </ul>
    {% endif %}
    <div class="row">
        {{ column_left }}
        <div id="content" class="{{ class }}">
            {{ content_top }}
            {{ content_main }}
            {{ content_bottom }}
        </div>
        {{ column_right }}
    </div>
</div>
{{ footer }}
