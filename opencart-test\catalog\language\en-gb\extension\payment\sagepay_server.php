<?php
// Text
$_['text_title']				= 'Credit Card / Debit Card (SagePay)';
$_['text_credit_card']			= 'Card Details';
$_['text_description']			= 'Items on %s Order No: %s';
$_['text_card_type']			= 'Card Type: ';
$_['text_card_name']			= 'Card Name: ';
$_['text_card_digits']			= 'Last Digits: ';
$_['text_card_expiry']			= 'Expiry: ';
$_['text_trial']				= '%s every %s %s for %s payments then ';
$_['text_recurring']			= '%s every %s %s';
$_['text_length']				= ' for %s payments';
$_['text_success']				= 'Your payment has been authorised.';
$_['text_decline']				= 'Your payment has been declined.';
$_['text_bank_error']			= 'There was an error processing your request with the bank.';
$_['text_transaction_error']	= 'There was an error processing your transaction.';
$_['text_generic_error']		= 'There was an error processing your request.';
$_['text_hash_failed']			= 'Hash check failed. Do not try your payment again as the payment status is unknown. Please contact the merchant.';
$_['text_link']					= 'Please click <a href="%s">here</a> to continue';
$_['text_confirm_delete']		= 'Are you sure you want to delete the card?';

// Entry
$_['entry_card']				= 'New or Existing Card: ';
$_['entry_card_existing']		= 'Existing';
$_['entry_card_new']			= 'New';
$_['entry_card_save']			= 'Remember card details for future use';
$_['entry_cc_choice']			= 'Choose an Existing Card';

// Button
$_['button_delete_card']		= 'Delete selected card';