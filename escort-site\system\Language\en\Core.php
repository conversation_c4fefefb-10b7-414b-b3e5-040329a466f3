<?php

declare(strict_types=1);

/**
 * This file is part of CodeIgniter 4 framework.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

// Core language settings
return [
    'copyError'                    => 'An error was encountered while attempting to replace the file "{0}". Please make sure your file directory is writable.',
    'enabledZlibOutputCompression' => 'Your zlib.output_compression ini directive is turned on. This will not work well with output buffers.',
    'invalidFile'                  => 'Invalid file: "{0}"',
    'invalidDirectory'             => 'Directory does not exist: "{0}"',
    'invalidPhpVersion'            => 'Your PHP version must be {0} or higher to run CodeIgniter. Current version: {1}',
    'missingExtension'             => 'The framework needs the following extension(s) installed and loaded: "{0}".',
    'noHandlers'                   => '"{0}" must provide at least one Handler.',
];
