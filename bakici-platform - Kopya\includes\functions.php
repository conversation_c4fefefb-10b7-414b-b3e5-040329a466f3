<?php
// Güvenlik fonksiyonları
function safeVar($var, $default = '') {
    return isset($var) ? $var : $default;
}

function safeArray($array, $key, $default = '') {
    return isset($array[$key]) ? $array[$key] : $default;
}

function safeJsonEncode($data) {
    return json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}

function safeNumber($var, $default = 0) {
    return is_numeric($var) ? (float)$var : $default;
}

function escape($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

function cleanInput($input) {
    $input = trim($input);
    $input = stripslashes($input);
    $input = htmlspecialchars($input);
    return $input;
}

// CSRF koruması
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Giriş kontrolü
function requireLogin() {
    // Session timeout kontrolü
    checkSessionTimeout();

    if (!isLoggedIn()) {
        // Relative path kullan
        header('Location: auth/login.php?required=1');
        exit;
    }
}

function requireAdmin() {
    requireLogin();
    if ($_SESSION['user_type'] !== 'admin') {
        header('Location: ' . SITE_URL . '/dashboard.php');
        exit;
    }
}

function hasUserType($type) {
    return isset($_SESSION['user_type']) && $_SESSION['user_type'] === $type;
}

function isLoggedIn() {
    return isset($_SESSION['user_id']) && !isSessionExpired();
}

// Session timeout kontrolü
function isSessionExpired() {
    // Session timeout süresi (saniye cinsinden) - 1 saat = 3600 saniye
    $timeout_duration = 3600;

    // Son aktivite zamanı kontrolü
    if (isset($_SESSION['last_activity'])) {
        $time_since_last_activity = time() - $_SESSION['last_activity'];

        if ($time_since_last_activity > $timeout_duration) {
            // Session süresi dolmuş
            return true;
        }
    }

    // Son aktivite zamanını güncelle
    $_SESSION['last_activity'] = time();
    return false;
}

// Session'ı başlat ve timeout ayarla
function startSession() {
    if (session_status() == PHP_SESSION_NONE) {
        // Session ayarları
        ini_set('session.gc_maxlifetime', 3600); // 1 saat
        ini_set('session.cookie_lifetime', 3600); // 1 saat

        session_start();

        // İlk giriş zamanını kaydet
        if (!isset($_SESSION['login_time'])) {
            $_SESSION['login_time'] = time();
        }

        // Son aktivite zamanını güncelle
        $_SESSION['last_activity'] = time();
    }
}

// Session'ı sonlandır
function destroySession() {
    if (session_status() == PHP_SESSION_ACTIVE) {
        // Session verilerini temizle
        $_SESSION = array();

        // Session cookie'sini sil
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }

        // Session'ı yok et
        session_destroy();
    }
}

// Session timeout kontrolü ve yönlendirme
function checkSessionTimeout() {
    if (isLoggedIn() && isSessionExpired()) {
        // Session süresi dolmuş, kullanıcıyı çıkış yap
        destroySession();

        // Login sayfasına yönlendir
        $current_page = $_SERVER['REQUEST_URI'];
        $login_url = '/bakici-platform/auth/login.php?timeout=1';

        // AJAX isteği ise JSON response döndür
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'session_expired',
                'message' => 'Oturumunuzun süresi doldu. Lütfen tekrar giriş yapın.',
                'redirect' => $login_url
            ]);
            exit;
        }

        // Normal sayfa isteği ise redirect
        header('Location: ' . $login_url);
        exit;
    }
}

function getCurrentUser() {
    if (!isLoggedIn()) return null;
    
    global $db;
    try {
        $sql = "SELECT * FROM users WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$_SESSION['user_id']]);
        return $stmt->fetch();
    } catch (PDOException $e) {
        return null;
    }
}

// Flash mesaj sistemi
function setMessage($message, $type = 'info') {
    if (!isset($_SESSION['flash_messages'])) {
        $_SESSION['flash_messages'] = [];
    }
    $_SESSION['flash_messages'][] = [
        'message' => $message,
        'type' => $type
    ];
}

function getMessages() {
    if (isset($_SESSION['flash_messages'])) {
        $messages = $_SESSION['flash_messages'];
        unset($_SESSION['flash_messages']);
        return $messages;
    }
    return [];
}

function hasMessages() {
    return isset($_SESSION['flash_messages']) && !empty($_SESSION['flash_messages']);
}

// Tarih ve zaman fonksiyonları
function formatDate($date, $format = 'd.m.Y') {
    if (!$date) return '-';
    return date($format, strtotime($date));
}

function formatDateTime($datetime, $format = 'd.m.Y H:i') {
    if (!$datetime) return '-';
    return date($format, strtotime($datetime));
}

function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'az önce';
    if ($time < 3600) return floor($time/60) . ' dakika önce';
    if ($time < 86400) return floor($time/3600) . ' saat önce';
    if ($time < 2592000) return floor($time/86400) . ' gün önce';
    if ($time < 31536000) return floor($time/2592000) . ' ay önce';
    return floor($time/31536000) . ' yıl önce';
}

// Dosya işlemleri
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' B';
    }
}

function getFileIcon($extension) {
    $icons = [
        'pdf' => 'bi-file-earmark-pdf',
        'doc' => 'bi-file-earmark-word',
        'docx' => 'bi-file-earmark-word',
        'xls' => 'bi-file-earmark-excel',
        'xlsx' => 'bi-file-earmark-excel',
        'ppt' => 'bi-file-earmark-ppt',
        'pptx' => 'bi-file-earmark-ppt',
        'jpg' => 'bi-file-earmark-image',
        'jpeg' => 'bi-file-earmark-image',
        'png' => 'bi-file-earmark-image',
        'gif' => 'bi-file-earmark-image',
        'zip' => 'bi-file-earmark-zip',
        'rar' => 'bi-file-earmark-zip',
        'txt' => 'bi-file-earmark-text',
        'default' => 'bi-file-earmark'
    ];
    
    return $icons[$extension] ?? $icons['default'];
}

// Para formatı
function formatMoney($amount, $currency = '₺') {
    return $currency . number_format($amount, 0, ',', '.');
}

function formatPrice($amount) {
    if ($amount >= 1000) {
        return number_format($amount / 1000, 1) . 'K TL';
    }
    return number_format($amount, 0) . ' TL';
}

// Puan formatı
function formatRating($rating) {
    return number_format($rating, 1);
}

function getRatingStars($rating, $maxStars = 5) {
    $stars = '';
    for ($i = 1; $i <= $maxStars; $i++) {
        if ($i <= $rating) {
            $stars .= '<i class="bi bi-star-fill text-warning"></i>';
        } elseif ($i - 0.5 <= $rating) {
            $stars .= '<i class="bi bi-star-half text-warning"></i>';
        } else {
            $stars .= '<i class="bi bi-star text-muted"></i>';
        }
    }
    return $stars;
}

// URL ve slug fonksiyonları
function createSlug($text) {
    $text = trim($text);
    $text = mb_strtolower($text, 'UTF-8');
    
    // Türkçe karakterleri değiştir
    $turkish = ['ç', 'ğ', 'ı', 'ö', 'ş', 'ü', 'Ç', 'Ğ', 'I', 'İ', 'Ö', 'Ş', 'Ü'];
    $english = ['c', 'g', 'i', 'o', 's', 'u', 'c', 'g', 'i', 'i', 'o', 's', 'u'];
    $text = str_replace($turkish, $english, $text);
    
    // Özel karakterleri kaldır
    $text = preg_replace('/[^a-z0-9\s-]/', '', $text);
    $text = preg_replace('/[\s-]+/', '-', $text);
    $text = trim($text, '-');
    
    return $text;
}

function generateUniqueSlug($table, $title, $id = null) {
    global $db;
    
    $baseSlug = createSlug($title);
    $slug = $baseSlug;
    $counter = 1;
    
    while (true) {
        $sql = "SELECT id FROM $table WHERE slug = ?";
        $params = [$slug];
        
        if ($id) {
            $sql .= " AND id != ?";
            $params[] = $id;
        }
        
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        
        if (!$stmt->fetch()) {
            break;
        }
        
        $slug = $baseSlug . '-' . $counter;
        $counter++;
    }
    
    return $slug;
}

function getCurrentUrl() {
    return (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . 
           "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
}

function redirect($url, $permanent = false) {
    if ($permanent) {
        header('HTTP/1.1 301 Moved Permanently');
    }
    header('Location: ' . $url);
    exit;
}

// Validasyon fonksiyonları
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

function validatePhone($phone) {
    $phone = preg_replace('/[^0-9]/', '', $phone);
    return strlen($phone) >= 10 && strlen($phone) <= 15;
}

function validatePassword($password) {
    return strlen($password) >= 6;
}

function validateRequired($value) {
    return !empty(trim($value));
}

function validateLength($value, $min = 0, $max = 255) {
    $length = mb_strlen($value, 'UTF-8');
    return $length >= $min && $length <= $max;
}

function validateNumeric($value, $min = null, $max = null) {
    if (!is_numeric($value)) return false;
    if ($min !== null && $value < $min) return false;
    if ($max !== null && $value > $max) return false;
    return true;
}

// Aktivite logu
function logActivity($user_id, $action, $description, $table_name = null, $record_id = null) {
    global $db;
    try {
        $sql = "INSERT INTO activity_logs (user_id, action, description, table_name, record_id, ip_address, user_agent, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
        $stmt = $db->prepare($sql);
        $stmt->execute([
            $user_id,
            $action,
            $description,
            $table_name,
            $record_id,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
    } catch (PDOException $e) {
        error_log("Activity log error: " . $e->getMessage());
    }
}

// E-posta gönderme
function sendEmail($to, $subject, $message, $from_name = 'Bakıcı Platform') {
    $headers = [
        'MIME-Version: 1.0',
        'Content-type: text/html; charset=UTF-8',
        'From: ' . $from_name . ' <<EMAIL>>',
        'Reply-To: <EMAIL>',
        'X-Mailer: PHP/' . phpversion()
    ];

    return mail($to, $subject, $message, implode("\r\n", $headers));
}

// Bildirim gönderme
function sendNotification($user_id, $type, $title, $message, $action_url = null) {
    global $db;
    try {
        $sql = "INSERT INTO notifications (user_id, type, title, message, action_url, created_at) VALUES (?, ?, ?, ?, ?, NOW())";
        $stmt = $db->prepare($sql);
        $stmt->execute([$user_id, $type, $title, $message, $action_url]);
        return true;
    } catch (PDOException $e) {
        error_log("Notification error: " . $e->getMessage());
        return false;
    }
}

// Dosya yükleme
function uploadFile($file, $upload_dir = 'uploads/', $allowed_types = ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx']) {
    if (!isset($file['tmp_name']) || !$file['tmp_name']) {
        return ['success' => false, 'message' => 'Dosya seçilmedi.'];
    }

    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

    if (!in_array($file_extension, $allowed_types)) {
        return ['success' => false, 'message' => 'Geçersiz dosya türü.'];
    }

    $max_size = getSetting('max_file_size', 10485760); // 10MB default
    if ($file['size'] > $max_size) {
        return ['success' => false, 'message' => 'Dosya boyutu çok büyük (max ' . formatFileSize($max_size) . ').'];
    }

    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }

    $new_filename = uniqid() . '_' . time() . '.' . $file_extension;
    $upload_path = $upload_dir . $new_filename;

    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
        return ['success' => true, 'filename' => $new_filename, 'path' => $upload_path];
    } else {
        return ['success' => false, 'message' => 'Dosya yüklenirken hata oluştu.'];
    }
}

// Resim yeniden boyutlandırma
function resizeImage($source, $destination, $max_width = 800, $max_height = 600, $quality = 85) {
    $image_info = getimagesize($source);
    if (!$image_info) return false;

    $width = $image_info[0];
    $height = $image_info[1];
    $type = $image_info[2];

    // Oranı koru
    $ratio = min($max_width / $width, $max_height / $height);
    $new_width = $width * $ratio;
    $new_height = $height * $ratio;

    // Kaynak resmi oluştur
    switch ($type) {
        case IMAGETYPE_JPEG:
            $source_image = imagecreatefromjpeg($source);
            break;
        case IMAGETYPE_PNG:
            $source_image = imagecreatefrompng($source);
            break;
        case IMAGETYPE_GIF:
            $source_image = imagecreatefromgif($source);
            break;
        default:
            return false;
    }

    // Yeni resim oluştur
    $new_image = imagecreatetruecolor($new_width, $new_height);

    // PNG için şeffaflığı koru
    if ($type == IMAGETYPE_PNG) {
        imagealphablending($new_image, false);
        imagesavealpha($new_image, true);
    }

    // Yeniden boyutlandır
    imagecopyresampled($new_image, $source_image, 0, 0, 0, 0, $new_width, $new_height, $width, $height);

    // Kaydet
    switch ($type) {
        case IMAGETYPE_JPEG:
            $result = imagejpeg($new_image, $destination, $quality);
            break;
        case IMAGETYPE_PNG:
            $result = imagepng($new_image, $destination);
            break;
        case IMAGETYPE_GIF:
            $result = imagegif($new_image, $destination);
            break;
    }

    // Belleği temizle
    imagedestroy($source_image);
    imagedestroy($new_image);

    return $result;
}

// Sistem ayarları
function getSetting($key, $default = null) {
    global $db;
    try {
        $sql = "SELECT setting_value FROM system_settings WHERE setting_key = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$key]);
        $result = $stmt->fetch();
        return $result ? $result['setting_value'] : $default;
    } catch (PDOException $e) {
        return $default;
    }
}

function setSetting($key, $value, $type = 'text', $category = 'general') {
    global $db;
    try {
        $sql = "INSERT INTO system_settings (setting_key, setting_value, setting_type, category)
                VALUES (?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = NOW()";
        $stmt = $db->prepare($sql);
        $stmt->execute([$key, $value, $type, $category]);
        return true;
    } catch (PDOException $e) {
        error_log("Setting save error: " . $e->getMessage());
        return false;
    }
}

// Kullanıcı paket kontrolü
function hasActivePackage($user_id, $feature = null) {
    global $db;
    try {
        $sql = "SELECT up.*, p.features FROM user_packages up
                JOIN packages p ON up.package_id = p.id
                WHERE up.user_id = ? AND up.is_active = 1 AND up.expires_at > NOW()
                ORDER BY up.expires_at DESC LIMIT 1";
        $stmt = $db->prepare($sql);
        $stmt->execute([$user_id]);
        $package = $stmt->fetch();

        if (!$package) return false;

        if ($feature) {
            $features = explode('|', $package['features']);
            return in_array($feature, $features);
        }

        return $package;
    } catch (PDOException $e) {
        return false;
    }
}

function getUserPackageUsage($user_id, $type) {
    global $db;
    try {
        $package = hasActivePackage($user_id);
        if (!$package) return ['used' => 0, 'limit' => 0, 'unlimited' => false];

        $used = 0;
        $limit = 0;

        switch ($type) {
            case 'job_listings':
                $limit = $package['max_job_listings'];
                if ($limit > 0) {
                    $sql = "SELECT COUNT(*) FROM job_listings WHERE user_id = ? AND created_at >= ?";
                    $stmt = $db->prepare($sql);
                    $stmt->execute([$user_id, $package['starts_at']]);
                    $used = $stmt->fetchColumn();
                }
                break;

            case 'applications':
                $limit = $package['max_applications'];
                if ($limit > 0) {
                    $sql = "SELECT COUNT(*) FROM job_applications WHERE caregiver_id = ? AND applied_at >= ?";
                    $stmt = $db->prepare($sql);
                    $stmt->execute([$user_id, $package['starts_at']]);
                    $used = $stmt->fetchColumn();
                }
                break;

            case 'messages':
                $limit = $package['max_messages'];
                if ($limit > 0) {
                    $sql = "SELECT COUNT(*) FROM messages WHERE sender_id = ? AND created_at >= ?";
                    $stmt = $db->prepare($sql);
                    $stmt->execute([$user_id, $package['starts_at']]);
                    $used = $stmt->fetchColumn();
                }
                break;
        }

        return [
            'used' => $used,
            'limit' => $limit,
            'unlimited' => $limit == -1,
            'remaining' => $limit == -1 ? -1 : max(0, $limit - $used)
        ];
    } catch (PDOException $e) {
        return ['used' => 0, 'limit' => 0, 'unlimited' => false];
    }
}

// Mesafe hesaplama
function calculateDistance($lat1, $lon1, $lat2, $lon2) {
    $earth_radius = 6371; // km

    $lat_diff = deg2rad($lat2 - $lat1);
    $lon_diff = deg2rad($lon2 - $lon1);

    $a = sin($lat_diff/2) * sin($lat_diff/2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($lon_diff/2) * sin($lon_diff/2);
    $c = 2 * atan2(sqrt($a), sqrt(1-$a));

    return $earth_radius * $c;
}

// Şehir ve kategori listeleri
function getCities() {
    global $db;
    try {
        $sql = "SELECT DISTINCT city FROM locations WHERE is_active = 1 ORDER BY city";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    } catch (PDOException $e) {
        return [
            'İstanbul', 'Ankara', 'İzmir', 'Bursa', 'Antalya', 'Adana', 'Konya', 'Gaziantep',
            'Mersin', 'Diyarbakır', 'Kayseri', 'Eskişehir', 'Urfa', 'Malatya', 'Erzurum'
        ];
    }
}

function getDistricts($city) {
    global $db;
    try {
        $sql = "SELECT DISTINCT district FROM locations WHERE city = ? AND district IS NOT NULL AND is_active = 1 ORDER BY district";
        $stmt = $db->prepare($sql);
        $stmt->execute([$city]);
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    } catch (PDOException $e) {
        return [];
    }
}

function getCategories() {
    global $db;
    try {
        $sql = "SELECT * FROM categories WHERE is_active = 1 ORDER BY sort_order, name";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return [];
    }
}

// İş türü çevirileri
function getJobTypes() {
    return [
        'child_care' => 'Çocuk Bakımı',
        'elderly_care' => 'Yaşlı Bakımı',
        'patient_care' => 'Hasta Bakımı',
        'house_help' => 'Ev Yardımcısı',
        'pet_care' => 'Evcil Hayvan Bakımı',
        'companion_care' => 'Refakatçi'
    ];
}

// Bakım türü çevirileri
function getCareTypes() {
    return [
        'live_in' => 'Yatılı',
        'live_out' => 'Gündüzlü',
        'hourly' => 'Saatlik',
        'daily' => 'Günlük',
        'weekly' => 'Haftalık',
        'monthly' => 'Aylık'
    ];
}

// Durum çevirileri
function getApplicationStatuses() {
    return [
        'pending' => 'Beklemede',
        'viewed' => 'Görüntülendi',
        'shortlisted' => 'Kısa Listede',
        'interview' => 'Mülakat',
        'accepted' => 'Kabul Edildi',
        'rejected' => 'Reddedildi',
        'withdrawn' => 'Geri Çekildi',
        'hired' => 'İşe Alındı'
    ];
}

function getJobStatuses() {
    return [
        'draft' => 'Taslak',
        'active' => 'Aktif',
        'paused' => 'Duraklatıldı',
        'closed' => 'Kapatıldı',
        'expired' => 'Süresi Doldu'
    ];
}

// Mesaj göster (session)
function getFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        $type = $_SESSION['flash_type'] ?? 'info';
        unset($_SESSION['flash_message'], $_SESSION['flash_type']);
        return ['message' => $message, 'type' => $type];
    }
    return null;
}





// PHPMailer namespace'lerini dahil et (PHP 7.1 uyumlu)
require_once __DIR__ . '/../vendor/autoload.php';

// Email bildirimi gönder (PHPMailer ile)
function sendEmailNotification($to_email, $subject, $message, $to_name = '') {

    // SMTP ayarları (varsayılan değerlerle)
    $smtp_host = getSetting('smtp_host', 'mail.bonusyalitim.com.tr');
    $smtp_username = getSetting('smtp_username', '<EMAIL>');
    $smtp_password = getSetting('smtp_password', '2M@;4zsdK_=Vg[K');
    $smtp_port = getSetting('smtp_port', '587');
    $smtp_encryption = getSetting('smtp_encryption', 'tls');
    $from_email = getSetting('smtp_from_email', '<EMAIL>');
    $from_name = getSetting('smtp_from_name', 'Bakıcı Platform');

    // Email headers
    $headers = [
        'MIME-Version: 1.0',
        'Content-Type: text/html; charset=UTF-8',
        'From: ' . $from_name . ' <' . $from_email . '>',
        'Reply-To: ' . $from_email,
        'X-Mailer: PHP/' . phpversion()
    ];

    // HTML email template
    $html_message = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>' . escape($subject) . '</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #2c5aa0; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>' . escape($from_name) . '</h1>
            </div>
            <div class="content">
                <h2>' . escape($subject) . '</h2>
                <p>Merhaba ' . escape($to_name) . ',</p>
                <p>' . nl2br(escape($message)) . '</p>
                <p>Platformumuza giriş yapmak için <a href="' . getBaseUrl() . '/auth/login.php">buraya tıklayın</a>.</p>
            </div>
            <div class="footer">
                <p>Bu email otomatik olarak gönderilmiştir. Lütfen yanıtlamayın.</p>
                <p>&copy; ' . date('Y') . ' ' . escape($from_name) . '</p>
            </div>
        </div>
    </body>
    </html>';

    // PHPMailer ile SMTP gönderimi
    try {
        $mail = new \PHPMailer\PHPMailer\PHPMailer(true); // Enable exceptions

        // SMTP ayarları
        $mail->isSMTP();
        $mail->Host = $smtp_host;
        $mail->SMTPAuth = true;
        $mail->Username = $smtp_username;
        $mail->Password = $smtp_password;
        $mail->SMTPSecure = $smtp_encryption;
        $mail->Port = $smtp_port;
        $mail->CharSet = 'UTF-8';

        // Email ayarları
        $mail->setFrom($from_email, $from_name);
        $mail->addAddress($to_email, $to_name);

        // İçerik ayarları
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $html_message;

        // Email gönder (geliştirme ortamı için simülasyon)
        // Gerçek SMTP yerine log'a yaz
        $log_message = "\n=== EMAIL SENT ===\n";
        $log_message .= "Date: " . date('Y-m-d H:i:s') . "\n";
        $log_message .= "To: " . $to_email . " (" . $to_name . ")\n";
        $log_message .= "From: " . $from_email . " (" . $from_name . ")\n";
        $log_message .= "Subject: " . $subject . "\n";
        $log_message .= "SMTP Host: " . $smtp_host . "\n";
        $log_message .= "SMTP User: " . $smtp_username . "\n";
        $log_message .= "Message: " . strip_tags($message) . "\n";
        $log_message .= "==================\n";

        error_log($log_message);

        // Başarılı olarak döndür (geliştirme ortamı)
        error_log("Email simulated successfully to: " . $to_email);
        return true;

    } catch (Exception $e) {
        error_log("Email simulation error: " . $e->getMessage());
        return false;
    }
}

// Base URL getir
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $path = dirname($_SERVER['SCRIPT_NAME']);
    return $protocol . '://' . $host . $path;
}







/**
 * Upload URL sabiti
 */
if (!defined('UPLOAD_URL')) {
    define('UPLOAD_URL', '/bakici-platform/uploads/');
}


?>
