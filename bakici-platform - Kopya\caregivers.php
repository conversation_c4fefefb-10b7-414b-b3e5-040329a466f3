<?php
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/session.php';

// Arama parametreleri
$search_query = trim(safeArray($_GET, 'q', ''));
$job_type = safeArray($_GET, 'job_type', '');
$city = safeArray($_GET, 'city', '');
$care_type = safeArray($_GET, 'care_type', '');
$budget = safeArray($_GET, 'budget', '');
$experience = safeArray($_GET, 'experience', '');
$rating = safeArray($_GET, 'rating', '');
$page = max(1, safeNumber(safeArray($_GET, 'page', 1)));
$per_page = 12;
$offset = ($page - 1) * $per_page;

// SQL sorgusu oluştur
$where_conditions = [];
$params = [];

$sql = "SELECT u.id, u.full_name, u.city, u.district, u.profile_photo, u.created_at,
               cp.experience_years, cp.hourly_rate, cp.daily_rate, cp.monthly_rate, 
               cp.rating, cp.total_reviews, cp.total_jobs, cp.specializations, cp.bio,
               cp.gender, cp.education_level, cp.languages
        FROM users u 
        LEFT JOIN caregiver_profiles cp ON u.id = cp.user_id 
        WHERE u.user_type = 'caregiver' AND u.is_active = 1 AND u.is_verified = 1";

if (!empty($search_query)) {
    $where_conditions[] = "(u.full_name LIKE ? OR cp.specializations LIKE ? OR cp.bio LIKE ?)";
    $search_param = '%' . $search_query . '%';
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

if (!empty($city)) {
    $where_conditions[] = "u.city = ?";
    $params[] = $city;
}

if (!empty($experience)) {
    $where_conditions[] = "cp.experience_years >= ?";
    $params[] = $experience;
}

if (!empty($rating)) {
    $where_conditions[] = "cp.rating >= ?";
    $params[] = $rating;
}

if (!empty($budget)) {
    $budget_parts = explode('-', $budget);
    if (count($budget_parts) == 2) {
        $where_conditions[] = "cp.monthly_rate BETWEEN ? AND ?";
        $params[] = $budget_parts[0];
        $params[] = $budget_parts[1];
    } elseif ($budget === '12000+') {
        $where_conditions[] = "cp.monthly_rate >= 12000";
    }
}

if (!empty($where_conditions)) {
    $sql .= " AND " . implode(" AND ", $where_conditions);
}

$sql .= " ORDER BY cp.rating DESC, cp.total_reviews DESC, u.created_at DESC";

// Toplam kayıt sayısı
$count_sql = str_replace("SELECT u.id, u.full_name, u.city, u.district, u.profile_photo, u.created_at,
               cp.experience_years, cp.hourly_rate, cp.daily_rate, cp.monthly_rate, 
               cp.rating, cp.total_reviews, cp.total_jobs, cp.specializations, cp.bio,
               cp.gender, cp.education_level, cp.languages", "SELECT COUNT(*)", $sql);
$count_sql = str_replace("ORDER BY cp.rating DESC, cp.total_reviews DESC, u.created_at DESC", "", $count_sql);

try {
    $count_stmt = $db->prepare($count_sql);
    $count_stmt->execute($params);
    $total_records = $count_stmt->fetchColumn();
    $total_pages = ceil($total_records / $per_page);

    // Ana sorgu
    $sql .= " LIMIT $per_page OFFSET $offset";
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    $caregivers = $stmt->fetchAll();
} catch (PDOException $e) {
    $caregivers = [];
    $total_records = 0;
    $total_pages = 0;
}

$page_title = 'Bakıcı Ara';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c5aa0;
        }
        
        .search-filters {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .caregiver-card {
            border: none;
            border-radius: 20px;
            overflow: hidden;
            transition: all 0.3s ease;
            height: 100%;
            background: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .caregiver-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }
        
        .rating-stars {
            color: #ffc107;
        }
        
        .navbar {
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="index.php">
                <i class="bi bi-heart-fill me-2"></i><?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="caregivers.php">Bakıcı Ara</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="jobs.php">İş İlanları</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">Hakkımızda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">İletişim</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle me-1"></i><?php echo escape(safeArray($_SESSION, 'full_name', 'Kullanıcı')); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="dashboard.php"><i class="bi bi-speedometer2 me-2"></i>Dashboard</a></li>
                                <li><a class="dropdown-item" href="profile.php"><i class="bi bi-person me-2"></i>Profil</a></li>
                                <li><a class="dropdown-item" href="messages.php"><i class="bi bi-chat-dots me-2"></i>Mesajlar</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="auth/logout.php"><i class="bi bi-box-arrow-right me-2"></i>Çıkış</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="auth/login.php">Giriş Yap</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-primary ms-2" href="auth/register.php">Üye Ol</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Alert Messages -->
        <?php if (isset($_GET['success'])): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="bi bi-check-circle me-2"></i>
                <?php
                $success_messages = [
                    'message_sent' => 'Mesajınız başarıyla gönderildi.',
                    'review_added' => 'Değerlendirmeniz kaydedildi.'
                ];
                echo $success_messages[$_GET['success']] ?? 'İşlem başarıyla tamamlandı.';
                ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_GET['error'])): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="bi bi-exclamation-triangle me-2"></i>
                <?php
                $error_messages = [
                    'caregiver_not_found' => 'Aradığınız bakıcı bulunamadı.',
                    'invalid_caregiver' => 'Geçersiz bakıcı ID\'si.',
                    'permission_denied' => 'Bu işlem için yetkiniz yok.',
                    'database_error' => 'Veritabanı hatası oluştu.',
                    'user_not_found' => 'Kullanıcı bulunamadı.',
                    'invalid_user' => 'Geçersiz kullanıcı.',
                    'cannot_message_self' => 'Kendinize mesaj gönderemezsiniz.',
                    'already_reviewed' => 'Bu bakıcıyı daha önce değerlendirmişsiniz.'
                ];
                echo $error_messages[$_GET['error']] ?? 'Bir hata oluştu.';
                ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Sayfa Başlığı -->
        <div class="row mb-4">
            <div class="col">
                <h2 class="fw-bold">Bakıcı Ara</h2>
                <p class="text-muted">Güvenilir ve deneyimli bakıcıları keşfedin</p>
            </div>
        </div>

        <!-- Arama Filtreleri -->
        <div class="search-filters">
            <form method="GET" action="caregivers.php">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Arama</label>
                        <input type="text" class="form-control" name="q" value="<?php echo escape($search_query); ?>" placeholder="Ad, uzmanlık...">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Şehir</label>
                        <select name="city" class="form-select">
                            <option value="">Tümü</option>
                            <?php foreach (getCities() as $city_option): ?>
                                <option value="<?php echo $city_option; ?>" <?php echo $city === $city_option ? 'selected' : ''; ?>>
                                    <?php echo $city_option; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Deneyim</label>
                        <select name="experience" class="form-select">
                            <option value="">Tümü</option>
                            <option value="1" <?php echo $experience === '1' ? 'selected' : ''; ?>>1+ yıl</option>
                            <option value="3" <?php echo $experience === '3' ? 'selected' : ''; ?>>3+ yıl</option>
                            <option value="5" <?php echo $experience === '5' ? 'selected' : ''; ?>>5+ yıl</option>
                            <option value="10" <?php echo $experience === '10' ? 'selected' : ''; ?>>10+ yıl</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Puan</label>
                        <select name="rating" class="form-select">
                            <option value="">Tümü</option>
                            <option value="4" <?php echo $rating === '4' ? 'selected' : ''; ?>>4+ ⭐</option>
                            <option value="4.5" <?php echo $rating === '4.5' ? 'selected' : ''; ?>>4.5+ ⭐</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Bütçe (Aylık)</label>
                        <select name="budget" class="form-select">
                            <option value="">Tümü</option>
                            <option value="0-3000" <?php echo $budget === '0-3000' ? 'selected' : ''; ?>>3.000 TL'ye kadar</option>
                            <option value="3000-5000" <?php echo $budget === '3000-5000' ? 'selected' : ''; ?>>3.000 - 5.000 TL</option>
                            <option value="5000-8000" <?php echo $budget === '5000-8000' ? 'selected' : ''; ?>>5.000 - 8.000 TL</option>
                            <option value="8000-12000" <?php echo $budget === '8000-12000' ? 'selected' : ''; ?>>8.000 - 12.000 TL</option>
                            <option value="12000+" <?php echo $budget === '12000+' ? 'selected' : ''; ?>>12.000 TL üzeri</option>
                        </select>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Sonuçlar -->
        <div class="row mb-3">
            <div class="col">
                <p class="text-muted">
                    <strong><?php echo number_format($total_records); ?></strong> bakıcı bulundu
                    <?php if (!empty($search_query)): ?>
                        "<strong><?php echo escape($search_query); ?></strong>" için
                    <?php endif; ?>
                </p>
            </div>
        </div>

        <!-- Bakıcı Kartları -->
        <div class="row g-4 mb-5">
            <?php if (!empty($caregivers)): ?>
                <?php foreach ($caregivers as $caregiver): ?>
                <div class="col-md-6 col-lg-4">
                    <div class="caregiver-card">
                        <div class="card-body text-center p-4">
                            <div class="mb-3">
                                <?php if ($caregiver['profile_photo']): ?>
                                    <img src="<?php echo UPLOAD_URL . 'profiles/' . $caregiver['profile_photo']; ?>" 
                                         class="rounded-circle" width="100" height="100" style="object-fit: cover;">
                                <?php else: ?>
                                    <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center" 
                                         style="width: 100px; height: 100px;">
                                        <i class="bi bi-person-fill text-white" style="font-size: 2.5rem;"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <h6 class="card-title mb-2"><?php echo escape($caregiver['full_name']); ?></h6>
                            <p class="text-muted small mb-2">
                                <i class="bi bi-geo-alt me-1"></i><?php echo escape($caregiver['city']); ?>
                                <?php if ($caregiver['district']): ?>
                                    , <?php echo escape($caregiver['district']); ?>
                                <?php endif; ?>
                            </p>
                            <?php if ($caregiver['experience_years']): ?>
                                <p class="text-muted small mb-2">
                                    <i class="bi bi-briefcase me-1"></i><?php echo $caregiver['experience_years']; ?> yıl deneyim
                                </p>
                            <?php endif; ?>
                            <?php if ($caregiver['rating'] > 0): ?>
                            <div class="mb-3">
                                <?php echo getRatingStars($caregiver['rating']); ?>
                                <small class="text-muted ms-1">(<?php echo $caregiver['total_reviews']; ?> değerlendirme)</small>
                            </div>
                            <?php endif; ?>
                            <?php if ($caregiver['specializations']): ?>
                                <p class="text-muted small mb-3"><?php echo escape(substr($caregiver['specializations'], 0, 50)); ?>...</p>
                            <?php endif; ?>
                            <p class="text-primary fw-bold mb-3">
                                <?php if ($caregiver['monthly_rate']): ?>
                                    <?php echo formatMoney($caregiver['monthly_rate']); ?>/ay
                                <?php elseif ($caregiver['daily_rate']): ?>
                                    <?php echo formatMoney($caregiver['daily_rate']); ?>/gün
                                <?php elseif ($caregiver['hourly_rate']): ?>
                                    <?php echo formatMoney($caregiver['hourly_rate']); ?>/saat
                                <?php else: ?>
                                    Fiyat Görüşülür
                                <?php endif; ?>
                            </p>
                            <div class="d-flex gap-2 justify-content-center">
                                <a href="caregiver-profile.php?id=<?php echo $caregiver['id']; ?>" class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-eye me-1"></i>Profili Gör
                                </a>
                                <?php if (isLoggedIn() && $_SESSION['user_type'] === 'family'): ?>
                                    <a href="send-message.php?to=<?php echo $caregiver['id']; ?>" class="btn btn-primary btn-sm">
                                        <i class="bi bi-chat-dots me-1"></i>Mesaj Gönder
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="bi bi-search text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">Bakıcı bulunamadı</h4>
                        <p class="text-muted">Arama kriterlerinizi değiştirerek tekrar deneyin.</p>
                        <a href="caregivers.php" class="btn btn-primary">Tüm Bakıcıları Gör</a>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Sayfalama -->
        <?php if ($total_pages > 1): ?>
        <nav aria-label="Sayfa navigasyonu">
            <ul class="pagination justify-content-center">
                <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">Önceki</a>
                    </li>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
                    </li>
                <?php endfor; ?>
                
                <?php if ($page < $total_pages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">Sonraki</a>
                    </li>
                <?php endif; ?>
            </ul>
        </nav>
        <?php endif; ?>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
