<?php

namespace App\Models;

use CodeIgniter\Model;

class SettingModel extends Model
{
    protected $table = 'settings';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'key', 'value', 'type', 'group', 'description'
    ];

    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    protected $validationRules = [
        'key' => 'required|alpha_dash|max_length[100]|is_unique[settings.key,id,{id}]',
        'type' => 'required|in_list[text,number,boolean,json,file]',
        'group' => 'permit_empty|alpha_dash|max_length[50]'
    ];

    protected $validationMessages = [
        'key' => [
            'required' => 'Ayar anahtarı gereklidir.',
            'alpha_dash' => '<PERSON><PERSON>r sadece harf, rakam, tire ve alt çizgi içerebilir.',
            'is_unique' => 'Bu anahtar zaten kullanılıyor.'
        ],
        'type' => [
            'required' => 'Ayar tipi gereklidir.',
            'in_list' => 'Geçersiz ayar tipi.'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Tüm ayarları key-value array olarak getir
    public function getSettings()
    {
        $settings = $this->findAll();
        $result = [];
        
        foreach ($settings as $setting) {
            $value = $setting['value'];
            
            // Tip dönüşümü
            switch ($setting['type']) {
                case 'number':
                    $value = is_numeric($value) ? (float)$value : 0;
                    break;
                case 'boolean':
                    $value = in_array(strtolower($value), ['1', 'true', 'yes', 'on']);
                    break;
                case 'json':
                    $value = json_decode($value, true) ?: [];
                    break;
            }
            
            $result[$setting['key']] = $value;
        }
        
        return $result;
    }

    // Belirli bir ayarı getir
    public function getSetting($key, $default = null)
    {
        $setting = $this->where('key', $key)->first();
        
        if (!$setting) {
            return $default;
        }
        
        $value = $setting['value'];
        
        // Tip dönüşümü
        switch ($setting['type']) {
            case 'number':
                return is_numeric($value) ? (float)$value : $default;
            case 'boolean':
                return in_array(strtolower($value), ['1', 'true', 'yes', 'on']);
            case 'json':
                return json_decode($value, true) ?: $default;
            default:
                return $value;
        }
    }

    // Ayar güncelle veya oluştur
    public function setSetting($key, $value, $type = 'text', $group = 'general', $description = null)
    {
        // Değeri string'e çevir
        if ($type === 'json') {
            $value = json_encode($value);
        } elseif ($type === 'boolean') {
            $value = $value ? '1' : '0';
        } else {
            $value = (string)$value;
        }
        
        $existing = $this->where('key', $key)->first();
        
        if ($existing) {
            return $this->update($existing['id'], [
                'value' => $value,
                'type' => $type,
                'group' => $group,
                'description' => $description
            ]);
        } else {
            return $this->insert([
                'key' => $key,
                'value' => $value,
                'type' => $type,
                'group' => $group,
                'description' => $description
            ]);
        }
    }

    // Gruba göre ayarları getir
    public function getSettingsByGroup($group)
    {
        return $this->where('group', $group)
                   ->orderBy('key', 'ASC')
                   ->findAll();
    }

    // Ayar sil
    public function deleteSetting($key)
    {
        return $this->where('key', $key)->delete();
    }

    // Birden fazla ayarı toplu güncelle
    public function updateSettings($settings)
    {
        $db = \Config\Database::connect();
        $db->transStart();
        
        foreach ($settings as $key => $data) {
            $value = $data['value'] ?? $data;
            $type = $data['type'] ?? 'text';
            $group = $data['group'] ?? 'general';
            $description = $data['description'] ?? null;
            
            $this->setSetting($key, $value, $type, $group, $description);
        }
        
        $db->transComplete();
        
        return $db->transStatus();
    }
}
