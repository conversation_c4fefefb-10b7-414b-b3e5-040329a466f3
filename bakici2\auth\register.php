<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Zaten giriş yapmışsa dashboard'a yönlendir
if (isLoggedIn()) {
    redirect('../dashboard.php');
}

$error_message = '';
$success_message = '';

// Form gönderildi mi?
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrf_token = safeArray($_POST, 'csrf_token', '');
    
    if (!validateCSRFToken($csrf_token)) {
        $error_message = 'Güvenlik hatası. Lütfen sayfayı yenileyin.';
    } else {
        $user_type = safeArray($_POST, 'user_type', 'family');
        $full_name = trim(safeArray($_POST, 'full_name', ''));
        $email = trim(safeArray($_POST, 'email', ''));
        $password = safeArray($_POST, 'password', '');
        $password_confirm = safeArray($_POST, 'password_confirm', '');
        $phone = trim(safeArray($_POST, 'phone', ''));
        $city = safeArray($_POST, 'city', '');
        $district = trim(safeArray($_POST, 'district', ''));
        $terms_accepted = safeArray($_POST, 'terms_accepted', false);
        
        // Validasyon
        if (empty($full_name)) {
            $error_message = 'Ad soyad gereklidir.';
        } elseif (!validateLength($full_name, 2, 100)) {
            $error_message = 'Ad soyad 2-100 karakter arasında olmalıdır.';
        } elseif (empty($email)) {
            $error_message = 'E-posta adresi gereklidir.';
        } elseif (!validateEmail($email)) {
            $error_message = 'Geçerli bir e-posta adresi girin.';
        } elseif (empty($password)) {
            $error_message = 'Şifre gereklidir.';
        } elseif (!validatePassword($password)) {
            $error_message = 'Şifre en az 6 karakter olmalıdır.';
        } elseif ($password !== $password_confirm) {
            $error_message = 'Şifreler eşleşmiyor.';
        } elseif (!empty($phone) && !validatePhone($phone)) {
            $error_message = 'Geçerli bir telefon numarası girin.';
        } elseif (!in_array($user_type, ['family', 'caregiver'])) {
            $error_message = 'Geçersiz kullanıcı tipi.';
        } elseif (!$terms_accepted) {
            $error_message = 'Kullanım şartlarını kabul etmelisiniz.';
        } else {
            try {
                // E-posta zaten kayıtlı mı?
                $check_sql = "SELECT id FROM users WHERE email = ?";
                $check_stmt = $db->prepare($check_sql);
                $check_stmt->execute([$email]);
                
                if ($check_stmt->fetch()) {
                    $error_message = 'Bu e-posta adresi zaten kayıtlı.';
                } else {
                    // Kullanıcıyı kaydet
                    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                    $email_verification_token = bin2hex(random_bytes(32));
                    
                    $sql = "INSERT INTO users (user_type, email, password, full_name, phone, city, district, email_verification_token, is_verified, is_active) 
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, 0, 1)";
                    $stmt = $db->prepare($sql);
                    $stmt->execute([$user_type, $email, $hashed_password, $full_name, $phone, $city, $district, $email_verification_token]);
                    
                    $user_id = $db->lastInsertId();
                    
                    // Kullanıcı tipine göre profil oluştur
                    if ($user_type === 'family') {
                        $profile_sql = "INSERT INTO family_profiles (user_id) VALUES (?)";
                        $profile_stmt = $db->prepare($profile_sql);
                        $profile_stmt->execute([$user_id]);
                    } elseif ($user_type === 'caregiver') {
                        $profile_sql = "INSERT INTO caregiver_profiles (user_id) VALUES (?)";
                        $profile_stmt = $db->prepare($profile_sql);
                        $profile_stmt->execute([$user_id]);
                    }
                    
                    // Aktivite kaydı
                    logActivity($user_id, 'register', 'Kullanıcı kayıt oldu', 'users', $user_id);
                    
                    // Hoş geldin bildirimi
                    $welcome_title = 'Hoş Geldiniz!';
                    $welcome_message = 'Bakıcı Platform\'a hoş geldiniz! Profilinizi tamamlayarak daha iyi hizmet alabilirsiniz.';
                    sendNotification($user_id, 'welcome', $welcome_title, $welcome_message, 'profile.php');
                    
                    // E-posta doğrulama gerekli mi?
                    $email_verification_required = getSetting('email_verification_required', '1');
                    
                    if ($email_verification_required === '1') {
                        // E-posta doğrulama gönder (gerçek uygulamada)
                        // sendEmail($email, 'E-posta Doğrulama', $verification_email_content);
                        
                        $success_message = 'Kayıt işleminiz başarıyla tamamlandı. E-posta adresinize gönderilen doğrulama linkine tıklayarak hesabınızı aktifleştirin.';
                    } else {
                        // E-posta doğrulama gerekli değilse direkt aktif et
                        $update_sql = "UPDATE users SET is_verified = 1, email_verified_at = NOW() WHERE id = ?";
                        $update_stmt = $db->prepare($update_sql);
                        $update_stmt->execute([$user_id]);
                        
                        // Otomatik giriş yap
                        $_SESSION['user_id'] = $user_id;
                        $_SESSION['email'] = $email;
                        $_SESSION['full_name'] = $full_name;
                        $_SESSION['user_type'] = $user_type;
                        $_SESSION['is_verified'] = 1;
                        
                        setMessage('Kayıt işleminiz başarıyla tamamlandı. Hoş geldiniz!', 'success');
                        redirect('../dashboard.php');
                    }
                }
            } catch (PDOException $e) {
                $error_message = 'Kayıt işlemi sırasında bir hata oluştu. Lütfen tekrar deneyin.';
            }
        }
    }
}

// URL'den kullanıcı tipi al
$default_user_type = safeArray($_GET, 'type', 'family');
if (!in_array($default_user_type, ['family', 'caregiver'])) {
    $default_user_type = 'family';
}

$page_title = 'Üye Ol';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c5aa0;
            --secondary-color: #f8f9fa;
        }
        
        body {
            background: linear-gradient(135deg, var(--primary-color) 0%, #1e3d72 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            padding: 20px 0;
        }
        
        .auth-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 600px;
            width: 100%;
        }
        
        .auth-header {
            background: var(--primary-color);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .auth-body {
            padding: 2rem;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            border-radius: 25px;
            padding: 12px 30px;
        }
        
        .btn-primary:hover {
            background-color: #1e3d72;
            border-color: #1e3d72;
            transform: translateY(-2px);
        }
        
        .input-group-text {
            background-color: var(--secondary-color);
            border-color: #dee2e6;
        }
        
        .user-type-card {
            border: 2px solid #dee2e6;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        
        .user-type-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .user-type-card.active {
            border-color: var(--primary-color);
            background: rgba(44, 90, 160, 0.05);
        }
        
        .user-type-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 2rem;
            color: white;
        }
        
        .password-strength {
            height: 5px;
            border-radius: 3px;
            margin-top: 5px;
            transition: all 0.3s ease;
        }
        
        .strength-weak { background: #dc3545; }
        .strength-medium { background: #ffc107; }
        .strength-strong { background: #28a745; }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10 col-lg-8">
                <div class="auth-card mx-auto">
                    <div class="auth-header">
                        <h3 class="mb-0">
                            <i class="bi bi-heart-fill me-2"></i><?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?>
                        </h3>
                        <p class="mb-0 mt-2">Yeni hesap oluşturun</p>
                    </div>
                    
                    <div class="auth-body">
                        <?php if ($error_message): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="bi bi-exclamation-triangle me-2"></i><?php echo escape($error_message); ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($success_message): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="bi bi-check-circle me-2"></i><?php echo escape($success_message); ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" id="registerForm">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            
                            <!-- Kullanıcı Tipi Seçimi -->
                            <div class="mb-4">
                                <label class="form-label fw-bold">Hesap Tipinizi Seçin</label>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <div class="user-type-card <?php echo $default_user_type === 'family' ? 'active' : ''; ?>" 
                                             onclick="selectUserType('family')">
                                            <div class="user-type-icon bg-primary">
                                                <i class="bi bi-house-heart"></i>
                                            </div>
                                            <h6 class="mb-2">Aile</h6>
                                            <small class="text-muted">Bakıcı arıyorum</small>
                                            <input type="radio" name="user_type" value="family" 
                                                   <?php echo $default_user_type === 'family' ? 'checked' : ''; ?> 
                                                   class="d-none" id="type_family">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="user-type-card <?php echo $default_user_type === 'caregiver' ? 'active' : ''; ?>" 
                                             onclick="selectUserType('caregiver')">
                                            <div class="user-type-icon bg-success">
                                                <i class="bi bi-person-heart"></i>
                                            </div>
                                            <h6 class="mb-2">Bakıcı</h6>
                                            <small class="text-muted">İş arıyorum</small>
                                            <input type="radio" name="user_type" value="caregiver" 
                                                   <?php echo $default_user_type === 'caregiver' ? 'checked' : ''; ?> 
                                                   class="d-none" id="type_caregiver">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Kişisel Bilgiler -->
                            <div class="row g-3 mb-3">
                                <div class="col-md-6">
                                    <label for="full_name" class="form-label">Ad Soyad *</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-person"></i>
                                        </span>
                                        <input type="text" class="form-control" id="full_name" name="full_name"
                                               value="<?php echo escape(safeArray($_POST, 'full_name', '')); ?>"
                                               placeholder="Adınız ve soyadınız" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="email" class="form-label">E-posta Adresi *</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-envelope"></i>
                                        </span>
                                        <input type="email" class="form-control" id="email" name="email"
                                               value="<?php echo escape(safeArray($_POST, 'email', '')); ?>"
                                               placeholder="<EMAIL>" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row g-3 mb-3">
                                <div class="col-md-6">
                                    <label for="password" class="form-label">Şifre *</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-lock"></i>
                                        </span>
                                        <input type="password" class="form-control" id="password" name="password"
                                               placeholder="En az 6 karakter" required>
                                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                    </div>
                                    <div class="password-strength" id="passwordStrength"></div>
                                    <small class="text-muted">En az 6 karakter, büyük-küçük harf ve rakam içermeli</small>
                                </div>
                                <div class="col-md-6">
                                    <label for="password_confirm" class="form-label">Şifre Tekrar *</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-lock-fill"></i>
                                        </span>
                                        <input type="password" class="form-control" id="password_confirm" name="password_confirm"
                                               placeholder="Şifrenizi tekrar girin" required>
                                        <button class="btn btn-outline-secondary" type="button" id="togglePasswordConfirm">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                    </div>
                                    <div id="passwordMatch" class="mt-1"></div>
                                </div>
                            </div>

                            <div class="row g-3 mb-3">
                                <div class="col-md-6">
                                    <label for="phone" class="form-label">Telefon</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-telephone"></i>
                                        </span>
                                        <input type="tel" class="form-control" id="phone" name="phone"
                                               value="<?php echo escape(safeArray($_POST, 'phone', '')); ?>"
                                               placeholder="0532 123 45 67">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="city" class="form-label">Şehir</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-geo-alt"></i>
                                        </span>
                                        <select class="form-select" id="city" name="city">
                                            <option value="">Şehir seçin</option>
                                            <?php foreach (getCities() as $city): ?>
                                                <option value="<?php echo $city; ?>"
                                                        <?php echo safeArray($_POST, 'city', '') === $city ? 'selected' : ''; ?>>
                                                    <?php echo $city; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="district" class="form-label">İlçe</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-pin-map"></i>
                                    </span>
                                    <input type="text" class="form-control" id="district" name="district"
                                           value="<?php echo escape(safeArray($_POST, 'district', '')); ?>"
                                           placeholder="İlçe adı">
                                </div>
                            </div>

                            <!-- Şartlar ve Koşullar -->
                            <div class="mb-4">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="terms_accepted" name="terms_accepted" value="1" required>
                                    <label class="form-check-label" for="terms_accepted">
                                        <a href="../terms.php" target="_blank" class="text-decoration-none">Kullanım Şartları</a> ve
                                        <a href="../privacy.php" target="_blank" class="text-decoration-none">Gizlilik Politikası</a>'nı
                                        okudum ve kabul ediyorum. *
                                    </label>
                                </div>
                                <div class="form-check mt-2">
                                    <input type="checkbox" class="form-check-input" id="marketing_emails" name="marketing_emails" value="1">
                                    <label class="form-check-label" for="marketing_emails">
                                        Kampanya ve duyuru e-postalarını almak istiyorum.
                                    </label>
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-person-plus me-2"></i>Hesap Oluştur
                                </button>
                            </div>
                        </form>

                        <div class="text-center mt-4">
                            <p class="mb-0">
                                Zaten hesabınız var mı?
                                <a href="login.php" class="text-decoration-none fw-bold">
                                    Giriş yapın
                                </a>
                            </p>
                        </div>

                        <div class="text-center mt-3">
                            <a href="../index.php" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-2"></i>Ana Sayfaya Dön
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Kullanıcı tipi seçimi
        function selectUserType(type) {
            // Tüm kartları pasif yap
            document.querySelectorAll('.user-type-card').forEach(card => {
                card.classList.remove('active');
            });

            // Seçilen kartı aktif yap
            event.currentTarget.classList.add('active');

            // Radio button'ı seç
            document.getElementById('type_' + type).checked = true;
        }

        // Şifre göster/gizle
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.className = 'bi bi-eye-slash';
            } else {
                passwordInput.type = 'password';
                icon.className = 'bi bi-eye';
            }
        });

        document.getElementById('togglePasswordConfirm').addEventListener('click', function() {
            const passwordInput = document.getElementById('password_confirm');
            const icon = this.querySelector('i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.className = 'bi bi-eye-slash';
            } else {
                passwordInput.type = 'password';
                icon.className = 'bi bi-eye';
            }
        });

        // Şifre gücü kontrolü
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strengthBar = document.getElementById('passwordStrength');

            let strength = 0;
            if (password.length >= 6) strength++;
            if (password.match(/[a-z]/)) strength++;
            if (password.match(/[A-Z]/)) strength++;
            if (password.match(/[0-9]/)) strength++;
            if (password.match(/[^a-zA-Z0-9]/)) strength++;

            strengthBar.className = 'password-strength';
            if (strength <= 2) {
                strengthBar.classList.add('strength-weak');
            } else if (strength <= 3) {
                strengthBar.classList.add('strength-medium');
            } else {
                strengthBar.classList.add('strength-strong');
            }
        });

        // Şifre eşleşme kontrolü
        function checkPasswordMatch() {
            const password = document.getElementById('password').value;
            const passwordConfirm = document.getElementById('password_confirm').value;
            const matchDiv = document.getElementById('passwordMatch');

            if (passwordConfirm.length > 0) {
                if (password === passwordConfirm) {
                    matchDiv.innerHTML = '<small class="text-success"><i class="bi bi-check-circle me-1"></i>Şifreler eşleşiyor</small>';
                } else {
                    matchDiv.innerHTML = '<small class="text-danger"><i class="bi bi-x-circle me-1"></i>Şifreler eşleşmiyor</small>';
                }
            } else {
                matchDiv.innerHTML = '';
            }
        }

        document.getElementById('password').addEventListener('input', checkPasswordMatch);
        document.getElementById('password_confirm').addEventListener('input', checkPasswordMatch);

        // Form validasyonu
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            const fullName = document.getElementById('full_name').value.trim();
            const email = document.getElementById('email').value.trim();
            const password = document.getElementById('password').value;
            const passwordConfirm = document.getElementById('password_confirm').value;
            const termsAccepted = document.getElementById('terms_accepted').checked;

            if (!fullName) {
                e.preventDefault();
                showAlert('Ad soyad gereklidir.', 'danger');
                return;
            }

            if (!email) {
                e.preventDefault();
                showAlert('E-posta adresi gereklidir.', 'danger');
                return;
            }

            // E-posta formatı kontrolü
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                e.preventDefault();
                showAlert('Geçerli bir e-posta adresi girin.', 'danger');
                return;
            }

            if (!password) {
                e.preventDefault();
                showAlert('Şifre gereklidir.', 'danger');
                return;
            }

            if (password.length < 6) {
                e.preventDefault();
                showAlert('Şifre en az 6 karakter olmalıdır.', 'danger');
                return;
            }

            if (password !== passwordConfirm) {
                e.preventDefault();
                showAlert('Şifreler eşleşmiyor.', 'danger');
                return;
            }

            if (!termsAccepted) {
                e.preventDefault();
                showAlert('Kullanım şartlarını kabul etmelisiniz.', 'danger');
                return;
            }

            // Loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Hesap oluşturuluyor...';
            submitBtn.disabled = true;

            // Form gönderilirken hata olursa butonu eski haline getir
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 10000);
        });

        // Alert gösterme fonksiyonu
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                <i class="bi bi-exclamation-triangle me-2"></i>${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            const form = document.getElementById('registerForm');
            form.insertBefore(alertDiv, form.firstChild);

            // 5 saniye sonra otomatik kapat
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        // Sayfa yüklendiğinde ad alanına odaklan
        window.addEventListener('load', function() {
            document.getElementById('full_name').focus();
        });
    </script>
</body>
</html>
