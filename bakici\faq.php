<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$page_title = '<PERSON><PERSON>k Sorulan Sorular';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c5aa0;
        }

        .faq-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .faq-header {
            background: linear-gradient(135deg, var(--primary-color), #1e3d72);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 30px;
        }

        .category-card {
            border: none;
            border-radius: 15px;
            padding: 25px;
            height: 100%;
            transition: transform 0.3s ease;
            background: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .category-card:hover {
            transform: translateY(-5px);
        }

        .category-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 20px;
        }

        .accordion-button:not(.collapsed) {
            background-color: var(--primary-color);
            color: white;
        }

        .accordion-button:focus {
            box-shadow: 0 0 0 0.25rem rgba(44, 90, 160, 0.25);
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="index.php">
                <i class="bi bi-heart-fill me-2"></i><?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="caregivers.php">Bakıcı Ara</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="jobs.php">İş İlanları</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">Hakkımızda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">İletişim</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle me-1"></i><?php echo escape(safeArray($_SESSION, 'full_name', 'Kullanıcı')); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="dashboard.php"><i class="bi bi-speedometer2 me-2"></i>Dashboard</a></li>
                                <li><a class="dropdown-item" href="profile.php"><i class="bi bi-person me-2"></i>Profil</a></li>
                                <li><a class="dropdown-item" href="messages.php"><i class="bi bi-chat-dots me-2"></i>Mesajlar</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="auth/logout.php"><i class="bi bi-box-arrow-right me-2"></i>Çıkış</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="auth/login.php">Giriş Yap</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-primary ms-2" href="auth/register.php">Üye Ol</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Header -->
    <div class="faq-card">
        <div class="faq-header text-center">
            <h1 class="fw-bold mb-3">Sık Sorulan Sorular</h1>
            <p class="lead mb-0">Merak ettiğiniz her şeyin cevabı burada</p>
        </div>
    </div>

    <div class="container mt-4 mb-5">
        <!-- Kategori Kartları -->
        <div class="row g-4 mb-5">
            <div class="col-md-6 col-lg-3">
                <div class="category-card text-center">
                    <div class="category-icon bg-primary mx-auto">
                        <i class="bi bi-person-plus"></i>
                    </div>
                    <h5 class="fw-bold mb-3">Üyelik & Kayıt</h5>
                    <p class="text-muted small">Hesap oluşturma, profil düzenleme ve üyelik işlemleri</p>
                    <a href="#uyerik" class="btn btn-outline-primary btn-sm">Sorulara Git</a>
                </div>
            </div>

            <div class="col-md-6 col-lg-3">
                <div class="category-card text-center">
                    <div class="category-icon bg-success mx-auto">
                        <i class="bi bi-briefcase"></i>
                    </div>
                    <h5 class="fw-bold mb-3">İş İlanları</h5>
                    <p class="text-muted small">İlan verme, başvuru yapma ve iş bulma süreci</p>
                    <a href="#isilan" class="btn btn-outline-success btn-sm">Sorulara Git</a>
                </div>
            </div>

            <div class="col-md-6 col-lg-3">
                <div class="category-card text-center">
                    <div class="category-icon bg-warning mx-auto">
                        <i class="bi bi-credit-card"></i>
                    </div>
                    <h5 class="fw-bold mb-3">Ödeme & Paketler</h5>
                    <p class="text-muted small">Paket satın alma, ödeme yöntemleri ve faturalama</p>
                    <a href="#odeme" class="btn btn-outline-warning btn-sm">Sorulara Git</a>
                </div>
            </div>

            <div class="col-md-6 col-lg-3">
                <div class="category-card text-center">
                    <div class="category-icon bg-info mx-auto">
                        <i class="bi bi-shield-check"></i>
                    </div>
                    <h5 class="fw-bold mb-3">Güvenlik</h5>
                    <p class="text-muted small">Güvenlik önlemleri, doğrulama ve gizlilik</p>
                    <a href="#guvenlik" class="btn btn-outline-info btn-sm">Sorulara Git</a>
                </div>
            </div>
        </div>

        <!-- FAQ Accordion -->
        <div class="row">
            <div class="col-lg-10 mx-auto">
                <!-- Üyelik & Kayıt -->
                <h3 class="fw-bold mb-4" id="uyerik">
                    <i class="bi bi-person-plus text-primary me-2"></i>Üyelik & Kayıt
                </h3>

                <div class="accordion mb-5" id="membershipAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#membership1">
                                Nasıl üye olabilirim?
                            </button>
                        </h2>
                        <div id="membership1" class="accordion-collapse collapse show" data-bs-parent="#membershipAccordion">
                            <div class="accordion-body">
                                <p>Üye olmak çok kolay:</p>
                                <ol>
                                    <li>"Üye Ol" butonuna tıklayın</li>
                                    <li>Aile veya bakıcı olarak kayıt türünüzü seçin</li>
                                    <li>Gerekli bilgileri doldurun (ad, email, telefon)</li>
                                    <li>Email adresinizi doğrulayın</li>
                                    <li>Profilinizi tamamlayın ve hemen kullanmaya başlayın</li>
                                </ol>
                                <p class="text-muted">Üyelik tamamen ücretsizdir. Sadece premium özellikler için ücret alınır.</p>
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#membership2">
                                Profilimi nasıl düzenleyebilirim?
                            </button>
                        </h2>
                        <div id="membership2" class="accordion-collapse collapse" data-bs-parent="#membershipAccordion">
                            <div class="accordion-body">
                                <p>Profil düzenleme adımları:</p>
                                <ul>
                                    <li>Dashboard'a giriş yapın</li>
                                    <li>"Profil" sekmesine tıklayın</li>
                                    <li>Kişisel bilgilerinizi güncelleyin</li>
                                    <li>Fotoğraf ekleyin veya değiştirin</li>
                                    <li>Deneyim ve becerilerinizi ekleyin</li>
                                    <li>"Kaydet" butonuna tıklayın</li>
                                </ul>
                                <p class="text-muted">Eksiksiz profil daha fazla görünürlük sağlar.</p>
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#membership3">
                                Şifremi unuttum, ne yapmalıyım?
                            </button>
                        </h2>
                        <div id="membership3" class="accordion-collapse collapse" data-bs-parent="#membershipAccordion">
                            <div class="accordion-body">
                                <p>Şifre sıfırlama işlemi:</p>
                                <ol>
                                    <li>Giriş sayfasında "Şifremi Unuttum" linkine tıklayın</li>
                                    <li>Email adresinizi girin</li>
                                    <li>Email'inize gelen linke tıklayın</li>
                                    <li>Yeni şifrenizi belirleyin</li>
                                    <li>Yeni şifrenizle giriş yapın</li>
                                </ol>
                                <p class="text-muted">Email gelmezse spam klasörünü kontrol edin.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- İş İlanları -->
                <h3 class="fw-bold mb-4" id="isilan">
                    <i class="bi bi-briefcase text-success me-2"></i>İş İlanları
                </h3>

                <div class="accordion mb-5" id="jobsAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#jobs1">
                                İş ilanı nasıl veririm?
                            </button>
                        </h2>
                        <div id="jobs1" class="accordion-collapse collapse" data-bs-parent="#jobsAccordion">
                            <div class="accordion-body">
                                <p>İş ilanı verme süreci:</p>
                                <ol>
                                    <li>Dashboard'dan "İlan Ver" butonuna tıklayın</li>
                                    <li>İş türünü seçin (çocuk, yaşlı, hasta bakımı vb.)</li>
                                    <li>Çalışma detaylarını belirtin (saat, gün, süre)</li>
                                    <li>Ücret bilgilerini girin</li>
                                    <li>İş tanımını detaylı yazın</li>
                                    <li>İlanınızı yayınlayın</li>
                                </ol>
                                <p class="text-muted">İlan verme premium üyelik gerektirir.</p>
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#jobs2">
                                İş başvurusu nasıl yaparım?
                            </button>
                        </h2>
                        <div id="jobs2" class="accordion-collapse collapse" data-bs-parent="#jobsAccordion">
                            <div class="accordion-body">
                                <p>Başvuru yapma adımları:</p>
                                <ul>
                                    <li>"İş İlanları" sayfasından uygun ilanı bulun</li>
                                    <li>İlan detaylarını inceleyin</li>
                                    <li>"Başvur" butonuna tıklayın</li>
                                    <li>Motivasyon mektubunuzu yazın</li>
                                    <li>Başvurunuzu gönderin</li>
                                    <li>İşveren yanıtını bekleyin</li>
                                </ul>
                                <p class="text-muted">Başvuru durumunuzu "Başvurularım" sayfasından takip edebilirsiniz.</p>
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#jobs3">
                                İlanımı nasıl düzenleyebilirim?
                            </button>
                        </h2>
                        <div id="jobs3" class="accordion-collapse collapse" data-bs-parent="#jobsAccordion">
                            <div class="accordion-body">
                                <p>İlan düzenleme işlemi:</p>
                                <ol>
                                    <li>"İlanlarım" sayfasına gidin</li>
                                    <li>Düzenlemek istediğiniz ilanı bulun</li>
                                    <li>"Düzenle" butonuna tıklayın</li>
                                    <li>Gerekli değişiklikleri yapın</li>
                                    <li>"Güncelle" butonuna tıklayın</li>
                                </ol>
                                <p class="text-muted">İlanınızı istediğiniz zaman duraklatabilir veya silebilirsiniz.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Ödeme & Paketler -->
                <h3 class="fw-bold mb-4" id="odeme">
                    <i class="bi bi-credit-card text-warning me-2"></i>Ödeme & Paketler
                </h3>

                <div class="accordion mb-5" id="paymentAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#payment1">
                                Hangi paketler mevcut?
                            </button>
                        </h2>
                        <div id="payment1" class="accordion-collapse collapse" data-bs-parent="#paymentAccordion">
                            <div class="accordion-body">
                                <p>Mevcut paket seçenekleri:</p>
                                <ul>
                                    <li><strong>Temel Paket (₺29/ay):</strong> 5 iş ilanı, email desteği</li>
                                    <li><strong>Standart Paket (₺59/ay):</strong> 15 iş ilanı, öncelikli destek, mesajlaşma</li>
                                    <li><strong>Premium Paket (₺99/ay):</strong> Sınırsız ilan, 7/24 destek, analitik</li>
                                </ul>
                                <p class="text-muted">Paket detaylarını "Paketler" sayfasından inceleyebilirsiniz.</p>
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#payment2">
                                Ödeme nasıl yapabilirim?
                            </button>
                        </h2>
                        <div id="payment2" class="accordion-collapse collapse" data-bs-parent="#paymentAccordion">
                            <div class="accordion-body">
                                <p>Kabul edilen ödeme yöntemleri:</p>
                                <ul>
                                    <li><strong>Kredi Kartı:</strong> Visa, MasterCard, American Express</li>
                                    <li><strong>Banka Kartı:</strong> Tüm Türk bankaları</li>
                                    <li><strong>Havale/EFT:</strong> Banka transferi</li>
                                    <li><strong>PayTR:</strong> Güvenli ödeme altyapısı</li>
                                </ul>
                                <p class="text-muted">Tüm ödemeler SSL şifreleme ile korunur.</p>
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#payment3">
                                Paketimi iptal edebilir miyim?
                            </button>
                        </h2>
                        <div id="payment3" class="accordion-collapse collapse" data-bs-parent="#paymentAccordion">
                            <div class="accordion-body">
                                <p>Paket iptali ve iade koşulları:</p>
                                <ul>
                                    <li>İlk 7 gün içinde tam iade</li>
                                    <li>İstediğiniz zaman iptal edebilirsiniz</li>
                                    <li>İptal sonrası mevcut süre sonuna kadar kullanım</li>
                                    <li>Otomatik yenileme durdurulur</li>
                                </ul>
                                <p class="text-muted">İptal işlemi için müşteri hizmetleri ile iletişime geçin.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Güvenlik -->
                <h3 class="fw-bold mb-4" id="guvenlik">
                    <i class="bi bi-shield-check text-info me-2"></i>Güvenlik
                </h3>

                <div class="accordion mb-5" id="securityAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#security1">
                                Bakıcılar nasıl doğrulanıyor?
                            </button>
                        </h2>
                        <div id="security1" class="accordion-collapse collapse" data-bs-parent="#securityAccordion">
                            <div class="accordion-body">
                                <p>Bakıcı doğrulama süreci:</p>
                                <ol>
                                    <li><strong>Kimlik Doğrulama:</strong> TC kimlik numarası kontrolü</li>
                                    <li><strong>Referans Kontrolü:</strong> Önceki işverenlerden referans</li>
                                    <li><strong>Adli Sicil:</strong> Sabıka kaydı sorgulaması</li>
                                    <li><strong>Sağlık Raporu:</strong> Gerekli durumlarda sağlık kontrolü</li>
                                    <li><strong>Eğitim Belgeleri:</strong> Sertifika ve diploma kontrolü</li>
                                </ol>
                                <p class="text-muted">Doğrulanmış bakıcılar profilde özel işaretle gösterilir.</p>
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#security2">
                                Kişisel bilgilerim güvende mi?
                            </button>
                        </h2>
                        <div id="security2" class="accordion-collapse collapse" data-bs-parent="#securityAccordion">
                            <div class="accordion-body">
                                <p>Veri güvenliği önlemlerimiz:</p>
                                <ul>
                                    <li><strong>SSL Şifreleme:</strong> Tüm veri transferleri şifrelenir</li>
                                    <li><strong>KVKK Uyumlu:</strong> Kişisel verilerin korunması kanununa uygun</li>
                                    <li><strong>Güvenli Sunucular:</strong> Veriler güvenli veri merkezlerinde saklanır</li>
                                    <li><strong>Erişim Kontrolü:</strong> Sınırlı personel erişimi</li>
                                    <li><strong>Düzenli Yedekleme:</strong> Veri kaybına karşı koruma</li>
                                </ul>
                                <p class="text-muted">Gizlilik politikamızı detaylı inceleyebilirsiniz.</p>
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#security3">
                                Sorun yaşarsam ne yapmalıyım?
                            </button>
                        </h2>
                        <div id="security3" class="accordion-collapse collapse" data-bs-parent="#securityAccordion">
                            <div class="accordion-body">
                                <p>Sorun bildirme yolları:</p>
                                <ul>
                                    <li><strong>Canlı Destek:</strong> Platform üzerinden anlık mesajlaşma</li>
                                    <li><strong>Email:</strong> <EMAIL></li>
                                    <li><strong>Telefon:</strong> +90 ************ (09:00-18:00)</li>
                                    <li><strong>Acil Durum:</strong> 7/24 acil destek hattı</li>
                                    <li><strong>Şikayet Formu:</strong> Resmi şikayet süreci</li>
                                </ul>
                                <p class="text-muted">Tüm şikayetler 24 saat içinde değerlendirilir.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- İletişim Kartı -->
                <div class="text-center mt-5">
                    <div class="category-card">
                        <div class="category-icon bg-primary mx-auto">
                            <i class="bi bi-headset"></i>
                        </div>
                        <h5 class="fw-bold mb-3">Hala Sorunuz Var mı?</h5>
                        <p class="text-muted mb-4">Aradığınız cevabı bulamadıysanız bizimle iletişime geçin</p>
                        <div class="d-flex gap-3 justify-content-center flex-wrap">
                            <a href="contact.php" class="btn btn-primary">
                                <i class="bi bi-envelope me-2"></i>İletişim
                            </a>
                            <a href="help.php" class="btn btn-outline-primary">
                                <i class="bi bi-question-circle me-2"></i>Yardım Merkezi
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Smooth scroll for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Auto-expand accordion based on URL hash
        window.addEventListener('load', function() {
            const hash = window.location.hash;
            if (hash) {
                const target = document.querySelector(hash);
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }
        });
    </script>
</body>
</html>
