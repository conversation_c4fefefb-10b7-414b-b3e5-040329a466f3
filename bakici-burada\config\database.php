<?php
// Veritabanı sabitleri
define('DB_HOST', 'localhost');
define('DB_NAME', 'bakici_burada');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

class Database {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $charset = DB_CHARSET;
    public $conn;

    public function getConnection() {
        $this->conn = null;
        try {
            // Önce veritabanı olmadan bağlan
            $dsn = "mysql:host=" . $this->host . ";charset=" . $this->charset;
            $this->conn = new PDO($dsn, $this->username, $this->password);
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

            // Veritabanını oluştur (yoksa)
            $this->conn->exec("CREATE DATABASE IF NOT EXISTS `" . $this->db_name . "` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");

            // Şimdi veritabanına bağlan
            $this->conn->exec("USE `" . $this->db_name . "`");

        } catch(PDOException $exception) {
            echo "Bağlantı hatası: " . $exception->getMessage();
            return null;
        }
        return $this->conn;
    }
}

// Global veritabanı bağlantısı
$database = new Database();
$db = $database->getConnection();

// Veritabanı tablolarını oluştur
try {
    // Kullanıcılar tablosu
    $db->exec("CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_type ENUM('family', 'caregiver', 'admin') NOT NULL DEFAULT 'family',
        email VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        full_name VARCHAR(255) NOT NULL,
        phone VARCHAR(20),
        city VARCHAR(100),
        district VARCHAR(100),
        address TEXT,
        profile_photo VARCHAR(255),
        is_verified BOOLEAN DEFAULT FALSE,
        is_active BOOLEAN DEFAULT TRUE,
        email_verified_at DATETIME NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Bakıcı profilleri tablosu
    $db->exec("CREATE TABLE IF NOT EXISTS caregiver_profiles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        birth_date DATE,
        gender ENUM('male', 'female', 'other') DEFAULT 'female',
        experience_years INT DEFAULT 0,
        education_level VARCHAR(100),
        languages TEXT,
        services TEXT,
        hourly_rate DECIMAL(10,2),
        daily_rate DECIMAL(10,2),
        monthly_rate DECIMAL(10,2),
        availability TEXT,
        bio TEXT,
        skills TEXT,
        certificates TEXT,
        reference_info TEXT,
        background_check BOOLEAN DEFAULT FALSE,
        background_check_file VARCHAR(255),
        cv_file VARCHAR(255),
        rating DECIMAL(3,2) DEFAULT 0.00,
        total_reviews INT DEFAULT 0,
        total_jobs INT DEFAULT 0,
        is_premium BOOLEAN DEFAULT FALSE,
        premium_until DATE NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // İlanlar tablosu
    $db->exec("CREATE TABLE IF NOT EXISTS job_listings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT NOT NULL,
        job_type ENUM('child_care', 'elderly_care', 'patient_care', 'house_help', 'pet_care') NOT NULL,
        care_type ENUM('live_in', 'live_out', 'hourly', 'daily', 'weekly') NOT NULL,
        location_city VARCHAR(100) NOT NULL,
        location_district VARCHAR(100),
        location_address TEXT,
        budget_min DECIMAL(10,2),
        budget_max DECIMAL(10,2),
        budget_type ENUM('hourly', 'daily', 'weekly', 'monthly') DEFAULT 'monthly',
        start_date DATE,
        requirements TEXT,
        preferred_gender ENUM('male', 'female', 'no_preference') DEFAULT 'no_preference',
        preferred_age_min INT,
        preferred_age_max INT,
        required_experience INT DEFAULT 0,
        required_languages TEXT,
        contact_phone VARCHAR(20),
        contact_email VARCHAR(255),
        is_urgent BOOLEAN DEFAULT FALSE,
        is_featured BOOLEAN DEFAULT FALSE,
        is_premium BOOLEAN DEFAULT FALSE,
        status ENUM('active', 'paused', 'closed', 'expired') DEFAULT 'active',
        expires_at DATETIME NULL,
        views_count INT DEFAULT 0,
        applications_count INT DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Başvurular tablosu
    $db->exec("CREATE TABLE IF NOT EXISTS job_applications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        job_id INT NOT NULL,
        caregiver_id INT NOT NULL,
        message TEXT,
        proposed_rate DECIMAL(10,2),
        status ENUM('pending', 'accepted', 'rejected', 'withdrawn') DEFAULT 'pending',
        employer_notes TEXT,
        applied_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (job_id) REFERENCES job_listings(id) ON DELETE CASCADE,
        FOREIGN KEY (caregiver_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_application (job_id, caregiver_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Mesajlaşma tablosu
    $db->exec("CREATE TABLE IF NOT EXISTS messages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        sender_id INT NOT NULL,
        receiver_id INT NOT NULL,
        job_id INT NULL,
        subject VARCHAR(255),
        message TEXT NOT NULL,
        is_read BOOLEAN DEFAULT FALSE,
        is_deleted_by_sender BOOLEAN DEFAULT FALSE,
        is_deleted_by_receiver BOOLEAN DEFAULT FALSE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (job_id) REFERENCES job_listings(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Değerlendirmeler tablosu
    $db->exec("CREATE TABLE IF NOT EXISTS reviews (
        id INT AUTO_INCREMENT PRIMARY KEY,
        reviewer_id INT NOT NULL,
        reviewed_id INT NOT NULL,
        job_id INT NULL,
        rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
        title VARCHAR(255),
        comment TEXT,
        is_anonymous BOOLEAN DEFAULT FALSE,
        is_approved BOOLEAN DEFAULT TRUE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (reviewer_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (reviewed_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (job_id) REFERENCES job_listings(id) ON DELETE SET NULL,
        UNIQUE KEY unique_review (reviewer_id, reviewed_id, job_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Paketler tablosu
    $db->exec("CREATE TABLE IF NOT EXISTS packages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        user_type ENUM('family', 'caregiver') NOT NULL,
        price DECIMAL(10,2) NOT NULL,
        duration_days INT NOT NULL,
        features TEXT,
        max_job_listings INT DEFAULT 0,
        max_applications INT DEFAULT 0,
        max_messages INT DEFAULT 0,
        can_see_contact_info BOOLEAN DEFAULT FALSE,
        is_featured BOOLEAN DEFAULT FALSE,
        is_active BOOLEAN DEFAULT TRUE,
        sort_order INT DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Kullanıcı paket satın alımları
    $db->exec("CREATE TABLE IF NOT EXISTS user_packages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        package_id INT NOT NULL,
        payment_id VARCHAR(255),
        amount DECIMAL(10,2) NOT NULL,
        starts_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        expires_at DATETIME NOT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (package_id) REFERENCES packages(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Ödemeler tablosu
    $db->exec("CREATE TABLE IF NOT EXISTS payments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        package_id INT NULL,
        payment_method VARCHAR(50) DEFAULT 'paytr',
        payment_id VARCHAR(255),
        merchant_oid VARCHAR(255),
        amount DECIMAL(10,2) NOT NULL,
        currency VARCHAR(3) DEFAULT 'TL',
        status ENUM('pending', 'success', 'failed', 'cancelled') DEFAULT 'pending',
        paytr_token VARCHAR(255),
        paytr_response TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (package_id) REFERENCES packages(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Bildirimler tablosu
    $db->exec("CREATE TABLE IF NOT EXISTS notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        type VARCHAR(50) NOT NULL,
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        data JSON NULL,
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Sistem ayarları tablosu
    $db->exec("CREATE TABLE IF NOT EXISTS settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT,
        setting_type ENUM('text', 'number', 'boolean', 'json') DEFAULT 'text',
        description TEXT,
        is_public BOOLEAN DEFAULT FALSE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Varsayılan admin kullanıcısı oluştur
    $admin_check = $db->prepare("SELECT id FROM users WHERE user_type = 'admin' LIMIT 1");
    $admin_check->execute();
    
    if (!$admin_check->fetch()) {
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $admin_sql = "INSERT INTO users (user_type, email, password, full_name, is_verified, is_active) 
                      VALUES ('admin', '<EMAIL>', ?, 'Sistem Yöneticisi', 1, 1)";
        $admin_stmt = $db->prepare($admin_sql);
        $admin_stmt->execute([$admin_password]);
    }

    // Varsayılan paketleri oluştur
    $package_check = $db->prepare("SELECT id FROM packages LIMIT 1");
    $package_check->execute();

    if (!$package_check->fetch()) {
        $packages = [
            ['Temel Aile Paketi', 'Temel özelliklerle bakıcı bulun', 'family', 29.90, 30, 'İlan verme, Mesajlaşma', 1, 10, 50, 0, 0],
            ['Premium Aile Paketi', 'Gelişmiş özelliklerle hızlı bakıcı bulun', 'family', 59.90, 30, 'İlan verme, Mesajlaşma, İletişim bilgileri', 3, 50, 200, 1, 1],
            ['Temel Bakıcı Paketi', 'İş fırsatlarına başvurun', 'caregiver', 19.90, 30, 'Profil oluşturma, Başvuru yapma', 0, 20, 30, 0, 0],
            ['Premium Bakıcı Paketi', 'Öne çıkan profil ile daha fazla iş', 'caregiver', 39.90, 30, 'Öne çıkan profil, Sınırsız başvuru', 0, 100, 100, 1, 1]
        ];

        foreach ($packages as $package) {
            $package_sql = "INSERT INTO packages (name, description, user_type, price, duration_days, features, max_job_listings, max_applications, max_messages, can_see_contact_info, is_featured)
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $package_stmt = $db->prepare($package_sql);
            $package_stmt->execute($package);
        }
    }

    // Demo kullanıcıları oluştur
    $demo_check = $db->prepare("SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1");
    $demo_check->execute();

    if (!$demo_check->fetch()) {
        // Demo aile kullanıcıları
        $demo_families = [
            ['<EMAIL>', 'Ahmet Yılmaz', '0532 123 45 67', 'İstanbul', 'Kadıköy', 'Çocuk bakımı için güvenilir bakıcı arıyoruz.'],
            ['<EMAIL>', 'Elif Kaya', '0533 234 56 78', 'Ankara', 'Çankaya', 'Yaşlı annem için yatılı bakıcı ihtiyacımız var.'],
            ['<EMAIL>', 'Mehmet Demir', '0534 345 67 89', 'İzmir', 'Bornova', 'Hasta babam için deneyimli bakıcı arıyoruz.'],
            ['<EMAIL>', 'Zeynep Özkan', '0535 456 78 90', 'Bursa', 'Nilüfer', 'İkiz bebeklerim için çocuk bakıcısı gerekiyor.'],
            ['<EMAIL>', 'Ali Çelik', '0536 567 89 01', 'Antalya', 'Muratpaşa', 'Ev temizliği ve yaşlı bakımı için yardımcı.']
        ];

        foreach ($demo_families as $family) {
            $password = password_hash('demo123', PASSWORD_DEFAULT);
            $sql = "INSERT INTO users (user_type, email, password, full_name, phone, city, district, is_verified, is_active)
                    VALUES ('family', ?, ?, ?, ?, ?, ?, 1, 1)";
            $stmt = $db->prepare($sql);
            $stmt->execute([$family[0], $family[1], $family[2], $family[3], $family[4]]);
        }

        // Demo bakıcı kullanıcıları
        $demo_caregivers = [
            ['<EMAIL>', 'Fatma Yılmaz', '0537 678 90 12', 'İstanbul', 'Beşiktaş', '1985-03-15', 'female', 8, 'Lise', 'Türkçe, İngilizce', 'Çocuk bakımı, Ev temizliği', 25.00, 200.00, 4500.00, 'Hafta içi 08:00-18:00', 'Çocuk bakımı konusunda 8 yıllık deneyimim var. Çocukları çok seviyorum.', 'Çocuk gelişimi, İlk yardım', 'Çocuk bakım sertifikası, İlk yardım sertifikası', 'Önceki işverenlerden mükemmel referanslar', 1],
            ['<EMAIL>', 'Ayşe Kara', '0538 789 01 23', 'Ankara', 'Kızılay', '1978-07-22', 'female', 12, 'Ön Lisans', 'Türkçe', 'Yaşlı bakımı, Hasta bakımı', 30.00, 250.00, 5500.00, '7/24 yatılı bakım', 'Yaşlı ve hasta bakımında uzmanım. Sabırlı ve anlayışlıyım.', 'Hasta bakımı, Yaşlı bakımı, Medikal bilgi', 'Hasta bakım sertifikası, Yaşlı bakım sertifikası', 'Hastane ve evde bakım deneyimi', 1],
            ['<EMAIL>', 'Merve Öztürk', '0539 890 12 34', 'İzmir', 'Alsancak', '1990-11-08', 'female', 5, 'Lisans', 'Türkçe, İngilizce, Almanca', 'Çocuk bakımı, Ev yardımcısı', 28.00, 220.00, 4800.00, 'Esnek çalışma saatleri', 'Üniversite mezunuyum. Çocuk gelişimi alanında eğitim aldım.', 'Çocuk gelişimi, Yabancı dil', 'Çocuk gelişimi sertifikası', 'Eğitimci ailelerden referanslar', 1],
            ['<EMAIL>', 'Hatice Yıldız', '0540 901 23 45', 'Bursa', 'Osmangazi', '1982-05-30', 'female', 10, 'Lise', 'Türkçe', 'Yaşlı bakımı, Ev yardımcısı', 22.00, 180.00, 4000.00, 'Hafta içi gündüz', 'Yaşlı bakımında deneyimliyim. Temizlik ve yemek yapma konularında iyiyim.', 'Ev işleri, Yemek yapma, Yaşlı bakımı', 'Ev yardımcısı sertifikası', 'Uzun süreli çalıştığım aileler', 1],
            ['<EMAIL>', 'Emine Şahin', '0541 012 34 56', 'Antalya', 'Kepez', '1987-09-12', 'female', 6, 'Lise', 'Türkçe, İngilizce', 'Çocuk bakımı, Evcil hayvan bakımı', 24.00, 190.00, 4200.00, 'Hafta sonu dahil', 'Çocuk ve hayvan sever bir kişiyim. Güvenilir ve sorumlu.', 'Çocuk bakımı, Evcil hayvan bakımı', 'Çocuk bakım sertifikası', 'Memnun ailelerden referanslar', 1],
            ['<EMAIL>', 'Sultan Arslan', '0542 123 45 67', 'İstanbul', 'Üsküdar', '1975-12-03', 'female', 15, 'Ortaokul', 'Türkçe', 'Yaşlı bakımı, Hasta bakımı', 35.00, 280.00, 6000.00, 'Yatılı bakım tercih', 'Uzun yıllardır yaşlı ve hasta bakımı yapıyorum. Çok deneyimliyim.', 'Hasta bakımı, Yaşlı bakımı, Medikal cihaz kullanımı', 'Hasta bakım sertifikası, Yaşlı bakım sertifikası', 'Doktor ve hemşire referansları', 1],
            ['<EMAIL>', 'Zehra Kurt', '0543 234 56 78', 'Ankara', 'Mamak', '1992-04-18', 'female', 3, 'Lisans', 'Türkçe, İngilizce', 'Çocuk bakımı, Özel eğitim', 32.00, 260.00, 5200.00, 'Hafta içi 09:00-17:00', 'Özel eğitim alanında mezunum. Özel gereksinimli çocuklarla çalışabilirim.', 'Özel eğitim, Çocuk gelişimi, Oyun terapisi', 'Özel eğitim sertifikası, Çocuk gelişimi sertifikası', 'Özel eğitim merkezlerinden referanslar', 1],
            ['<EMAIL>', 'Gülsüm Yavuz', '0544 345 67 89', 'İzmir', 'Karşıyaka', '1980-08-25', 'female', 9, 'Lise', 'Türkçe', 'Ev yardımcısı, Yaşlı bakımı', 20.00, 160.00, 3800.00, 'Günlük temizlik', 'Ev işlerinde çok titizim. Yaşlılarla iyi anlaşırım.', 'Ev temizliği, Ütü, Yemek yapma', 'Ev yardımcısı sertifikası', 'Düzenli çalıştığım aileler', 1]
        ];

        foreach ($demo_caregivers as $caregiver) {
            $password = password_hash('demo123', PASSWORD_DEFAULT);
            $sql = "INSERT INTO users (user_type, email, password, full_name, phone, city, district, is_verified, is_active)
                    VALUES ('caregiver', ?, ?, ?, ?, ?, ?, 1, 1)";
            $stmt = $db->prepare($sql);
            $stmt->execute([$caregiver[0], $caregiver[1], $caregiver[2], $caregiver[3], $caregiver[4]]);

            $caregiver_id = $db->lastInsertId();

            // Bakıcı profili oluştur
            $profile_sql = "INSERT INTO caregiver_profiles (user_id, birth_date, gender, experience_years, education_level, languages, services, hourly_rate, daily_rate, monthly_rate, availability, bio, skills, certificates, reference_info, background_check, background_check_file, cv_file, rating, total_reviews, total_jobs)
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, NULL, ?, ?, ?)";
            $profile_stmt = $db->prepare($profile_sql);
            $rating = 3.5 + (rand(0, 15) / 10); // 3.5 - 5.0 arası rating
            $reviews = rand(5, 25);
            $jobs = rand(10, 50);
            $profile_stmt->execute([
                $caregiver_id, $caregiver[5], $caregiver[6], $caregiver[7], $caregiver[8],
                $caregiver[9], $caregiver[10], $caregiver[11], $caregiver[12], $caregiver[13],
                $caregiver[14], $caregiver[15], $caregiver[16], $caregiver[17], $caregiver[18],
                $caregiver[19], $rating, $reviews, $jobs
            ]);
        }
    }

    // Demo iş ilanları oluştur
    $job_check = $db->prepare("SELECT id FROM job_listings LIMIT 1");
    $job_check->execute();

    if (!$job_check->fetch()) {
        // Aile kullanıcılarının ID'lerini al
        $family_ids = $db->query("SELECT id FROM users WHERE user_type = 'family' ORDER BY id LIMIT 5")->fetchAll(PDO::FETCH_COLUMN);

        $demo_jobs = [
            ['Deneyimli Çocuk Bakıcısı Aranıyor', 'child_care', 'live_out', 'İstanbul', 'Kadıköy', 'Kadıköy merkezde oturan ailemiz için 3 yaşındaki kızımıza bakacak deneyimli, güvenilir ve sevecen bir bakıcı arıyoruz.', 3000, 4000, 'monthly', '2024-02-01', 'En az 2 yıl deneyim, referans, temiz adli sicil', 'female', 25, 40, 2, 'Türkçe', '0532 123 45 67', '<EMAIL>', 0, 0, 0, 'active'],
            ['Yaşlı Bakım Elemanı - Yatılı', 'elderly_care', 'live_in', 'Ankara', 'Çankaya', '85 yaşındaki annem için yatılı bakım elemanı arıyoruz. Alzheimer hastası olduğu için deneyimli birini tercih ediyoruz.', 5000, 6000, 'monthly', '2024-02-05', 'Yaşlı bakım deneyimi, sabırlı, Alzheimer deneyimi', 'no_preference', 30, 55, 3, 'Türkçe', '0533 234 56 78', '<EMAIL>', 1, 0, 0, 'active'],
            ['Hasta Bakım Elemanı - Acil', 'patient_care', 'live_in', 'İzmir', 'Bornova', 'Ameliyat sonrası iyileşme sürecindeki babam için hasta bakım elemanı gerekiyor. Medikal bilgisi olan tercih edilir.', 4000, 5000, 'monthly', '2024-01-28', 'Hasta bakım sertifikası, medikal bilgi, referans', 'no_preference', 25, 50, 2, 'Türkçe', '0534 345 67 89', '<EMAIL>', 1, 0, 0, 'active'],
            ['İkiz Bebek Bakıcısı', 'child_care', 'daily', 'Bursa', 'Nilüfer', '6 aylık ikiz bebeklerimiz için günlük bakıcı arıyoruz. Bebek bakımında deneyimli olması şart.', 200, 250, 'daily', '2024-02-10', 'Bebek bakım deneyimi, referans, hijyen kurallarına uyum', 'female', 22, 45, 3, 'Türkçe', '0535 456 78 90', '<EMAIL>', 0, 1, 0, 'active'],
            ['Ev Yardımcısı ve Yaşlı Bakımı', 'house_help', 'live_out', 'Antalya', 'Muratpaşa', 'Ev temizliği ve 78 yaşındaki babama bakım için yardımcı arıyoruz. Hafta içi gündüz çalışma.', 25, 30, 'hourly', '2024-02-15', 'Ev işleri deneyimi, yaşlı bakım bilgisi', 'female', 25, 50, 1, 'Türkçe', '0536 567 89 01', '<EMAIL>', 0, 0, 0, 'active']
        ];

        foreach ($demo_jobs as $index => $job) {
            // Family ID'yi array'den al
            $family_id = $family_ids[$index % count($family_ids)];

            $sql = "INSERT INTO job_listings (user_id, title, job_type, care_type, location_city, location_district, description, budget_min, budget_max, budget_type, start_date, requirements, preferred_gender, preferred_age_min, preferred_age_max, required_experience, required_languages, contact_phone, contact_email, is_urgent, is_featured, is_premium, status)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $db->prepare($sql);

            // User_id'yi başa ekleyerek execute et
            $job_data = array_merge([$family_id], $job);
            $stmt->execute($job_data);

            // expires_at'ı güncelle
            $job_id = $db->lastInsertId();
            $update_sql = "UPDATE job_listings SET expires_at = DATE_ADD(NOW(), INTERVAL 30 DAY) WHERE id = ?";
            $update_stmt = $db->prepare($update_sql);
            $update_stmt->execute([$job_id]);
        }
    }

    // Demo başvurular oluştur
    $application_check = $db->prepare("SELECT id FROM job_applications LIMIT 1");
    $application_check->execute();

    if (!$application_check->fetch()) {
        // İş ilanları ve bakıcı ID'lerini al
        $job_ids = $db->query("SELECT id FROM job_listings ORDER BY id LIMIT 8")->fetchAll(PDO::FETCH_COLUMN);
        $caregiver_ids = $db->query("SELECT id FROM users WHERE user_type = 'caregiver' ORDER BY id LIMIT 8")->fetchAll(PDO::FETCH_COLUMN);

        // Her iş ilanına 2-4 başvuru oluştur
        foreach ($job_ids as $job_id) {
            $application_count = rand(2, 4);
            $selected_caregivers = array_rand($caregiver_ids, min($application_count, count($caregiver_ids)));
            if (!is_array($selected_caregivers)) $selected_caregivers = [$selected_caregivers];

            foreach ($selected_caregivers as $index) {
                $caregiver_id = $caregiver_ids[$index];
                $messages = [
                    'Merhaba, ilanınızı gördüm ve çok ilgimi çekti. Bu konuda deneyimliyim ve referanslarım mevcut.',
                    'İlanınız için başvuru yapmak istiyorum. Uzun yıllardır bu alanda çalışıyorum.',
                    'Merhaba, ilanınıza uygun olduğumu düşünüyorum. Detayları konuşmak isterim.',
                    'İlanınız için başvuruyorum. Deneyimim ve referanslarım hakkında bilgi verebilirim.',
                    'Merhaba, bu pozisyon için çok uygun olduğumu düşünüyorum. Görüşmek isterim.'
                ];

                $sql = "INSERT INTO job_applications (job_id, caregiver_id, message, proposed_rate, status)
                        VALUES (?, ?, ?, ?, 'pending')";
                $stmt = $db->prepare($sql);
                $proposed_rate = rand(20, 50) + (rand(0, 9) / 10);
                $stmt->execute([$job_id, $caregiver_id, $messages[array_rand($messages)], $proposed_rate]);
            }
        }
    }

    // Demo değerlendirmeler oluştur
    $review_check = $db->prepare("SELECT id FROM reviews LIMIT 1");
    $review_check->execute();

    if (!$review_check->fetch()) {
        $family_ids = $db->query("SELECT id FROM users WHERE user_type = 'family' ORDER BY id LIMIT 5")->fetchAll(PDO::FETCH_COLUMN);
        $caregiver_ids = $db->query("SELECT id FROM users WHERE user_type = 'caregiver' ORDER BY id LIMIT 8")->fetchAll(PDO::FETCH_COLUMN);

        $review_comments = [
            ['Çok Memnun Kaldık', 'Fatma hanım çok güvenilir ve çocuklarımızı çok seviyor. Kesinlikle tavsiye ederim.', 5],
            ['Harika Bir Bakıcı', 'Ayşe hanım babama çok iyi bakıyor. Çok sabırlı ve anlayışlı.', 5],
            ['Profesyonel Hizmet', 'Merve hanım çok eğitimli ve çocuk gelişimi konusunda bilgili.', 4],
            ['Güvenilir ve Titiz', 'Hatice hanım ev işlerinde çok titiz ve yaşlı anneme çok iyi bakıyor.', 4],
            ['Çocuklar Çok Seviyor', 'Emine hanım çocuklarla çok iyi anlaşıyor. Çocuklarımız onu çok seviyor.', 5],
            ['Deneyimli ve Bilgili', 'Sultan hanım çok deneyimli. Hasta bakımında gerçekten uzman.', 5],
            ['Özel Eğitimde Başarılı', 'Zehra hanım özel gereksinimli çocuğumuzla harika çalışıyor.', 4],
            ['Temiz ve Düzenli', 'Gülsüm hanım ev temizliğinde çok başarılı. Çok memnunuz.', 4]
        ];

        foreach ($caregiver_ids as $index => $caregiver_id) {
            if (isset($review_comments[$index])) {
                $family_id = $family_ids[array_rand($family_ids)];
                $review = $review_comments[$index];

                $sql = "INSERT INTO reviews (reviewer_id, reviewed_id, rating, title, comment, is_approved)
                        VALUES (?, ?, ?, ?, ?, 1)";
                $stmt = $db->prepare($sql);
                $stmt->execute([$family_id, $caregiver_id, $review[2], $review[0], $review[1]]);
            }
        }
    }

    // Demo mesajlar oluştur
    $message_check = $db->prepare("SELECT id FROM messages LIMIT 1");
    $message_check->execute();

    if (!$message_check->fetch()) {
        $family_ids = $db->query("SELECT id FROM users WHERE user_type = 'family' ORDER BY id LIMIT 3")->fetchAll(PDO::FETCH_COLUMN);
        $caregiver_ids = $db->query("SELECT id FROM users WHERE user_type = 'caregiver' ORDER BY id LIMIT 3")->fetchAll(PDO::FETCH_COLUMN);

        $demo_messages = [
            ['İş İlanı Hakkında', 'Merhaba, çocuk bakımı ilanınız hakkında bilgi almak istiyorum.'],
            ['Referans Bilgileri', 'Merhaba, referanslarınızı paylaşabilir misiniz?'],
            ['Çalışma Saatleri', 'Hafta sonu çalışma konusunda nasıl düşünüyorsunuz?'],
            ['Deneyim Hakkında', 'Yaşlı bakımı konusundaki deneyiminizi anlatabilir misiniz?'],
            ['Ücret Görüşmesi', 'Ücret konusunda görüşmek istiyorum.']
        ];

        foreach ($demo_messages as $index => $message) {
            if ($index < count($family_ids) && $index < count($caregiver_ids)) {
                $sql = "INSERT INTO messages (sender_id, receiver_id, subject, message)
                        VALUES (?, ?, ?, ?)";
                $stmt = $db->prepare($sql);
                $stmt->execute([$family_ids[$index], $caregiver_ids[$index], $message[0], $message[1]]);

                // Cevap mesajı
                $reply = 'Merhaba, mesajınız için teşekkürler. Bu konuda detaylı konuşabiliriz.';
                $stmt->execute([$caregiver_ids[$index], $family_ids[$index], 'Re: ' . $message[0], $reply]);
            }
        }
    }

    // Demo bildirimler oluştur
    $notification_check = $db->prepare("SELECT id FROM notifications LIMIT 1");
    $notification_check->execute();

    if (!$notification_check->fetch()) {
        $all_user_ids = $db->query("SELECT id FROM users WHERE user_type IN ('family', 'caregiver') ORDER BY id LIMIT 10")->fetchAll(PDO::FETCH_COLUMN);

        $demo_notifications = [
            ['new_application', 'Yeni Başvuru', 'İlanınıza yeni bir başvuru geldi.'],
            ['message_received', 'Yeni Mesaj', 'Size yeni bir mesaj gönderildi.'],
            ['profile_viewed', 'Profil Görüntülendi', 'Profiliniz bir işveren tarafından görüntülendi.'],
            ['job_match', 'Uygun İş İlanı', 'Size uygun yeni bir iş ilanı yayınlandı.'],
            ['review_received', 'Yeni Değerlendirme', 'Hakkınızda yeni bir değerlendirme yazıldı.']
        ];

        foreach ($all_user_ids as $index => $user_id) {
            if ($index < count($demo_notifications)) {
                $notification = $demo_notifications[$index];
                $sql = "INSERT INTO notifications (user_id, type, title, message)
                        VALUES (?, ?, ?, ?)";
                $stmt = $db->prepare($sql);
                $stmt->execute([$user_id, $notification[0], $notification[1], $notification[2]]);
            }
        }
    }

} catch(PDOException $e) {
    echo "Tablo oluşturma hatası: " . $e->getMessage();
}
?>
