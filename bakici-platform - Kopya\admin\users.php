<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Giriş kontrolü
if (!isset($_SESSION['user_id'])) {
    header('Location: ../auth/login.php');
    exit;
}

// Sadece admin kullanıcıları erişebilir
if ($_SESSION['user_type'] !== 'admin') {
    header('Location: ../dashboard.php');
    exit;
}

// CRUD İşlemleri
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $user_id = intval($_POST['user_id'] ?? 0);

    try {
        if ($action === 'toggle_status') {
            // Kullanıcı durumunu değiştir
            $sql = "UPDATE users SET status = CASE WHEN status = 'active' THEN 'inactive' ELSE 'active' END WHERE id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$user_id]);
            $success = 'Kullanıcı durumu güncellendi.';

        } elseif ($action === 'verify_user') {
            // Kullanıcıyı doğrula
            $sql = "UPDATE users SET is_verified = 1 WHERE id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$user_id]);
            $success = 'Kullanıcı doğrulandı.';

        } elseif ($action === 'delete_user') {
            // Kullanıcıyı sil (soft delete)
            $sql = "UPDATE users SET status = 'deleted', updated_at = NOW() WHERE id = ? AND id != ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$user_id, $_SESSION['user_id']]); // Admin kendini silemez
            $success = 'Kullanıcı silindi.';
        }
    } catch (PDOException $e) {
        $error = 'İşlem sırasında hata oluştu: ' . $e->getMessage();
    }
}

// Kullanıcıları getir
try {
    // Basit kullanıcı listesi
    $sql = "SELECT * FROM users ORDER BY created_at DESC LIMIT 100";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $users = $stmt->fetchAll();

    // Her kullanıcı için iş ilanı ve başvuru sayısını ayrı ayrı getir
    foreach ($users as &$user) {
        // İş ilanı sayısı (sadece aile kullanıcıları için)
        if ($user['user_type'] === 'family') {
            $job_sql = "SELECT COUNT(*) FROM job_listings WHERE user_id = ?";
            $job_stmt = $db->prepare($job_sql);
            $job_stmt->execute([$user['id']]);
            $user['job_count'] = $job_stmt->fetchColumn();
            $user['application_count'] = 0;
        }
        // Başvuru sayısı (sadece bakıcı kullanıcıları için)
        elseif ($user['user_type'] === 'caregiver') {
            $app_sql = "SELECT COUNT(*) FROM job_applications WHERE caregiver_id = ?";
            $app_stmt = $db->prepare($app_sql);
            $app_stmt->execute([$user['id']]);
            $user['application_count'] = $app_stmt->fetchColumn();
            $user['job_count'] = 0;
        } else {
            $user['job_count'] = 0;
            $user['application_count'] = 0;
        }
    }

    // İstatistikler
    $stats = [
        'total' => $db->query("SELECT COUNT(*) FROM users")->fetchColumn(),
        'families' => $db->query("SELECT COUNT(*) FROM users WHERE user_type = 'family'")->fetchColumn(),
        'caregivers' => $db->query("SELECT COUNT(*) FROM users WHERE user_type = 'caregiver'")->fetchColumn(),
        'active' => $db->query("SELECT COUNT(*) FROM users WHERE status = 'active'")->fetchColumn()
    ];

} catch (PDOException $e) {
    $users = [];
    $stats = ['total' => 0, 'families' => 0, 'caregivers' => 0, 'active' => 0];
    error_log("Admin users query error: " . $e->getMessage());
}



$page_title = 'Kullanıcı Yönetimi';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Admin Panel</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="../index.php">
                <i class="bi bi-heart-fill me-2"></i>Bakıcı Platform - Admin
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../dashboard.php">Dashboard</a>
                <a class="nav-link" href="settings.php">Ayarlar</a>
                <a class="nav-link" href="../auth/logout.php">Çıkış</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="bi bi-people me-2"></i>Kullanıcı Yönetimi</h4>
                    </div>
                    <div class="card-body">
                        <!-- Alerts -->
                        <?php if (isset($success)): ?>
                            <div class="alert alert-success alert-dismissible fade show">
                                <i class="bi bi-check-circle me-2"></i><?php echo $success; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($error)): ?>
                            <div class="alert alert-danger alert-dismissible fade show">
                                <i class="bi bi-exclamation-triangle me-2"></i><?php echo $error; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <!-- İstatistikler -->
                        <div class="row g-3 mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h3><?php echo $stats['total']; ?></h3>
                                        <small>Toplam Kullanıcı</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h3><?php echo $stats['families']; ?></h3>
                                        <small>Aile</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h3><?php echo $stats['caregivers']; ?></h3>
                                        <small>Bakıcı</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <h3><?php echo $stats['active']; ?></h3>
                                        <small>Aktif</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Kullanıcı Listesi -->
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Ad Soyad</th>
                                        <th>Email</th>
                                        <th>Tip</th>
                                        <th>Durum</th>
                                        <th>İlan/Başvuru</th>
                                        <th>Kayıt Tarihi</th>
                                        <th>İşlemler</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                    <tr>
                                        <td><?php echo $user['id']; ?></td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($user['full_name']); ?></strong>
                                            <?php if (isset($user['email_verified']) && $user['email_verified']): ?>
                                                <i class="bi bi-check-circle text-success" title="Email doğrulandı"></i>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($user['email']); ?></td>
                                        <td>
                                            <?php if ($user['user_type'] === 'family'): ?>
                                                <span class="badge bg-success">Aile</span>
                                            <?php elseif ($user['user_type'] === 'caregiver'): ?>
                                                <span class="badge bg-info">Bakıcı</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Admin</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if (isset($user['status']) && $user['status'] === 'active'): ?>
                                                <span class="badge bg-success">Aktif</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Pasif</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($user['user_type'] === 'family'): ?>
                                                <small><?php echo $user['job_count']; ?> İlan</small>
                                            <?php elseif ($user['user_type'] === 'caregiver'): ?>
                                                <small><?php echo $user['application_count']; ?> Başvuru</small>
                                            <?php else: ?>
                                                <small>-</small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small><?php echo date('d.m.Y', strtotime($user['created_at'])); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="viewUser(<?php echo $user['id']; ?>)" title="Görüntüle">
                                                    <i class="bi bi-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-secondary" onclick="editUser(<?php echo $user['id']; ?>)" title="Düzenle">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <?php if (isset($user['status']) && $user['status'] === 'active'): ?>
                                                    <button class="btn btn-outline-warning" onclick="toggleUser(<?php echo $user['id']; ?>, 'inactive')" title="Durdur">
                                                        <i class="bi bi-pause"></i>
                                                    </button>
                                                <?php else: ?>
                                                    <button class="btn btn-outline-success" onclick="toggleUser(<?php echo $user['id']; ?>, 'active')" title="Aktifleştir">
                                                        <i class="bi bi-play"></i>
                                                    </button>
                                                <?php endif; ?>
                                                <?php if (!isset($user['is_verified']) || !$user['is_verified']): ?>
                                                    <button class="btn btn-outline-info" onclick="verifyUser(<?php echo $user['id']; ?>)" title="Doğrula">
                                                        <i class="bi bi-check-circle"></i>
                                                    </button>
                                                <?php endif; ?>
                                                <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                                    <button class="btn btn-outline-danger" onclick="deleteUser(<?php echo $user['id']; ?>)" title="Sil">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <?php if (empty($users)): ?>
                            <div class="text-center py-4">
                                <i class="bi bi-people text-muted" style="font-size: 3rem;"></i>
                                <h5 class="text-muted mt-3">Henüz kullanıcı yok</h5>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function viewUser(userId) {
            // Kullanıcı detaylarını modal'da göster
            fetch(`user-details.php?id=${userId}`)
                .then(response => response.text())
                .then(data => {
                    document.getElementById('userModalContent').innerHTML = data;
                    new bootstrap.Modal(document.getElementById('userModal')).show();
                })
                .catch(error => {
                    alert('Kullanıcı bilgileri yüklenirken hata oluştu.');
                });
        }

        function editUser(userId) {
            // Kullanıcı düzenleme sayfasına yönlendir
            window.location.href = `user-edit.php?id=${userId}`;
        }

        function toggleUser(userId, status) {
            if (confirm('Kullanıcı durumunu değiştirmek istediğinizden emin misiniz?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="toggle_status">
                    <input type="hidden" name="user_id" value="${userId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function verifyUser(userId) {
            if (confirm('Bu kullanıcıyı doğrulamak istediğinizden emin misiniz?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="verify_user">
                    <input type="hidden" name="user_id" value="${userId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function deleteUser(userId) {
            if (confirm('Bu kullanıcıyı silmek istediğinizden emin misiniz? Bu işlem geri alınamaz!')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_user">
                    <input type="hidden" name="user_id" value="${userId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>

    <!-- User Modal -->
    <div class="modal fade" id="userModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Kullanıcı Detayları</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="userModalContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Yükleniyor...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div></script>
</body>
</html>
