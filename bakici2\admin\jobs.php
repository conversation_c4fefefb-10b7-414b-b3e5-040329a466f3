<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Giriş kontrolü
if (!isset($_SESSION['user_id'])) {
    header('Location: ../auth/login.php');
    exit;
}

// Sadece admin kullanıcıları erişebilir
if ($_SESSION['user_type'] !== 'admin') {
    header('Location: ../dashboard.php');
    exit;
}

// CRUD İşlemleri
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $job_id = intval($_POST['job_id'] ?? 0);

    try {
        if ($action === 'toggle_status') {
            // İş ilanı durumunu değiştir
            $sql = "UPDATE job_listings SET status = CASE
                    WHEN status = 'active' THEN 'paused'
                    WHEN status = 'paused' THEN 'active'
                    ELSE 'active' END WHERE id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$job_id]);
            $success = 'İş ilanı durumu güncellendi.';

        } elseif ($action === 'close_job') {
            // İş ilanını kapat
            $sql = "UPDATE job_listings SET status = 'closed', updated_at = NOW() WHERE id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$job_id]);
            $success = 'İş ilanı kapatıldı.';

        } elseif ($action === 'reactivate_job') {
            // Kapatılmış iş ilanını tekrar aktifleştir
            $sql = "UPDATE job_listings SET status = 'active', updated_at = NOW() WHERE id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$job_id]);
            $success = 'İş ilanı tekrar aktifleştirildi.';

        } elseif ($action === 'delete_job') {
            // İş ilanını sil
            $sql = "DELETE FROM job_listings WHERE id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$job_id]);
            $success = 'İş ilanı silindi.';
        }
    } catch (PDOException $e) {
        $error = 'İşlem sırasında hata oluştu: ' . $e->getMessage();
    }
}

// İş ilanlarını getir
try {
    $sql = "SELECT jl.*, u.full_name as employer_name, u.email as employer_email,
                   COUNT(ja.id) as application_count
            FROM job_listings jl
            LEFT JOIN users u ON jl.user_id = u.id
            LEFT JOIN job_applications ja ON jl.id = ja.job_id
            GROUP BY jl.id
            ORDER BY jl.created_at DESC
            LIMIT 100";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $jobs = $stmt->fetchAll();

    // İstatistikler
    $stats = [
        'total' => $db->query("SELECT COUNT(*) FROM job_listings")->fetchColumn(),
        'active' => $db->query("SELECT COUNT(*) FROM job_listings WHERE status = 'active'")->fetchColumn(),
        'paused' => $db->query("SELECT COUNT(*) FROM job_listings WHERE status = 'paused'")->fetchColumn(),
        'completed' => $db->query("SELECT COUNT(*) FROM job_listings WHERE status = 'completed'")->fetchColumn()
    ];

} catch (PDOException $e) {
    $jobs = [];
    $stats = ['total' => 0, 'active' => 0, 'paused' => 0, 'completed' => 0];
    error_log("Admin jobs query error: " . $e->getMessage());
}

$page_title = 'İş İlanları Yönetimi';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Admin Panel</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="../index.php">
                <i class="bi bi-heart-fill me-2"></i>Bakıcı Platform - Admin
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../dashboard.php">Dashboard</a>
                <a class="nav-link" href="settings.php">Ayarlar</a>
                <a class="nav-link" href="../auth/logout.php">Çıkış</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="bi bi-briefcase me-2"></i>İş İlanları Yönetimi</h4>
                    </div>
                    <div class="card-body">
                        <!-- Alerts -->
                        <?php if (isset($success)): ?>
                            <div class="alert alert-success alert-dismissible fade show">
                                <i class="bi bi-check-circle me-2"></i><?php echo $success; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($error)): ?>
                            <div class="alert alert-danger alert-dismissible fade show">
                                <i class="bi bi-exclamation-triangle me-2"></i><?php echo $error; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($_GET['success'])): ?>
                            <div class="alert alert-success alert-dismissible fade show">
                                <i class="bi bi-check-circle me-2"></i>
                                <?php
                                $success_messages = [
                                    'job_updated' => 'İş ilanı başarıyla güncellendi.',
                                    'job_created' => 'İş ilanı başarıyla oluşturuldu.',
                                    'job_deleted' => 'İş ilanı silindi.',
                                    'status_updated' => 'İş ilanı durumu güncellendi.'
                                ];
                                echo $success_messages[$_GET['success']] ?? 'İşlem başarıyla tamamlandı.';
                                ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($_GET['error'])): ?>
                            <div class="alert alert-danger alert-dismissible fade show">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                <?php
                                $error_messages = [
                                    'job_not_found' => 'İş ilanı bulunamadı.',
                                    'invalid_job' => 'Geçersiz iş ilanı.',
                                    'permission_denied' => 'Bu işlem için yetkiniz yok.',
                                    'database_error' => 'Veritabanı hatası oluştu.'
                                ];
                                echo $error_messages[$_GET['error']] ?? 'Bir hata oluştu.';
                                ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <!-- İstatistikler -->
                        <div class="row g-3 mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h3><?php echo $stats['total']; ?></h3>
                                        <small>Toplam İlan</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h3><?php echo $stats['active']; ?></h3>
                                        <small>Aktif İlan</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <h3><?php echo $stats['paused']; ?></h3>
                                        <small>Duraklatılmış</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h3><?php echo $stats['completed']; ?></h3>
                                        <small>Tamamlanmış</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- İş İlanları Listesi -->
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>İlan Başlığı</th>
                                        <th>İşveren</th>
                                        <th>Konum</th>
                                        <th>Ücret</th>
                                        <th>Durum</th>
                                        <th>Başvuru</th>
                                        <th>Tarih</th>
                                        <th>İşlemler</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($jobs as $job): ?>
                                    <tr>
                                        <td><?php echo $job['id']; ?></td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($job['title']); ?></strong>
                                            <br><small class="text-muted"><?php echo htmlspecialchars(substr($job['description'], 0, 50)); ?>...</small>
                                        </td>
                                        <td>
                                            <?php echo htmlspecialchars($job['employer_name']); ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($job['employer_email']); ?></small>
                                        </td>
                                        <td><?php echo htmlspecialchars($job['location'] ?? 'Belirtilmemiş'); ?></td>
                                        <td>
                                            <?php if (isset($job['salary_min']) && isset($job['salary_max']) && $job['salary_min'] && $job['salary_max']): ?>
                                                ₺<?php echo number_format($job['salary_min']); ?> - ₺<?php echo number_format($job['salary_max']); ?>
                                            <?php elseif (isset($job['salary_min']) && $job['salary_min']): ?>
                                                ₺<?php echo number_format($job['salary_min']); ?>+
                                            <?php else: ?>
                                                Belirtilmemiş
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($job['status'] === 'active'): ?>
                                                <span class="badge bg-success">Aktif</span>
                                            <?php elseif ($job['status'] === 'paused'): ?>
                                                <span class="badge bg-warning">Duraklatılmış</span>
                                            <?php elseif ($job['status'] === 'closed'): ?>
                                                <span class="badge bg-secondary">Kapatıldı</span>
                                            <?php elseif ($job['status'] === 'completed'): ?>
                                                <span class="badge bg-info">Tamamlanmış</span>
                                            <?php elseif ($job['status'] === 'expired'): ?>
                                                <span class="badge bg-danger">Süresi Doldu</span>
                                            <?php else: ?>
                                                <span class="badge bg-dark">Bilinmeyen</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary"><?php echo $job['application_count']; ?></span>
                                        </td>
                                        <td>
                                            <small><?php echo date('d.m.Y', strtotime($job['created_at'])); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="viewJob(<?php echo $job['id']; ?>)" title="Görüntüle">
                                                    <i class="bi bi-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-secondary" onclick="editJob(<?php echo $job['id']; ?>)" title="Düzenle">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <?php if ($job['status'] === 'active'): ?>
                                                    <button class="btn btn-outline-warning" onclick="pauseJob(<?php echo $job['id']; ?>)" title="Durdur">
                                                        <i class="bi bi-pause"></i>
                                                    </button>
                                                <?php elseif ($job['status'] === 'paused'): ?>
                                                    <button class="btn btn-outline-success" onclick="activateJob(<?php echo $job['id']; ?>)" title="Aktifleştir">
                                                        <i class="bi bi-play"></i>
                                                    </button>
                                                <?php elseif ($job['status'] === 'closed'): ?>
                                                    <button class="btn btn-outline-success" onclick="reactivateJob(<?php echo $job['id']; ?>)" title="Tekrar Aktifleştir">
                                                        <i class="bi bi-arrow-clockwise"></i>
                                                    </button>
                                                <?php endif; ?>
                                                <?php if ($job['status'] !== 'closed'): ?>
                                                    <button class="btn btn-outline-info" onclick="closeJob(<?php echo $job['id']; ?>)" title="Kapat">
                                                        <i class="bi bi-x-circle"></i>
                                                    </button>
                                                <?php endif; ?>
                                                <button class="btn btn-outline-danger" onclick="deleteJob(<?php echo $job['id']; ?>)" title="Sil">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <?php if (empty($jobs)): ?>
                            <div class="text-center py-4">
                                <i class="bi bi-briefcase text-muted" style="font-size: 3rem;"></i>
                                <h5 class="text-muted mt-3">Henüz iş ilanı yok</h5>
                                <p class="text-muted">Kullanıcılar iş ilanı vermeye başladığında burada görünecek.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function viewJob(jobId) {
            // İş ilanı detaylarını yeni sekmede aç
            window.open(`../job-details.php?id=${jobId}`, '_blank');
        }

        function editJob(jobId) {
            // İş ilanı düzenleme sayfasına yönlendir
            window.location.href = `job-edit.php?id=${jobId}`;
        }

        function pauseJob(jobId) {
            if (confirm('Bu iş ilanını duraklatmak istediğinizden emin misiniz?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="toggle_status">
                    <input type="hidden" name="job_id" value="${jobId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function activateJob(jobId) {
            if (confirm('Bu iş ilanını aktifleştirmek istediğinizden emin misiniz?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="toggle_status">
                    <input type="hidden" name="job_id" value="${jobId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function reactivateJob(jobId) {
            if (confirm('Bu kapatılmış iş ilanını tekrar aktifleştirmek istediğinizden emin misiniz?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="reactivate_job">
                    <input type="hidden" name="job_id" value="${jobId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function closeJob(jobId) {
            if (confirm('Bu iş ilanını kapatmak istediğinizden emin misiniz?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="close_job">
                    <input type="hidden" name="job_id" value="${jobId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function deleteJob(jobId) {
            if (confirm('Bu iş ilanını silmek istediğinizden emin misiniz? Bu işlem geri alınamaz!')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_job">
                    <input type="hidden" name="job_id" value="${jobId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
