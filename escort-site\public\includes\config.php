<?php
// EscortNews - Konfigürasyon Dosyası
// PHP 7.1 Uyumlu

session_start();

// Veritabanı bağlantısı
function getDbConnection() {
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=escort_site;charset=utf8mb4", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch (PDOException $e) {
        die("Veritabanı bağlantı hatası: " . $e->getMessage());
    }
}

// Güvenlik fonksiyonu
function sanitize($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

// URL slug oluştur
function createSlug($text) {
    $text = strtolower($text);
    $text = preg_replace('/[^a-z0-9\s-]/', '', $text);
    $text = preg_replace('/[\s-]+/', '-', $text);
    return trim($text, '-');
}

// Site ayarlarını al
function getSettings() {
    $pdo = getDbConnection();
    $stmt = $pdo->query("SELECT `key`, `value` FROM settings");
    $settings = array();
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $settings[$row['key']] = $row['value'];
    }
    return $settings;
}

// Kategorileri al
function getCategories() {
    $pdo = getDbConnection();
    $stmt = $pdo->query("SELECT * FROM categories WHERE status = 1 ORDER BY sort_order ASC");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Şehirleri al
function getCities() {
    $pdo = getDbConnection();
    $stmt = $pdo->query("SELECT * FROM cities WHERE status = 1 ORDER BY sort_order ASC");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Son ilanları al
function getLatestAds($limit = 12) {
    $pdo = getDbConnection();
    $limit = (int)$limit;
    $stmt = $pdo->query("
        SELECT a.*, c.name as category_name, c.color as category_color, 
               ci.name as city_name, u.username,
               (SELECT filename FROM ad_photos WHERE ad_id = a.id AND is_primary = 1 LIMIT 1) as primary_photo
        FROM ads a 
        LEFT JOIN categories c ON a.category_id = c.id 
        LEFT JOIN cities ci ON a.city_id = ci.id 
        LEFT JOIN users u ON a.user_id = u.id
        WHERE a.status = 'active' AND (a.expires_at IS NULL OR a.expires_at > NOW())
        ORDER BY a.featured DESC, a.created_at DESC 
        LIMIT $limit
    ");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Öne çıkan ilanları al
function getFeaturedAds($limit = 8) {
    $pdo = getDbConnection();
    $limit = (int)$limit;
    $stmt = $pdo->query("
        SELECT a.*, c.name as category_name, c.color as category_color, 
               ci.name as city_name, u.username,
               (SELECT filename FROM ad_photos WHERE ad_id = a.id AND is_primary = 1 LIMIT 1) as primary_photo
        FROM ads a 
        LEFT JOIN categories c ON a.category_id = c.id 
        LEFT JOIN cities ci ON a.city_id = ci.id 
        LEFT JOIN users u ON a.user_id = u.id
        WHERE a.status = 'active' AND a.featured = 1 AND (a.expires_at IS NULL OR a.expires_at > NOW())
        ORDER BY a.priority DESC, a.created_at DESC 
        LIMIT $limit
    ");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Kategori ilanlarını al
function getAdsByCategory($categorySlug, $limit = 20, $offset = 0) {
    $pdo = getDbConnection();
    $limit = (int)$limit;
    $offset = (int)$offset;
    
    $stmt = $pdo->prepare("
        SELECT a.*, c.name as category_name, c.color as category_color, 
               ci.name as city_name, u.username,
               (SELECT filename FROM ad_photos WHERE ad_id = a.id AND is_primary = 1 LIMIT 1) as primary_photo
        FROM ads a 
        LEFT JOIN categories c ON a.category_id = c.id 
        LEFT JOIN cities ci ON a.city_id = ci.id 
        LEFT JOIN users u ON a.user_id = u.id
        WHERE a.status = 'active' AND c.slug = ? AND (a.expires_at IS NULL OR a.expires_at > NOW())
        ORDER BY a.featured DESC, a.created_at DESC 
        LIMIT $limit OFFSET $offset
    ");
    $stmt->execute(array($categorySlug));
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Şehir ilanlarını al
function getAdsByCity($citySlug, $limit = 20, $offset = 0) {
    $pdo = getDbConnection();
    $limit = (int)$limit;
    $offset = (int)$offset;
    
    $stmt = $pdo->prepare("
        SELECT a.*, c.name as category_name, c.color as category_color, 
               ci.name as city_name, u.username,
               (SELECT filename FROM ad_photos WHERE ad_id = a.id AND is_primary = 1 LIMIT 1) as primary_photo
        FROM ads a 
        LEFT JOIN categories c ON a.category_id = c.id 
        LEFT JOIN cities ci ON a.city_id = ci.id 
        LEFT JOIN users u ON a.user_id = u.id
        WHERE a.status = 'active' AND ci.slug = ? AND (a.expires_at IS NULL OR a.expires_at > NOW())
        ORDER BY a.featured DESC, a.created_at DESC 
        LIMIT $limit OFFSET $offset
    ");
    $stmt->execute(array($citySlug));
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// İlan detayını al
function getAdDetail($slug) {
    $pdo = getDbConnection();
    $stmt = $pdo->prepare("
        SELECT a.*, c.name as category_name, c.color as category_color, c.slug as category_slug,
               ci.name as city_name, ci.slug as city_slug, u.username, u.email,
               (SELECT GROUP_CONCAT(filename) FROM ad_photos WHERE ad_id = a.id ORDER BY is_primary DESC, id ASC) as photos
        FROM ads a 
        LEFT JOIN categories c ON a.category_id = c.id 
        LEFT JOIN cities ci ON a.city_id = ci.id 
        LEFT JOIN users u ON a.user_id = u.id
        WHERE a.status = 'active' AND a.slug = ? AND (a.expires_at IS NULL OR a.expires_at > NOW())
    ");
    $stmt->execute(array($slug));
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

// İstatistikleri al
function getStats() {
    $pdo = getDbConnection();
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM categories WHERE status = 1");
    $categoryCount = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM cities WHERE status = 1");
    $cityCount = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM ads WHERE status = 'active' AND (expires_at IS NULL OR expires_at > NOW())");
    $adsCount = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM ads WHERE status = 'active' AND DATE(created_at) = CURDATE()");
    $todayCount = $stmt->fetch()['count'];
    
    return array(
        'total_categories' => $categoryCount,
        'total_cities' => $cityCount,
        'total_ads' => $adsCount,
        'today_ads' => $todayCount
    );
}

// Arama yap
function searchAds($searchTerm, $limit = 20, $offset = 0) {
    $pdo = getDbConnection();
    $limit = (int)$limit;
    $offset = (int)$offset;
    
    $stmt = $pdo->prepare("
        SELECT a.*, c.name as category_name, c.color as category_color, 
               ci.name as city_name, u.username,
               (SELECT filename FROM ad_photos WHERE ad_id = a.id AND is_primary = 1 LIMIT 1) as primary_photo
        FROM ads a 
        LEFT JOIN categories c ON a.category_id = c.id 
        LEFT JOIN cities ci ON a.city_id = ci.id 
        LEFT JOIN users u ON a.user_id = u.id
        WHERE a.status = 'active' AND (a.title LIKE ? OR a.description LIKE ?) 
              AND (a.expires_at IS NULL OR a.expires_at > NOW())
        ORDER BY a.featured DESC, a.created_at DESC 
        LIMIT $limit OFFSET $offset
    ");
    $searchTerm = '%' . $searchTerm . '%';
    $stmt->execute(array($searchTerm, $searchTerm));
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Sayfa URL'lerini oluştur
function getPageUrl($page, $params = array()) {
    $baseUrls = array(
        'home' => 'index.php',
        'ads' => 'ads.php',
        'ad_detail' => 'ad-detail.php',
        'category' => 'category.php',
        'city' => 'city.php',
        'search' => 'search.php',
        'login' => 'login.php',
        'register' => 'register.php',
        'profile' => 'profile.php'
    );
    
    $url = isset($baseUrls[$page]) ? $baseUrls[$page] : 'index.php';
    
    if (!empty($params)) {
        $url .= '?' . http_build_query($params);
    }
    
    return $url;
}

// Sayfalama hesapla
function calculatePagination($totalItems, $itemsPerPage, $currentPage) {
    $totalPages = ceil($totalItems / $itemsPerPage);
    $currentPage = max(1, min($currentPage, $totalPages));
    $offset = ($currentPage - 1) * $itemsPerPage;
    
    return array(
        'total_pages' => $totalPages,
        'current_page' => $currentPage,
        'offset' => $offset,
        'has_prev' => $currentPage > 1,
        'has_next' => $currentPage < $totalPages,
        'prev_page' => $currentPage - 1,
        'next_page' => $currentPage + 1
    );
}

// Ortak değişkenler
$settings = getSettings();
$categories = getCategories();
$cities = getCities();
$stats = getStats();

$siteName = isset($settings['site_name']) ? $settings['site_name'] : 'EscortNews';
$siteDescription = isset($settings['site_description']) ? $settings['site_description'] : 'Türkiye\'nin en güvenilir escort haber ve ilan platformu';
?>
