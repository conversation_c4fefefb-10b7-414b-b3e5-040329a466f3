<?php
// Heading
$_['heading_title']          = 'Configuration';

// Text
$_['text_step_3']            = 'Enter your database and administration details';
$_['text_db_connection']     = '1. Please enter your database connection details.';
$_['text_db_administration'] = '2. Please enter a username and password for the administration.';
$_['text_mysqli']            = 'MySQLi';
$_['text_mysql']             = 'MySQL';
$_['text_mpdo']              = 'mPDO';
$_['text_pgsql']             = 'PostgreSQL';

// Entry
$_['entry_db_driver']        = 'DB Driver';
$_['entry_db_hostname']      = 'Hostname';
$_['entry_db_username']      = 'Username';
$_['entry_db_password']      = 'Password';
$_['entry_db_database']      = 'Database';
$_['entry_db_port']          = 'Port';
$_['entry_db_prefix']        = 'Prefix';
$_['entry_username']         = 'Username';
$_['entry_password']         = 'Password';
$_['entry_email']            = 'E-Mail';

// Error
$_['error_db_hostname']      = 'Hostname required!';
$_['error_db_username']      = 'Username required!';
$_['error_db_database']      = 'Database Name required!';
$_['error_db_port']          = 'Database Port required!';
$_['error_db_prefix']        = 'DB Prefix can only contain lowercase characters in the a-z range, 0-9 and underscores';
$_['error_db_connect']       = 'Error: Could not connect to the database please make sure the database server, username and password is correct!';
$_['error_username']         = 'Username required!';
$_['error_password']         = 'Password required!';
$_['error_email']            = 'Invalid E-Mail!';
$_['error_config']           = 'Error: Could not write to config.php please check you have set the correct permissions on: ';
