<?php
echo "<h1>🔧 Debug Sayfası</h1>";
echo "<h2>PHP Bilgileri:</h2>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Server: " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p>Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p>Script Name: " . $_SERVER['SCRIPT_NAME'] . "</p>";

echo "<h2>Dosya Kontrolleri:</h2>";
echo "<p>CodeIgniter index.php: " . (file_exists('index.php') ? '✅ Mevcut' : '❌ Yok') . "</p>";
echo "<p>App klasörü: " . (is_dir('../app') ? '✅ Mevcut' : '❌ Yok') . "</p>";
echo "<p>.env dosyası: " . (file_exists('../.env') ? '✅ Mevcut' : '❌ Yok') . "</p>";

echo "<h2>Veritabanı Testi:</h2>";
try {
    $host = 'localhost';
    $dbname = 'escort_site';
    $username = 'root';
    $password = '';
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    echo "<p style='color: green;'>✅ Veritabanı bağlantısı başarılı!</p>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM settings");
    $result = $stmt->fetch();
    echo "<p>Ayar sayısı: " . $result['count'] . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Veritabanı hatası: " . $e->getMessage() . "</p>";
}

echo "<h2>Linkler:</h2>";
echo "<p><a href='index.php'>CodeIgniter Ana Sayfa</a></p>";
echo "<p><a href='../'>Proje Ana Dizini</a></p>";
?>
