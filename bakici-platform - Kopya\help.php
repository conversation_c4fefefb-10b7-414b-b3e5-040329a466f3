<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$page_title = 'Yardım Merkezi';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bakıcı Platform</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c5aa0;
        }

        .help-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .help-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }

        .faq-item {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            margin-bottom: 10px;
        }

        .faq-header {
            background: #f8f9fa;
            border-radius: 10px 10px 0 0;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .faq-header:hover {
            background: #e9ecef;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="index.php">
                <i class="bi bi-heart-fill me-2"></i>Bakıcı Platform
            </a>

            <div class="navbar-nav ms-auto">
                <?php if (isset($_SESSION['user_id'])): ?>
                    <a class="nav-link" href="dashboard.php">Dashboard</a>
                    <a class="nav-link" href="messages.php">Mesajlar</a>
                    <a class="nav-link" href="auth/logout.php">Çıkış</a>
                <?php else: ?>
                    <a class="nav-link" href="auth/login.php">Giriş</a>
                    <a class="nav-link" href="auth/register.php">Kayıt</a>
                <?php endif; ?>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Sayfa Başlığı -->
        <div class="row mb-5">
            <div class="col text-center">
                <h2 class="fw-bold">Yardım Merkezi</h2>
                <p class="text-muted">Size nasıl yardımcı olabiliriz?</p>
            </div>
        </div>

        <!-- Hızlı Yardım Kartları -->
        <div class="row g-4 mb-5">
            <div class="col-md-4">
                <div class="help-card card h-100 text-center">
                    <div class="card-body p-4">
                        <i class="bi bi-person-plus text-primary" style="font-size: 3rem;"></i>
                        <h5 class="mt-3">Nasıl Kayıt Olurum?</h5>
                        <p class="text-muted">Platform kayıt işlemi ve hesap oluşturma</p>
                        <a href="#kayit" class="btn btn-outline-primary">Detaylar</a>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="help-card card h-100 text-center">
                    <div class="card-body p-4">
                        <i class="bi bi-briefcase text-success" style="font-size: 3rem;"></i>
                        <h5 class="mt-3">İş İlanı Nasıl Verilir?</h5>
                        <p class="text-muted">Bakıcı arama ve ilan verme süreci</p>
                        <a href="#ilan" class="btn btn-outline-success">Detaylar</a>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="help-card card h-100 text-center">
                    <div class="card-body p-4">
                        <i class="bi bi-credit-card text-warning" style="font-size: 3rem;"></i>
                        <h5 class="mt-3">Ödeme Nasıl Yapılır?</h5>
                        <p class="text-muted">Güvenli ödeme yöntemleri ve süreç</p>
                        <a href="#odeme" class="btn btn-outline-warning">Detaylar</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sık Sorulan Sorular -->
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <h3 class="mb-4">Sık Sorulan Sorular</h3>

                <!-- FAQ Item 1 -->
                <div class="faq-item" id="kayit">
                    <div class="faq-header p-3" data-bs-toggle="collapse" data-bs-target="#faq1">
                        <h6 class="mb-0">
                            <i class="bi bi-plus-circle me-2"></i>
                            Platform nasıl kullanılır?
                        </h6>
                    </div>
                    <div id="faq1" class="collapse">
                        <div class="p-3">
                            <p>Bakıcı Platform'u kullanmak çok kolay:</p>
                            <ol>
                                <li><strong>Kayıt Olun:</strong> Aile veya bakıcı olarak hesap oluşturun</li>
                                <li><strong>Profil Tamamlayın:</strong> Bilgilerinizi eksiksiz doldurun</li>
                                <li><strong>Arama Yapın:</strong> İhtiyacınıza uygun bakıcı/iş bulun</li>
                                <li><strong>İletişim Kurun:</strong> Mesajlaşma sistemi ile konuşun</li>
                                <li><strong>Anlaşma Yapın:</strong> Güvenli ödeme ile işlemi tamamlayın</li>
                            </ol>
                        </div>
                    </div>
                </div>

                <!-- FAQ Item 2 -->
                <div class="faq-item" id="ilan">
                    <div class="faq-header p-3" data-bs-toggle="collapse" data-bs-target="#faq2">
                        <h6 class="mb-0">
                            <i class="bi bi-plus-circle me-2"></i>
                            İş ilanı nasıl verilir?
                        </h6>
                    </div>
                    <div id="faq2" class="collapse">
                        <div class="p-3">
                            <p>İş ilanı vermek için:</p>
                            <ul>
                                <li>Dashboard'dan "İlan Ver" butonuna tıklayın</li>
                                <li>İş detaylarını (yaş, cinsiyet, deneyim) belirtin</li>
                                <li>Çalışma saatleri ve ücret bilgilerini girin</li>
                                <li>İlanınızı yayınlayın ve başvuruları bekleyin</li>
                                <li>Uygun adaylarla mesajlaşarak görüşme ayarlayın</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- FAQ Item 3 -->
                <div class="faq-item" id="odeme">
                    <div class="faq-header p-3" data-bs-toggle="collapse" data-bs-target="#faq3">
                        <h6 class="mb-0">
                            <i class="bi bi-plus-circle me-2"></i>
                            Ödeme sistemi nasıl çalışır?
                        </h6>
                    </div>
                    <div id="faq3" class="collapse">
                        <div class="p-3">
                            <p>Güvenli ödeme sistemi:</p>
                            <ul>
                                <li><strong>PayTR Entegrasyonu:</strong> Güvenli ödeme altyapısı</li>
                                <li><strong>Kredi Kartı:</strong> Tüm banka kartları kabul edilir</li>
                                <li><strong>Havale/EFT:</strong> Banka transferi ile ödeme</li>
                                <li><strong>Güvence:</strong> Para iade garantisi</li>
                                <li><strong>Komisyon:</strong> Sadece başarılı işlemlerden</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- FAQ Item 4 -->
                <div class="faq-item">
                    <div class="faq-header p-3" data-bs-toggle="collapse" data-bs-target="#faq4">
                        <h6 class="mb-0">
                            <i class="bi bi-plus-circle me-2"></i>
                            Güvenlik önlemleri nelerdir?
                        </h6>
                    </div>
                    <div id="faq4" class="collapse">
                        <div class="p-3">
                            <p>Güvenliğiniz için aldığımız önlemler:</p>
                            <ul>
                                <li><strong>Kimlik Doğrulama:</strong> Tüm kullanıcılar doğrulanır</li>
                                <li><strong>Referans Kontrolü:</strong> Geçmiş deneyimler kontrol edilir</li>
                                <li><strong>Güvenli Mesajlaşma:</strong> Platform üzerinden iletişim</li>
                                <li><strong>Değerlendirme Sistemi:</strong> Kullanıcı puanlama sistemi</li>
                                <li><strong>7/24 Destek:</strong> Sorun durumunda anında müdahale</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- FAQ Item 5 -->
                <div class="faq-item">
                    <div class="faq-header p-3" data-bs-toggle="collapse" data-bs-target="#faq5">
                        <h6 class="mb-0">
                            <i class="bi bi-plus-circle me-2"></i>
                            İletişim bilgileri nelerdir?
                        </h6>
                    </div>
                    <div id="faq5" class="collapse">
                        <div class="p-3">
                            <p>Bizimle iletişime geçin:</p>
                            <ul>
                                <li><strong>Email:</strong> <EMAIL></li>
                                <li><strong>Telefon:</strong> +90 ************</li>
                                <li><strong>Çalışma Saatleri:</strong> Pazartesi-Cuma 09:00-18:00</li>
                                <li><strong>Canlı Destek:</strong> Platform üzerinden mesaj</li>
                                <li><strong>Acil Durum:</strong> 7/24 destek hattı</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- İletişim Kartı -->
        <div class="row mt-5">
            <div class="col-lg-6 mx-auto">
                <div class="card text-center">
                    <div class="card-body p-4">
                        <i class="bi bi-headset text-primary" style="font-size: 3rem;"></i>
                        <h5 class="mt-3">Hala Yardıma İhtiyacınız Var mı?</h5>
                        <p class="text-muted">Destek ekibimiz size yardımcı olmaya hazır</p>
                        <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                            <?php if (isset($_SESSION['user_id'])): ?>
                                <a href="messages.php" class="btn btn-primary">
                                    <i class="bi bi-chat-dots me-2"></i>Mesaj Gönder
                                </a>
                            <?php else: ?>
                                <a href="contact.php" class="btn btn-primary">
                                    <i class="bi bi-envelope me-2"></i>İletişim
                                </a>
                            <?php endif; ?>
                            <a href="tel:+902125550123" class="btn btn-outline-primary">
                                <i class="bi bi-telephone me-2"></i>Ara
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // FAQ açılırken ikonu değiştir
        document.querySelectorAll('[data-bs-toggle="collapse"]').forEach(function(element) {
            element.addEventListener('click', function() {
                const icon = this.querySelector('i');
                const target = document.querySelector(this.getAttribute('data-bs-target'));

                target.addEventListener('shown.bs.collapse', function() {
                    icon.className = 'bi bi-dash-circle me-2';
                });

                target.addEventListener('hidden.bs.collapse', function() {
                    icon.className = 'bi bi-plus-circle me-2';
                });
            });
        });
    </script>
</body>
</html>
