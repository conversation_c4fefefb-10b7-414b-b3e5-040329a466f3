<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Giriş kontrolü
if (!isLoggedIn()) {
    header('Location: auth/login.php');
    exit;
}

// Sadece aile kullanıcıları değerlendirme yapabilir
if ($_SESSION['user_type'] !== 'family') {
    header('Location: dashboard.php?error=permission_denied');
    exit;
}

$caregiver_id = intval($_GET['caregiver_id'] ?? 0);
if (!$caregiver_id) {
    header('Location: caregivers.php?error=invalid_caregiver');
    exit;
}

// Bakıcı bilgilerini getir
try {
    $sql = "SELECT u.*, cp.* FROM users u 
            LEFT JOIN caregiver_profiles cp ON u.id = cp.user_id 
            WHERE u.id = ? AND u.user_type = 'caregiver' AND u.status = 'active'";
    $stmt = $db->prepare($sql);
    $stmt->execute([$caregiver_id]);
    $caregiver = $stmt->fetch();
    
    if (!$caregiver) {
        header('Location: caregivers.php?error=caregiver_not_found');
        exit;
    }
    
    // Daha önce değerlendirme yapılmış mı kontrol et
    $existing_review_sql = "SELECT id FROM reviews WHERE reviewer_id = ? AND reviewed_id = ?";
    $existing_review_stmt = $db->prepare($existing_review_sql);
    $existing_review_stmt->execute([$_SESSION['user_id'], $caregiver_id]);
    $existing_review = $existing_review_stmt->fetch();
    
    if ($existing_review) {
        header('Location: caregiver-profile.php?id=' . $caregiver_id . '&error=already_reviewed');
        exit;
    }
    
    // Bu bakıcıdan hizmet alınmış mı kontrol et (opsiyonel)
    $service_check_sql = "SELECT COUNT(*) FROM job_applications ja 
                         JOIN job_listings jl ON ja.job_id = jl.id 
                         WHERE ja.caregiver_id = ? AND jl.user_id = ? AND ja.status IN ('hired', 'accepted')";
    $service_check_stmt = $db->prepare($service_check_sql);
    $service_check_stmt->execute([$caregiver_id, $_SESSION['user_id']]);
    $has_service = $service_check_stmt->fetchColumn() > 0;
    
} catch (PDOException $e) {
    header('Location: caregivers.php?error=database_error');
    exit;
}

// Form gönderildi
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $rating = intval($_POST['rating'] ?? 0);
    $comment = trim($_POST['comment'] ?? '');
    $service_type = trim($_POST['service_type'] ?? '');
    $would_recommend = isset($_POST['would_recommend']) ? 1 : 0;
    
    $errors = [];
    
    // Validasyon
    if ($rating < 1 || $rating > 5) {
        $errors[] = 'Lütfen 1-5 arası bir puan verin.';
    }
    
    if (empty($comment)) {
        $errors[] = 'Yorum gereklidir.';
    } elseif (strlen($comment) < 20) {
        $errors[] = 'Yorum en az 20 karakter olmalıdır.';
    } elseif (strlen($comment) > 1000) {
        $errors[] = 'Yorum en fazla 1000 karakter olabilir.';
    }
    
    if (empty($service_type)) {
        $errors[] = 'Hizmet türü seçiniz.';
    }
    
    // Değerlendirme kaydet
    if (empty($errors)) {
        try {
            // Reviews tablosu var mı kontrol et
            $table_check = $db->query("SHOW TABLES LIKE 'reviews'")->fetch();
            if (!$table_check) {
                // Reviews tablosunu oluştur
                $create_table_sql = "CREATE TABLE reviews (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    reviewer_id INT NOT NULL,
                    reviewed_id INT NOT NULL,
                    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
                    comment TEXT NOT NULL,
                    service_type VARCHAR(50),
                    would_recommend BOOLEAN DEFAULT FALSE,
                    is_active BOOLEAN DEFAULT TRUE,
                    is_approved BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (reviewer_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY (reviewed_id) REFERENCES users(id) ON DELETE CASCADE,
                    UNIQUE KEY unique_review (reviewer_id, reviewed_id)
                )";
                $db->exec($create_table_sql);
            }

            $db->beginTransaction();

            // Değerlendirme ekle
            $insert_sql = "INSERT INTO reviews (reviewer_id, reviewed_id, rating, comment, service_type, would_recommend, created_at)
                          VALUES (?, ?, ?, ?, ?, ?, NOW())";
            $insert_stmt = $db->prepare($insert_sql);
            $insert_stmt->execute([$_SESSION['user_id'], $caregiver_id, $rating, $comment, $service_type, $would_recommend]);
            
            // Bakıcının ortalama puanını güncelle
            $avg_sql = "SELECT AVG(rating) as avg_rating, COUNT(*) as total_reviews 
                       FROM reviews WHERE reviewed_id = ? AND is_active = 1";
            $avg_stmt = $db->prepare($avg_sql);
            $avg_stmt->execute([$caregiver_id]);
            $avg_data = $avg_stmt->fetch();
            
            // Caregiver profiles tablosunu güncelle
            $update_profile_sql = "UPDATE caregiver_profiles SET 
                                  rating = ?, 
                                  total_reviews = ?, 
                                  updated_at = NOW() 
                                  WHERE user_id = ?";
            $update_profile_stmt = $db->prepare($update_profile_sql);
            $update_profile_stmt->execute([
                round($avg_data['avg_rating'], 2), 
                $avg_data['total_reviews'], 
                $caregiver_id
            ]);
            
            $db->commit();
            
            header('Location: caregiver-profile.php?id=' . $caregiver_id . '&success=review_added');
            exit;
            
        } catch (PDOException $e) {
            $db->rollBack();
            $errors[] = 'Değerlendirme kaydedilirken hata oluştu: ' . $e->getMessage();
        }
    }
}

$page_title = 'Bakıcı Değerlendir';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bakıcı Platform</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c5aa0;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
        }
        
        .star-rating {
            font-size: 2rem;
            color: #ddd;
            cursor: pointer;
            transition: color 0.2s;
        }
        
        .star-rating:hover,
        .star-rating.active {
            color: #ffc107;
        }
        
        .char-counter {
            font-size: 0.875rem;
            color: #6c757d;
        }
        
        .char-counter.warning {
            color: #fd7e14;
        }
        
        .char-counter.danger {
            color: #dc3545;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="index.php">
                <i class="bi bi-heart-fill me-2"></i>Bakıcı Platform
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.php">Dashboard</a>
                <a class="nav-link" href="caregivers.php">Bakıcılar</a>
                <a class="nav-link" href="auth/logout.php">Çıkış</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="bi bi-star me-2"></i>Bakıcı Değerlendir</h4>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0">
                                <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                                <li class="breadcrumb-item"><a href="caregivers.php">Bakıcılar</a></li>
                                <li class="breadcrumb-item"><a href="caregiver-profile.php?id=<?php echo $caregiver['id']; ?>"><?php echo htmlspecialchars($caregiver['full_name']); ?></a></li>
                                <li class="breadcrumb-item active">Değerlendir</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="card-body">
                        <!-- Errors -->
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <!-- Bakıcı Bilgileri -->
                        <div class="alert alert-info">
                            <h6><i class="bi bi-person me-2"></i>Değerlendireceğiniz Bakıcı</h6>
                            <div class="d-flex align-items-center">
                                <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center me-3" 
                                     style="width: 50px; height: 50px;">
                                    <i class="bi bi-person"></i>
                                </div>
                                <div>
                                    <strong><?php echo htmlspecialchars($caregiver['full_name']); ?></strong>
                                    <br>
                                    <small class="text-muted">
                                        <?php echo $caregiver['experience_years'] ?? 0; ?> yıl deneyim
                                        <?php if ($caregiver['rating'] > 0): ?>
                                            | <?php echo number_format($caregiver['rating'], 1); ?>★ (<?php echo $caregiver['total_reviews'] ?? 0; ?> değerlendirme)
                                        <?php endif; ?>
                                    </small>
                                </div>
                            </div>
                        </div>

                        <?php if (!$has_service): ?>
                            <div class="alert alert-warning">
                                <h6><i class="bi bi-exclamation-triangle me-2"></i>Bilgi</h6>
                                <p class="mb-0">Bu bakıcıdan henüz hizmet almamış görünüyorsunuz. Değerlendirme yapmadan önce hizmet almanızı öneririz.</p>
                            </div>
                        <?php endif; ?>

                        <!-- Değerlendirme Formu -->
                        <form method="POST">
                            <div class="row g-3">
                                <div class="col-12">
                                    <label class="form-label">Puanınız *</label>
                                    <div class="d-flex align-items-center">
                                        <div class="me-3">
                                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                                <i class="bi bi-star-fill star-rating" data-rating="<?php echo $i; ?>"></i>
                                            <?php endfor; ?>
                                        </div>
                                        <span id="ratingText" class="text-muted">Puan seçin</span>
                                    </div>
                                    <input type="hidden" id="rating" name="rating" value="<?php echo $_POST['rating'] ?? ''; ?>" required>
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="service_type" class="form-label">Hizmet Türü *</label>
                                    <select class="form-select" id="service_type" name="service_type" required>
                                        <option value="">Seçiniz</option>
                                        <option value="child_care" <?php echo ($_POST['service_type'] ?? '') === 'child_care' ? 'selected' : ''; ?>>Çocuk Bakımı</option>
                                        <option value="elderly_care" <?php echo ($_POST['service_type'] ?? '') === 'elderly_care' ? 'selected' : ''; ?>>Yaşlı Bakımı</option>
                                        <option value="patient_care" <?php echo ($_POST['service_type'] ?? '') === 'patient_care' ? 'selected' : ''; ?>>Hasta Bakımı</option>
                                        <option value="house_cleaning" <?php echo ($_POST['service_type'] ?? '') === 'house_cleaning' ? 'selected' : ''; ?>>Ev Temizliği</option>
                                        <option value="companion" <?php echo ($_POST['service_type'] ?? '') === 'companion' ? 'selected' : ''; ?>>Refakatçi</option>
                                        <option value="other" <?php echo ($_POST['service_type'] ?? '') === 'other' ? 'selected' : ''; ?>>Diğer</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-6">
                                    <label class="form-label">Tavsiye Eder misiniz?</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="would_recommend" name="would_recommend"
                                               <?php echo isset($_POST['would_recommend']) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="would_recommend">
                                            Evet, bu bakıcıyı başkalarına tavsiye ederim
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="col-12">
                                    <label for="comment" class="form-label">Yorumunuz *</label>
                                    <textarea class="form-control" id="comment" name="comment" rows="6" 
                                              placeholder="Bakıcı hakkındaki deneyimlerinizi, memnuniyetinizi ve önerilerinizi paylaşın..." 
                                              required maxlength="1000"><?php echo htmlspecialchars($_POST['comment'] ?? ''); ?></textarea>
                                    <div class="d-flex justify-content-between">
                                        <div class="form-text">En az 20, en fazla 1000 karakter</div>
                                        <div class="char-counter" id="charCounter">
                                            <span id="charCount"><?php echo strlen($_POST['comment'] ?? ''); ?></span>/1000
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="alert alert-info mt-4">
                                <h6><i class="bi bi-info-circle me-2"></i>Değerlendirme Kuralları</h6>
                                <ul class="mb-0">
                                    <li>Objektif ve yapıcı yorumlar yazın</li>
                                    <li>Kişisel bilgileri paylaşmayın</li>
                                    <li>Saygılı bir dil kullanın</li>
                                    <li>Gerçek deneyimlerinizi paylaşın</li>
                                </ul>
                            </div>
                            
                            <div class="d-flex justify-content-between mt-4">
                                <a href="caregiver-profile.php?id=<?php echo $caregiver['id']; ?>" class="btn btn-secondary">
                                    <i class="bi bi-arrow-left me-2"></i>Geri Dön
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-star me-2"></i>Değerlendirmeyi Kaydet
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Yıldız puanlama sistemi
        const stars = document.querySelectorAll('.star-rating');
        const ratingInput = document.getElementById('rating');
        const ratingText = document.getElementById('ratingText');
        
        const ratingTexts = {
            1: 'Çok Kötü',
            2: 'Kötü',
            3: 'Orta',
            4: 'İyi',
            5: 'Mükemmel'
        };
        
        stars.forEach(star => {
            star.addEventListener('click', function() {
                const rating = parseInt(this.dataset.rating);
                ratingInput.value = rating;
                ratingText.textContent = ratingTexts[rating];
                
                // Yıldızları güncelle
                stars.forEach((s, index) => {
                    if (index < rating) {
                        s.classList.add('active');
                    } else {
                        s.classList.remove('active');
                    }
                });
            });
            
            star.addEventListener('mouseover', function() {
                const rating = parseInt(this.dataset.rating);
                stars.forEach((s, index) => {
                    if (index < rating) {
                        s.style.color = '#ffc107';
                    } else {
                        s.style.color = '#ddd';
                    }
                });
            });
        });
        
        // Mouse leave olayı
        document.querySelector('.star-rating').parentElement.addEventListener('mouseleave', function() {
            const currentRating = parseInt(ratingInput.value) || 0;
            stars.forEach((s, index) => {
                if (index < currentRating) {
                    s.style.color = '#ffc107';
                } else {
                    s.style.color = '#ddd';
                }
            });
        });
        
        // Karakter sayacı
        const commentTextarea = document.getElementById('comment');
        const charCount = document.getElementById('charCount');
        const charCounter = document.getElementById('charCounter');
        
        function updateCharCount() {
            const length = commentTextarea.value.length;
            charCount.textContent = length;
            
            charCounter.className = 'char-counter';
            if (length > 900) {
                charCounter.classList.add('danger');
            } else if (length > 800) {
                charCounter.classList.add('warning');
            }
        }
        
        commentTextarea.addEventListener('input', updateCharCount);
        updateCharCount();
        
        // Form validasyonu
        document.querySelector('form').addEventListener('submit', function(e) {
            const rating = parseInt(ratingInput.value);
            const comment = commentTextarea.value.trim();
            
            if (!rating || rating < 1 || rating > 5) {
                e.preventDefault();
                alert('Lütfen 1-5 arası bir puan verin.');
                return;
            }
            
            if (comment.length < 20) {
                e.preventDefault();
                alert('Yorum en az 20 karakter olmalıdır.');
                commentTextarea.focus();
                return;
            }
        });
        
        // Sayfa yüklendiğinde mevcut puanı göster
        const currentRating = parseInt(ratingInput.value) || 0;
        if (currentRating > 0) {
            ratingText.textContent = ratingTexts[currentRating];
            stars.forEach((s, index) => {
                if (index < currentRating) {
                    s.classList.add('active');
                }
            });
        }
    </script>
</body>
</html>
