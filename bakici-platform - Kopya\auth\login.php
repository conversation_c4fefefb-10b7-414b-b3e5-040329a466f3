<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Zaten giriş yapmışsa dashboard'a yönlendir
if (isLoggedIn()) {
    redirect('../dashboard.php');
}

$error_message = '';
$success_message = '';

// Form gönderildi mi?
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrf_token = safeArray($_POST, 'csrf_token', '');
    
    if (!validateCSRFToken($csrf_token)) {
        $error_message = 'Güvenlik hatası. Lütfen sayfayı yenileyin.';
    } else {
        $email = trim(safeArray($_POST, 'email', ''));
        $password = safeArray($_POST, 'password', '');
        $remember_me = safeArray($_POST, 'remember_me', false);
        
        // Validasyon
        if (empty($email)) {
            $error_message = 'E-posta adresi gereklidir.';
        } elseif (!validateEmail($email)) {
            $error_message = 'Geçerli bir e-posta adresi girin.';
        } elseif (empty($password)) {
            $error_message = 'Şifre gereklidir.';
        } else {
            try {
                // Kullanıcıyı bul
                $sql = "SELECT id, email, password, full_name, user_type, is_active, is_verified FROM users WHERE email = ?";
                $stmt = $db->prepare($sql);
                $stmt->execute([$email]);
                $user = $stmt->fetch();
                
                if ($user && password_verify($password, $user['password'])) {
                    // Hesap aktif mi?
                    if (!$user['is_active']) {
                        $error_message = 'Hesabınız deaktif edilmiştir. Lütfen destek ekibi ile iletişime geçin.';
                    } else {
                        // Giriş başarılı - Session bilgilerini ayarla
                        $_SESSION['user_id'] = $user['id'];
                        $_SESSION['email'] = $user['email'];
                        $_SESSION['full_name'] = $user['full_name'];
                        $_SESSION['user_type'] = $user['user_type'];
                        $_SESSION['is_verified'] = $user['is_verified'];
                        $_SESSION['login_time'] = time();
                        $_SESSION['last_activity'] = time();
                        $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? '';
                        $_SESSION['session_id'] = session_id();
                        
                        // Remember me cookie
                        if ($remember_me) {
                            $token = bin2hex(random_bytes(32));
                            setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/'); // 30 gün
                            
                            // Token'ı veritabanına kaydet
                            $update_sql = "UPDATE users SET remember_token = ? WHERE id = ?";
                            $update_stmt = $db->prepare($update_sql);
                            $update_stmt->execute([$token, $user['id']]);
                        }
                        
                        // Son giriş zamanını güncelle
                        $update_sql = "UPDATE users SET last_login = NOW(), login_count = login_count + 1 WHERE id = ?";
                        $update_stmt = $db->prepare($update_sql);
                        $update_stmt->execute([$user['id']]);
                        
                        // Aktivite kaydı
                        logActivity($user['id'], 'login', 'Kullanıcı giriş yaptı', 'users', $user['id']);
                        
                        // Yönlendirme
                        $redirect = safeArray($_GET, 'redirect', 'dashboard.php');
                        redirect('../' . $redirect);
                    }
                } else {
                    $error_message = 'E-posta adresi veya şifre hatalı.';
                    
                    // Başarısız giriş denemesi kaydet
                    if ($user) {
                        logActivity($user['id'], 'failed_login', 'Başarısız giriş denemesi', 'users', $user['id']);
                    }
                }
            } catch (PDOException $e) {
                $error_message = 'Giriş yapılırken bir hata oluştu. Lütfen tekrar deneyin.';
            }
        }
    }
}

// URL'den gelen mesajları kontrol et
if (isset($_GET['message'])) {
    switch ($_GET['message']) {
        case 'registered':
            $success_message = 'Kayıt işleminiz başarıyla tamamlandı. Giriş yapabilirsiniz.';
            break;
        case 'verified':
            $success_message = 'E-posta adresiniz doğrulandı. Giriş yapabilirsiniz.';
            break;
        case 'logout':
            $success_message = 'Başarıyla çıkış yaptınız.';
            break;
        case 'password_reset':
            $success_message = 'Şifreniz başarıyla sıfırlandı. Yeni şifrenizle giriş yapabilirsiniz.';
            break;
    }
}

// Timeout ve güvenlik mesajları
if (isset($_GET['timeout'])) {
    $error_message = 'Oturumunuzun süresi doldu. Güvenliğiniz için tekrar giriş yapmanız gerekiyor.';
} elseif (isset($_GET['required'])) {
    $error_message = 'Bu sayfaya erişmek için giriş yapmanız gerekiyor.';
} elseif (isset($_GET['security'])) {
    $error_message = 'Güvenlik nedeniyle oturumunuz sonlandırıldı. Lütfen tekrar giriş yapın.';
}

$page_title = 'Giriş Yap';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c5aa0;
            --secondary-color: #f8f9fa;
        }
        
        body {
            background: linear-gradient(135deg, var(--primary-color) 0%, #1e3d72 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        
        .auth-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
        }
        
        .auth-header {
            background: var(--primary-color);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .auth-body {
            padding: 2rem;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            border-radius: 25px;
            padding: 12px 30px;
        }
        
        .btn-primary:hover {
            background-color: #1e3d72;
            border-color: #1e3d72;
            transform: translateY(-2px);
        }
        
        .input-group-text {
            background-color: var(--secondary-color);
            border-color: #dee2e6;
        }
        
        .demo-accounts {
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .demo-account {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid var(--primary-color);
        }
        
        .demo-account:last-child {
            margin-bottom: 0;
        }
        
        .social-login {
            border-top: 1px solid #dee2e6;
            padding-top: 20px;
            margin-top: 20px;
        }
        
        .btn-social {
            width: 100%;
            margin-bottom: 10px;
            border-radius: 25px;
            padding: 12px;
        }
        
        .btn-google {
            background-color: #db4437;
            border-color: #db4437;
            color: white;
        }
        
        .btn-facebook {
            background-color: #3b5998;
            border-color: #3b5998;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="auth-card mx-auto">
                    <div class="auth-header">
                        <h3 class="mb-0">
                            <i class="bi bi-heart-fill me-2"></i><?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?>
                        </h3>
                        <p class="mb-0 mt-2">Hesabınıza giriş yapın</p>
                    </div>
                    
                    <div class="auth-body">
                        <?php if ($error_message): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="bi bi-exclamation-triangle me-2"></i><?php echo escape($error_message); ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($success_message): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="bi bi-check-circle me-2"></i><?php echo escape($success_message); ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" id="loginForm">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">E-posta Adresi</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-envelope"></i>
                                    </span>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?php echo escape(safeArray($_POST, 'email', '')); ?>" 
                                           placeholder="<EMAIL>" required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">Şifre</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-lock"></i>
                                    </span>
                                    <input type="password" class="form-control" id="password" name="password" 
                                           placeholder="Şifrenizi girin" required>
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me" value="1">
                                        <label class="form-check-label" for="remember_me">
                                            Beni hatırla
                                        </label>
                                    </div>
                                </div>
                                <div class="col text-end">
                                    <a href="forgot-password.php" class="text-decoration-none small">
                                        Şifremi unuttum
                                    </a>
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-box-arrow-in-right me-2"></i>Giriş Yap
                                </button>
                            </div>
                        </form>
                        
                        <div class="text-center mt-4">
                            <p class="mb-0">
                                Hesabınız yok mu? 
                                <a href="register.php" class="text-decoration-none fw-bold">
                                    Hemen üye olun
                                </a>
                            </p>
                        </div>
                        
                        <div class="text-center mt-3">
                            <a href="../index.php" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-2"></i>Ana Sayfaya Dön
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Demo Hesaplar -->
                <div class="demo-accounts">
                    <h6 class="text-center mb-3">
                        <i class="bi bi-info-circle me-2"></i>Demo Hesaplar
                    </h6>
                    <div class="demo-account">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong class="text-primary">Admin Hesabı</strong>
                                <br>
                                <small class="text-muted"><EMAIL></small>
                            </div>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="fillLogin('<EMAIL>', 'admin123')">
                                Kullan
                            </button>
                        </div>
                    </div>
                    <div class="demo-account">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong class="text-success">Test Hesabı</strong>
                                <br>
                                <small class="text-muted">Kayıt olarak test edebilirsiniz</small>
                            </div>
                            <a href="register.php" class="btn btn-sm btn-outline-success">
                                Kayıt Ol
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Şifre göster/gizle
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.className = 'bi bi-eye-slash';
            } else {
                passwordInput.type = 'password';
                icon.className = 'bi bi-eye';
            }
        });

        // Demo hesap bilgilerini doldur
        function fillLogin(email, password) {
            document.getElementById('email').value = email;
            document.getElementById('password').value = password;

            // Form alanlarını vurgula
            const emailField = document.getElementById('email');
            const passwordField = document.getElementById('password');

            emailField.classList.add('border-success');
            passwordField.classList.add('border-success');

            setTimeout(() => {
                emailField.classList.remove('border-success');
                passwordField.classList.remove('border-success');
            }, 2000);
        }

        // Form validasyonu
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const email = document.getElementById('email').value.trim();
            const password = document.getElementById('password').value;

            if (!email) {
                e.preventDefault();
                showAlert('E-posta adresi gereklidir.', 'danger');
                return;
            }

            if (!password) {
                e.preventDefault();
                showAlert('Şifre gereklidir.', 'danger');
                return;
            }

            // E-posta formatı kontrolü
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                e.preventDefault();
                showAlert('Geçerli bir e-posta adresi girin.', 'danger');
                return;
            }

            // Loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Giriş yapılıyor...';
            submitBtn.disabled = true;

            // Form gönderilirken hata olursa butonu eski haline getir
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 5000);
        });

        // Alert gösterme fonksiyonu
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                <i class="bi bi-exclamation-triangle me-2"></i>${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            const form = document.getElementById('loginForm');
            form.insertBefore(alertDiv, form.firstChild);

            // 5 saniye sonra otomatik kapat
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        // Sayfa yüklendiğinde e-posta alanına odaklan
        window.addEventListener('load', function() {
            document.getElementById('email').focus();
        });
    </script>
</body>
</html>
