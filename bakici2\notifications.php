<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Giriş kontrolü
requireLogin();

$user_id = $_SESSION['user_id'];

// Bildirimleri getir
try {
    $sql = "SELECT * FROM notifications WHERE user_id = ? ORDER BY created_at DESC";
    $stmt = $db->prepare($sql);
    $stmt->execute([$user_id]);
    $notifications = $stmt->fetchAll();
    
    // Okunmamış bildirimleri okundu olarak işaretle
    $update_sql = "UPDATE notifications SET is_read = 1 WHERE user_id = ? AND is_read = 0";
    $update_stmt = $db->prepare($update_sql);
    $update_stmt->execute([$user_id]);
    
} catch (PDOException $e) {
    $notifications = [];
}

$page_title = 'Bildirimler';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c5aa0;
        }
        
        .notification-item {
            border-left: 4px solid var(--primary-color);
            background: rgba(44, 90, 160, 0.05);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        
        .notification-item:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .notification-new {
            border-left-color: #28a745;
            background: rgba(40, 167, 69, 0.05);
        }
        
        .navbar {
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="index.php">
                <i class="bi bi-heart-fill me-2"></i><?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="caregivers.php">Bakıcı Ara</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="jobs.php">İş İlanları</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">Hakkımızda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">İletişim</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i><?php echo escape($_SESSION['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="dashboard.php"><i class="bi bi-speedometer2 me-2"></i>Dashboard</a></li>
                            <li><a class="dropdown-item" href="profile.php"><i class="bi bi-person me-2"></i>Profil</a></li>
                            <li><a class="dropdown-item" href="messages.php"><i class="bi bi-chat-dots me-2"></i>Mesajlar</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="auth/logout.php"><i class="bi bi-box-arrow-right me-2"></i>Çıkış</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Sayfa Başlığı -->
        <div class="row mb-4">
            <div class="col">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                        <li class="breadcrumb-item active">Bildirimler</li>
                    </ol>
                </nav>
                <h2 class="fw-bold">Bildirimler</h2>
                <p class="text-muted">Tüm bildirimlerinizi buradan takip edebilirsiniz</p>
            </div>
        </div>

        <!-- Bildirimler -->
        <div class="row">
            <div class="col-lg-8">
                <?php if (!empty($notifications)): ?>
                    <?php foreach ($notifications as $notification): ?>
                    <div class="notification-item <?php echo !$notification['is_read'] ? 'notification-new' : ''; ?>">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center mb-2">
                                    <div class="me-3">
                                        <?php
                                        $icon = 'bi-bell';
                                        switch ($notification['type']) {
                                            case 'welcome': $icon = 'bi-heart'; break;
                                            case 'job_application': $icon = 'bi-briefcase'; break;
                                            case 'message': $icon = 'bi-chat-dots'; break;
                                            case 'payment': $icon = 'bi-credit-card'; break;
                                            case 'review': $icon = 'bi-star'; break;
                                        }
                                        ?>
                                        <i class="<?php echo $icon; ?> text-primary" style="font-size: 1.5rem;"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1 fw-bold"><?php echo escape($notification['title']); ?></h6>
                                        <p class="mb-2 text-muted"><?php echo escape($notification['message']); ?></p>
                                        <small class="text-muted">
                                            <i class="bi bi-clock me-1"></i><?php echo timeAgo($notification['created_at']); ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                            <div class="ms-3">
                                <?php if (!$notification['is_read']): ?>
                                    <span class="badge bg-success">Yeni</span>
                                <?php endif; ?>
                                <?php if ($notification['action_url']): ?>
                                    <a href="<?php echo escape($notification['action_url']); ?>" class="btn btn-sm btn-outline-primary ms-2">
                                        <i class="bi bi-arrow-right"></i>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="bi bi-bell text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">Henüz bildirim yok</h4>
                        <p class="text-muted">Yeni bildirimler burada görünecek.</p>
                        <a href="dashboard.php" class="btn btn-primary">Dashboard'a Dön</a>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Bildirim Ayarları</h6>
                    </div>
                    <div class="card-body">
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="email_notifications" checked>
                            <label class="form-check-label" for="email_notifications">
                                E-posta bildirimleri
                            </label>
                        </div>
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="job_notifications" checked>
                            <label class="form-check-label" for="job_notifications">
                                İş ilanı bildirimleri
                            </label>
                        </div>
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="message_notifications" checked>
                            <label class="form-check-label" for="message_notifications">
                                Mesaj bildirimleri
                            </label>
                        </div>
                        <button class="btn btn-primary btn-sm">Kaydet</button>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">Hızlı İşlemler</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="dashboard.php" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-speedometer2 me-2"></i>Dashboard
                            </a>
                            <a href="messages.php" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-chat-dots me-2"></i>Mesajlar
                            </a>
                            <?php if ($_SESSION['user_type'] === 'family'): ?>
                                <a href="jobs/create.php" class="btn btn-outline-success btn-sm">
                                    <i class="bi bi-plus-circle me-2"></i>İlan Ver
                                </a>
                            <?php elseif ($_SESSION['user_type'] === 'caregiver'): ?>
                                <a href="jobs.php" class="btn btn-outline-success btn-sm">
                                    <i class="bi bi-search me-2"></i>İş Ara
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
