<?php
/**
 * PayTR Ödeme Entegrasyonu
 * https://dev.paytr.com/direkt-api/direkt-api-1-adim
 */

class PayTR {
    private $merchant_id;
    private $merchant_key;
    private $merchant_salt;
    private $test_mode;
    private $success_url;
    private $fail_url;
    
    public function __construct() {
        $this->merchant_id = getSetting('paytr_merchant_id', '');
        $this->merchant_key = getSetting('paytr_merchant_key', '');
        $this->merchant_salt = getSetting('paytr_merchant_salt', '');
        $this->test_mode = getSetting('paytr_test_mode', '1') === '1';
        $this->success_url = getSetting('paytr_success_url', 'http://localhost/bakici-platform/payment-success.php');
        $this->fail_url = getSetting('paytr_fail_url', 'http://localhost/bakici-platform/payment-fail.php');
    }
    
    /**
     * Ödeme formu oluştur
     */
    public function createPaymentForm($order_data) {
        // Gerekli alanları kontrol et
        if (empty($this->merchant_id) || empty($this->merchant_key) || empty($this->merchant_salt)) {
            throw new Exception('PayTR ayarları eksik. Admin panelinden PayTR bilgilerini girin.');
        }
        
        // Sipariş verilerini hazırla
        $merchant_id = $this->merchant_id;
        $user_ip = $this->getUserIP();
        $merchant_oid = $order_data['order_id'];
        $email = $order_data['email'];
        $payment_amount = $order_data['amount'] * 100; // Kuruş cinsinden
        $currency = 'TL';
        $test_mode = $this->test_mode ? '1' : '0';
        
        // Kullanıcı sepeti
        $user_basket = base64_encode(json_encode($order_data['basket']));
        
        // Timeout süresi (dakika)
        $timeout_limit = "30";
        
        // Taksit sayısı
        $installment_count = "0";
        
        // Hash oluştur
        $hash_str = $merchant_id . $user_ip . $merchant_oid . $email . $payment_amount . $user_basket . 
                   $this->test_mode . $this->merchant_salt;
        $paytr_token = base64_encode(hash_hmac('sha256', $hash_str, $this->merchant_key, true));
        
        // POST verileri
        $post_vals = [
            'merchant_id' => $merchant_id,
            'user_ip' => $user_ip,
            'merchant_oid' => $merchant_oid,
            'email' => $email,
            'payment_amount' => $payment_amount,
            'currency' => $currency,
            'user_basket' => $user_basket,
            'debug_on' => $test_mode,
            'test_mode' => $test_mode,
            'merchant_ok_url' => $this->success_url,
            'merchant_fail_url' => $this->fail_url,
            'timeout_limit' => $timeout_limit,
            'installment_count' => $installment_count,
            'paytr_token' => $paytr_token
        ];
        
        // PayTR'ye istek gönder
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://www.paytr.com/odeme/api/get-token");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post_vals);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 20);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $result = curl_exec($ch);
        
        if (curl_error($ch)) {
            curl_close($ch);
            throw new Exception('PayTR bağlantı hatası: ' . curl_error($ch));
        }
        
        curl_close($ch);
        
        $result = json_decode($result, true);
        
        if ($result['status'] == 'success') {
            return [
                'status' => 'success',
                'token' => $result['token'],
                'payment_url' => 'https://www.paytr.com/odeme/guvenli/' . $result['token']
            ];
        } else {
            throw new Exception('PayTR Token Hatası: ' . $result['reason']);
        }
    }
    
    /**
     * Callback doğrulama
     */
    public function verifyCallback($post_data) {
        $merchant_oid = $post_data['merchant_oid'];
        $status = $post_data['status'];
        $total_amount = $post_data['total_amount'];
        $hash = $post_data['hash'];
        
        // Hash doğrulama
        $hash_str = $merchant_oid . $this->merchant_salt . $status . $total_amount;
        $calculated_hash = base64_encode(hash_hmac('sha256', $hash_str, $this->merchant_key, true));
        
        if ($hash !== $calculated_hash) {
            throw new Exception('PayTR Callback hash doğrulaması başarısız');
        }
        
        return [
            'merchant_oid' => $merchant_oid,
            'status' => $status,
            'total_amount' => $total_amount / 100, // TL cinsine çevir
            'verified' => true
        ];
    }
    
    /**
     * Kullanıcı IP adresini al
     */
    private function getUserIP() {
        if (isset($_SERVER['HTTP_CLIENT_IP'])) {
            return $_SERVER['HTTP_CLIENT_IP'];
        } elseif (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            return $_SERVER['HTTP_X_FORWARDED_FOR'];
        } elseif (isset($_SERVER['HTTP_X_FORWARDED'])) {
            return $_SERVER['HTTP_X_FORWARDED'];
        } elseif (isset($_SERVER['HTTP_X_CLUSTER_CLIENT_IP'])) {
            return $_SERVER['HTTP_X_CLUSTER_CLIENT_IP'];
        } elseif (isset($_SERVER['HTTP_FORWARDED_FOR'])) {
            return $_SERVER['HTTP_FORWARDED_FOR'];
        } elseif (isset($_SERVER['HTTP_FORWARDED'])) {
            return $_SERVER['HTTP_FORWARDED'];
        } elseif (isset($_SERVER['REMOTE_ADDR'])) {
            return $_SERVER['REMOTE_ADDR'];
        }
        return '127.0.0.1';
    }
    
    /**
     * Test bağlantısı
     */
    public function testConnection() {
        try {
            // Test siparişi oluştur
            $test_order = [
                'order_id' => 'TEST_' . time(),
                'email' => '<EMAIL>',
                'amount' => 1.00,
                'basket' => [
                    ['Test Ürün', '1.00', 1]
                ]
            ];
            
            $result = $this->createPaymentForm($test_order);
            return ['status' => 'success', 'message' => 'PayTR bağlantısı başarılı'];
            
        } catch (Exception $e) {
            return ['status' => 'error', 'message' => $e->getMessage()];
        }
    }
    
    /**
     * Paket bilgilerini al
     */
    public static function getPackages() {
        return [
            'basic' => [
                'name' => 'Temel Paket',
                'price' => floatval(getSetting('package_basic_price', 29.00)),
                'features' => explode(',', getSetting('package_basic_features', 'Temel özellikler,5 iş ilanı,Email desteği')),
                'duration' => 30, // gün
                'job_limit' => 5
            ],
            'standard' => [
                'name' => 'Standart Paket',
                'price' => floatval(getSetting('package_standard_price', 59.00)),
                'features' => explode(',', getSetting('package_standard_features', 'Tüm temel özellikler,15 iş ilanı,Öncelikli destek,Mesajlaşma sistemi')),
                'duration' => 30, // gün
                'job_limit' => 15,
                'popular' => true
            ],
            'premium' => [
                'name' => 'Premium Paket',
                'price' => floatval(getSetting('package_premium_price', 99.00)),
                'features' => explode(',', getSetting('package_premium_features', 'Tüm özellikler,Sınırsız iş ilanı,7/24 destek,Özel danışman,Analitik raporlar')),
                'duration' => 30, // gün
                'job_limit' => -1 // Sınırsız
            ]
        ];
    }
}
?>
