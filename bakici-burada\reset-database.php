<?php
// Veritabanını sıfırlama ve yeniden kurma sayfası

// Veritabanı bağlantısı (veritabanı adı olmadan)
$host = 'localhost';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Veritabanını sil ve yeniden oluştur
    $pdo->exec("DROP DATABASE IF EXISTS bakici_burada");
    $pdo->exec("CREATE DATABASE bakici_burada CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    
    echo "✅ Veritabanı başarıyla sıfırlandı!<br>";
    echo "<a href='install.php'><PERSON><PERSON><PERSON><PERSON> devam et</a>";
    
} catch (PDOException $e) {
    echo "❌ Hata: " . $e->getMessage();
}
?>
