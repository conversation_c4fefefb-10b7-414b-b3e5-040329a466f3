<?php
// Debug sayfası - hataları görmek için
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<h2>Debug Bilgileri</h2>";

// Veritabanı bağlantısını test et
try {
    echo "<p>✅ Veritabanı bağlantısı başarılı</p>";
    
    // Tabloları kontrol et
    $tables = ['users', 'job_listings', 'categories', 'activity_logs', 'notifications'];
    foreach ($tables as $table) {
        $stmt = $db->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<p>✅ $table tablosu mevcut</p>";
        } else {
            echo "<p>❌ $table tablosu eksik</p>";
        }
    }
    
    // Fonksiyonları test et
    echo "<h3>Fonksiyon Testleri:</h3>";
    
    if (function_exists('generateUniqueSlug')) {
        echo "<p>✅ generateUniqueSlug fonksiyonu mevcut</p>";
        $test_slug = generateUniqueSlug('job_listings', 'Test İlan Başlığı');
        echo "<p>Test slug: $test_slug</p>";
    } else {
        echo "<p>❌ generateUniqueSlug fonksiyonu eksik</p>";
    }
    
    if (function_exists('validateLength')) {
        echo "<p>✅ validateLength fonksiyonu mevcut</p>";
    } else {
        echo "<p>❌ validateLength fonksiyonu eksik</p>";
    }
    
    if (function_exists('logActivity')) {
        echo "<p>✅ logActivity fonksiyonu mevcut</p>";
    } else {
        echo "<p>❌ logActivity fonksiyonu eksik</p>";
    }
    
    if (function_exists('getJobTypes')) {
        echo "<p>✅ getJobTypes fonksiyonu mevcut</p>";
        $job_types = getJobTypes();
        echo "<p>İş tipleri sayısı: " . count($job_types) . "</p>";
    } else {
        echo "<p>❌ getJobTypes fonksiyonu eksik</p>";
    }
    
    if (function_exists('getCareTypes')) {
        echo "<p>✅ getCareTypes fonksiyonu mevcut</p>";
        $care_types = getCareTypes();
        echo "<p>Bakım tipleri sayısı: " . count($care_types) . "</p>";
    } else {
        echo "<p>❌ getCareTypes fonksiyonu eksik</p>";
    }
    
    // Kategori kontrolü
    $stmt = $db->query("SELECT COUNT(*) FROM categories");
    $category_count = $stmt->fetchColumn();
    echo "<p>Kategori sayısı: $category_count</p>";
    
    if ($category_count == 0) {
        echo "<p>⚠️ Kategori bulunamadı, varsayılan kategori ekleniyor...</p>";
        $db->exec("INSERT INTO categories (name, slug, icon, description) VALUES ('Genel', 'genel', 'bi-briefcase', 'Genel iş ilanları')");
        echo "<p>✅ Varsayılan kategori eklendi</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Hata: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='jobs/create.php'>İlan Oluşturma Sayfasına Git</a></p>";
echo "<p><a href='dashboard.php'>Dashboard'a Git</a></p>";
?>
