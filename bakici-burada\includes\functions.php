<?php
// Gü<PERSON>li değişken alma
function safeVar($var, $default = '') {
    return isset($var) ? $var : $default;
}

// Güvenli array değeri alma
function safeArray($array, $key, $default = '') {
    return isset($array[$key]) ? $array[$key] : $default;
}

// Güvenli JSON encode
function safeJsonEncode($data) {
    return json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}

// Güvenli sayı alma
function safeNumber($var, $default = 0) {
    return is_numeric($var) ? (float)$var : $default;
}

// HTML escape
function escape($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

// Giriş kontrolü
function requireLogin() {
    if (!isset($_SESSION['user_id'])) {
        header('Location: /bakici-burada/auth/login.php');
        exit;
    }
}

// Admin kontrolü
function requireAdmin() {
    requireLogin();
    if ($_SESSION['user_type'] !== 'admin') {
        header('Location: /bakici-burada/dashboard.php');
        exit;
    }
}

// Kullanıcı türü kontrolü
function hasUserType($type) {
    return isset($_SESSION['user_type']) && $_SESSION['user_type'] === $type;
}

// Flash mesaj sistemi
function setMessage($message, $type = 'info') {
    if (!isset($_SESSION['flash_messages'])) {
        $_SESSION['flash_messages'] = [];
    }
    $_SESSION['flash_messages'][] = [
        'message' => $message,
        'type' => $type
    ];
}

function getMessages() {
    if (isset($_SESSION['flash_messages'])) {
        $messages = $_SESSION['flash_messages'];
        unset($_SESSION['flash_messages']);
        return $messages;
    }
    return [];
}

// Tarih formatı
function formatDate($date, $format = 'd.m.Y') {
    if (!$date) return '-';
    return date($format, strtotime($date));
}

function formatDateTime($datetime, $format = 'd.m.Y H:i') {
    if (!$datetime) return '-';
    return date($format, strtotime($datetime));
}

// Dosya boyutu formatı
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' B';
    }
}

// Para formatı
function formatMoney($amount, $currency = 'TL') {
    return number_format($amount, 2, ',', '.') . ' ' . $currency;
}

// Puan formatı
function formatRating($rating) {
    return number_format($rating, 1);
}

// Aktivite logu
function logActivity($user_id, $action, $description, $table_name = '', $record_id = 0) {
    global $db;
    try {
        // Activity log tablosu yoksa oluştur
        $db->exec("CREATE TABLE IF NOT EXISTS activity_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            action VARCHAR(50),
            description TEXT,
            table_name VARCHAR(50),
            record_id INT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        $sql = "INSERT INTO activity_logs (user_id, action, description, table_name, record_id, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?, ?, ?)";
        $stmt = $db->prepare($sql);
        $stmt->execute([
            $user_id,
            $action,
            $description,
            $table_name,
            $record_id,
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
    } catch (PDOException $e) {
        // Log hatası durumunda sessizce devam et
    }
}

// E-posta gönderme
function sendEmail($to, $subject, $message, $from_name = 'Bakıcı Burada') {
    $headers = [
        'MIME-Version: 1.0',
        'Content-type: text/html; charset=UTF-8',
        'From: ' . $from_name . ' <<EMAIL>>',
        'Reply-To: <EMAIL>',
        'X-Mailer: PHP/' . phpversion()
    ];
    
    return mail($to, $subject, $message, implode("\r\n", $headers));
}

// Bildirim gönderme
function sendNotification($user_id, $type, $title, $message, $data = null) {
    global $db;
    try {
        $sql = "INSERT INTO notifications (user_id, type, title, message, data) VALUES (?, ?, ?, ?, ?)";
        $stmt = $db->prepare($sql);
        $stmt->execute([$user_id, $type, $title, $message, $data ? json_encode($data) : null]);
        return true;
    } catch (PDOException $e) {
        return false;
    }
}

// Dosya yükleme
function uploadFile($file, $upload_dir = 'uploads/', $allowed_types = ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx']) {
    if (!isset($file['tmp_name']) || !$file['tmp_name']) {
        return ['success' => false, 'message' => 'Dosya seçilmedi.'];
    }
    
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    if (!in_array($file_extension, $allowed_types)) {
        return ['success' => false, 'message' => 'Geçersiz dosya türü.'];
    }
    
    if ($file['size'] > 10 * 1024 * 1024) { // 10MB
        return ['success' => false, 'message' => 'Dosya boyutu çok büyük (max 10MB).'];
    }
    
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    $new_filename = uniqid() . '_' . time() . '.' . $file_extension;
    $upload_path = $upload_dir . $new_filename;
    
    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
        return ['success' => true, 'filename' => $new_filename, 'path' => $upload_path];
    } else {
        return ['success' => false, 'message' => 'Dosya yüklenirken hata oluştu.'];
    }
}

// Resim yeniden boyutlandırma
function resizeImage($source, $destination, $max_width = 800, $max_height = 600, $quality = 85) {
    $image_info = getimagesize($source);
    if (!$image_info) return false;
    
    $width = $image_info[0];
    $height = $image_info[1];
    $type = $image_info[2];
    
    // Oranı koru
    $ratio = min($max_width / $width, $max_height / $height);
    $new_width = $width * $ratio;
    $new_height = $height * $ratio;
    
    // Kaynak resmi oluştur
    switch ($type) {
        case IMAGETYPE_JPEG:
            $source_image = imagecreatefromjpeg($source);
            break;
        case IMAGETYPE_PNG:
            $source_image = imagecreatefrompng($source);
            break;
        case IMAGETYPE_GIF:
            $source_image = imagecreatefromgif($source);
            break;
        default:
            return false;
    }
    
    // Yeni resim oluştur
    $new_image = imagecreatetruecolor($new_width, $new_height);
    
    // PNG için şeffaflığı koru
    if ($type == IMAGETYPE_PNG) {
        imagealphablending($new_image, false);
        imagesavealpha($new_image, true);
    }
    
    // Yeniden boyutlandır
    imagecopyresampled($new_image, $source_image, 0, 0, 0, 0, $new_width, $new_height, $width, $height);
    
    // Kaydet
    switch ($type) {
        case IMAGETYPE_JPEG:
            $result = imagejpeg($new_image, $destination, $quality);
            break;
        case IMAGETYPE_PNG:
            $result = imagepng($new_image, $destination);
            break;
        case IMAGETYPE_GIF:
            $result = imagegif($new_image, $destination);
            break;
    }
    
    // Belleği temizle
    imagedestroy($source_image);
    imagedestroy($new_image);
    
    return $result;
}

// Slug oluşturma
function createSlug($text) {
    $text = trim($text);
    $text = mb_strtolower($text, 'UTF-8');
    
    // Türkçe karakterleri değiştir
    $turkish = ['ç', 'ğ', 'ı', 'ö', 'ş', 'ü'];
    $english = ['c', 'g', 'i', 'o', 's', 'u'];
    $text = str_replace($turkish, $english, $text);
    
    // Özel karakterleri kaldır
    $text = preg_replace('/[^a-z0-9\s-]/', '', $text);
    $text = preg_replace('/[\s-]+/', '-', $text);
    $text = trim($text, '-');
    
    return $text;
}

// Mesafe hesaplama (basit)
function calculateDistance($lat1, $lon1, $lat2, $lon2) {
    $earth_radius = 6371; // km
    
    $lat_diff = deg2rad($lat2 - $lat1);
    $lon_diff = deg2rad($lon2 - $lon1);
    
    $a = sin($lat_diff/2) * sin($lat_diff/2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($lon_diff/2) * sin($lon_diff/2);
    $c = 2 * atan2(sqrt($a), sqrt(1-$a));
    
    return $earth_radius * $c;
}

// Kullanıcı paket kontrolü
function hasActivePackage($user_id, $feature = null) {
    global $db;
    try {
        $sql = "SELECT up.*, p.features FROM user_packages up 
                JOIN packages p ON up.package_id = p.id 
                WHERE up.user_id = ? AND up.is_active = 1 AND up.expires_at > NOW() 
                ORDER BY up.expires_at DESC LIMIT 1";
        $stmt = $db->prepare($sql);
        $stmt->execute([$user_id]);
        $package = $stmt->fetch();
        
        if (!$package) return false;
        
        if ($feature) {
            $features = explode(',', $package['features']);
            return in_array($feature, $features);
        }
        
        return $package;
    } catch (PDOException $e) {
        return false;
    }
}

// Şehir listesi
function getCities() {
    return [
        'Adana', 'Adıyaman', 'Afyonkarahisar', 'Ağrı', 'Amasya', 'Ankara', 'Antalya', 'Artvin',
        'Aydın', 'Balıkesir', 'Bilecik', 'Bingöl', 'Bitlis', 'Bolu', 'Burdur', 'Bursa',
        'Çanakkale', 'Çankırı', 'Çorum', 'Denizli', 'Diyarbakır', 'Edirne', 'Elazığ', 'Erzincan',
        'Erzurum', 'Eskişehir', 'Gaziantep', 'Giresun', 'Gümüşhane', 'Hakkari', 'Hatay', 'Isparta',
        'Mersin', 'İstanbul', 'İzmir', 'Kars', 'Kastamonu', 'Kayseri', 'Kırklareli', 'Kırşehir',
        'Kocaeli', 'Konya', 'Kütahya', 'Malatya', 'Manisa', 'Kahramanmaraş', 'Mardin', 'Muğla',
        'Muş', 'Nevşehir', 'Niğde', 'Ordu', 'Rize', 'Sakarya', 'Samsun', 'Siirt',
        'Sinop', 'Sivas', 'Tekirdağ', 'Tokat', 'Trabzon', 'Tunceli', 'Şanlıurfa', 'Uşak',
        'Van', 'Yozgat', 'Zonguldak', 'Aksaray', 'Bayburt', 'Karaman', 'Kırıkkale', 'Batman',
        'Şırnak', 'Bartın', 'Ardahan', 'Iğdır', 'Yalova', 'Karabük', 'Kilis', 'Osmaniye', 'Düzce'
    ];
}

// İş türü çevirileri
function getJobTypes() {
    return [
        'child_care' => 'Çocuk Bakımı',
        'elderly_care' => 'Yaşlı Bakımı',
        'patient_care' => 'Hasta Bakımı',
        'house_help' => 'Ev Yardımcısı',
        'pet_care' => 'Evcil Hayvan Bakımı'
    ];
}

// Bakım türü çevirileri
function getCareTypes() {
    return [
        'live_in' => 'Yatılı',
        'live_out' => 'Gündüzlü',
        'hourly' => 'Saatlik',
        'daily' => 'Günlük',
        'weekly' => 'Haftalık'
    ];
}
?>
