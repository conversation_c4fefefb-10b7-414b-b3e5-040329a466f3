<?php

namespace App\Controllers;

class Test extends BaseController
{
    public function index()
    {
        echo "<h1>🎉 CodeIgniter 4 Çalışıyor!</h1>";
        echo "<p>Escort İlan Sitesi başarıyla kuruldu.</p>";
        echo "<p>Veritabanı bağlantısı test ediliyor...</p>";
        
        try {
            $db = \Config\Database::connect();
            $query = $db->query("SELECT COUNT(*) as count FROM settings");
            $result = $query->getRow();
            
            echo "<p style='color: green;'>✅ Veritabanı bağlantısı başarılı!</p>";
            echo "<p>Ayar sayısı: " . $result->count . "</p>";
            
            // Kategorileri test et
            $query = $db->query("SELECT * FROM categories LIMIT 5");
            $categories = $query->getResult();
            
            echo "<h3>Kategoriler:</h3>";
            echo "<ul>";
            foreach ($categories as $category) {
                echo "<li>" . $category->name . " (" . $category->slug . ")</li>";
            }
            echo "</ul>";
            
            // Şehirleri test et
            $query = $db->query("SELECT * FROM cities LIMIT 5");
            $cities = $query->getResult();
            
            echo "<h3>Şehirler:</h3>";
            echo "<ul>";
            foreach ($cities as $city) {
                echo "<li>" . $city->name . " (" . $city->slug . ")</li>";
            }
            echo "</ul>";
            
            echo "<p><a href='" . base_url() . "'>Ana Sayfaya Dön</a></p>";
            
        } catch (\Exception $e) {
            echo "<p style='color: red;'>❌ Veritabanı hatası: " . $e->getMessage() . "</p>";
        }
    }
    
    public function phpinfo()
    {
        phpinfo();
    }
}
