<?php
// Text
$_['text_title']						= 'Cards or PayPal';
$_['text_express_title']      			= 'Confirm order';
$_['text_vaulted_payment_method_name']	= '%s ending in %s, expires %s';
$_['text_remember']						= 'Save for next time?';
$_['text_remove']						= 'Remove';
$_['text_remove_confirm']				= 'Are you sure you want to remove the saved payment method?';
$_['text_month']						= 'Month';
$_['text_year']							= 'Year';
$_['text_loading']						= 'Loading...';
$_['text_new_method']					= 'New payment method';
$_['text_saved_method']					= 'Choose a saved payment method';
$_['text_paypal']						= 'PayPal';
$_['text_pay_by_paypal']				= 'Pay by PayPal';
$_['text_method_removed']				= 'Payment method has been removed';
$_['text_method_not_removed']			= 'Unable to remove payment method';
$_['text_authentication']				= 'Authentication';
$_['text_cart']               			= 'Shopping Cart';
$_['text_shipping_updated']   			= 'Shipping service updated';

// Entry
$_['entry_new']							= 'New payment method';
$_['entry_saved_methods']				= 'Saved payment method';
$_['entry_card']						= 'Card Number';
$_['entry_expires']						= 'Expires on';
$_['entry_cvv']							= 'CVV';
$_['entry_remember_card_method']		= 'Remember card?';
$_['entry_remember_paypal_method']		= 'Remember PayPal account?';
$_['entry_card_placeholder']			= 'Card Number';
$_['entry_month_placeholder']			= 'Month (MM)';
$_['entry_year_placeholder']			= 'Year (YYYY)';
$_['entry_cvv_placeholder']				= 'CVV2';

// Error
$_['error_process_order']				= 'There was an error processing your order. Please contact the shop administrator for help.';
$_['error_alert_fields_empty']			= 'All fields are empty! Please fill out the form';
$_['error_alert_fields_invalid']		= 'Some fields are invalid, please check your information';
$_['error_alert_failed_token']			= 'Our payment provider did not recognise the card. Is the card valid?';
$_['error_alert_failed_network']		= 'A network error occurred, please try again';
$_['error_alert_unknown']				= 'An unknown error occurred, if the issue continued please contact support';
$_['error_unavailable'] 	  			= 'Please use the full checkout with this order';
$_['error_no_shipping']    				= 'Warning: No Shipping options are available.';

// Button
$_['button_confirm']					= 'Pay Now';
$_['button_express_confirm']  			= 'Confirm';
$_['button_express_shipping'] 			= 'Update shipping';
