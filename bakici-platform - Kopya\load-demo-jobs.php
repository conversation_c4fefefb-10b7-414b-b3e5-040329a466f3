<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

try {
    echo "<h2>Demo İş İlanları Yükleniyor...</h2>";
    
    // Demo aile kullanıcısının ID'sini al
    $stmt = $db->prepare("SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1");
    $stmt->execute();
    $demo_family = $stmt->fetch();
    
    if (!$demo_family) {
        echo "<p>❌ Demo aile kullanıcısı bulunamadı. Önce demo kullanıcıları oluşturun.</p>";
        exit;
    }
    
    $family_id = $demo_family['id'];
    
    // Kategori ID'sini al
    $stmt = $db->prepare("SELECT id FROM categories ORDER BY id LIMIT 1");
    $stmt->execute();
    $category_id = $stmt->fetchColumn() ?: 1;
    
    // Demo iş ilanları
    $demo_jobs = [
        [
            'title' => 'Deneyimli Çocuk Bakıcısı Aranıyor',
            'description' => '2 yaşında çocuğumuz için deney<PERSON>, güvenilir ve sevecen bir bakıcı arıyoruz. Hafta içi 08:00-18:00 saatleri arasında çalışacak. Çocuk gelişimi konusunda bilgili olması tercih sebebidir. Ev temizliği ve yemek yapma becerisi olan adaylar öncelikli değerlendirilecektir.',
            'job_type' => 'child_care',
            'care_type' => 'live_out',
            'location_city' => 'İstanbul',
            'location_district' => 'Kadıköy',
            'budget_min' => 6000,
            'budget_max' => 8000,
            'budget_type' => 'monthly',
            'requirements' => 'En az 2 yıl deneyim, referanslar, çocuk gelişimi sertifikası tercih edilir',
            'benefits' => 'Yemek, ulaşım desteği, sigorta, yıllık izin',
            'is_urgent' => 1,
            'status' => 'active'
        ],
        [
            'title' => 'Yaşlı Bakım Elemanı - Yatılı',
            'description' => '75 yaşındaki babamız için yatılı bakım elemanı arıyoruz. Alzheimer hastası olan babamızın günlük ihtiyaçlarını karşılayacak, ilaçlarını verecek ve genel bakımını yapacak deneyimli bir bakıcı arıyoruz. Sabırlı, anlayışlı ve yaşlı bakımı konusunda deneyimli olması gerekiyor.',
            'job_type' => 'elderly_care',
            'care_type' => 'live_in',
            'location_city' => 'Ankara',
            'location_district' => 'Çankaya',
            'budget_min' => 8000,
            'budget_max' => 12000,
            'budget_type' => 'monthly',
            'requirements' => 'Yaşlı bakımı deneyimi, hasta bakım sertifikası, referanslar zorunlu',
            'benefits' => 'Tam pansiyon, sigorta, haftalık izin, prim sistemi',
            'is_urgent' => 0,
            'status' => 'active'
        ],
        [
            'title' => 'Bebek Bakıcısı - Part Time',
            'description' => '6 aylık bebeğimiz için part-time bakıcı arıyoruz. Hafta içi 13:00-18:00 saatleri arasında çalışacak. Bebek bakımı konusunda deneyimli, temiz ve güvenilir olması şart. İlk yardım bilgisi olan adaylar tercih edilecektir.',
            'job_type' => 'child_care',
            'care_type' => 'hourly',
            'location_city' => 'İzmir',
            'location_district' => 'Bornova',
            'budget_min' => 80,
            'budget_max' => 120,
            'budget_type' => 'hourly',
            'requirements' => 'Bebek bakımı deneyimi, ilk yardım sertifikası tercih edilir',
            'benefits' => 'Esnek çalışma saatleri, ulaşım desteği',
            'is_urgent' => 0,
            'status' => 'active'
        ],
        [
            'title' => 'Ev Yardımcısı - Günlük',
            'description' => '150 m² evimiz için haftada 3 gün (Pazartesi, Çarşamba, Cuma) ev temizliği yapacak yardımcı arıyoruz. Ütü, bulaşık, genel temizlik işlerini yapacak. Deneyimli, titiz ve güvenilir olması önemli.',
            'job_type' => 'house_help',
            'care_type' => 'daily',
            'location_city' => 'Bursa',
            'location_district' => 'Nilüfer',
            'budget_min' => 200,
            'budget_max' => 300,
            'budget_type' => 'daily',
            'requirements' => 'Ev temizliği deneyimi, referanslar',
            'benefits' => 'Yemek, çay-kahve, ulaşım desteği',
            'is_urgent' => 0,
            'status' => 'paused'
        ],
        [
            'title' => 'Hasta Bakıcısı - Gece Vardiyası',
            'description' => 'Ameliyat sonrası iyileşme sürecindeki annemiz için gece vardiyası bakıcısı arıyoruz. 20:00-08:00 saatleri arasında çalışacak. Hasta bakımı konusunda deneyimli, sabırlı ve dikkatli olması gerekiyor.',
            'job_type' => 'patient_care',
            'care_type' => 'hourly',
            'location_city' => 'Antalya',
            'location_district' => 'Muratpaşa',
            'budget_min' => 100,
            'budget_max' => 150,
            'budget_type' => 'hourly',
            'requirements' => 'Hasta bakımı deneyimi, sağlık meslek lisesi mezunu tercih edilir',
            'benefits' => 'Gece vardiyası primi, sigorta, yemek',
            'is_urgent' => 1,
            'status' => 'active'
        ],
        [
            'title' => 'Çocuk Bakıcısı - Hafta Sonu',
            'description' => '5 ve 8 yaşlarında iki çocuğumuz için hafta sonu bakıcısı arıyoruz. Cumartesi-Pazar 09:00-19:00 saatleri arasında çalışacak. Çocuklarla oyun oynayacak, ödevlerine yardım edecek ve genel bakımlarını yapacak.',
            'job_type' => 'child_care',
            'care_type' => 'weekend',
            'location_city' => 'İstanbul',
            'location_district' => 'Beşiktaş',
            'budget_min' => 400,
            'budget_max' => 600,
            'budget_type' => 'daily',
            'requirements' => 'Çocuk bakımı deneyimi, oyun ve aktivite bilgisi',
            'benefits' => 'Yemek, ulaşım, hafta içi esnek çalışma imkanı',
            'is_urgent' => 0,
            'status' => 'draft'
        ]
    ];
    
    $success_count = 0;
    
    foreach ($demo_jobs as $job) {
        try {
            // Slug oluştur
            $slug = generateUniqueSlug('job_listings', $job['title']);
            
            // Bitiş tarihi (30 gün sonra)
            $expires_at = date('Y-m-d H:i:s', strtotime('+30 days'));
            
            $sql = "INSERT INTO job_listings (
                user_id, category_id, title, slug, description, job_type, care_type,
                location_city, location_district, budget_min, budget_max, budget_type,
                requirements, benefits, is_urgent, status, expires_at, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $stmt = $db->prepare($sql);
            $stmt->execute([
                $family_id,
                $category_id,
                $job['title'],
                $slug,
                $job['description'],
                $job['job_type'],
                $job['care_type'],
                $job['location_city'],
                $job['location_district'],
                $job['budget_min'],
                $job['budget_max'],
                $job['budget_type'],
                $job['requirements'],
                $job['benefits'],
                $job['is_urgent'],
                $job['status'],
                $expires_at
            ]);
            
            $success_count++;
            echo "<p>✅ İlan eklendi: " . $job['title'] . " (Durum: " . $job['status'] . ")</p>";
            
        } catch (PDOException $e) {
            echo "<p>❌ İlan eklenirken hata: " . $job['title'] . " - " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h3>✅ Demo İş İlanları Yükleme Tamamlandı!</h3>";
    echo "<p><strong>$success_count</strong> adet demo iş ilanı başarıyla eklendi.</p>";
    
    // İstatistikleri göster
    $stats = $db->query("SELECT status, COUNT(*) as count FROM job_listings GROUP BY status")->fetchAll();
    echo "<h4>İlan Durumları:</h4>";
    echo "<ul>";
    foreach ($stats as $stat) {
        $status_names = [
            'active' => 'Aktif',
            'paused' => 'Duraklatılmış', 
            'draft' => 'Taslak',
            'closed' => 'Kapatılmış'
        ];
        echo "<li>" . ($status_names[$stat['status']] ?? $stat['status']) . ": " . $stat['count'] . " adet</li>";
    }
    echo "</ul>";
    
    echo "<p><a href='jobs/my-jobs.php'>İlanlarımı Görüntüle</a></p>";
    echo "<p><a href='jobs.php'>Tüm İlanları Görüntüle</a></p>";
    
} catch (PDOException $e) {
    echo "<p>❌ Genel Hata: " . $e->getMessage() . "</p>";
}
?>
