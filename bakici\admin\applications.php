<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// <PERSON><PERSON>ş kontrolü
if (!isset($_SESSION['user_id'])) {
    header('Location: ../auth/login.php');
    exit;
}

// Sadece admin kullanıcıları erişebilir
if ($_SESSION['user_type'] !== 'admin') {
    header('Location: ../dashboard.php');
    exit;
}

// CRUD İşlemleri
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $application_id = intval($_POST['application_id'] ?? 0);

    try {
        if ($action === 'update_status') {
            $status = $_POST['status'] ?? '';
            if (in_array($status, ['pending', 'viewed', 'shortlisted', 'interview', 'accepted', 'rejected', 'withdrawn', 'hired'])) {
                $sql = "UPDATE job_applications SET status = ?, updated_at = NOW() WHERE id = ?";
                $stmt = $db->prepare($sql);
                $stmt->execute([$status, $application_id]);
                $success = 'Başvuru durumu güncellendi.';
            }

        } elseif ($action === 'delete_application') {
            $sql = "DELETE FROM job_applications WHERE id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$application_id]);
            $success = 'Başvuru silindi.';
        }
    } catch (PDOException $e) {
        $error = 'İşlem sırasında hata oluştu: ' . $e->getMessage();
    }
}

// Başvuruları getir
try {
    $sql = "SELECT ja.*,
                   jl.title as job_title, jl.location as job_location,
                   u.full_name as caregiver_name, u.email as caregiver_email, u.phone as caregiver_phone,
                   emp.full_name as employer_name, emp.email as employer_email
            FROM job_applications ja
            LEFT JOIN job_listings jl ON ja.job_id = jl.id
            LEFT JOIN users u ON ja.caregiver_id = u.id
            LEFT JOIN users emp ON jl.user_id = emp.id
            ORDER BY ja.created_at DESC
            LIMIT 100";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $applications = $stmt->fetchAll();

    // İstatistikler
    $stats = [
        'total' => $db->query("SELECT COUNT(*) FROM job_applications")->fetchColumn(),
        'pending' => $db->query("SELECT COUNT(*) FROM job_applications WHERE status = 'pending'")->fetchColumn(),
        'accepted' => $db->query("SELECT COUNT(*) FROM job_applications WHERE status = 'accepted'")->fetchColumn(),
        'rejected' => $db->query("SELECT COUNT(*) FROM job_applications WHERE status = 'rejected'")->fetchColumn()
    ];

} catch (PDOException $e) {
    $applications = [];
    $stats = ['total' => 0, 'pending' => 0, 'accepted' => 0, 'rejected' => 0];
    error_log("Admin applications query error: " . $e->getMessage());
}

$page_title = 'Başvuru Yönetimi';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Admin Panel</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="../index.php">
                <i class="bi bi-heart-fill me-2"></i>Bakıcı Platform - Admin
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../dashboard.php">Dashboard</a>
                <a class="nav-link" href="settings.php">Ayarlar</a>
                <a class="nav-link" href="../auth/logout.php">Çıkış</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="bi bi-file-text me-2"></i>Başvuru Yönetimi</h4>
                    </div>
                    <div class="card-body">
                        <!-- Alerts -->
                        <?php if (isset($success)): ?>
                            <div class="alert alert-success alert-dismissible fade show">
                                <i class="bi bi-check-circle me-2"></i><?php echo $success; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($error)): ?>
                            <div class="alert alert-danger alert-dismissible fade show">
                                <i class="bi bi-exclamation-triangle me-2"></i><?php echo $error; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <!-- İstatistikler -->
                        <div class="row g-3 mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h3><?php echo $stats['total']; ?></h3>
                                        <small>Toplam Başvuru</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <h3><?php echo $stats['pending']; ?></h3>
                                        <small>Bekleyen</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h3><?php echo $stats['accepted']; ?></h3>
                                        <small>Kabul Edilen</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-danger text-white">
                                    <div class="card-body text-center">
                                        <h3><?php echo $stats['rejected']; ?></h3>
                                        <small>Reddedilen</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Başvuru Listesi -->
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Bakıcı</th>
                                        <th>İş İlanı</th>
                                        <th>İşveren</th>
                                        <th>Durum</th>
                                        <th>Başvuru Tarihi</th>
                                        <th>İşlemler</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($applications as $app): ?>
                                    <tr>
                                        <td><?php echo $app['id']; ?></td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($app['caregiver_name']); ?></strong>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($app['caregiver_email']); ?></small>
                                            <?php if ($app['caregiver_phone']): ?>
                                                <br><small class="text-muted"><?php echo htmlspecialchars($app['caregiver_phone']); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($app['job_title']); ?></strong>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($app['job_location']); ?></small>
                                        </td>
                                        <td>
                                            <?php echo htmlspecialchars($app['employer_name']); ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($app['employer_email']); ?></small>
                                        </td>
                                        <td>
                                            <?php if ($app['status'] === 'pending'): ?>
                                                <span class="badge bg-warning">Bekleyen</span>
                                            <?php elseif ($app['status'] === 'accepted'): ?>
                                                <span class="badge bg-success">Kabul Edildi</span>
                                            <?php elseif ($app['status'] === 'rejected'): ?>
                                                <span class="badge bg-danger">Reddedildi</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Bilinmiyor</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small><?php echo date('d.m.Y H:i', strtotime($app['created_at'])); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="viewApplication(<?php echo $app['id']; ?>)">
                                                    <i class="bi bi-eye"></i>
                                                </button>
                                                <?php if ($app['status'] === 'pending'): ?>
                                                    <button class="btn btn-outline-success" onclick="acceptApplication(<?php echo $app['id']; ?>)">
                                                        <i class="bi bi-check"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger" onclick="rejectApplication(<?php echo $app['id']; ?>)">
                                                        <i class="bi bi-x"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <?php if (empty($applications)): ?>
                            <div class="text-center py-4">
                                <i class="bi bi-file-text text-muted" style="font-size: 3rem;"></i>
                                <h5 class="text-muted mt-3">Henüz başvuru yok</h5>
                                <p class="text-muted">Bakıcılar iş başvurusu yapmaya başladığında burada görünecek.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function viewApplication(appId) {
            alert('Başvuru detay sayfası yakında aktif olacak. ID: ' + appId);
        }

        function acceptApplication(appId) {
            if (confirm('Bu başvuruyu kabul etmek istediğinizden emin misiniz?')) {
                alert('Başvuru kabul etme özelliği yakında aktif olacak. ID: ' + appId);
            }
        }

        function rejectApplication(appId) {
            if (confirm('Bu başvuruyu reddetmek istediğinizden emin misiniz?')) {
                alert('Başvuru reddetme özelliği yakında aktif olacak. ID: ' + appId);
            }
        }
    </script>
</body>
</html>
