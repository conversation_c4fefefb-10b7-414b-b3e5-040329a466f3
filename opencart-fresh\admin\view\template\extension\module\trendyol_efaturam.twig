{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-trendyol-efaturam" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_edit }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-trendyol-efaturam" class="form-horizontal">
          
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-status">{{ entry_status }}</label>
            <div class="col-sm-10">
              <select name="module_trendyol_efaturam_status" id="input-status" class="form-control">
                {% if module_trendyol_efaturam_status %}
                <option value="1" selected="selected">{{ text_enabled }}</option>
                <option value="0">{{ text_disabled }}</option>
                {% else %}
                <option value="1">{{ text_enabled }}</option>
                <option value="0" selected="selected">{{ text_disabled }}</option>
                {% endif %}
              </select>
            </div>
          </div>
          
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-mode">{{ entry_mode }}</label>
            <div class="col-sm-10">
              <select name="module_trendyol_efaturam_mode" id="input-mode" class="form-control">
                {% if module_trendyol_efaturam_mode == 'test' %}
                <option value="test" selected="selected">{{ text_test_mode }}</option>
                <option value="production">{{ text_production_mode }}</option>
                {% else %}
                <option value="test">{{ text_test_mode }}</option>
                <option value="production" selected="selected">{{ text_production_mode }}</option>
                {% endif %}
              </select>
            </div>
          </div>
          
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-api-url">{{ entry_api_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="module_trendyol_efaturam_api_url" value="{{ module_trendyol_efaturam_api_url }}" placeholder="{{ entry_api_url }}" id="input-api-url" class="form-control" />
              {% if error_api_url %}
              <div class="text-danger">{{ error_api_url }}</div>
              {% endif %}
            </div>
          </div>
          
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-username">{{ entry_username }}</label>
            <div class="col-sm-10">
              <input type="text" name="module_trendyol_efaturam_username" value="{{ module_trendyol_efaturam_username }}" placeholder="{{ entry_username }}" id="input-username" class="form-control" />
              {% if error_username %}
              <div class="text-danger">{{ error_username }}</div>
              {% endif %}
            </div>
          </div>
          
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-password">{{ entry_password }}</label>
            <div class="col-sm-10">
              <input type="password" name="module_trendyol_efaturam_password" value="{{ module_trendyol_efaturam_password }}" placeholder="{{ entry_password }}" id="input-password" class="form-control" />
              {% if error_password %}
              <div class="text-danger">{{ error_password }}</div>
              {% endif %}
            </div>
          </div>
          
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-company-name">{{ entry_company_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="module_trendyol_efaturam_company_name" value="{{ module_trendyol_efaturam_company_name }}" placeholder="{{ entry_company_name }}" id="input-company-name" class="form-control" />
            </div>
          </div>
          
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-company-tax-number">{{ entry_company_tax_number }}</label>
            <div class="col-sm-10">
              <input type="text" name="module_trendyol_efaturam_company_tax_number" value="{{ module_trendyol_efaturam_company_tax_number }}" placeholder="{{ entry_company_tax_number }}" id="input-company-tax-number" class="form-control" />
            </div>
          </div>
          
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-company-tax-office">{{ entry_company_tax_office }}</label>
            <div class="col-sm-10">
              <input type="text" name="module_trendyol_efaturam_company_tax_office" value="{{ module_trendyol_efaturam_company_tax_office }}" placeholder="{{ entry_company_tax_office }}" id="input-company-tax-office" class="form-control" />
            </div>
          </div>
          
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-auto-send">{{ entry_auto_send }}</label>
            <div class="col-sm-10">
              <select name="module_trendyol_efaturam_auto_send" id="input-auto-send" class="form-control">
                {% if module_trendyol_efaturam_auto_send %}
                <option value="1" selected="selected">{{ text_enabled }}</option>
                <option value="0">{{ text_disabled }}</option>
                {% else %}
                <option value="1">{{ text_enabled }}</option>
                <option value="0" selected="selected">{{ text_disabled }}</option>
                {% endif %}
              </select>
            </div>
          </div>
          
          <div class="form-group">
            <div class="col-sm-offset-2 col-sm-10">
              <button type="button" id="button-test-connection" class="btn btn-info">{{ button_test_connection }}</button>
            </div>
          </div>
          
        </form>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
$('#button-test-connection').on('click', function() {
    $.ajax({
        url: '{{ test_connection }}',
        type: 'post',
        data: $('#form-trendyol-efaturam').serialize(),
        dataType: 'json',
        beforeSend: function() {
            $('#button-test-connection').button('loading');
        },
        complete: function() {
            $('#button-test-connection').button('reset');
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            
            if (json['error']) {
                $('#content > .container-fluid').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
            }
            
            if (json['success']) {
                $('#content > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});
</script>

{{ footer }}
