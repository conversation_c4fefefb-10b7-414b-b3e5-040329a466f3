    <!-- Newsletter Section -->
    <section class="py-5" style="background: var(--gradient-primary);">
        <div class="container">
            <div class="row align-items-center text-white">
                <div class="col-md-8">
                    <h3 class="font-display mb-2">Güncel Haberler ve İlanlar</h3>
                    <p class="mb-0">En son haberler ve özel ilanlar için bültenimize abone olun.</p>
                </div>
                <div class="col-md-4">
                    <div class="input-group">
                        <input type="email" class="form-control" placeholder="E-posta adresiniz" id="newsletter-email">
                        <button class="btn btn-light" type="button" id="newsletter-btn">
                            <i class="fas fa-paper-plane me-1"></i> Abone Ol
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer style="background: var(--dark-color); color: white; padding: 3rem 0 1rem;">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <h5 class="font-display" style="color: var(--primary-color); margin-bottom: 1rem;">
                        <i class="fas fa-newspaper me-2"></i>EscortNews
                    </h5>
                    <p style="color: rgba(255,255,255,0.7);">
                        Türkiye'nin en güvenilir escort haber ve ilan platformu. 
                        Kaliteli içerik ve güvenli deneyim için doğru adrestesiniz.
                    </p>
                    <div class="social-links">
                        <a href="#" class="me-3" style="color: rgba(255,255,255,0.7); font-size: 1.2rem;"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="me-3" style="color: rgba(255,255,255,0.7); font-size: 1.2rem;"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="me-3" style="color: rgba(255,255,255,0.7); font-size: 1.2rem;"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="me-3" style="color: rgba(255,255,255,0.7); font-size: 1.2rem;"><i class="fab fa-telegram"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5 style="color: var(--primary-color); margin-bottom: 1rem;">Hızlı Linkler</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.php" style="color: rgba(255,255,255,0.7); text-decoration: none;">Ana Sayfa</a></li>
                        <li><a href="ads.php" style="color: rgba(255,255,255,0.7); text-decoration: none;">İlanlar</a></li>
                        <li><a href="#categories" style="color: rgba(255,255,255,0.7); text-decoration: none;">Kategoriler</a></li>
                        <li><a href="#news" style="color: rgba(255,255,255,0.7); text-decoration: none;">Haberler</a></li>
                        <li><a href="#premium" style="color: rgba(255,255,255,0.7); text-decoration: none;">Premium</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5 style="color: var(--primary-color); margin-bottom: 1rem;">Kategoriler</h5>
                    <ul class="list-unstyled">
                        <?php foreach (array_slice($categories, 0, 4) as $category): ?>
                        <li><a href="category.php?slug=<?php echo $category['slug']; ?>" style="color: rgba(255,255,255,0.7); text-decoration: none;"><?php echo htmlspecialchars($category['name']); ?></a></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5 style="color: var(--primary-color); margin-bottom: 1rem;">Şehirler</h5>
                    <ul class="list-unstyled">
                        <?php foreach (array_slice($cities, 0, 4) as $city): ?>
                        <li><a href="city.php?slug=<?php echo $city['slug']; ?>" style="color: rgba(255,255,255,0.7); text-decoration: none;"><?php echo htmlspecialchars($city['name']); ?></a></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5 style="color: var(--primary-color); margin-bottom: 1rem;">Yasal</h5>
                    <ul class="list-unstyled">
                        <li><a href="privacy.php" style="color: rgba(255,255,255,0.7); text-decoration: none;">Gizlilik Politikası</a></li>
                        <li><a href="terms.php" style="color: rgba(255,255,255,0.7); text-decoration: none;">Kullanım Şartları</a></li>
                        <li><a href="contact.php" style="color: rgba(255,255,255,0.7); text-decoration: none;">İletişim</a></li>
                        <li><a href="about.php" style="color: rgba(255,255,255,0.7); text-decoration: none;">Hakkımızda</a></li>
                    </ul>
                </div>
            </div>
            <hr style="border-color: rgba(255,255,255,0.2); margin: 2rem 0;">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0" style="color: rgba(255,255,255,0.7);">
                        &copy; <?php echo date('Y'); ?> EscortNews. Tüm hakları saklıdır.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0" style="color: rgba(255,255,255,0.7);">
                        <i class="fas fa-code" style="color: var(--primary-color);"></i> 
                        PHP <?php echo phpversion(); ?> ile geliştirilmiştir
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <!-- Common JavaScript -->
    <script>
        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });
        
        // Search form enhancement
        const searchForms = document.querySelectorAll('form[action*="search"]');
        searchForms.forEach(form => {
            form.addEventListener('submit', function(e) {
                const searchInput = this.querySelector('input[name="q"]');
                if (searchInput && searchInput.value.trim() === '') {
                    e.preventDefault();
                    searchInput.focus();
                    searchInput.placeholder = 'Lütfen arama terimi girin...';
                }
            });
        });
        
        // Card hover effects
        document.querySelectorAll('.ad-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
        
        // Newsletter subscription
        const newsletterBtn = document.getElementById('newsletter-btn');
        if (newsletterBtn) {
            newsletterBtn.addEventListener('click', function() {
                const emailInput = document.getElementById('newsletter-email');
                const email = emailInput.value.trim();
                
                if (email === '') {
                    alert('Lütfen e-posta adresinizi girin.');
                    emailInput.focus();
                    return;
                }
                
                if (!isValidEmail(email)) {
                    alert('Lütfen geçerli bir e-posta adresi girin.');
                    emailInput.focus();
                    return;
                }
                
                // Simulate subscription
                this.innerHTML = '<i class="fas fa-check me-1"></i> Abone Olundu!';
                this.classList.remove('btn-light');
                this.classList.add('btn-success');
                emailInput.value = '';
                
                setTimeout(() => {
                    this.innerHTML = '<i class="fas fa-paper-plane me-1"></i> Abone Ol';
                    this.classList.remove('btn-success');
                    this.classList.add('btn-light');
                }, 3000);
            });
        }
        
        // Email validation
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
        
        // Lazy loading for images
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        if (img.dataset.src) {
                            img.src = img.dataset.src;
                            img.classList.remove('lazy');
                            imageObserver.unobserve(img);
                        }
                    }
                });
            });
            
            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
        
        // Back to top button
        const backToTopBtn = document.createElement('button');
        backToTopBtn.innerHTML = '<i class="fas fa-arrow-up"></i>';
        backToTopBtn.className = 'btn btn-primary position-fixed';
        backToTopBtn.style.cssText = 'bottom: 2rem; right: 2rem; z-index: 1000; border-radius: 50%; width: 50px; height: 50px; display: none;';
        document.body.appendChild(backToTopBtn);
        
        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopBtn.style.display = 'block';
            } else {
                backToTopBtn.style.display = 'none';
            }
        });
        
        backToTopBtn.addEventListener('click', () => {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
    </script>
    
    <?php if (isset($additionalJS)): ?>
    <script><?php echo $additionalJS; ?></script>
    <?php endif; ?>
</body>
</html>
