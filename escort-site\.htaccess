# Escort İlan Sitesi - Apache Configuration
# CodeIgniter 4 için .htaccess

# Disable directory browsing
Options -Indexes

# Redirect to public folder
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ public/$1 [L]

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
</IfModule>

# Prevent access to sensitive files
<FilesMatch "\.(env|ini|log|sh|sql)$">
    Order allow,deny
    <PERSON>y from all
</FilesMatch>

# Prevent access to system directories
RedirectMatch 403 ^/?(app|system|writable)/.*$
