# Escort İlan Sitesi - Apache Configuration
# CodeIgniter 4 için .htaccess

# Disable directory browsing
Options -Indexes

# Redirect everything to public folder
<IfModule mod_rewrite.c>
    RewriteEngine On

    # If the request is for a file or directory that exists in public, serve it directly
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d

    # Redirect all requests to public/index.php
    RewriteRule ^(.*)$ public/index.php/$1 [L]
</IfModule>

# If mod_rewrite is not available, redirect using ErrorDocument
<IfModule !mod_rewrite.c>
    ErrorDocument 404 /escort-site/public/index.php
</IfModule>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
</IfModule>

# Prevent access to sensitive files
<FilesMatch "\.(env|ini|log|sh|sql)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Prevent access to system directories
RedirectMatch 403 ^/?(app|system|writable)/.*$
