# Escort İlan Sitesi - Apache Configuration
# CodeIgniter 4 için .htaccess

# Enable directory browsing for development
Options +Indexes +FollowSymLinks

# Redirect to public folder
RewriteEngine On

# Handle Angular and other front-end routes
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/public/
RewriteRule ^(.*)$ public/index.php/$1 [L,QSA]

# Direct access to public folder
RewriteCond %{REQUEST_URI} ^/public/
RewriteRule ^public/(.*)$ public/$1 [L]

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
</IfModule>

# Prevent access to sensitive files
<FilesMatch "\.(env|ini|log|sh|sql)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Prevent access to system directories
RedirectMatch 403 ^/?(app|system|writable)/.*$
