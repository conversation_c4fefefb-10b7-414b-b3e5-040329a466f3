<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Giriş kontrolü
if (!isLoggedIn()) {
    header('Location: ../auth/login.php');
    exit;
}

// Sadece admin kullanıcıları erişebilir
if ($_SESSION['user_type'] !== 'admin') {
    header('Location: ../dashboard.php?error=permission_denied');
    exit;
}

$job_id = intval($_GET['id'] ?? 0);
if (!$job_id) {
    header('Location: jobs.php?error=invalid_job');
    exit;
}

// İş ilanı bilgilerini getir
try {
    $sql = "SELECT jl.*, u.full_name as employer_name, u.email as employer_email 
            FROM job_listings jl 
            JOIN users u ON jl.user_id = u.id 
            WHERE jl.id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$job_id]);
    $job = $stmt->fetch();
    
    if (!$job) {
        header('Location: jobs.php?error=job_not_found');
        exit;
    }
    
} catch (PDOException $e) {
    header('Location: jobs.php?error=database_error');
    exit;
}

// Form gönderildi
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = trim($_POST['title'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $location = trim($_POST['location'] ?? '');
    $location_city = trim($_POST['location_city'] ?? '');
    $job_type = $_POST['job_type'] ?? '';
    $budget_min = floatval($_POST['budget_min'] ?? 0);
    $budget_max = floatval($_POST['budget_max'] ?? 0);
    $budget_type = $_POST['budget_type'] ?? '';
    $requirements = trim($_POST['requirements'] ?? '');
    $status = $_POST['status'] ?? '';
    $admin_notes = trim($_POST['admin_notes'] ?? '');
    
    $errors = [];
    
    // Validasyon
    if (empty($title)) {
        $errors[] = 'İş başlığı gereklidir.';
    } elseif (strlen($title) < 10) {
        $errors[] = 'İş başlığı en az 10 karakter olmalıdır.';
    }
    
    if (empty($description)) {
        $errors[] = 'İş açıklaması gereklidir.';
    } elseif (strlen($description) < 50) {
        $errors[] = 'İş açıklaması en az 50 karakter olmalıdır.';
    }
    
    if (empty($location)) {
        $errors[] = 'Lokasyon gereklidir.';
    }
    
    if (empty($job_type)) {
        $errors[] = 'İş türü seçiniz.';
    }
    
    $valid_statuses = ['active', 'paused', 'closed', 'expired'];
    if (!in_array($status, $valid_statuses)) {
        $errors[] = 'Geçerli bir durum seçiniz.';
    }
    
    // Güncelleme
    if (empty($errors)) {
        try {
            $update_sql = "UPDATE job_listings SET 
                          title = ?, 
                          description = ?, 
                          location = ?, 
                          location_city = ?, 
                          job_type = ?, 
                          budget_min = ?, 
                          budget_max = ?, 
                          budget_type = ?, 
                          requirements = ?, 
                          status = ?, 
                          admin_notes = ?, 
                          updated_at = NOW() 
                          WHERE id = ?";
            $update_stmt = $db->prepare($update_sql);
            $update_stmt->execute([
                $title, $description, $location, $location_city, 
                $job_type, $budget_min, $budget_max, $budget_type, 
                $requirements, $status, $admin_notes, $job_id
            ]);
            
            header('Location: jobs.php?success=job_updated');
            exit;
            
        } catch (PDOException $e) {
            $errors[] = 'Güncelleme sırasında hata oluştu: ' . $e->getMessage();
        }
    }
}

$page_title = 'İş İlanı Düzenle';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Admin Panel</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c5aa0;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
        }
        
        .char-counter {
            font-size: 0.875rem;
            color: #6c757d;
        }
        
        .char-counter.warning {
            color: #fd7e14;
        }
        
        .char-counter.danger {
            color: #dc3545;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../dashboard.php">
                <i class="bi bi-shield-check me-2"></i>Admin Panel
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="jobs.php">İş İlanları</a>
                <a class="nav-link" href="../dashboard.php">Dashboard</a>
                <a class="nav-link" href="../auth/logout.php">Çıkış</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="bi bi-pencil me-2"></i>İş İlanı Düzenle</h4>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0">
                                <li class="breadcrumb-item"><a href="../dashboard.php">Dashboard</a></li>
                                <li class="breadcrumb-item"><a href="jobs.php">İş İlanları</a></li>
                                <li class="breadcrumb-item active">Düzenle</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="card-body">
                        <!-- Errors -->
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <!-- İş İlanı Bilgileri -->
                        <div class="row mb-4">
                            <div class="col-md-8">
                                <h6>İş İlanı Bilgileri</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>İlan ID:</strong></td>
                                        <td>#<?php echo $job['id']; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>İşveren:</strong></td>
                                        <td><?php echo htmlspecialchars($job['employer_name']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>E-posta:</strong></td>
                                        <td><?php echo htmlspecialchars($job['employer_email']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Oluşturulma:</strong></td>
                                        <td><?php echo date('d.m.Y H:i', strtotime($job['created_at'])); ?></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-4">
                                <h6>Mevcut Durum</h6>
                                <div class="mb-3">
                                    <?php
                                    $status_colors = [
                                        'active' => 'success',
                                        'paused' => 'warning',
                                        'closed' => 'secondary',
                                        'expired' => 'danger'
                                    ];
                                    
                                    $status_names = [
                                        'active' => 'Aktif',
                                        'paused' => 'Duraklatıldı',
                                        'closed' => 'Kapatıldı',
                                        'expired' => 'Süresi Doldu'
                                    ];
                                    ?>
                                    <span class="badge bg-<?php echo $status_colors[$job['status']] ?? 'secondary'; ?> fs-6">
                                        <?php echo $status_names[$job['status']] ?? $job['status']; ?>
                                    </span>
                                </div>
                                
                                <div class="d-grid gap-2">
                                    <a href="../job-details.php?id=<?php echo $job['id']; ?>" target="_blank" class="btn btn-outline-primary btn-sm">
                                        <i class="bi bi-eye me-2"></i>Önizleme
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Düzenleme Formu -->
                        <form method="POST">
                            <div class="row g-3">
                                <div class="col-12">
                                    <label for="title" class="form-label">İş Başlığı *</label>
                                    <input type="text" class="form-control" id="title" name="title" 
                                           value="<?php echo htmlspecialchars($job['title']); ?>" 
                                           required maxlength="200">
                                </div>
                                
                                <div class="col-12">
                                    <label for="description" class="form-label">İş Açıklaması *</label>
                                    <textarea class="form-control" id="description" name="description" rows="6" 
                                              required maxlength="2000"><?php echo htmlspecialchars($job['description']); ?></textarea>
                                    <div class="d-flex justify-content-between">
                                        <div class="form-text">En az 50 karakter</div>
                                        <div class="char-counter" id="descCounter">
                                            <span id="descCount"><?php echo strlen($job['description']); ?></span>/2000
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="location" class="form-label">Lokasyon *</label>
                                    <input type="text" class="form-control" id="location" name="location" 
                                           value="<?php echo htmlspecialchars($job['location']); ?>" 
                                           placeholder="Örn: Kadıköy, İstanbul" required>
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="location_city" class="form-label">Şehir</label>
                                    <input type="text" class="form-control" id="location_city" name="location_city" 
                                           value="<?php echo htmlspecialchars($job['location_city']); ?>" 
                                           placeholder="İstanbul">
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="job_type" class="form-label">İş Türü *</label>
                                    <select class="form-select" id="job_type" name="job_type" required>
                                        <option value="">Seçiniz</option>
                                        <option value="child_care" <?php echo $job['job_type'] === 'child_care' ? 'selected' : ''; ?>>Çocuk Bakımı</option>
                                        <option value="elderly_care" <?php echo $job['job_type'] === 'elderly_care' ? 'selected' : ''; ?>>Yaşlı Bakımı</option>
                                        <option value="patient_care" <?php echo $job['job_type'] === 'patient_care' ? 'selected' : ''; ?>>Hasta Bakımı</option>
                                        <option value="house_cleaning" <?php echo $job['job_type'] === 'house_cleaning' ? 'selected' : ''; ?>>Ev Temizliği</option>
                                        <option value="companion" <?php echo $job['job_type'] === 'companion' ? 'selected' : ''; ?>>Refakatçi</option>
                                        <option value="other" <?php echo $job['job_type'] === 'other' ? 'selected' : ''; ?>>Diğer</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="status" class="form-label">Durum *</label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="active" <?php echo $job['status'] === 'active' ? 'selected' : ''; ?>>Aktif</option>
                                        <option value="paused" <?php echo $job['status'] === 'paused' ? 'selected' : ''; ?>>Duraklatıldı</option>
                                        <option value="closed" <?php echo $job['status'] === 'closed' ? 'selected' : ''; ?>>Kapatıldı</option>
                                        <option value="expired" <?php echo $job['status'] === 'expired' ? 'selected' : ''; ?>>Süresi Doldu</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-4">
                                    <label for="budget_min" class="form-label">Minimum Bütçe</label>
                                    <input type="number" class="form-control" id="budget_min" name="budget_min" 
                                           value="<?php echo $job['budget_min']; ?>" min="0" step="0.01">
                                </div>
                                
                                <div class="col-md-4">
                                    <label for="budget_max" class="form-label">Maksimum Bütçe</label>
                                    <input type="number" class="form-control" id="budget_max" name="budget_max" 
                                           value="<?php echo $job['budget_max']; ?>" min="0" step="0.01">
                                </div>
                                
                                <div class="col-md-4">
                                    <label for="budget_type" class="form-label">Bütçe Türü</label>
                                    <select class="form-select" id="budget_type" name="budget_type">
                                        <option value="hourly" <?php echo $job['budget_type'] === 'hourly' ? 'selected' : ''; ?>>Saatlik</option>
                                        <option value="daily" <?php echo $job['budget_type'] === 'daily' ? 'selected' : ''; ?>>Günlük</option>
                                        <option value="weekly" <?php echo $job['budget_type'] === 'weekly' ? 'selected' : ''; ?>>Haftalık</option>
                                        <option value="monthly" <?php echo $job['budget_type'] === 'monthly' ? 'selected' : ''; ?>>Aylık</option>
                                        <option value="fixed" <?php echo $job['budget_type'] === 'fixed' ? 'selected' : ''; ?>>Sabit</option>
                                    </select>
                                </div>
                                
                                <div class="col-12">
                                    <label for="requirements" class="form-label">Gereksinimler</label>
                                    <textarea class="form-control" id="requirements" name="requirements" rows="4" 
                                              maxlength="1000"><?php echo htmlspecialchars($job['requirements']); ?></textarea>
                                    <div class="form-text">İş için gerekli özellikler, deneyim vb.</div>
                                </div>
                                
                                <div class="col-12">
                                    <label for="admin_notes" class="form-label">Admin Notları</label>
                                    <textarea class="form-control" id="admin_notes" name="admin_notes" rows="3" 
                                              maxlength="500"><?php echo htmlspecialchars($job['admin_notes'] ?? ''); ?></textarea>
                                    <div class="form-text">Sadece admin tarafından görülebilir notlar</div>
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-between mt-4">
                                <a href="jobs.php" class="btn btn-secondary">
                                    <i class="bi bi-arrow-left me-2"></i>Geri Dön
                                </a>
                                <div>
                                    <a href="../job-details.php?id=<?php echo $job['id']; ?>" target="_blank" class="btn btn-outline-info me-2">
                                        <i class="bi bi-eye me-2"></i>Önizleme
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check me-2"></i>Güncelle
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Karakter sayacı
        const descTextarea = document.getElementById('description');
        const descCount = document.getElementById('descCount');
        const descCounter = document.getElementById('descCounter');
        
        function updateDescCount() {
            const length = descTextarea.value.length;
            descCount.textContent = length;
            
            descCounter.className = 'char-counter';
            if (length > 1800) {
                descCounter.classList.add('danger');
            } else if (length > 1600) {
                descCounter.classList.add('warning');
            }
        }
        
        descTextarea.addEventListener('input', updateDescCount);
        updateDescCount();
        
        // Form validasyonu
        document.querySelector('form').addEventListener('submit', function(e) {
            const title = document.getElementById('title').value.trim();
            const description = descTextarea.value.trim();
            const location = document.getElementById('location').value.trim();
            
            if (title.length < 10) {
                e.preventDefault();
                alert('İş başlığı en az 10 karakter olmalıdır.');
                document.getElementById('title').focus();
                return;
            }
            
            if (description.length < 50) {
                e.preventDefault();
                alert('İş açıklaması en az 50 karakter olmalıdır.');
                descTextarea.focus();
                return;
            }
            
            if (location.length < 3) {
                e.preventDefault();
                alert('Lokasyon en az 3 karakter olmalıdır.');
                document.getElementById('location').focus();
                return;
            }
        });
    </script>
</body>
</html>
