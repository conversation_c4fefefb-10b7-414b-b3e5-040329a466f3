<?php
/**
 * Trendyol E-Faturam OpenCart 3.0.3.2 Extension
 * Admin Controller
 * 
 * <AUTHOR> Name
 * @version 1.0.0
 * @license MIT
 */

class ControllerExtensionModuleTrendyolEfaturam extends Controller {
    
    private $error = array();
    
    public function index() {
        $this->load->language('extension/module/trendyol_efaturam');
        
        $this->document->setTitle($this->language->get('heading_title'));
        
        $this->load->model('setting/setting');
        
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validate()) {
            $this->model_setting_setting->editSetting('module_trendyol_efaturam', $this->request->post);
            
            $this->session->data['success'] = $this->language->get('text_success');
            
            $this->response->redirect($this->url->link('marketplace/extension', 'user_token=' . $this->session->data['user_token'] . '&type=module', true));
        }
        
        $data['heading_title'] = $this->language->get('heading_title');
        
        $data['text_edit'] = $this->language->get('text_edit');
        $data['text_enabled'] = $this->language->get('text_enabled');
        $data['text_disabled'] = $this->language->get('text_disabled');
        $data['text_test_mode'] = $this->language->get('text_test_mode');
        $data['text_production_mode'] = $this->language->get('text_production_mode');
        
        $data['entry_status'] = $this->language->get('entry_status');
        $data['entry_mode'] = $this->language->get('entry_mode');
        $data['entry_api_url'] = $this->language->get('entry_api_url');
        $data['entry_username'] = $this->language->get('entry_username');
        $data['entry_password'] = $this->language->get('entry_password');
        $data['entry_company_name'] = $this->language->get('entry_company_name');
        $data['entry_company_tax_number'] = $this->language->get('entry_company_tax_number');
        $data['entry_company_tax_office'] = $this->language->get('entry_company_tax_office');
        $data['entry_auto_send'] = $this->language->get('entry_auto_send');
        
        $data['button_save'] = $this->language->get('button_save');
        $data['button_cancel'] = $this->language->get('button_cancel');
        $data['button_test_connection'] = $this->language->get('button_test_connection');
        
        if (isset($this->error['warning'])) {
            $data['error_warning'] = $this->error['warning'];
        } else {
            $data['error_warning'] = '';
        }
        
        if (isset($this->error['api_url'])) {
            $data['error_api_url'] = $this->error['api_url'];
        } else {
            $data['error_api_url'] = '';
        }
        
        if (isset($this->error['username'])) {
            $data['error_username'] = $this->error['username'];
        } else {
            $data['error_username'] = '';
        }
        
        if (isset($this->error['password'])) {
            $data['error_password'] = $this->error['password'];
        } else {
            $data['error_password'] = '';
        }
        
        $data['breadcrumbs'] = array();
        
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_extension'),
            'href' => $this->url->link('marketplace/extension', 'user_token=' . $this->session->data['user_token'] . '&type=module', true)
        );
        
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('extension/module/trendyol_efaturam', 'user_token=' . $this->session->data['user_token'], true)
        );
        
        $data['action'] = $this->url->link('extension/module/trendyol_efaturam', 'user_token=' . $this->session->data['user_token'], true);
        
        $data['cancel'] = $this->url->link('marketplace/extension', 'user_token=' . $this->session->data['user_token'] . '&type=module', true);
        
        $data['test_connection'] = $this->url->link('extension/module/trendyol_efaturam/testConnection', 'user_token=' . $this->session->data['user_token'], true);
        
        // Get current settings
        if (isset($this->request->post['module_trendyol_efaturam_status'])) {
            $data['module_trendyol_efaturam_status'] = $this->request->post['module_trendyol_efaturam_status'];
        } else {
            $data['module_trendyol_efaturam_status'] = $this->config->get('module_trendyol_efaturam_status');
        }
        
        if (isset($this->request->post['module_trendyol_efaturam_mode'])) {
            $data['module_trendyol_efaturam_mode'] = $this->request->post['module_trendyol_efaturam_mode'];
        } else {
            $data['module_trendyol_efaturam_mode'] = $this->config->get('module_trendyol_efaturam_mode');
        }
        
        if (isset($this->request->post['module_trendyol_efaturam_api_url'])) {
            $data['module_trendyol_efaturam_api_url'] = $this->request->post['module_trendyol_efaturam_api_url'];
        } else {
            $data['module_trendyol_efaturam_api_url'] = $this->config->get('module_trendyol_efaturam_api_url');
        }
        
        if (isset($this->request->post['module_trendyol_efaturam_username'])) {
            $data['module_trendyol_efaturam_username'] = $this->request->post['module_trendyol_efaturam_username'];
        } else {
            $data['module_trendyol_efaturam_username'] = $this->config->get('module_trendyol_efaturam_username');
        }
        
        if (isset($this->request->post['module_trendyol_efaturam_password'])) {
            $data['module_trendyol_efaturam_password'] = $this->request->post['module_trendyol_efaturam_password'];
        } else {
            $data['module_trendyol_efaturam_password'] = $this->config->get('module_trendyol_efaturam_password');
        }
        
        if (isset($this->request->post['module_trendyol_efaturam_company_name'])) {
            $data['module_trendyol_efaturam_company_name'] = $this->request->post['module_trendyol_efaturam_company_name'];
        } else {
            $data['module_trendyol_efaturam_company_name'] = $this->config->get('module_trendyol_efaturam_company_name');
        }
        
        if (isset($this->request->post['module_trendyol_efaturam_company_tax_number'])) {
            $data['module_trendyol_efaturam_company_tax_number'] = $this->request->post['module_trendyol_efaturam_company_tax_number'];
        } else {
            $data['module_trendyol_efaturam_company_tax_number'] = $this->config->get('module_trendyol_efaturam_company_tax_number');
        }
        
        if (isset($this->request->post['module_trendyol_efaturam_company_tax_office'])) {
            $data['module_trendyol_efaturam_company_tax_office'] = $this->request->post['module_trendyol_efaturam_company_tax_office'];
        } else {
            $data['module_trendyol_efaturam_company_tax_office'] = $this->config->get('module_trendyol_efaturam_company_tax_office');
        }
        
        if (isset($this->request->post['module_trendyol_efaturam_auto_send'])) {
            $data['module_trendyol_efaturam_auto_send'] = $this->request->post['module_trendyol_efaturam_auto_send'];
        } else {
            $data['module_trendyol_efaturam_auto_send'] = $this->config->get('module_trendyol_efaturam_auto_send');
        }
        
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        
        $this->response->setOutput($this->load->view('extension/module/trendyol_efaturam', $data));
    }
    
    public function testConnection() {
        $this->load->language('extension/module/trendyol_efaturam');
        
        $json = array();
        
        if (!$this->user->hasPermission('modify', 'extension/module/trendyol_efaturam')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            $this->load->library('trendyol_efaturam_api');
            
            $api = new TrendyolEfaturamApi(
                $this->request->post['api_url'],
                $this->request->post['username'],
                $this->request->post['password'],
                $this->request->post['mode'] == 'test'
            );
            
            $result = $api->testConnection();
            
            if ($result['success']) {
                $json['success'] = $this->language->get('text_connection_success');
            } else {
                $json['error'] = $this->language->get('text_connection_failed') . ': ' . $result['error'];
            }
        }
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }
    
    protected function validate() {
        if (!$this->user->hasPermission('modify', 'extension/module/trendyol_efaturam')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }
        
        if (!$this->request->post['module_trendyol_efaturam_api_url']) {
            $this->error['api_url'] = $this->language->get('error_api_url');
        }
        
        if (!$this->request->post['module_trendyol_efaturam_username']) {
            $this->error['username'] = $this->language->get('error_username');
        }
        
        if (!$this->request->post['module_trendyol_efaturam_password']) {
            $this->error['password'] = $this->language->get('error_password');
        }
        
        return !$this->error;
    }
    
    public function install() {
        $this->load->model('setting/event');
        
        // Add event for order status change
        $this->model_setting_event->addEvent('trendyol_efaturam_order_status', 'catalog/model/checkout/order/addOrderHistory/after', 'extension/module/trendyol_efaturam/sendInvoice');
    }
    
    public function uninstall() {
        $this->load->model('setting/event');
        
        // Remove event
        $this->model_setting_event->deleteEventByCode('trendyol_efaturam_order_status');
    }
}
