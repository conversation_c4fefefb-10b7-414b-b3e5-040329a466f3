<?php
// Text
$_['text_title']				= 'Credit or Debit Card (Processed securely by PayPal)';
$_['text_credit_card']			= 'Credit Card Details';
$_['text_start_date']			= '(if available)';
$_['text_issue']				= '(for Maestro and <PERSON> cards only)';
$_['text_wait']					= 'Please wait!';

// Entry
$_['entry_cc_owner']			= 'Card Owner:';
$_['entry_cc_type']				= 'Card Type:';
$_['entry_cc_number']			= 'Card Number:';
$_['entry_cc_start_date']		= 'Card Valid From Date:';
$_['entry_cc_expire_date']		= 'Card Expiry Date:';
$_['entry_cc_cvv2']				= 'Card Security Code (CVV2):';
$_['entry_cc_issue']			= 'Card Issue Number:';

// Error
$_['error_required']			= 'Warning: All payment information fields are required.';
$_['error_general']				= 'Warning: A general problem has occurred with the transaction. Please try again.';
$_['error_config']				= 'Warning: Payment module configuration error. Please verify the login credentials.';
$_['error_address']				= 'Warning: A match of the Payment Address City, State, and Postal Code failed. Please try again.';
$_['error_declined']			= 'Warning: This transaction has been declined. Please try again.';
$_['error_invalid']				= 'Warning: The provided credit card information is invalid. Please try again.';