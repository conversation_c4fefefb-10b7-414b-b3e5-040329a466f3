<?php
// EscortNews - İlanlar Sayfası
require_once 'includes/config.php';

// Sayfa parametreleri
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$itemsPerPage = 12;
$featured = isset($_GET['featured']) ? 1 : 0;
$categorySlug = isset($_GET['category']) ? sanitize($_GET['category']) : '';
$citySlug = isset($_GET['city']) ? sanitize($_GET['city']) : '';
$sortBy = isset($_GET['sort']) ? sanitize($_GET['sort']) : 'newest';

// Sayfa bilgileri
$pageTitle = 'İlanlar - ' . $siteName;
$pageDescription = 'Güncel escort ilanları, kategoriler ve şehirlere göre filtreleme seçenekleri.';
$pageKeywords = 'escort ilanları, güncel ilanlar, kategori, şehir, filtreleme';

// Filtreleme ve sıralama
$whereConditions = array("a.status = 'active'", "(a.expires_at IS NULL OR a.expires_at > NOW())");
$params = array();

if ($featured) {
    $whereConditions[] = "a.featured = 1";
    $pageTitle = 'Öne Çıkan İlanlar - ' . $siteName;
}

if ($categorySlug) {
    $whereConditions[] = "c.slug = ?";
    $params[] = $categorySlug;
    
    // Kategori bilgisini al
    $pdo = getDbConnection();
    $stmt = $pdo->prepare("SELECT name FROM categories WHERE slug = ? AND status = 1");
    $stmt->execute(array($categorySlug));
    $categoryInfo = $stmt->fetch();
    if ($categoryInfo) {
        $pageTitle = $categoryInfo['name'] . ' İlanları - ' . $siteName;
    }
}

if ($citySlug) {
    $whereConditions[] = "ci.slug = ?";
    $params[] = $citySlug;
    
    // Şehir bilgisini al
    $pdo = getDbConnection();
    $stmt = $pdo->prepare("SELECT name FROM cities WHERE slug = ? AND status = 1");
    $stmt->execute(array($citySlug));
    $cityInfo = $stmt->fetch();
    if ($cityInfo) {
        $pageTitle = $cityInfo['name'] . ' İlanları - ' . $siteName;
    }
}

// Sıralama
$orderBy = "a.featured DESC, a.created_at DESC";
switch ($sortBy) {
    case 'price_low':
        $orderBy = "a.featured DESC, a.price_per_hour ASC";
        break;
    case 'price_high':
        $orderBy = "a.featured DESC, a.price_per_hour DESC";
        break;
    case 'oldest':
        $orderBy = "a.featured DESC, a.created_at ASC";
        break;
    default:
        $orderBy = "a.featured DESC, a.created_at DESC";
}

// Toplam ilan sayısını al
$pdo = getDbConnection();
$countSql = "
    SELECT COUNT(*) as total
    FROM ads a 
    LEFT JOIN categories c ON a.category_id = c.id 
    LEFT JOIN cities ci ON a.city_id = ci.id 
    WHERE " . implode(' AND ', $whereConditions);

$stmt = $pdo->prepare($countSql);
$stmt->execute($params);
$totalAds = $stmt->fetch()['total'];

// Sayfalama hesapla
$pagination = calculatePagination($totalAds, $itemsPerPage, $page);

// İlanları al
$sql = "
    SELECT a.*, c.name as category_name, c.color as category_color, c.slug as category_slug,
           ci.name as city_name, ci.slug as city_slug, u.username,
           (SELECT filename FROM ad_photos WHERE ad_id = a.id AND is_primary = 1 LIMIT 1) as primary_photo
    FROM ads a 
    LEFT JOIN categories c ON a.category_id = c.id 
    LEFT JOIN cities ci ON a.city_id = ci.id 
    LEFT JOIN users u ON a.user_id = u.id
    WHERE " . implode(' AND ', $whereConditions) . "
    ORDER BY $orderBy 
    LIMIT $itemsPerPage OFFSET " . $pagination['offset'];

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$ads = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Header'ı dahil et
include 'includes/header.php';
?>

<!-- Page Header -->
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <?php if ($featured): ?>
                        <i class="fas fa-star me-2"></i> Öne Çıkan İlanlar
                    <?php elseif ($categorySlug && isset($categoryInfo)): ?>
                        <i class="fas fa-tag me-2"></i> <?php echo htmlspecialchars($categoryInfo['name']); ?> İlanları
                    <?php elseif ($citySlug && isset($cityInfo)): ?>
                        <i class="fas fa-map-marker-alt me-2"></i> <?php echo htmlspecialchars($cityInfo['name']); ?> İlanları
                    <?php else: ?>
                        <i class="fas fa-list me-2"></i> Tüm İlanlar
                    <?php endif; ?>
                </h1>
                <p class="page-subtitle">
                    Toplam <?php echo number_format($totalAds); ?> ilan bulundu
                </p>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="post-ad.php" class="btn btn-light btn-lg">
                    <i class="fas fa-plus me-2"></i> İlan Ver
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Breadcrumb -->
<div class="container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="index.php">Ana Sayfa</a></li>
            <li class="breadcrumb-item"><a href="ads.php">İlanlar</a></li>
            <?php if ($categorySlug && isset($categoryInfo)): ?>
            <li class="breadcrumb-item active"><?php echo htmlspecialchars($categoryInfo['name']); ?></li>
            <?php elseif ($citySlug && isset($cityInfo)): ?>
            <li class="breadcrumb-item active"><?php echo htmlspecialchars($cityInfo['name']); ?></li>
            <?php elseif ($featured): ?>
            <li class="breadcrumb-item active">Öne Çıkan</li>
            <?php else: ?>
            <li class="breadcrumb-item active">Tümü</li>
            <?php endif; ?>
        </ol>
    </nav>
</div>

<!-- Filters and Sorting -->
<div class="container mb-4">
    <div class="row">
        <div class="col-md-8">
            <div class="d-flex flex-wrap gap-2">
                <!-- Category Filter -->
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-tags me-1"></i> Kategori
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="ads.php">Tüm Kategoriler</a></li>
                        <?php foreach ($categories as $cat): ?>
                        <li><a class="dropdown-item <?php echo ($categorySlug == $cat['slug']) ? 'active' : ''; ?>" 
                               href="ads.php?category=<?php echo $cat['slug']; ?>"><?php echo htmlspecialchars($cat['name']); ?></a></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                
                <!-- City Filter -->
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-map-marker-alt me-1"></i> Şehir
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="ads.php">Tüm Şehirler</a></li>
                        <?php foreach ($cities as $city): ?>
                        <li><a class="dropdown-item <?php echo ($citySlug == $city['slug']) ? 'active' : ''; ?>" 
                               href="ads.php?city=<?php echo $city['slug']; ?>"><?php echo htmlspecialchars($city['name']); ?></a></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                
                <!-- Featured Filter -->
                <a href="ads.php?featured=1" class="btn <?php echo $featured ? 'btn-primary' : 'btn-outline-primary'; ?>">
                    <i class="fas fa-star me-1"></i> Öne Çıkan
                </a>
            </div>
        </div>
        <div class="col-md-4">
            <div class="d-flex justify-content-end">
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-sort me-1"></i> Sırala
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item <?php echo ($sortBy == 'newest') ? 'active' : ''; ?>" 
                               href="?<?php echo http_build_query(array_merge($_GET, array('sort' => 'newest'))); ?>">En Yeni</a></li>
                        <li><a class="dropdown-item <?php echo ($sortBy == 'oldest') ? 'active' : ''; ?>" 
                               href="?<?php echo http_build_query(array_merge($_GET, array('sort' => 'oldest'))); ?>">En Eski</a></li>
                        <li><a class="dropdown-item <?php echo ($sortBy == 'price_low') ? 'active' : ''; ?>" 
                               href="?<?php echo http_build_query(array_merge($_GET, array('sort' => 'price_low'))); ?>">Fiyat (Düşük)</a></li>
                        <li><a class="dropdown-item <?php echo ($sortBy == 'price_high') ? 'active' : ''; ?>" 
                               href="?<?php echo http_build_query(array_merge($_GET, array('sort' => 'price_high'))); ?>">Fiyat (Yüksek)</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Ads Grid -->
<div class="container">
    <?php if (!empty($ads)): ?>
    <div class="row">
        <?php foreach ($ads as $ad): ?>
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card ad-card h-100">
                <?php if ($ad['featured']): ?>
                <div class="ad-badge featured-badge">
                    <i class="fas fa-crown me-1"></i> Premium
                </div>
                <?php endif; ?>
                
                <?php if ($ad['primary_photo']): ?>
                <img src="uploads/ads/<?php echo htmlspecialchars($ad['primary_photo']); ?>" 
                     class="card-img-top" alt="<?php echo htmlspecialchars($ad['title']); ?>">
                <?php else: ?>
                <div class="card-img-top d-flex align-items-center justify-content-center" style="background: var(--gradient-primary); height: 200px;">
                    <i class="fas fa-image fa-3x text-white opacity-50"></i>
                </div>
                <?php endif; ?>
                
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <a href="category.php?slug=<?php echo $ad['category_slug']; ?>" class="badge text-decoration-none" 
                           style="background-color: <?php echo $ad['category_color'] ?? '#DC2626'; ?>;">
                            <?php echo htmlspecialchars($ad['category_name']); ?>
                        </a>
                        <a href="city.php?slug=<?php echo $ad['city_slug']; ?>" class="text-muted text-decoration-none">
                            <small>
                                <i class="fas fa-map-marker-alt me-1"></i>
                                <?php echo htmlspecialchars($ad['city_name']); ?>
                            </small>
                        </a>
                    </div>
                    <h5 class="card-title">
                        <a href="ad-detail.php?slug=<?php echo $ad['slug']; ?>" class="text-decoration-none text-dark">
                            <?php echo htmlspecialchars($ad['title']); ?>
                        </a>
                    </h5>
                    <p class="card-text text-muted">
                        <?php echo htmlspecialchars(substr($ad['description'], 0, 100)) . '...'; ?>
                    </p>
                    <div class="d-flex justify-content-between align-items-center">
                        <?php if ($ad['price_per_hour']): ?>
                        <span class="fw-bold text-primary">
                            ₺<?php echo number_format($ad['price_per_hour'], 0, ',', '.'); ?>/saat
                        </span>
                        <?php else: ?>
                        <span class="text-muted">Fiyat Belirtilmemiş</span>
                        <?php endif; ?>
                        <a href="ad-detail.php?slug=<?php echo $ad['slug']; ?>" class="btn btn-primary btn-sm">
                            <i class="fas fa-eye me-1"></i> Detay
                        </a>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            <?php echo date('d.m.Y H:i', strtotime($ad['created_at'])); ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
    
    <!-- Pagination -->
    <?php if ($pagination['total_pages'] > 1): ?>
    <nav aria-label="İlan sayfaları">
        <ul class="pagination justify-content-center">
            <?php if ($pagination['has_prev']): ?>
            <li class="page-item">
                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, array('page' => $pagination['prev_page']))); ?>">
                    <i class="fas fa-chevron-left"></i> Önceki
                </a>
            </li>
            <?php endif; ?>
            
            <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
            <li class="page-item <?php echo ($i == $pagination['current_page']) ? 'active' : ''; ?>">
                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, array('page' => $i))); ?>"><?php echo $i; ?></a>
            </li>
            <?php endfor; ?>
            
            <?php if ($pagination['has_next']): ?>
            <li class="page-item">
                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, array('page' => $pagination['next_page']))); ?>">
                    Sonraki <i class="fas fa-chevron-right"></i>
                </a>
            </li>
            <?php endif; ?>
        </ul>
    </nav>
    <?php endif; ?>
    
    <?php else: ?>
    <!-- No Ads Found -->
    <div class="text-center py-5">
        <i class="fas fa-search fa-4x text-muted mb-3"></i>
        <h3>İlan Bulunamadı</h3>
        <p class="text-muted">Aradığınız kriterlere uygun ilan bulunmuyor.</p>
        <a href="ads.php" class="btn btn-primary">Tüm İlanları Gör</a>
    </div>
    <?php endif; ?>
</div>

<?php
// Footer'ı dahil et
include 'includes/footer.php';
?>
