<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$job_id = intval($_GET['id'] ?? 0);
if (!$job_id) {
    header('Location: jobs.php?error=invalid_job');
    exit;
}

// İş ilanı bilgilerini getir
try {
    $sql = "SELECT jl.*, u.full_name as employer_name, u.email as employer_email, u.phone as employer_phone, u.city as employer_city
            FROM job_listings jl 
            JOIN users u ON jl.user_id = u.id 
            WHERE jl.id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$job_id]);
    $job = $stmt->fetch();
    
    if (!$job) {
        header('Location: jobs.php?error=job_not_found');
        exit;
    }
    
    // Başvuru sayısını getir
    $applications_count_sql = "SELECT COUNT(*) FROM job_applications WHERE job_id = ?";
    $applications_count_stmt = $db->prepare($applications_count_sql);
    $applications_count_stmt->execute([$job_id]);
    $applications_count = $applications_count_stmt->fetchColumn();
    
    // Kullanıcı başvuru durumunu kontrol et (giriş yapmışsa)
    $user_application = null;
    if (isLoggedIn() && $_SESSION['user_type'] === 'caregiver') {
        $user_app_sql = "SELECT * FROM job_applications WHERE job_id = ? AND caregiver_id = ?";
        $user_app_stmt = $db->prepare($user_app_sql);
        $user_app_stmt->execute([$job_id, $_SESSION['user_id']]);
        $user_application = $user_app_stmt->fetch();
    }
    
} catch (PDOException $e) {
    header('Location: jobs.php?error=database_error');
    exit;
}

$page_title = htmlspecialchars($job['title']);
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bakıcı Platform</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #ff6b6b;
            --primary-dark: #e55555;
            --secondary-color: #4ecdc4;
            --accent-color: #45b7d1;
            --warning-color: #feca57;
            --success-color: #48dbfb;
            --text-dark: #2c3e50;
            --text-light: #7f8c8d;
            --bg-light: #f8f9fa;
            --white: #ffffff;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-dark);
        }

        .job-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 50%, var(--secondary-color) 100%);
            color: white;
            padding: 100px 0 80px;
            position: relative;
            overflow: hidden;
        }

        .job-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" fill="white" opacity="0.1"><path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25"/></svg>');
            background-size: cover;
            background-position: bottom;
        }

        .job-header .container {
            position: relative;
            z-index: 2;
        }

        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            overflow: hidden;
            pointer-events: none;
        }

        .floating-elements::before,
        .floating-elements::after {
            content: '';
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 6s ease-in-out infinite;
        }

        .floating-elements::before {
            width: 150px;
            height: 150px;
            top: 20%;
            right: 10%;
            animation-delay: -2s;
        }

        .floating-elements::after {
            width: 100px;
            height: 100px;
            bottom: 20%;
            left: 10%;
            animation-delay: -4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .job-meta {
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.05), rgba(78, 205, 196, 0.05));
            border-radius: 20px;
            padding: 30px;
            border: 2px solid rgba(255, 107, 107, 0.1);
        }

        .employer-card {
            background: var(--white);
            border-radius: 25px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .employer-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 5px 25px rgba(0,0,0,0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .navbar-brand {
            font-weight: 800;
            font-size: 1.8rem;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-link {
            font-weight: 500;
            color: var(--text-dark) !important;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-link:hover::before {
            width: 100%;
        }

        .card {
            border: none;
            border-radius: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            background: var(--white);
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: var(--white);
            border: none;
            padding: 20px 25px;
            font-weight: 600;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border: none;
            border-radius: 50px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(255, 107, 107, 0.4);
        }

        .btn-outline-primary {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            border-radius: 50px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-outline-primary:hover {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--white);
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(255, 107, 107, 0.4);
        }

        .btn-outline-success {
            border: 2px solid var(--success-color);
            color: var(--success-color);
            border-radius: 50px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-outline-success:hover {
            background: var(--success-color);
            border-color: var(--success-color);
            color: var(--white);
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(72, 219, 251, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, var(--text-light), #95a5a6);
            border: none;
            border-radius: 50px;
            padding: 12px 30px;
            font-weight: 600;
            color: var(--white);
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #95a5a6, var(--text-light));
            transform: translateY(-3px);
            color: var(--white);
        }

        .badge {
            border-radius: 15px;
            padding: 8px 15px;
            font-weight: 600;
        }

        .badge.bg-success {
            background: linear-gradient(135deg, var(--success-color), #00d2d3) !important;
        }

        .badge.bg-warning {
            background: linear-gradient(135deg, var(--warning-color), #f39c12) !important;
        }

        .badge.bg-light {
            background: rgba(255, 255, 255, 0.9) !important;
            color: var(--text-dark) !important;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .alert {
            border: none;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .alert-info {
            background: linear-gradient(135deg, rgba(69, 183, 209, 0.1), rgba(78, 205, 196, 0.1));
            border-left: 4px solid var(--accent-color);
            color: var(--text-dark);
        }

        .alert-warning {
            background: linear-gradient(135deg, rgba(254, 202, 87, 0.1), rgba(243, 156, 18, 0.1));
            border-left: 4px solid var(--warning-color);
            color: var(--text-dark);
        }

        .gradient-text {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .job-price-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .job-header {
                padding: 60px 0 40px;
            }

            .navbar-brand {
                font-size: 1.5rem;
            }

            .btn {
                padding: 10px 20px;
                font-size: 0.9rem;
            }

            .card {
                margin-bottom: 20px;
            }

            .job-meta {
                padding: 20px;
            }

            .employer-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="index.php">
                <i class="bi bi-heart-fill me-2"></i>Bakıcı Platform
            </a>
            
            <div class="navbar-nav ms-auto">
                <?php if (isLoggedIn()): ?>
                    <a class="nav-link" href="dashboard.php">Dashboard</a>
                    <a class="nav-link" href="jobs.php">İş İlanları</a>
                    <a class="nav-link" href="auth/logout.php">Çıkış</a>
                <?php else: ?>
                    <a class="nav-link" href="jobs.php">İş İlanları</a>
                    <a class="nav-link" href="auth/login.php">Giriş</a>
                    <a class="nav-link" href="auth/register.php">Kayıt</a>
                <?php endif; ?>
            </div>
        </div>
    </nav>

    <!-- Job Header -->
    <section class="job-header">
        <div class="floating-elements"></div>
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <nav aria-label="breadcrumb" class="mb-4">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="index.php" style="color: rgba(255, 255, 255, 0.8);">🏠 Ana Sayfa</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="jobs.php" style="color: rgba(255, 255, 255, 0.8);">💼 İş İlanları</a>
                            </li>
                            <li class="breadcrumb-item active" style="color: white;">📋 İş Detayı</li>
                        </ol>
                    </nav>

                    <h1 class="display-5 fw-bold mb-4">
                        ✨ <?php echo htmlspecialchars($job['title']); ?>
                        <?php if ($job['is_urgent']): ?>
                            <span class="badge bg-warning ms-2">
                                <i class="bi bi-exclamation-triangle-fill"></i> ACİL
                            </span>
                        <?php endif; ?>
                    </h1>

                    <div class="d-flex flex-wrap gap-3 mb-4">
                        <span class="badge bg-light text-dark fs-6">
                            <i class="bi bi-geo-alt-fill me-1"></i>📍 <?php echo htmlspecialchars($job['location_city']); ?>
                            <?php if ($job['location_district']): ?>
                                , <?php echo htmlspecialchars($job['location_district']); ?>
                            <?php endif; ?>
                        </span>
                        <span class="badge bg-light text-dark fs-6">
                            <i class="bi bi-briefcase-fill me-1"></i>
                            <?php
                            $job_types = [
                                'child_care' => '👶 Çocuk Bakımı',
                                'elderly_care' => '👴 Yaşlı Bakımı',
                                'patient_care' => '🏥 Hasta Bakımı',
                                'house_cleaning' => '🏠 Ev Temizliği',
                                'companion' => '🤝 Refakatçi',
                                'other' => '📋 Diğer'
                            ];
                            echo $job_types[$job['job_type']] ?? $job['job_type'];
                            ?>
                        </span>
                        <span class="badge bg-<?php echo $job['status'] === 'active' ? 'success' : 'secondary'; ?> fs-6">
                            <?php echo $job['status'] === 'active' ? '✅ Aktif' : '❌ ' . ucfirst($job['status']); ?>
                        </span>
                        <?php if ($job['care_type']): ?>
                        <span class="badge bg-light text-dark fs-6">
                            <i class="bi bi-clock-fill me-1"></i>
                            <?php
                            $care_types = [
                                'live_in' => '🏠 Yatılı',
                                'daily' => '📅 Günlük',
                                'hourly' => '⏰ Saatlik',
                                'weekly' => '📆 Haftalık'
                            ];
                            echo $care_types[$job['care_type']] ?? $job['care_type'];
                            ?>
                        </span>
                        <?php endif; ?>
                    </div>

                    <div class="d-flex align-items-center gap-4 mb-3">
                        <p class="mb-0">
                            <i class="bi bi-calendar3 me-2"></i>
                            📅 <?php echo date('d.m.Y', strtotime($job['created_at'])); ?> tarihinde yayınlandı
                        </p>
                        <?php if ($job['expires_at']): ?>
                        <p class="mb-0">
                            <i class="bi bi-clock me-2"></i>
                            ⏳ Son başvuru: <?php echo date('d.m.Y', strtotime($job['expires_at'])); ?>
                        </p>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="col-lg-4 text-lg-end">
                    <div class="job-price-card">
                        <h4 class="gradient-text fw-bold mb-2">
                            💰 <?php if ($job['budget_min'] || $job['budget_max']): ?>
                                <?php if ($job['budget_min'] && $job['budget_max']): ?>
                                    <?php echo formatMoney($job['budget_min']); ?> - <?php echo formatMoney($job['budget_max']); ?>
                                <?php elseif ($job['budget_min']): ?>
                                    <?php echo formatMoney($job['budget_min']); ?>+
                                <?php else: ?>
                                    <?php echo formatMoney($job['budget_max']); ?>'e kadar
                                <?php endif; ?>
                                <?php if ($job['budget_type']): ?>
                                    <br><small class="text-muted">/<?php echo $job['budget_type'] === 'hourly' ? 'saat' : ($job['budget_type'] === 'daily' ? 'gün' : ($job['budget_type'] === 'weekly' ? 'hafta' : 'ay')); ?></small>
                                <?php endif; ?>
                            <?php else: ?>
                                Fiyat Görüşülür
                            <?php endif; ?>
                        </h4>
                        <div class="d-flex align-items-center justify-content-center gap-3 mt-3">
                            <div class="text-center">
                                <i class="bi bi-people-fill text-primary" style="font-size: 1.5rem;"></i>
                                <p class="mb-0 mt-1 fw-semibold">👥 <?php echo $applications_count; ?> başvuru</p>
                            </div>
                            <div class="text-center">
                                <i class="bi bi-eye-fill text-success" style="font-size: 1.5rem;"></i>
                                <p class="mb-0 mt-1 fw-semibold">👀 <?php echo rand(50, 200); ?> görüntüleme</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div class="container mt-4">
        <div class="row">
            <div class="col-lg-8">
                <!-- İş Açıklaması -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-file-text me-2"></i>İş Açıklaması</h5>
                    </div>
                    <div class="card-body">
                        <div class="job-description">
                            <?php echo nl2br(htmlspecialchars($job['description'])); ?>
                        </div>
                    </div>
                </div>

                <!-- Gereksinimler -->
                <?php if ($job['requirements']): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-list-check me-2"></i>Gereksinimler</h5>
                    </div>
                    <div class="card-body">
                        <?php echo nl2br(htmlspecialchars($job['requirements'])); ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- İş Detayları -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>İş Detayları</h5>
                    </div>
                    <div class="card-body">
                        <div class="job-meta">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>İş Türü:</strong></p>
                                    <p><?php echo $job_types[$job['job_type']] ?? $job['job_type']; ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Lokasyon:</strong></p>
                                    <p><i class="bi bi-geo-alt text-muted me-1"></i><?php echo htmlspecialchars($job['location']); ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Bütçe:</strong></p>
                                    <p>
                                        <?php if ($job['budget_min'] || $job['budget_max']): ?>
                                            <?php if ($job['budget_min'] && $job['budget_max']): ?>
                                                <?php echo formatMoney($job['budget_min']); ?> - <?php echo formatMoney($job['budget_max']); ?>
                                            <?php elseif ($job['budget_min']): ?>
                                                <?php echo formatMoney($job['budget_min']); ?>+
                                            <?php else: ?>
                                                <?php echo formatMoney($job['budget_max']); ?>'e kadar
                                            <?php endif; ?>
                                            <small class="text-muted">(<?php echo $job['budget_type']; ?>)</small>
                                        <?php else: ?>
                                            <span class="text-muted">Görüşülür</span>
                                        <?php endif; ?>
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Durum:</strong></p>
                                    <p>
                                        <span class="badge bg-<?php echo $job['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                            <?php echo $job['status'] === 'active' ? 'Aktif' : ucfirst($job['status']); ?>
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <!-- İşveren Bilgileri -->
                <div class="employer-card mb-4">
                    <h6 class="mb-3"><i class="bi bi-person me-2"></i>İşveren Bilgileri</h6>
                    <div class="text-center mb-3">
                        <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" 
                             style="width: 60px; height: 60px;">
                            <i class="bi bi-person"></i>
                        </div>
                        <h5 class="mt-2 mb-1"><?php echo htmlspecialchars($job['employer_name']); ?></h5>
                        <?php if ($job['employer_city']): ?>
                            <small class="text-muted"><?php echo htmlspecialchars($job['employer_city']); ?></small>
                        <?php endif; ?>
                    </div>
                    
                    <?php if (isLoggedIn() && $_SESSION['user_type'] === 'caregiver' && $_SESSION['user_id'] != $job['user_id']): ?>
                        <div class="d-grid gap-2">
                            <a href="send-message.php?to=<?php echo $job['user_id']; ?>" class="btn btn-outline-primary">
                                <i class="bi bi-chat-dots me-2"></i>Mesaj Gönder
                            </a>
                            <?php if ($job['employer_phone']): ?>
                                <a href="tel:<?php echo $job['employer_phone']; ?>" class="btn btn-outline-success">
                                    <i class="bi bi-telephone me-2"></i>Ara
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Başvuru Bölümü -->
                <?php if (isLoggedIn() && $_SESSION['user_type'] === 'caregiver' && $_SESSION['user_id'] != $job['user_id']): ?>
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-briefcase me-2"></i>Başvuru</h6>
                        </div>
                        <div class="card-body">
                            <?php if ($user_application): ?>
                                <div class="alert alert-info">
                                    <h6><i class="bi bi-info-circle me-2"></i>Başvuru Durumunuz</h6>
                                    <p class="mb-2">Bu iş ilanına <?php echo date('d.m.Y', strtotime($user_application['applied_at'])); ?> tarihinde başvurdunuz.</p>
                                    <span class="badge bg-<?php echo $user_application['status'] === 'accepted' ? 'success' : ($user_application['status'] === 'pending' ? 'warning' : 'secondary'); ?>">
                                        <?php 
                                        $status_names = [
                                            'pending' => 'Bekliyor',
                                            'viewed' => 'Görüldü',
                                            'accepted' => 'Kabul Edildi',
                                            'rejected' => 'Reddedildi'
                                        ];
                                        echo $status_names[$user_application['status']] ?? $user_application['status'];
                                        ?>
                                    </span>
                                </div>
                                <div class="d-grid gap-2">
                                    <a href="my-application.php?id=<?php echo $user_application['id']; ?>" class="btn btn-outline-primary">
                                        <i class="bi bi-eye me-2"></i>Başvurumu Görüntüle
                                    </a>
                                    <?php if (in_array($user_application['status'], ['pending', 'viewed'])): ?>
                                        <a href="edit-my-application.php?id=<?php echo $user_application['id']; ?>" class="btn btn-outline-secondary">
                                            <i class="bi bi-pencil me-2"></i>Başvurumu Düzenle
                                        </a>
                                    <?php endif; ?>
                                </div>
                            <?php elseif ($job['status'] === 'active'): ?>
                                <p class="text-muted mb-3">Bu iş ilanına başvurmak için aşağıdaki butona tıklayın.</p>
                                <div class="d-grid">
                                    <a href="job-application.php?job_id=<?php echo $job['id']; ?>" class="btn btn-primary">
                                        <i class="bi bi-send me-2"></i>Başvur
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-warning">
                                    <i class="bi bi-exclamation-triangle me-2"></i>
                                    Bu iş ilanı artık aktif değil.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php elseif (!isLoggedIn()): ?>
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-briefcase me-2"></i>Başvuru</h6>
                        </div>
                        <div class="card-body">
                            <p class="text-muted mb-3">Bu iş ilanına başvurmak için giriş yapmanız gerekiyor.</p>
                            <div class="d-grid gap-2">
                                <a href="auth/login.php" class="btn btn-primary">
                                    <i class="bi bi-box-arrow-in-right me-2"></i>Giriş Yap
                                </a>
                                <a href="auth/register.php" class="btn btn-outline-primary">
                                    <i class="bi bi-person-plus me-2"></i>Kayıt Ol
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Geri Dön -->
                <div class="mt-3">
                    <a href="jobs.php" class="btn btn-secondary w-100">
                        <i class="bi bi-arrow-left me-2"></i>İş İlanlarına Dön
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
