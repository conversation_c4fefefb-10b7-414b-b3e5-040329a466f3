<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 * Escort İlan Sitesi - Route Tanımlamaları
 */

// Ana sayfa
$routes->get('/', 'Home::index');

// Kullanıcı işlemleri
$routes->group('auth', function($routes) {
    $routes->get('login', 'Auth::login');
    $routes->post('login', 'Auth::attemptLogin');
    $routes->get('register', 'Auth::register');
    $routes->post('register', 'Auth::attemptRegister');
    $routes->get('logout', 'Auth::logout');
    $routes->get('forgot-password', 'Auth::forgotPassword');
    $routes->post('forgot-password', 'Auth::sendResetLink');
    $routes->get('reset-password/(:segment)', 'Auth::resetPassword/$1');
    $routes->post('reset-password', 'Auth::updatePassword');
    $routes->get('verify-email/(:segment)', 'Auth::verifyEmail/$1');
});

// İlan işlemleri
$routes->group('ads', function($routes) {
    $routes->get('/', 'Ads::index');
    $routes->get('category/(:segment)', 'Ads::category/$1');
    $routes->get('city/(:segment)', 'Ads::city/$1');
    $routes->get('search', 'Ads::search');
    $routes->get('(:num)', 'Ads::detail/$1');
    $routes->get('(:segment)', 'Ads::detail/$1'); // slug ile erişim
});

// Kullanıcı paneli
$routes->group('dashboard', ['filter' => 'auth'], function($routes) {
    $routes->get('/', 'Dashboard::index');
    $routes->get('profile', 'Dashboard::profile');
    $routes->post('profile', 'Dashboard::updateProfile');
    $routes->get('my-ads', 'Dashboard::myAds');
    $routes->get('create-ad', 'Dashboard::createAd');
    $routes->post('create-ad', 'Dashboard::storeAd');
    $routes->get('edit-ad/(:num)', 'Dashboard::editAd/$1');
    $routes->post('edit-ad/(:num)', 'Dashboard::updateAd/$1');
    $routes->delete('delete-ad/(:num)', 'Dashboard::deleteAd/$1');
    $routes->get('messages', 'Dashboard::messages');
    $routes->get('favorites', 'Dashboard::favorites');
    $routes->get('payments', 'Dashboard::payments');
    $routes->get('packages', 'Dashboard::packages');
});

// Ödeme işlemleri
$routes->group('payment', ['filter' => 'auth'], function($routes) {
    $routes->get('package/(:num)', 'Payment::package/$1');
    $routes->post('process', 'Payment::process');
    $routes->get('success', 'Payment::success');
    $routes->get('cancel', 'Payment::cancel');
    $routes->post('callback', 'Payment::callback');
});

// Admin paneli
$routes->group('admin', ['filter' => 'admin'], function($routes) {
    $routes->get('/', 'Admin\Dashboard::index');
    $routes->get('dashboard', 'Admin\Dashboard::index');

    // Kullanıcı yönetimi
    $routes->get('users', 'Admin\Users::index');
    $routes->get('users/(:num)', 'Admin\Users::show/$1');
    $routes->post('users/(:num)/ban', 'Admin\Users::ban/$1');
    $routes->post('users/(:num)/unban', 'Admin\Users::unban/$1');

    // İlan yönetimi
    $routes->get('ads', 'Admin\Ads::index');
    $routes->get('ads/pending', 'Admin\Ads::pending');
    $routes->get('ads/(:num)', 'Admin\Ads::show/$1');
    $routes->post('ads/(:num)/approve', 'Admin\Ads::approve/$1');
    $routes->post('ads/(:num)/reject', 'Admin\Ads::reject/$1');
    $routes->delete('ads/(:num)', 'Admin\Ads::delete/$1');

    // Kategori yönetimi
    $routes->get('categories', 'Admin\Categories::index');
    $routes->get('categories/create', 'Admin\Categories::create');
    $routes->post('categories', 'Admin\Categories::store');
    $routes->get('categories/(:num)/edit', 'Admin\Categories::edit/$1');
    $routes->post('categories/(:num)', 'Admin\Categories::update/$1');
    $routes->delete('categories/(:num)', 'Admin\Categories::delete/$1');

    // Paket yönetimi
    $routes->get('packages', 'Admin\Packages::index');
    $routes->get('packages/create', 'Admin\Packages::create');
    $routes->post('packages', 'Admin\Packages::store');
    $routes->get('packages/(:num)/edit', 'Admin\Packages::edit/$1');
    $routes->post('packages/(:num)', 'Admin\Packages::update/$1');
    $routes->delete('packages/(:num)', 'Admin\Packages::delete/$1');

    // Ödeme yönetimi
    $routes->get('payments', 'Admin\Payments::index');
    $routes->get('payments/(:num)', 'Admin\Payments::show/$1');

    // Şikayet yönetimi
    $routes->get('reports', 'Admin\Reports::index');
    $routes->get('reports/(:num)', 'Admin\Reports::show/$1');
    $routes->post('reports/(:num)/resolve', 'Admin\Reports::resolve/$1');

    // Site ayarları
    $routes->get('settings', 'Admin\Settings::index');
    $routes->post('settings', 'Admin\Settings::update');

    // Sistem logları
    $routes->get('logs', 'Admin\Logs::index');
});

// API routes
$routes->group('api', function($routes) {
    $routes->post('upload-photo', 'Api\Upload::photo');
    $routes->delete('delete-photo/(:num)', 'Api\Upload::deletePhoto/$1');
    $routes->post('add-favorite', 'Api\Favorites::add');
    $routes->delete('remove-favorite/(:num)', 'Api\Favorites::remove/$1');
    $routes->post('send-message', 'Api\Messages::send');
    $routes->get('districts/(:num)', 'Api\Location::districts/$1');
});

// Catch-all route for 404
$routes->set404Override('Errors::show404');
