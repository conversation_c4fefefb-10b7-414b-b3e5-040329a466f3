<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Giriş yapmış kullanıcı var mı?
if (isLoggedIn()) {
    $user_id = $_SESSION['user_id'];
    
    // Aktivite kaydı
    logActivity($user_id, 'logout', 'Kullanıcı çıkış yaptı', 'users', $user_id);
    
    // Remember me cookie'sini temizle
    if (isset($_COOKIE['remember_token'])) {
        setcookie('remember_token', '', time() - 3600, '/');
        
        // Veritabanından da temizle
        try {
            $sql = "UPDATE users SET remember_token = NULL WHERE id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$user_id]);
        } catch (PDOException $e) {
            // Hata durumunda sessizce devam et
        }
    }
    
    // Session'ı güvenli şekilde temizle
    destroySession();
}

// Login sayfasına yönlendir
redirect('login.php?message=logout');
?>
