<?php

$_['breadcrumb_cart'] = 'Shopping Cart';
$_['breadcrumb_payment'] = 'Payment';
$_['breadcrumb_shipping'] = 'Shipping';
$_['breadcrumb_summary'] = 'Summary';
$_['error_constraint_amount_not_set'] = 'The order failed due to a technical error, please select another payment method or contact our support.';
$_['error_constraint_payment_method_not_allowed'] = 'The selected payment method is not available for this transaction. Please select another one or add a new payment method to the wallet widget.';
$_['error_constraint_payment_plan_not_set'] = 'No payment instrument has been selected for this order, please try to refresh the page or add a new payment instrument in the wallet widget.';
$_['error_decline_amazon_rejected'] = 'Your payment could not be processed. Please try to place the order again using another payment method.';
$_['error_decline_invalid_payment_method'] = 'Your payment could not be processed, please follow the instructions in the payment method box.';
$_['error_decline_processing_failure'] = 'Your order could not be processed due to a system error. Please try to place the order again.';
$_['error_decline_transaction_timed_out'] = 'Your payment could not be processed. Please try to place the order again using another payment method.';
$_['error_login'] = 'Login failed';
$_['error_login_email'] = 'Login failed: %s account email address did not match Amazon account email address';
$_['error_minimum'] = 'Minimum order amount for Amazon Pay and Login with Amazon is %s!';
$_['error_no_shipping_methods'] = 'There are no shipping options to the selected address. Please select a different shipping address.';
$_['error_order_total_zero'] = 'Unfortunately, Amazon Pay does not accept orders with <strong>%s</strong> total amount. You may use the <strong>Standard Checkout</strong> instead.';
$_['error_payment_method'] = 'Please select a payment method';
$_['error_process_order'] = 'There was an error processing your order. Please contact the shop administrator for help.';
$_['error_session_expired'] = 'Your session has expired. Please sign in again by clicking on the Amazon Pay Button.';
$_['error_shipping'] = 'Please select a shipping method';
$_['error_shipping_address'] = 'Please select a delivery address';
$_['error_shipping_methods'] = 'There was an error retrieving your address from Amazon. Please contact the shop administrator for help.';
$_['heading_address'] = 'Please choose a delivery address';
$_['heading_confirm'] = 'Order summary';
$_['heading_payment'] = 'Please select a payment method';
$_['heading_title'] = 'Amazon Pay and Login with Amazon';
$_['text_amount_converted'] = 'Warning: The total paid amount will be converted into <strong>%s</strong> at a conversion rate of <strong>%s</strong>. The expected transaction amount will be <strong>%s</strong>.';
$_['text_back'] = 'Back';
$_['text_cart'] = 'Cart';
$_['text_confirm'] = 'Confirm';
$_['text_continue'] = 'Continue';
$_['text_continue_checkout'] = 'Continue Checkout Here';
$_['text_coupon'] = 'Coupon';
$_['text_enter_coupon'] = 'Enter your coupon code here.';
$_['text_lpa'] = 'Amazon Pay and Login with Amazon';
$_['text_must_apply_coupon'] = 'Please apply your coupon.';
$_['text_payment_success'] = 'Your order was successfully placed. Order details are below';
$_['text_success_title'] = 'Your order has been placed!';
$_['text_tax_other'] = 'Taxes / Other handling fees';
