<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// G<PERSON>ş kontrolü
requireLogin();

// <PERSON><PERSON>e bakıcı kullanıcıları erişebilir
if ($_SESSION['user_type'] !== 'caregiver') {
    redirect('dashboard.php');
}

$user_id = $_SESSION['user_id'];

// Değerlendirmeleri getir
try {
    $sql = "SELECT r.*, u.full_name as reviewer_name, jl.title as job_title
            FROM reviews r
            JOIN users u ON r.reviewer_id = u.id
            LEFT JOIN job_listings jl ON r.job_id = jl.id
            WHERE r.reviewee_id = ? AND r.is_approved = 1
            ORDER BY r.created_at DESC";
    $stmt = $db->prepare($sql);
    $stmt->execute([$user_id]);
    $reviews = $stmt->fetchAll();
    
    // Ortalama puan hesapla
    $avg_sql = "SELECT AVG(rating) as avg_rating, COUNT(*) as total_reviews FROM reviews WHERE reviewee_id = ? AND is_approved = 1";
    $avg_stmt = $db->prepare($avg_sql);
    $avg_stmt->execute([$user_id]);
    $stats = $avg_stmt->fetch();
    
} catch (PDOException $e) {
    $reviews = [];
    $stats = ['avg_rating' => 0, 'total_reviews' => 0];
}

$page_title = 'Değerlendirmelerim';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c5aa0;
        }
        
        .review-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }
        
        .review-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }
        
        .rating-stars {
            color: #ffc107;
        }
        
        .stats-card {
            background: linear-gradient(135deg, var(--primary-color), #1e3d72);
            color: white;
            border-radius: 20px;
            padding: 30px;
            text-align: center;
        }
        
        .navbar {
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="index.php">
                <i class="bi bi-heart-fill me-2"></i><?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="caregivers.php">Bakıcı Ara</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="jobs.php">İş İlanları</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">Hakkımızda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">İletişim</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i><?php echo escape($_SESSION['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="dashboard.php"><i class="bi bi-speedometer2 me-2"></i>Dashboard</a></li>
                            <li><a class="dropdown-item" href="profile.php"><i class="bi bi-person me-2"></i>Profil</a></li>
                            <li><a class="dropdown-item" href="messages.php"><i class="bi bi-chat-dots me-2"></i>Mesajlar</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="auth/logout.php"><i class="bi bi-box-arrow-right me-2"></i>Çıkış</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Sayfa Başlığı -->
        <div class="row mb-4">
            <div class="col">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                        <li class="breadcrumb-item active">Değerlendirmelerim</li>
                    </ol>
                </nav>
                <h2 class="fw-bold">Değerlendirmelerim</h2>
                <p class="text-muted">Müşterilerinizden aldığınız değerlendirmeler</p>
            </div>
        </div>

        <!-- İstatistikler -->
        <div class="row mb-5">
            <div class="col-md-6">
                <div class="stats-card">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h1 class="display-4 fw-bold mb-0"><?php echo number_format($stats['avg_rating'], 1); ?></h1>
                            <div class="mb-2">
                                <?php echo getRatingStars($stats['avg_rating']); ?>
                            </div>
                            <p class="mb-0">Ortalama Puan</p>
                        </div>
                        <div class="col-md-6">
                            <h3 class="fw-bold"><?php echo $stats['total_reviews']; ?></h3>
                            <p class="mb-0">Toplam Değerlendirme</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="fw-bold mb-3">Puan Dağılımı</h6>
                        <?php
                        // Puan dağılımını hesapla
                        $rating_counts = [5 => 0, 4 => 0, 3 => 0, 2 => 0, 1 => 0];
                        foreach ($reviews as $review) {
                            $rating_counts[floor($review['rating'])]++;
                        }
                        
                        for ($i = 5; $i >= 1; $i--):
                            $count = $rating_counts[$i];
                            $percentage = $stats['total_reviews'] > 0 ? ($count / $stats['total_reviews']) * 100 : 0;
                        ?>
                        <div class="d-flex align-items-center mb-2">
                            <span class="me-2"><?php echo $i; ?> ⭐</span>
                            <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                <div class="progress-bar bg-warning" style="width: <?php echo $percentage; ?>%"></div>
                            </div>
                            <small class="text-muted"><?php echo $count; ?></small>
                        </div>
                        <?php endfor; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Değerlendirmeler -->
        <div class="row">
            <div class="col-lg-8">
                <?php if (!empty($reviews)): ?>
                    <?php foreach ($reviews as $review): ?>
                    <div class="review-card card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div>
                                    <h6 class="fw-bold mb-1"><?php echo escape($review['reviewer_name']); ?></h6>
                                    <?php if ($review['job_title']): ?>
                                        <p class="text-muted small mb-2">
                                            <i class="bi bi-briefcase me-1"></i><?php echo escape($review['job_title']); ?>
                                        </p>
                                    <?php endif; ?>
                                    <div class="mb-2">
                                        <?php echo getRatingStars($review['rating']); ?>
                                        <span class="ms-2 fw-bold"><?php echo number_format($review['rating'], 1); ?></span>
                                    </div>
                                </div>
                                <small class="text-muted"><?php echo timeAgo($review['created_at']); ?></small>
                            </div>
                            
                            <?php if ($review['comment']): ?>
                            <div class="bg-light p-3 rounded">
                                <p class="mb-0"><?php echo nl2br(escape($review['comment'])); ?></p>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($review['response']): ?>
                            <div class="mt-3 ps-4 border-start border-primary">
                                <h6 class="fw-bold text-primary mb-2">Yanıtınız:</h6>
                                <p class="mb-0"><?php echo nl2br(escape($review['response'])); ?></p>
                            </div>
                            <?php else: ?>
                            <div class="mt-3">
                                <button class="btn btn-outline-primary btn-sm" onclick="replyToReview(<?php echo $review['id']; ?>)">
                                    <i class="bi bi-reply me-1"></i>Yanıtla
                                </button>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="bi bi-star text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">Henüz değerlendirme yok</h4>
                        <p class="text-muted">İlk işinizi tamamladığınızda değerlendirmeler burada görünecek.</p>
                        <a href="jobs.php" class="btn btn-primary">İş Ara</a>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Değerlendirme İpuçları</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li class="mb-3">
                                <i class="bi bi-check-circle text-success me-2"></i>
                                <strong>Profesyonel olun:</strong> Müşterilerinizle her zaman saygılı iletişim kurun.
                            </li>
                            <li class="mb-3">
                                <i class="bi bi-check-circle text-success me-2"></i>
                                <strong>Zamanında olun:</strong> Belirlenen saatlere uyun ve gecikmelerinizi bildirin.
                            </li>
                            <li class="mb-3">
                                <i class="bi bi-check-circle text-success me-2"></i>
                                <strong>İletişim halinde kalın:</strong> İş sürecinde düzenli güncelleme verin.
                            </li>
                            <li class="mb-0">
                                <i class="bi bi-check-circle text-success me-2"></i>
                                <strong>Kaliteli hizmet:</strong> İşinizi en iyi şekilde yapmaya odaklanın.
                            </li>
                        </ul>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">Hızlı İşlemler</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="dashboard.php" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-speedometer2 me-2"></i>Dashboard
                            </a>
                            <a href="jobs.php" class="btn btn-outline-success btn-sm">
                                <i class="bi bi-search me-2"></i>İş Ara
                            </a>
                            <a href="profile/caregiver.php" class="btn btn-outline-secondary btn-sm">
                                <i class="bi bi-person me-2"></i>Profili Düzenle
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Reply Modal -->
    <div class="modal fade" id="replyModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Değerlendirmeye Yanıt Ver</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="replyForm">
                        <input type="hidden" id="reviewId">
                        <div class="mb-3">
                            <label for="response" class="form-label">Yanıtınız</label>
                            <textarea class="form-control" id="response" rows="4" placeholder="Değerlendirme için teşekkür ederim..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                    <button type="button" class="btn btn-primary" onclick="submitReply()">Yanıtla</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function replyToReview(reviewId) {
            document.getElementById('reviewId').value = reviewId;
            document.getElementById('response').value = '';
            const modal = new bootstrap.Modal(document.getElementById('replyModal'));
            modal.show();
        }
        
        function submitReply() {
            const reviewId = document.getElementById('reviewId').value;
            const response = document.getElementById('response').value;
            
            if (!response.trim()) {
                alert('Lütfen yanıtınızı yazın.');
                return;
            }
            
            // Ajax ile yanıt gönderilecek
            alert('Yanıt gönderme özelliği yakında aktif olacak.');
            
            // Modal'ı kapat
            bootstrap.Modal.getInstance(document.getElementById('replyModal')).hide();
        }
    </script>
</body>
</html>
