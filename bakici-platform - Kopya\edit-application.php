<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Giriş kontrolü
if (!isLoggedIn()) {
    header('Location: auth/login.php');
    exit;
}

$application_id = intval($_GET['id'] ?? 0);
if (!$application_id) {
    header('Location: dashboard.php?error=invalid_application');
    exit;
}

// Başvuru bilgilerini getir
try {
    $sql = "SELECT ja.*, 
                   jl.title as job_title, jl.description as job_description, jl.user_id as employer_id,
                   caregiver.full_name as caregiver_name,
                   employer.full_name as employer_name
            FROM job_applications ja
            JOIN job_listings jl ON ja.job_id = jl.id
            JOIN users caregiver ON ja.caregiver_id = caregiver.id
            JOIN users employer ON jl.user_id = employer.id
            WHERE ja.id = ?";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([$application_id]);
    $application = $stmt->fetch();
    
    if (!$application) {
        header('Location: dashboard.php?error=application_not_found');
        exit;
    }
    
    // Yetki kontrolü - sadece admin veya işveren düzenleyebilir
    $user_id = $_SESSION['user_id'];
    $user_type = $_SESSION['user_type'];
    
    if ($user_type !== 'admin' && $user_id != $application['employer_id']) {
        header('Location: dashboard.php?error=permission_denied');
        exit;
    }
    
} catch (PDOException $e) {
    header('Location: dashboard.php?error=database_error');
    exit;
}

// Form gönderildi
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $status = $_POST['status'] ?? '';
    $admin_notes = trim($_POST['admin_notes'] ?? '');
    
    $errors = [];
    
    // Validasyon
    $valid_statuses = ['pending', 'viewed', 'shortlisted', 'interview', 'accepted', 'rejected', 'withdrawn', 'hired'];
    if (!in_array($status, $valid_statuses)) {
        $errors[] = 'Geçerli bir durum seçiniz.';
    }
    
    // Güncelleme
    if (empty($errors)) {
        try {
            $update_sql = "UPDATE job_applications SET 
                          status = ?, 
                          admin_notes = ?, 
                          updated_at = NOW() 
                          WHERE id = ?";
            $update_stmt = $db->prepare($update_sql);
            $update_stmt->execute([$status, $admin_notes, $application_id]);
            
            // Bildirim gönder
            if ($status === 'accepted') {
                $notification_sql = "INSERT INTO notifications (user_id, title, message, type, created_at) 
                                    VALUES (?, ?, ?, ?, NOW())";
                $notification_stmt = $db->prepare($notification_sql);
                $notification_stmt->execute([
                    $application['caregiver_id'],
                    'Başvurunuz Kabul Edildi!',
                    'Başvurunuz kabul edildi. İşveren ile iletişime geçebilirsiniz.',
                    'application_accepted'
                ]);
            } elseif ($status === 'hired') {
                $notification_sql = "INSERT INTO notifications (user_id, title, message, type, created_at) 
                                    VALUES (?, ?, ?, ?, NOW())";
                $notification_stmt = $db->prepare($notification_sql);
                $notification_stmt->execute([
                    $application['caregiver_id'],
                    'Tebrikler! İşe Alındınız!',
                    'İşe alındınız. İşveren ile detayları konuşabilirsiniz.',
                    'hired'
                ]);
            }
            
            header('Location: dashboard.php?success=application_updated');
            exit;
            
        } catch (PDOException $e) {
            $errors[] = 'Güncelleme sırasında hata oluştu: ' . $e->getMessage();
        }
    }
}

$page_title = 'Başvuru Düzenle';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bakıcı Platform</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c5aa0;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
        }
        
        .status-badge {
            font-size: 0.9rem;
            padding: 8px 16px;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="index.php">
                <i class="bi bi-heart-fill me-2"></i>Bakıcı Platform
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.php">Dashboard</a>
                <a class="nav-link" href="auth/logout.php">Çıkış</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="bi bi-pencil me-2"></i>Başvuru Düzenle</h4>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0">
                                <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                                <li class="breadcrumb-item active">Başvuru Düzenle</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="card-body">
                        <!-- Errors -->
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <!-- Başvuru Bilgileri -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h6>Başvuru Bilgileri</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>Başvuru ID:</strong></td>
                                        <td>#<?php echo $application['id']; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>İş İlanı:</strong></td>
                                        <td><?php echo htmlspecialchars($application['job_title']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Bakıcı:</strong></td>
                                        <td><?php echo htmlspecialchars($application['caregiver_name']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Başvuru Tarihi:</strong></td>
                                        <td><?php echo date('d.m.Y H:i', strtotime($application['applied_at'])); ?></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6>Mevcut Durum</h6>
                                <div class="mb-3">
                                    <?php
                                    $status_colors = [
                                        'pending' => 'warning',
                                        'viewed' => 'info',
                                        'shortlisted' => 'primary',
                                        'interview' => 'secondary',
                                        'accepted' => 'success',
                                        'rejected' => 'danger',
                                        'withdrawn' => 'dark',
                                        'hired' => 'success'
                                    ];
                                    
                                    $status_names = [
                                        'pending' => 'Bekliyor',
                                        'viewed' => 'Görüldü',
                                        'shortlisted' => 'Kısa Liste',
                                        'interview' => 'Görüşme',
                                        'accepted' => 'Kabul Edildi',
                                        'rejected' => 'Reddedildi',
                                        'withdrawn' => 'Geri Çekildi',
                                        'hired' => 'İşe Alındı'
                                    ];
                                    ?>
                                    <span class="badge bg-<?php echo $status_colors[$application['status']] ?? 'secondary'; ?> status-badge">
                                        <?php echo $status_names[$application['status']] ?? $application['status']; ?>
                                    </span>
                                </div>
                                
                                <?php if ($application['message']): ?>
                                    <h6>Başvuru Mesajı</h6>
                                    <div class="bg-light p-3 rounded">
                                        <?php echo nl2br(htmlspecialchars($application['message'])); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Düzenleme Formu -->
                        <form method="POST">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="status" class="form-label">Başvuru Durumu *</label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="pending" <?php echo $application['status'] === 'pending' ? 'selected' : ''; ?>>Bekliyor</option>
                                        <option value="viewed" <?php echo $application['status'] === 'viewed' ? 'selected' : ''; ?>>Görüldü</option>
                                        <option value="shortlisted" <?php echo $application['status'] === 'shortlisted' ? 'selected' : ''; ?>>Kısa Liste</option>
                                        <option value="interview" <?php echo $application['status'] === 'interview' ? 'selected' : ''; ?>>Görüşme</option>
                                        <option value="accepted" <?php echo $application['status'] === 'accepted' ? 'selected' : ''; ?>>Kabul Edildi</option>
                                        <option value="rejected" <?php echo $application['status'] === 'rejected' ? 'selected' : ''; ?>>Reddedildi</option>
                                        <option value="hired" <?php echo $application['status'] === 'hired' ? 'selected' : ''; ?>>İşe Alındı</option>
                                    </select>
                                </div>
                                
                                <div class="col-12">
                                    <label for="admin_notes" class="form-label">Admin/İşveren Notları</label>
                                    <textarea class="form-control" id="admin_notes" name="admin_notes" rows="4" 
                                              placeholder="Başvuru hakkında notlarınızı yazın..."><?php echo htmlspecialchars($application['admin_notes'] ?? ''); ?></textarea>
                                    <div class="form-text">Bu notlar sadece admin ve işveren tarafından görülebilir.</div>
                                </div>
                                
                                <div class="col-12">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <small class="text-muted">
                                                <strong>Oluşturulma:</strong> 
                                                <?php echo date('d.m.Y H:i', strtotime($application['created_at'])); ?>
                                            </small>
                                        </div>
                                        <div class="col-md-6">
                                            <small class="text-muted">
                                                <strong>Son Güncelleme:</strong> 
                                                <?php echo $application['updated_at'] ? date('d.m.Y H:i', strtotime($application['updated_at'])) : 'Hiç'; ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-between mt-4">
                                <a href="dashboard.php" class="btn btn-secondary">
                                    <i class="bi bi-arrow-left me-2"></i>Geri Dön
                                </a>
                                <div>
                                    <a href="application-details.php?id=<?php echo $application['id']; ?>" class="btn btn-outline-primary me-2">
                                        <i class="bi bi-eye me-2"></i>Detayları Görüntüle
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check me-2"></i>Güncelle
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Status değişikliğinde renk güncelle
        document.getElementById('status').addEventListener('change', function() {
            const status = this.value;
            const colors = {
                'pending': 'warning',
                'viewed': 'info',
                'shortlisted': 'primary',
                'interview': 'secondary',
                'accepted': 'success',
                'rejected': 'danger',
                'hired': 'success'
            };
            
            // Önizleme için badge rengini güncelle (opsiyonel)
            console.log('Status changed to:', status);
        });
    </script>
</body>
</html>
