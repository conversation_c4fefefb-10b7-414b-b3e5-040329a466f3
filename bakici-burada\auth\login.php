<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Zaten giriş yapmışsa dashboard'a yönlendir
if (isset($_SESSION['user_id'])) {
    header('Location: ../dashboard.php');
    exit;
}

$error_message = '';

// Form gönderildi mi?
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim(safeArray($_POST, 'email', ''));
    $password = safeArray($_POST, 'password', '');
    $remember_me = safeArray($_POST, 'remember_me', false);
    
    // Validasyon
    if (empty($email)) {
        $error_message = 'E-posta adresi gereklidir.';
    } elseif (empty($password)) {
        $error_message = 'Şifre gereklidir.';
    } else {
        try {
            // Kullanıcıyı bul
            $sql = "SELECT id, email, password, full_name, user_type, is_active, is_verified FROM users WHERE email = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$email]);
            $user = $stmt->fetch();
            
            if ($user && password_verify($password, $user['password'])) {
                // Hesap aktif mi?
                if (!$user['is_active']) {
                    $error_message = 'Hesabınız deaktif edilmiştir. Lütfen destek ekibi ile iletişime geçin.';
                } else {
                    // Giriş başarılı
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['email'] = $user['email'];
                    $_SESSION['full_name'] = $user['full_name'];
                    $_SESSION['user_type'] = $user['user_type'];
                    $_SESSION['is_verified'] = $user['is_verified'];
                    
                    // Remember me cookie
                    if ($remember_me) {
                        $token = bin2hex(random_bytes(32));
                        setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/'); // 30 gün
                        
                        // Token'ı veritabanına kaydet
                        $update_sql = "UPDATE users SET remember_token = ? WHERE id = ?";
                        $update_stmt = $db->prepare($update_sql);
                        $update_stmt->execute([$token, $user['id']]);
                    }
                    
                    // Son giriş zamanını güncelle
                    $update_sql = "UPDATE users SET last_login = NOW() WHERE id = ?";
                    $update_stmt = $db->prepare($update_sql);
                    $update_stmt->execute([$user['id']]);
                    
                    // Aktivite kaydı
                    logActivity($user['id'], 'login', 'Kullanıcı giriş yaptı', 'users', $user['id']);
                    
                    // Yönlendirme
                    $redirect = safeArray($_GET, 'redirect', 'dashboard.php');
                    header('Location: ../' . $redirect);
                    exit;
                }
            } else {
                $error_message = 'E-posta adresi veya şifre hatalı.';
                
                // Başarısız giriş denemesi kaydet
                if ($user) {
                    logActivity($user['id'], 'failed_login', 'Başarısız giriş denemesi', 'users', $user['id']);
                }
            }
        } catch (PDOException $e) {
            $error_message = 'Giriş yapılırken bir hata oluştu. Lütfen tekrar deneyin.';
        }
    }
}

$page_title = 'Giriş Yap';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bakıcı Burada</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c5aa0;
            --secondary-color: #f8f9fa;
        }
        
        body {
            background: linear-gradient(135deg, var(--primary-color) 0%, #1e3d72 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        
        .auth-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .auth-header {
            background: var(--primary-color);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .auth-body {
            padding: 2rem;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: #1e3d72;
            border-color: #1e3d72;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="auth-card">
                    <div class="auth-header">
                        <h3 class="mb-0">
                            <i class="bi bi-heart-fill me-2"></i>Bakıcı Burada
                        </h3>
                        <p class="mb-0 mt-2">Hesabınıza giriş yapın</p>
                    </div>
                    
                    <div class="auth-body">
                        <?php if ($error_message): ?>
                            <div class="alert alert-danger" role="alert">
                                <i class="bi bi-exclamation-triangle me-2"></i><?php echo escape($error_message); ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label for="email" class="form-label">E-posta Adresi</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-envelope"></i>
                                    </span>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?php echo escape(safeArray($_POST, 'email', '')); ?>" 
                                           placeholder="<EMAIL>" required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">Şifre</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-lock"></i>
                                    </span>
                                    <input type="password" class="form-control" id="password" name="password" 
                                           placeholder="Şifrenizi girin" required>
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me" value="1">
                                <label class="form-check-label" for="remember_me">
                                    Beni hatırla
                                </label>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-box-arrow-in-right me-2"></i>Giriş Yap
                                </button>
                            </div>
                        </form>
                        
                        <hr class="my-4">
                        
                        <div class="text-center">
                            <p class="mb-2">
                                <a href="forgot-password.php" class="text-decoration-none">
                                    <i class="bi bi-question-circle me-1"></i>Şifremi unuttum
                                </a>
                            </p>
                            <p class="mb-0">
                                Hesabınız yok mu? 
                                <a href="register.php" class="text-decoration-none fw-bold">
                                    Hemen üye olun
                                </a>
                            </p>
                        </div>
                        
                        <div class="text-center mt-4">
                            <a href="../index.php" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-2"></i>Ana Sayfaya Dön
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Demo Hesaplar -->
                <div class="card mt-4 bg-light">
                    <div class="card-body">
                        <h6 class="card-title">Demo Hesaplar</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <p class="small mb-1"><strong>Admin:</strong></p>
                                <p class="small"><EMAIL> / admin123</p>
                            </div>
                            <div class="col-md-6">
                                <p class="small mb-1"><strong>Test için:</strong></p>
                                <p class="small">Kayıt olarak test edebilirsiniz</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Şifre göster/gizle
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.className = 'bi bi-eye-slash';
            } else {
                passwordInput.type = 'password';
                icon.className = 'bi bi-eye';
            }
        });
        
        // Form validasyonu
        document.querySelector('form').addEventListener('submit', function(e) {
            const email = document.getElementById('email').value.trim();
            const password = document.getElementById('password').value;
            
            if (!email) {
                e.preventDefault();
                alert('E-posta adresi gereklidir.');
                return;
            }
            
            if (!password) {
                e.preventDefault();
                alert('Şifre gereklidir.');
                return;
            }
            
            // E-posta formatı kontrolü
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                e.preventDefault();
                alert('Geçerli bir e-posta adresi girin.');
                return;
            }
        });
    </script>
</body>
</html>
