<?php
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/session.php';

// Arama parametreleri
$search_query = trim(safeArray($_GET, 'q', ''));
$job_type = safeArray($_GET, 'job_type', '');
$city = safeArray($_GET, 'city', '');
$care_type = safeArray($_GET, 'care_type', '');
$budget = safeArray($_GET, 'budget', '');
$page = max(1, safeNumber(safeArray($_GET, 'page', 1)));
$per_page = 12;
$offset = ($page - 1) * $per_page;

// SQL sorgusu oluştur
$where_conditions = [];
$params = [];

$sql = "SELECT jl.*, u.full_name as employer_name, c.name as category_name, c.icon as category_icon
        FROM job_listings jl 
        LEFT JOIN users u ON jl.user_id = u.id 
        LEFT JOIN categories c ON jl.category_id = c.id
        WHERE jl.status = 'active' AND (jl.expires_at IS NULL OR jl.expires_at > NOW())";

if (!empty($search_query)) {
    $where_conditions[] = "(jl.title LIKE ? OR jl.description LIKE ?)";
    $search_param = '%' . $search_query . '%';
    $params[] = $search_param;
    $params[] = $search_param;
}

if (!empty($job_type)) {
    $where_conditions[] = "jl.job_type = ?";
    $params[] = $job_type;
}

if (!empty($city)) {
    $where_conditions[] = "jl.location_city = ?";
    $params[] = $city;
}

if (!empty($care_type)) {
    $where_conditions[] = "jl.care_type = ?";
    $params[] = $care_type;
}

if (!empty($budget)) {
    $budget_parts = explode('-', $budget);
    if (count($budget_parts) == 2) {
        $where_conditions[] = "jl.budget_max BETWEEN ? AND ?";
        $params[] = $budget_parts[0];
        $params[] = $budget_parts[1];
    } elseif ($budget === '8000+') {
        $where_conditions[] = "jl.budget_max >= 8000";
    }
}

if (!empty($where_conditions)) {
    $sql .= " AND " . implode(" AND ", $where_conditions);
}

$sql .= " ORDER BY jl.is_urgent DESC, jl.created_at DESC";

// Toplam kayıt sayısı
$count_sql = str_replace("SELECT jl.*, u.full_name as employer_name, c.name as category_name, c.icon as category_icon", "SELECT COUNT(*)", $sql);
$count_sql = str_replace("ORDER BY jl.is_urgent DESC, jl.created_at DESC", "", $count_sql);

try {
    $count_stmt = $db->prepare($count_sql);
    $count_stmt->execute($params);
    $total_records = $count_stmt->fetchColumn();
    $total_pages = ceil($total_records / $per_page);

    // Ana sorgu
    $sql .= " LIMIT $per_page OFFSET $offset";
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    $jobs = $stmt->fetchAll();
} catch (PDOException $e) {
    $jobs = [];
    $total_records = 0;
    $total_pages = 0;
}

$page_title = 'İş İlanları';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c5aa0;
        }
        
        .search-filters {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .job-card {
            border: none;
            border-radius: 15px;
            transition: all 0.3s ease;
            height: 100%;
            background: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .job-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }
        
        .urgent-job {
            border-left: 4px solid #dc3545;
        }
        
        .navbar {
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="index.php">
                <i class="bi bi-heart-fill me-2"></i><?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="caregivers.php">Bakıcı Ara</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="jobs.php">İş İlanları</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">Hakkımızda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">İletişim</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle me-1"></i><?php echo escape(safeArray($_SESSION, 'full_name', 'Kullanıcı')); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="dashboard.php"><i class="bi bi-speedometer2 me-2"></i>Dashboard</a></li>
                                <li><a class="dropdown-item" href="profile.php"><i class="bi bi-person me-2"></i>Profil</a></li>
                                <li><a class="dropdown-item" href="messages.php"><i class="bi bi-chat-dots me-2"></i>Mesajlar</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="auth/logout.php"><i class="bi bi-box-arrow-right me-2"></i>Çıkış</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="auth/login.php">Giriş Yap</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-primary ms-2" href="auth/register.php">Üye Ol</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Alert Messages -->
        <?php if (isset($_GET['success'])): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="bi bi-check-circle me-2"></i>
                <?php
                $success_messages = [
                    'application_sent' => 'Başvurunuz başarıyla gönderildi.',
                    'application_updated' => 'Başvurunuz güncellendi.',
                    'application_withdrawn' => 'Başvurunuz geri çekildi.'
                ];
                echo $success_messages[$_GET['success']] ?? 'İşlem başarıyla tamamlandı.';
                ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_GET['error'])): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="bi bi-exclamation-triangle me-2"></i>
                <?php
                $error_messages = [
                    'job_not_found' => 'İş ilanı bulunamadı.',
                    'invalid_job' => 'Geçersiz iş ilanı.',
                    'already_applied' => 'Bu iş ilanına daha önce başvurdunuz.',
                    'permission_denied' => 'Bu işlem için yetkiniz yok.',
                    'database_error' => 'Veritabanı hatası oluştu.',
                    'application_not_found' => 'Başvuru bulunamadı.',
                    'cannot_apply_own_job' => 'Kendi iş ilanınıza başvuru yapamazsınız.'
                ];
                echo $error_messages[$_GET['error']] ?? 'Bir hata oluştu.';
                ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Sayfa Başlığı -->
        <div class="row mb-4">
            <div class="col">
                <h2 class="fw-bold">İş İlanları</h2>
                <p class="text-muted">Size uygun iş fırsatlarını keşfedin</p>
            </div>
        </div>

        <!-- Arama Filtreleri -->
        <div class="search-filters">
            <form method="GET" action="jobs.php">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Arama</label>
                        <input type="text" class="form-control" name="q" value="<?php echo escape($search_query); ?>" placeholder="İş başlığı, açıklama...">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Şehir</label>
                        <select name="city" class="form-select">
                            <option value="">Tümü</option>
                            <?php foreach (getCities() as $city_option): ?>
                                <option value="<?php echo $city_option; ?>" <?php echo $city === $city_option ? 'selected' : ''; ?>>
                                    <?php echo $city_option; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">İş Tipi</label>
                        <select name="job_type" class="form-select">
                            <option value="">Tümü</option>
                            <?php foreach (getJobTypes() as $key => $value): ?>
                                <option value="<?php echo $key; ?>" <?php echo $job_type === $key ? 'selected' : ''; ?>>
                                    <?php echo $value; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Bakım Tipi</label>
                        <select name="care_type" class="form-select">
                            <option value="">Tümü</option>
                            <?php foreach (getCareTypes() as $key => $value): ?>
                                <option value="<?php echo $key; ?>" <?php echo $care_type === $key ? 'selected' : ''; ?>>
                                    <?php echo $value; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Bütçe</label>
                        <select name="budget" class="form-select">
                            <option value="">Tümü</option>
                            <option value="0-2000" <?php echo $budget === '0-2000' ? 'selected' : ''; ?>>2.000 TL'ye kadar</option>
                            <option value="2000-4000" <?php echo $budget === '2000-4000' ? 'selected' : ''; ?>>2.000 - 4.000 TL</option>
                            <option value="4000-6000" <?php echo $budget === '4000-6000' ? 'selected' : ''; ?>>4.000 - 6.000 TL</option>
                            <option value="6000-8000" <?php echo $budget === '6000-8000' ? 'selected' : ''; ?>>6.000 - 8.000 TL</option>
                            <option value="8000+" <?php echo $budget === '8000+' ? 'selected' : ''; ?>>8.000 TL üzeri</option>
                        </select>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Sonuçlar -->
        <div class="row mb-3">
            <div class="col">
                <p class="text-muted">
                    <strong><?php echo number_format($total_records); ?></strong> iş ilanı bulundu
                    <?php if (!empty($search_query)): ?>
                        "<strong><?php echo escape($search_query); ?></strong>" için
                    <?php endif; ?>
                </p>
            </div>
        </div>

        <!-- İş İlanları -->
        <div class="row g-4 mb-5">
            <?php if (!empty($jobs)): ?>
                <?php foreach ($jobs as $job): ?>
                <div class="col-md-6 col-lg-4">
                    <div class="card job-card <?php echo $job['is_urgent'] ? 'urgent-job' : ''; ?>">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <?php if ($job['is_urgent']): ?>
                                    <span class="badge bg-danger">ACİL</span>
                                <?php endif; ?>
                                <?php if ($job['category_icon']): ?>
                                    <i class="<?php echo $job['category_icon']; ?> text-primary" style="font-size: 1.5rem;"></i>
                                <?php endif; ?>
                            </div>
                            <h6 class="card-title"><?php echo escape($job['title']); ?></h6>
                            <p class="text-muted small mb-2">
                                <i class="bi bi-person me-1"></i><?php echo escape($job['employer_name']); ?>
                            </p>
                            <p class="text-muted small mb-2">
                                <i class="bi bi-geo-alt me-1"></i>
                                <?php echo escape($job['location_city']); ?>
                                <?php if ($job['location_district']): ?>
                                    , <?php echo escape($job['location_district']); ?>
                                <?php endif; ?>
                            </p>
                            <p class="text-muted small mb-2">
                                <i class="bi bi-briefcase me-1"></i>
                                <?php echo getJobTypes()[$job['job_type']] ?? $job['job_type']; ?> - 
                                <?php echo getCareTypes()[$job['care_type']] ?? $job['care_type']; ?>
                            </p>
                            <?php if ($job['budget_min'] || $job['budget_max']): ?>
                            <p class="text-primary fw-bold mb-2">
                                <i class="bi bi-currency-exchange me-1"></i>
                                <?php if ($job['budget_min'] && $job['budget_max']): ?>
                                    <?php echo formatMoney($job['budget_min']); ?> - <?php echo formatMoney($job['budget_max']); ?>
                                <?php elseif ($job['budget_min']): ?>
                                    <?php echo formatMoney($job['budget_min']); ?>+
                                <?php else: ?>
                                    <?php echo formatMoney($job['budget_max']); ?>'e kadar
                                <?php endif; ?>
                                <?php if ($job['budget_type']): ?>
                                    /<?php echo $job['budget_type'] === 'hourly' ? 'saat' : ($job['budget_type'] === 'daily' ? 'gün' : ($job['budget_type'] === 'weekly' ? 'hafta' : 'ay')); ?>
                                <?php endif; ?>
                            </p>
                            <?php endif; ?>
                            <p class="text-muted small mb-3"><?php echo escape(substr($job['description'], 0, 100)); ?>...</p>
                            <small class="text-muted">
                                <i class="bi bi-clock me-1"></i><?php echo timeAgo($job['created_at']); ?>
                            </small>
                            <div class="mt-3">
                                <a href="job-detail.php?slug=<?php echo $job['slug']; ?>" class="btn btn-primary btn-sm">
                                    <i class="bi bi-eye me-1"></i>Detayları Gör
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="bi bi-briefcase text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">İş ilanı bulunamadı</h4>
                        <p class="text-muted">Arama kriterlerinizi değiştirerek tekrar deneyin.</p>
                        <a href="jobs.php" class="btn btn-primary">Tüm İlanları Gör</a>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Sayfalama -->
        <?php if ($total_pages > 1): ?>
        <nav aria-label="Sayfa navigasyonu">
            <ul class="pagination justify-content-center">
                <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">Önceki</a>
                    </li>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
                    </li>
                <?php endfor; ?>
                
                <?php if ($page < $total_pages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">Sonraki</a>
                    </li>
                <?php endif; ?>
            </ul>
        </nav>
        <?php endif; ?>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
