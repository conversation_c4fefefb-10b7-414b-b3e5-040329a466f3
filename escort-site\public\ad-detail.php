<?php
// EscortNews - İlan Detay Sayfası
require_once 'includes/config.php';

// İlan slug'ını al
$slug = isset($_GET['slug']) ? sanitize($_GET['slug']) : '';

if (empty($slug)) {
    header('Location: ads.php');
    exit;
}

// <PERSON>lan detayını al
$ad = getAdDetail($slug);

if (!$ad) {
    header('Location: ads.php');
    exit;
}

// Sayfa bilgileri
$pageTitle = htmlspecialchars($ad['title']) . ' - ' . $siteName;
$pageDescription = htmlspecialchars(substr($ad['description'], 0, 160));
$pageKeywords = htmlspecialchars($ad['category_name']) . ', ' . htmlspecialchars($ad['city_name']) . ', escort, ilan';

// Fotoğrafları ayır
$photos = !empty($ad['photos']) ? explode(',', $ad['photos']) : array();

// Benzer ilanları al
$pdo = getDbConnection();
$stmt = $pdo->prepare("
    SELECT a.*, c.name as category_name, c.color as category_color,
           ci.name as city_name,
           (SELECT filename FROM ad_photos WHERE ad_id = a.id AND is_primary = 1 LIMIT 1) as primary_photo
    FROM ads a 
    LEFT JOIN categories c ON a.category_id = c.id 
    LEFT JOIN cities ci ON a.city_id = ci.id 
    WHERE a.status = 'active' AND a.category_id = ? AND a.id != ? 
          AND (a.expires_at IS NULL OR a.expires_at > NOW())
    ORDER BY a.featured DESC, a.created_at DESC 
    LIMIT 4
");
$stmt->execute(array($ad['category_id'], $ad['id']));
$relatedAds = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Görüntülenme sayısını artır
$stmt = $pdo->prepare("UPDATE ads SET views = views + 1 WHERE id = ?");
$stmt->execute(array($ad['id']));

// Header'ı dahil et
include 'includes/header.php';
?>

<!-- Breadcrumb -->
<div class="container mt-3">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="index.php">Ana Sayfa</a></li>
            <li class="breadcrumb-item"><a href="ads.php">İlanlar</a></li>
            <li class="breadcrumb-item"><a href="category.php?slug=<?php echo $ad['category_slug']; ?>"><?php echo htmlspecialchars($ad['category_name']); ?></a></li>
            <li class="breadcrumb-item active"><?php echo htmlspecialchars($ad['title']); ?></li>
        </ol>
    </nav>
</div>

<!-- Ad Detail -->
<div class="container">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Ad Header -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div>
                            <span class="badge mb-2" style="background-color: <?php echo $ad['category_color'] ?? '#DC2626'; ?>;">
                                <?php echo htmlspecialchars($ad['category_name']); ?>
                            </span>
                            <?php if ($ad['featured']): ?>
                            <span class="badge bg-warning text-dark ms-2">
                                <i class="fas fa-crown me-1"></i> Premium
                            </span>
                            <?php endif; ?>
                        </div>
                        <div class="text-end">
                            <small class="text-muted d-block">
                                <i class="fas fa-eye me-1"></i> <?php echo number_format($ad['views'] ?? 0); ?> görüntülenme
                            </small>
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i> <?php echo date('d.m.Y H:i', strtotime($ad['created_at'])); ?>
                            </small>
                        </div>
                    </div>
                    
                    <h1 class="h2 mb-3"><?php echo htmlspecialchars($ad['title']); ?></h1>
                    
                    <div class="row align-items-center mb-3">
                        <div class="col-md-6">
                            <p class="mb-0">
                                <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                <strong><?php echo htmlspecialchars($ad['city_name']); ?></strong>
                            </p>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <?php if ($ad['price_per_hour']): ?>
                            <h3 class="text-primary mb-0">
                                ₺<?php echo number_format($ad['price_per_hour'], 0, ',', '.'); ?>/saat
                            </h3>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Photo Gallery -->
            <?php if (!empty($photos)): ?>
            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-images me-2"></i> Fotoğraflar
                    </h5>
                    <div class="row">
                        <?php foreach ($photos as $index => $photo): ?>
                        <div class="col-md-4 mb-3">
                            <img src="uploads/ads/<?php echo htmlspecialchars(trim($photo)); ?>" 
                                 class="img-fluid rounded" 
                                 alt="<?php echo htmlspecialchars($ad['title']); ?>"
                                 style="height: 200px; object-fit: cover; width: 100%; cursor: pointer;"
                                 data-bs-toggle="modal" 
                                 data-bs-target="#photoModal"
                                 data-photo-index="<?php echo $index; ?>">
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- Description -->
            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-info-circle me-2"></i> Açıklama
                    </h5>
                    <div class="description-content">
                        <?php echo nl2br(htmlspecialchars($ad['description'])); ?>
                    </div>
                </div>
            </div>
            
            <!-- Additional Info -->
            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-list me-2"></i> Detaylar
                    </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><strong>Kategori:</strong> <?php echo htmlspecialchars($ad['category_name']); ?></li>
                                <li><strong>Şehir:</strong> <?php echo htmlspecialchars($ad['city_name']); ?></li>
                                <?php if ($ad['age']): ?>
                                <li><strong>Yaş:</strong> <?php echo htmlspecialchars($ad['age']); ?></li>
                                <?php endif; ?>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <?php if ($ad['height']): ?>
                                <li><strong>Boy:</strong> <?php echo htmlspecialchars($ad['height']); ?> cm</li>
                                <?php endif; ?>
                                <?php if ($ad['weight']): ?>
                                <li><strong>Kilo:</strong> <?php echo htmlspecialchars($ad['weight']); ?> kg</li>
                                <?php endif; ?>
                                <li><strong>İlan No:</strong> #<?php echo $ad['id']; ?></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Contact Info -->
            <div class="card mb-4 sticky-top" style="top: 2rem;">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-phone me-2"></i> İletişim Bilgileri
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($ad['contact_phone']): ?>
                    <div class="mb-3">
                        <label class="form-label">Telefon:</label>
                        <div class="d-flex">
                            <input type="text" class="form-control" value="<?php echo htmlspecialchars($ad['contact_phone']); ?>" readonly id="phone-number">
                            <button class="btn btn-outline-primary ms-2" onclick="copyToClipboard('phone-number')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($ad['contact_whatsapp']): ?>
                    <div class="mb-3">
                        <a href="https://wa.me/<?php echo preg_replace('/[^0-9]/', '', $ad['contact_whatsapp']); ?>" 
                           class="btn btn-success w-100" target="_blank">
                            <i class="fab fa-whatsapp me-2"></i> WhatsApp
                        </a>
                    </div>
                    <?php endif; ?>
                    
                    <div class="mb-3">
                        <button class="btn btn-outline-primary w-100" onclick="shareAd()">
                            <i class="fas fa-share me-2"></i> Paylaş
                        </button>
                    </div>
                    
                    <div class="alert alert-warning">
                        <small>
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            Güvenliğiniz için dikkatli olun ve şüpheli durumları bildirin.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Related Ads -->
    <?php if (!empty($relatedAds)): ?>
    <div class="mt-5">
        <h3 class="mb-4">
            <i class="fas fa-heart me-2 text-primary"></i> Benzer İlanlar
        </h3>
        <div class="row">
            <?php foreach ($relatedAds as $relatedAd): ?>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card ad-card h-100">
                    <?php if ($relatedAd['featured']): ?>
                    <div class="ad-badge featured-badge">
                        <i class="fas fa-star me-1"></i> Öne Çıkan
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($relatedAd['primary_photo']): ?>
                    <img src="uploads/ads/<?php echo htmlspecialchars($relatedAd['primary_photo']); ?>" 
                         class="card-img-top" alt="<?php echo htmlspecialchars($relatedAd['title']); ?>">
                    <?php else: ?>
                    <div class="card-img-top d-flex align-items-center justify-content-center" style="background: var(--gradient-primary); height: 200px;">
                        <i class="fas fa-image fa-3x text-white opacity-50"></i>
                    </div>
                    <?php endif; ?>
                    
                    <div class="card-body">
                        <h6 class="card-title">
                            <a href="ad-detail.php?slug=<?php echo $relatedAd['slug']; ?>" class="text-decoration-none text-dark">
                                <?php echo htmlspecialchars($relatedAd['title']); ?>
                            </a>
                        </h6>
                        <p class="card-text text-muted small">
                            <?php echo htmlspecialchars(substr($relatedAd['description'], 0, 80)) . '...'; ?>
                        </p>
                        <div class="d-flex justify-content-between align-items-center">
                            <?php if ($relatedAd['price_per_hour']): ?>
                            <small class="fw-bold text-primary">
                                ₺<?php echo number_format($relatedAd['price_per_hour'], 0, ',', '.'); ?>/saat
                            </small>
                            <?php endif; ?>
                            <a href="ad-detail.php?slug=<?php echo $relatedAd['slug']; ?>" class="btn btn-primary btn-sm">
                                Detay
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Photo Modal -->
<?php if (!empty($photos)): ?>
<div class="modal fade" id="photoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Fotoğraf Galerisi</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalPhoto" src="" class="img-fluid" alt="">
                <div class="mt-3">
                    <button class="btn btn-outline-primary" onclick="prevPhoto()">
                        <i class="fas fa-chevron-left"></i> Önceki
                    </button>
                    <span id="photoCounter" class="mx-3"></span>
                    <button class="btn btn-outline-primary" onclick="nextPhoto()">
                        Sonraki <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php
// Ek JavaScript
$additionalJS = "
let currentPhotoIndex = 0;
const photos = " . json_encode($photos) . ";

function showPhoto(index) {
    if (photos.length > 0) {
        document.getElementById('modalPhoto').src = 'uploads/ads/' + photos[index].trim();
        document.getElementById('photoCounter').textContent = (index + 1) + ' / ' + photos.length;
        currentPhotoIndex = index;
    }
}

function prevPhoto() {
    currentPhotoIndex = currentPhotoIndex > 0 ? currentPhotoIndex - 1 : photos.length - 1;
    showPhoto(currentPhotoIndex);
}

function nextPhoto() {
    currentPhotoIndex = currentPhotoIndex < photos.length - 1 ? currentPhotoIndex + 1 : 0;
    showPhoto(currentPhotoIndex);
}

document.addEventListener('DOMContentLoaded', function() {
    const photoModal = document.getElementById('photoModal');
    if (photoModal) {
        photoModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const photoIndex = parseInt(button.getAttribute('data-photo-index'));
            showPhoto(photoIndex);
        });
    }
});

function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    element.select();
    document.execCommand('copy');
    
    // Show feedback
    const btn = element.nextElementSibling;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class=\"fas fa-check\"></i>';
    btn.classList.add('btn-success');
    btn.classList.remove('btn-outline-primary');
    
    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.classList.remove('btn-success');
        btn.classList.add('btn-outline-primary');
    }, 2000);
}

function shareAd() {
    if (navigator.share) {
        navigator.share({
            title: '" . addslashes($ad['title']) . "',
            text: '" . addslashes(substr($ad['description'], 0, 100)) . "...',
            url: window.location.href
        });
    } else {
        // Fallback: copy URL to clipboard
        navigator.clipboard.writeText(window.location.href).then(() => {
            alert('Link kopyalandı!');
        });
    }
}
";

// Footer'ı dahil et
include 'includes/footer.php';
?>
