<?php

namespace App\Models;

use CodeIgniter\Model;

class AdModel extends Model
{
    protected $table = 'ads';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'user_id', 'escort_id', 'category_id', 'city_id', 'district_id', 'package_id',
        'title', 'slug', 'description', 'price_per_hour', 'price_per_night',
        'contact_phone', 'contact_whatsapp', 'contact_telegram', 'location_details',
        'services_offered', 'working_hours', 'featured', 'priority', 'status',
        'rejection_reason', 'views', 'clicks', 'expires_at', 'published_at'
    ];

    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    protected $validationRules = [
        'user_id' => 'required|integer',
        'category_id' => 'required|integer',
        'city_id' => 'required|integer',
        'title' => 'required|min_length[10]|max_length[200]',
        'description' => 'required|min_length[50]',
        'contact_phone' => 'permit_empty|regex_match[/^[0-9+\-\s\(\)]+$/]'
    ];

    protected $validationMessages = [
        'title' => [
            'required' => 'İlan başlığı gereklidir.',
            'min_length' => 'İlan başlığı en az 10 karakter olmalıdır.',
            'max_length' => 'İlan başlığı en fazla 200 karakter olabilir.'
        ],
        'description' => [
            'required' => 'İlan açıklaması gereklidir.',
            'min_length' => 'İlan açıklaması en az 50 karakter olmalıdır.'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    protected $allowCallbacks = true;
    protected $beforeInsert = ['generateSlug'];
    protected $beforeUpdate = ['generateSlug'];

    // Öne çıkan ilanları getir
    public function getFeaturedAds($limit = 8)
    {
        return $this->select('ads.*, categories.name as category_name, cities.name as city_name, 
                            districts.name as district_name, users.username,
                            (SELECT filename FROM ad_photos WHERE ad_id = ads.id AND is_primary = 1 LIMIT 1) as primary_photo')
                    ->join('categories', 'categories.id = ads.category_id')
                    ->join('cities', 'cities.id = ads.city_id')
                    ->join('districts', 'districts.id = ads.district_id', 'left')
                    ->join('users', 'users.id = ads.user_id')
                    ->where('ads.status', 'active')
                    ->where('ads.featured', 1)
                    ->where('ads.expires_at >', date('Y-m-d H:i:s'))
                    ->orderBy('ads.priority', 'DESC')
                    ->orderBy('ads.created_at', 'DESC')
                    ->limit($limit)
                    ->findAll();
    }

    // Son eklenen ilanları getir
    public function getLatestAds($limit = 12)
    {
        return $this->select('ads.*, categories.name as category_name, cities.name as city_name, 
                            districts.name as district_name, users.username,
                            (SELECT filename FROM ad_photos WHERE ad_id = ads.id AND is_primary = 1 LIMIT 1) as primary_photo')
                    ->join('categories', 'categories.id = ads.category_id')
                    ->join('cities', 'cities.id = ads.city_id')
                    ->join('districts', 'districts.id = ads.district_id', 'left')
                    ->join('users', 'users.id = ads.user_id')
                    ->where('ads.status', 'active')
                    ->where('ads.expires_at >', date('Y-m-d H:i:s'))
                    ->orderBy('ads.created_at', 'DESC')
                    ->limit($limit)
                    ->findAll();
    }

    // Kategoriye göre ilanları getir
    public function getAdsByCategory($categoryId, $limit = 20, $offset = 0)
    {
        return $this->select('ads.*, categories.name as category_name, cities.name as city_name, 
                            districts.name as district_name, users.username,
                            (SELECT filename FROM ad_photos WHERE ad_id = ads.id AND is_primary = 1 LIMIT 1) as primary_photo')
                    ->join('categories', 'categories.id = ads.category_id')
                    ->join('cities', 'cities.id = ads.city_id')
                    ->join('districts', 'districts.id = ads.district_id', 'left')
                    ->join('users', 'users.id = ads.user_id')
                    ->where('ads.status', 'active')
                    ->where('ads.category_id', $categoryId)
                    ->where('ads.expires_at >', date('Y-m-d H:i:s'))
                    ->orderBy('ads.featured', 'DESC')
                    ->orderBy('ads.priority', 'DESC')
                    ->orderBy('ads.created_at', 'DESC')
                    ->limit($limit, $offset)
                    ->findAll();
    }

    // Şehre göre ilanları getir
    public function getAdsByCity($cityId, $limit = 20, $offset = 0)
    {
        return $this->select('ads.*, categories.name as category_name, cities.name as city_name, 
                            districts.name as district_name, users.username,
                            (SELECT filename FROM ad_photos WHERE ad_id = ads.id AND is_primary = 1 LIMIT 1) as primary_photo')
                    ->join('categories', 'categories.id = ads.category_id')
                    ->join('cities', 'cities.id = ads.city_id')
                    ->join('districts', 'districts.id = ads.district_id', 'left')
                    ->join('users', 'users.id = ads.user_id')
                    ->where('ads.status', 'active')
                    ->where('ads.city_id', $cityId)
                    ->where('ads.expires_at >', date('Y-m-d H:i:s'))
                    ->orderBy('ads.featured', 'DESC')
                    ->orderBy('ads.priority', 'DESC')
                    ->orderBy('ads.created_at', 'DESC')
                    ->limit($limit, $offset)
                    ->findAll();
    }

    // İlan detayını getir
    public function getAdDetail($id)
    {
        $ad = $this->select('ads.*, categories.name as category_name, categories.slug as category_slug,
                           cities.name as city_name, cities.slug as city_slug,
                           districts.name as district_name, districts.slug as district_slug,
                           users.username, escorts.*')
                    ->join('categories', 'categories.id = ads.category_id')
                    ->join('cities', 'cities.id = ads.city_id')
                    ->join('districts', 'districts.id = ads.district_id', 'left')
                    ->join('users', 'users.id = ads.user_id')
                    ->join('escorts', 'escorts.id = ads.escort_id', 'left')
                    ->where('ads.id', $id)
                    ->where('ads.status', 'active')
                    ->where('ads.expires_at >', date('Y-m-d H:i:s'))
                    ->first();

        if ($ad) {
            // Görüntülenme sayısını artır
            $this->update($id, ['views' => $ad['views'] + 1]);
            
            // Fotoğrafları getir
            $photoModel = new \App\Models\AdPhotoModel();
            $ad['photos'] = $photoModel->getAdPhotos($id);
        }

        return $ad;
    }

    // Arama yap
    public function searchAds($filters = [], $limit = 20, $offset = 0)
    {
        $builder = $this->select('ads.*, categories.name as category_name, cities.name as city_name, 
                                districts.name as district_name, users.username,
                                (SELECT filename FROM ad_photos WHERE ad_id = ads.id AND is_primary = 1 LIMIT 1) as primary_photo')
                        ->join('categories', 'categories.id = ads.category_id')
                        ->join('cities', 'cities.id = ads.city_id')
                        ->join('districts', 'districts.id = ads.district_id', 'left')
                        ->join('users', 'users.id = ads.user_id')
                        ->where('ads.status', 'active')
                        ->where('ads.expires_at >', date('Y-m-d H:i:s'));

        // Filtreleri uygula
        if (!empty($filters['keyword'])) {
            $builder->groupStart()
                   ->like('ads.title', $filters['keyword'])
                   ->orLike('ads.description', $filters['keyword'])
                   ->groupEnd();
        }

        if (!empty($filters['category_id'])) {
            $builder->where('ads.category_id', $filters['category_id']);
        }

        if (!empty($filters['city_id'])) {
            $builder->where('ads.city_id', $filters['city_id']);
        }

        if (!empty($filters['district_id'])) {
            $builder->where('ads.district_id', $filters['district_id']);
        }

        if (!empty($filters['min_price'])) {
            $builder->where('ads.price_per_hour >=', $filters['min_price']);
        }

        if (!empty($filters['max_price'])) {
            $builder->where('ads.price_per_hour <=', $filters['max_price']);
        }

        return $builder->orderBy('ads.featured', 'DESC')
                      ->orderBy('ads.priority', 'DESC')
                      ->orderBy('ads.created_at', 'DESC')
                      ->limit($limit, $offset)
                      ->findAll();
    }

    // İstatistikler
    public function getTotalActiveAds()
    {
        return $this->where('status', 'active')
                   ->where('expires_at >', date('Y-m-d H:i:s'))
                   ->countAllResults();
    }

    public function getTodayAds()
    {
        return $this->where('status', 'active')
                   ->where('DATE(created_at)', date('Y-m-d'))
                   ->countAllResults();
    }

    // Slug oluştur
    protected function generateSlug(array $data)
    {
        if (isset($data['data']['title'])) {
            $data['data']['slug'] = url_title($data['data']['title'], '-', true);
            
            // Benzersiz slug kontrolü
            $count = 1;
            $originalSlug = $data['data']['slug'];
            
            while ($this->where('slug', $data['data']['slug'])->first()) {
                $data['data']['slug'] = $originalSlug . '-' . $count;
                $count++;
            }
        }
        
        return $data;
    }
}
