# OpenCart ******* Trendyol E-Faturam Entegrasyonu

Bu modül, OpenCart ******* e-ticaret platformu ile Trendyol E-Faturam sistemini entegre eder.

## 🚀 Özellikler

- **E-Fatura Gönderimi**: Kurumsal müşteriler için otomatik e-fatura gönderimi
- **E-Arşiv Fatura**: Bireysel müşteriler için e-arşiv fatura oluşturma
- **Otomatik Entegrasyon**: Sipar<PERSON>ş tamamlandığında otomatik fatura gönderimi
- **Admin Panel**: <PERSON><PERSON> yap<PERSON>landırma ve yönetim arayüzü
- **Hata Yönetimi**: Detaylı loglama ve hata raporlama
- **Test Modu**: Geliştirme ve test için sandbox desteği
- **Fatura Takibi**: Gönderilen faturaların durumunu takip etme
- **Ye<PERSON>den Gönderim**: Başarısız faturaları yeniden gönderme

## 📋 Gereksinimler

- OpenCart *******+
- PHP 7.1+
- MySQL 5.6+
- cURL extension
- JSON extension
- OpenSSL extension
- SSL sertifikası (production için)
- Trendyol E-Faturam hesabı

## 📦 Kurulum

### 1. Dosyaları Yükleme

1. `upload` klasöründeki tüm dosyaları OpenCart kurulum dizininize yükleyin
2. Dosya izinlerinin doğru olduğundan emin olun (755 veya 777)

### 2. Modül Kurulumu

1. Admin panele giriş yapın
2. **Extensions > Extensions** menüsüne gidin
3. **Choose the extension type** dropdown'dan **Modules** seçin
4. **Trendyol E-Faturam** modülünü bulun
5. **Install** butonuna tıklayın
6. **Edit** butonuna tıklayarak modül ayarlarını açın

### 3. OCMOD Kurulumu (Opsiyonel)

1. Admin panelde **Extensions > Installer** bölümüne gidin
2. `install.ocmod.xml` dosyasını yükleyin
3. **Extensions > Modifications** bölümüne gidin
4. **Refresh** butonuna tıklayın

## ⚙️ Yapılandırma

### Temel Ayarlar

1. **Status**: Modülü etkinleştirin
2. **Mode**: Test modu veya Canlı mod seçin
3. **API URL**: Trendyol E-Faturam API endpoint'i
4. **Username**: API kullanıcı adınız
5. **Password**: API şifreniz

### Şirket Bilgileri

1. **Company Name**: Şirket adınız
2. **Tax Number**: Vergi numaranız
3. **Tax Office**: Vergi daireniz

### Otomatik Gönderim

1. **Auto Send**: Sipariş tamamlandığında otomatik fatura gönderimini etkinleştirin

## 🔧 Kullanım

### Otomatik Fatura Gönderimi

Modül kurulumu ve yapılandırması tamamlandıktan sonra:

1. Sipariş durumu "Complete", "Processed" veya "Shipped" olduğunda otomatik olarak fatura gönderilir
2. Müşteri tipine göre (kurumsal/bireysel) otomatik olarak E-Fatura veya E-Arşiv seçilir
3. Fatura durumu admin panelden takip edilebilir

### Manuel Fatura Yönetimi

1. **Extensions > Trendyol E-Faturam > Invoices** menüsüne gidin
2. Gönderilen faturaları görüntüleyin
3. Başarısız faturaları yeniden gönderin
4. Fatura detaylarını inceleyin

### Log Takibi

1. **Extensions > Trendyol E-Faturam > Logs** menüsüne gidin
2. API isteklerini ve hatalarını takip edin
3. Sorun giderme için detaylı logları inceleyin

## 🧪 Test Etme

### Test Modu

1. Modül ayarlarında **Test Mode** seçin
2. Test API bilgilerini girin
3. **Test Connection** butonuna tıklayın

### Test Sayfası

1. **Extensions > Trendyol E-Faturam > Test** menüsüne gidin
2. Tüm sistem testlerini çalıştırın
3. Sorunları tespit edin ve düzeltin

## 📊 Veritabanı Tabloları

Modül aşağıdaki tabloları oluşturur:

- `oc_trendyol_efaturam_invoices`: Fatura kayıtları
- `oc_trendyol_efaturam_logs`: Log kayıtları
- `oc_trendyol_efaturam_settings`: Modül ayarları

## 🔍 Sorun Giderme

### Yaygın Sorunlar

1. **API Bağlantı Hatası**
   - API URL'sini kontrol edin
   - Kullanıcı adı ve şifrenizi doğrulayın
   - SSL sertifikasını kontrol edin

2. **Fatura Gönderilmiyor**
   - Modülün etkin olduğunu kontrol edin
   - Otomatik gönderimin açık olduğunu kontrol edin
   - Sipariş durumunu kontrol edin

3. **Log Dosyası Yazılamıyor**
   - `system/storage/logs/` klasörünün yazılabilir olduğunu kontrol edin
   - Dosya izinlerini kontrol edin (755 veya 777)

### Debug Modu

1. Test sayfasından sistem kontrollerini çalıştırın
2. Log dosyalarını inceleyin
3. API yanıtlarını kontrol edin

## 📝 Özelleştirme

### Müşteri Alanları

Kurumsal müşteriler için vergi numarası ve vergi dairesi bilgilerini toplamak için:

1. Checkout formuna özel alanlar ekleyin
2. `extractTaxNumber()` ve `extractTaxOffice()` metodlarını güncelleyin

### Fatura Numarası Formatı

`generateInvoiceNumber()` metodunu düzenleyerek fatura numarası formatını değiştirebilirsiniz.

### Sipariş Durumları

`sendInvoice()` metodundaki `$completed_status_ids` dizisini düzenleyerek hangi sipariş durumlarında fatura gönderileceğini belirleyebilirsiniz.

## 🔒 Güvenlik

- API şifrelerini güvenli saklayın
- HTTPS kullanın
- Log dosyalarını düzenli olarak temizleyin
- Dosya izinlerini kontrol edin

## 📞 Destek

Herhangi bir sorun yaşadığınızda:

1. Log dosyalarını kontrol edin (`system/storage/logs/trendyol_efaturam.log`)
2. Test modunda API bağlantısını test edin
3. Sistem testlerini çalıştırın
4. Trendyol E-Faturam destek ekibiyle iletişime geçin

## 📄 Lisans

Bu modül MIT lisansı altında dağıtılmaktadır.

## 🔄 Güncellemeler

### Versiyon 1.0.0
- İlk sürüm
- E-Fatura ve E-Arşiv desteği
- Otomatik gönderim
- Admin panel arayüzü
- Test ve log sistemi

## 👥 Katkıda Bulunma

1. Fork edin
2. Feature branch oluşturun (`git checkout -b feature/AmazingFeature`)
3. Commit edin (`git commit -m 'Add some AmazingFeature'`)
4. Push edin (`git push origin feature/AmazingFeature`)
5. Pull Request oluşturun

## 📧 İletişim

- Email: <EMAIL>
- Website: https://www.yourwebsite.com
- GitHub: https://github.com/yourusername
