# OpenCart 3.0.32 Trendyol E-Faturam Entegrasyonu

Bu modül, OpenCart 3.0.32 e-ticaret platformu ile Trendyol E-Faturam sistemini entegre eder.

## Özel<PERSON>ler

- **E-Fatura Gönderimi**: Kurumsal müşteriler için otomatik e-fatura gönderimi
- **E-Arşiv Fatura**: Bireysel müşteriler için e-arşiv fatura oluşturma
- **Otomatik Entegrasyon**: Sipariş tamamlandığında otomatik fatura gönderimi
- **Admin Panel**: <PERSON><PERSON> yap<PERSON>landırma ve yönetim arayüzü
- **Hata Yönetimi**: Detaylı loglama ve hata raporlama
- **Test Modu**: Geliştirme ve test için sandbox desteği

## Gereksinimler

- OpenCart 3.0.32+
- PHP 7.1+
- cURL extension
- JSON extension
- SSL sertifikası (production için)

## Kurulum

1. <PERSON>d<PERSON>l dosyalarını OpenCart kurulum dizininize yükleyin
2. Admin panelden Extensions > Extensions > Modules bölümüne gidin
3. "Trendyol E-Faturam" modülünü bulun ve Install butonuna tıklayın
4. Edit butonuna tıklayarak modül ayarlarını yapılandırın

## Yapılandırma

### API Bilgileri
- **Test/Production Modu**: Geliştirme için test modunu seçin
- **API URL**: Trendyol E-Faturam API endpoint'i
- **Username**: API kullanıcı adınız
- **Password**: API şifreniz
- **Company Info**: Şirket bilgileriniz

### Fatura Ayarları
- **Otomatik Gönderim**: Sipariş durumuna göre otomatik fatura gönderimi
- **E-Fatura/E-Arşiv**: Müşteri tipine göre otomatik seçim
- **Fatura Şablonu**: Kullanılacak fatura şablonu

## Kullanım

Modül kurulumu ve yapılandırması tamamlandıktan sonra:

1. Sipariş tamamlandığında otomatik olarak fatura gönderilir
2. Admin panelden manuel fatura gönderimi yapılabilir
3. Fatura durumları ve loglar takip edilebilir

## Destek

Herhangi bir sorun yaşadığınızda:
1. Log dosyalarını kontrol edin
2. Test modunda API bağlantısını test edin
3. Trendyol E-Faturam destek ekibiyle iletişime geçin

## Lisans

Bu modül MIT lisansı altında dağıtılmaktadır.
