<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Giriş kontrolü
if (!isset($_SESSION['user_id'])) {
    header('Location: auth/login.php');
    exit;
}

$page_title = 'Ödeme Başarılı';
$payment_info = null;

// URL'den order_id al
$order_id = $_GET['order_id'] ?? '';

if ($order_id) {
    try {
        // Ödeme bilgilerini getir
        $sql = "SELECT p.*, u.full_name, u.email 
                FROM payments p 
                LEFT JOIN users u ON p.user_id = u.id 
                WHERE p.order_id = ? AND p.user_id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$order_id, $_SESSION['user_id']]);
        $payment_info = $stmt->fetch();
        
    } catch (PDOException $e) {
        error_log("Payment success page error: " . $e->getMessage());
    }
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bakıcı Platform</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c5aa0;
        }
        
        .success-icon {
            font-size: 4rem;
            color: #28a745;
        }
        
        .payment-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="index.php">
                <i class="bi bi-heart-fill me-2"></i>Bakıcı Platform
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.php">Dashboard</a>
                <a class="nav-link" href="packages.php">Paketler</a>
                <a class="nav-link" href="auth/logout.php">Çıkış</a>
            </div>
        </div>
    </nav>

    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="payment-card card">
                    <div class="card-body text-center p-5">
                        <!-- Başarı İkonu -->
                        <div class="success-icon mb-4">
                            <i class="bi bi-check-circle-fill"></i>
                        </div>
                        
                        <h2 class="text-success fw-bold mb-3">Ödeme Başarılı!</h2>
                        <p class="text-muted mb-4">Paket satın alma işleminiz başarıyla tamamlandı.</p>
                        
                        <?php if ($payment_info): ?>
                            <!-- Ödeme Detayları -->
                            <div class="row g-3 mb-4">
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6 class="card-title">Paket Bilgileri</h6>
                                            <p class="card-text">
                                                <strong><?php echo ucfirst($payment_info['package_type']); ?> Paket</strong><br>
                                                <span class="text-success">₺<?php echo number_format($payment_info['amount'], 2); ?></span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6 class="card-title">Sipariş Bilgileri</h6>
                                            <p class="card-text">
                                                <strong>Sipariş No:</strong><br>
                                                <code><?php echo htmlspecialchars($payment_info['order_id']); ?></code>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Durum Bilgisi -->
                            <div class="alert alert-success">
                                <h6><i class="bi bi-info-circle me-2"></i>Paket Aktivasyonu</h6>
                                <p class="mb-0">
                                    <?php if ($payment_info['status'] === 'completed'): ?>
                                        Paketiniz aktif edildi! Artık premium özelliklerden yararlanabilirsiniz.
                                    <?php else: ?>
                                        Ödemeniz alındı, paketiniz kısa süre içinde aktif edilecektir.
                                    <?php endif; ?>
                                </p>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                Ödeme bilgileri bulunamadı. Lütfen müşteri hizmetleri ile iletişime geçin.
                            </div>
                        <?php endif; ?>
                        
                        <!-- Aksiyon Butonları -->
                        <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                            <a href="dashboard.php" class="btn btn-primary btn-lg">
                                <i class="bi bi-speedometer2 me-2"></i>Dashboard'a Git
                            </a>
                            <a href="packages.php" class="btn btn-outline-primary btn-lg">
                                <i class="bi bi-box me-2"></i>Diğer Paketler
                            </a>
                        </div>
                        
                        <!-- Destek Bilgisi -->
                        <div class="mt-4 pt-4 border-top">
                            <p class="text-muted small">
                                <i class="bi bi-headset me-2"></i>
                                Herhangi bir sorunuz varsa <a href="mailto:<EMAIL>"><EMAIL></a> adresinden bize ulaşabilirsiniz.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 5 saniye sonra dashboard'a yönlendir
        setTimeout(function() {
            if (confirm('Dashboard sayfasına yönlendirilmek istiyor musunuz?')) {
                window.location.href = 'dashboard.php';
            }
        }, 5000);
    </script>
</body>
</html>
