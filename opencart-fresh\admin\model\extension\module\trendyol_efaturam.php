<?php
/**
 * Trendyol E-Faturam OpenCart 3.0.3.2 Extension
 * Admin Model - Database operations and logging
 * 
 * <AUTHOR> Name
 * @version 1.0.0
 * @license MIT
 */

class ModelExtensionModuleTrendyolEfaturam extends Model {
    
    /**
     * Install module - Create necessary database tables
     */
    public function install() {
        // Create invoices table
        $this->db->query("
            CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "trendyol_efaturam_invoices` (
                `invoice_id` int(11) NOT NULL AUTO_INCREMENT,
                `order_id` int(11) NOT NULL,
                `invoice_number` varchar(50) NOT NULL,
                `invoice_type` enum('e-invoice','e-archive') NOT NULL,
                `status` varchar(20) NOT NULL DEFAULT 'pending',
                `api_response` text,
                `error_message` text,
                `retry_count` int(3) NOT NULL DEFAULT 0,
                `date_created` datetime NOT NULL,
                `date_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`invoice_id`),
                <PERSON><PERSON>Y `order_id` (`order_id`),
                KEY `invoice_number` (`invoice_number`),
                KEY `status` (`status`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;
        ");
        
        // Create logs table
        $this->db->query("
            CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "trendyol_efaturam_logs` (
                `log_id` int(11) NOT NULL AUTO_INCREMENT,
                `order_id` int(11) DEFAULT NULL,
                `invoice_id` int(11) DEFAULT NULL,
                `log_level` enum('info','warning','error','debug') NOT NULL DEFAULT 'info',
                `message` text NOT NULL,
                `data` longtext,
                `ip_address` varchar(45),
                `user_agent` varchar(255),
                `date_created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`log_id`),
                KEY `order_id` (`order_id`),
                KEY `invoice_id` (`invoice_id`),
                KEY `log_level` (`log_level`),
                KEY `date_created` (`date_created`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;
        ");
        
        // Create settings table for module-specific settings
        $this->db->query("
            CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "trendyol_efaturam_settings` (
                `setting_id` int(11) NOT NULL AUTO_INCREMENT,
                `key` varchar(100) NOT NULL,
                `value` text,
                `serialized` tinyint(1) NOT NULL DEFAULT 0,
                `date_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`setting_id`),
                UNIQUE KEY `key` (`key`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;
        ");
    }
    
    /**
     * Uninstall module - Clean up database tables
     */
    public function uninstall() {
        // Note: We don't drop tables on uninstall to preserve data
        // Uncomment the following lines if you want to remove all data on uninstall
        
        // $this->db->query("DROP TABLE IF EXISTS `" . DB_PREFIX . "trendyol_efaturam_invoices`");
        // $this->db->query("DROP TABLE IF EXISTS `" . DB_PREFIX . "trendyol_efaturam_logs`");
        // $this->db->query("DROP TABLE IF EXISTS `" . DB_PREFIX . "trendyol_efaturam_settings`");
    }
    
    /**
     * Get all invoices with pagination
     */
    public function getInvoices($data = array()) {
        $sql = "SELECT i.*, o.firstname, o.lastname, o.email, o.total 
                FROM " . DB_PREFIX . "trendyol_efaturam_invoices i 
                LEFT JOIN " . DB_PREFIX . "order o ON (i.order_id = o.order_id) 
                WHERE 1=1";
        
        if (!empty($data['filter_order_id'])) {
            $sql .= " AND i.order_id = '" . (int)$data['filter_order_id'] . "'";
        }
        
        if (!empty($data['filter_invoice_number'])) {
            $sql .= " AND i.invoice_number LIKE '%" . $this->db->escape($data['filter_invoice_number']) . "%'";
        }
        
        if (!empty($data['filter_status'])) {
            $sql .= " AND i.status = '" . $this->db->escape($data['filter_status']) . "'";
        }
        
        if (!empty($data['filter_type'])) {
            $sql .= " AND i.invoice_type = '" . $this->db->escape($data['filter_type']) . "'";
        }
        
        if (!empty($data['filter_date_from'])) {
            $sql .= " AND DATE(i.date_created) >= '" . $this->db->escape($data['filter_date_from']) . "'";
        }
        
        if (!empty($data['filter_date_to'])) {
            $sql .= " AND DATE(i.date_created) <= '" . $this->db->escape($data['filter_date_to']) . "'";
        }
        
        $sql .= " ORDER BY i.date_created DESC";
        
        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }
            
            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }
            
            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }
        
        $query = $this->db->query($sql);
        
        return $query->rows;
    }
    
    /**
     * Get total invoices count
     */
    public function getTotalInvoices($data = array()) {
        $sql = "SELECT COUNT(*) AS total 
                FROM " . DB_PREFIX . "trendyol_efaturam_invoices i 
                LEFT JOIN " . DB_PREFIX . "order o ON (i.order_id = o.order_id) 
                WHERE 1=1";
        
        if (!empty($data['filter_order_id'])) {
            $sql .= " AND i.order_id = '" . (int)$data['filter_order_id'] . "'";
        }
        
        if (!empty($data['filter_invoice_number'])) {
            $sql .= " AND i.invoice_number LIKE '%" . $this->db->escape($data['filter_invoice_number']) . "%'";
        }
        
        if (!empty($data['filter_status'])) {
            $sql .= " AND i.status = '" . $this->db->escape($data['filter_status']) . "'";
        }
        
        if (!empty($data['filter_type'])) {
            $sql .= " AND i.invoice_type = '" . $this->db->escape($data['filter_type']) . "'";
        }
        
        if (!empty($data['filter_date_from'])) {
            $sql .= " AND DATE(i.date_created) >= '" . $this->db->escape($data['filter_date_from']) . "'";
        }
        
        if (!empty($data['filter_date_to'])) {
            $sql .= " AND DATE(i.date_created) <= '" . $this->db->escape($data['filter_date_to']) . "'";
        }
        
        $query = $this->db->query($sql);
        
        return $query->row['total'];
    }
    
    /**
     * Get invoice by ID
     */
    public function getInvoice($invoice_id) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "trendyol_efaturam_invoices WHERE invoice_id = '" . (int)$invoice_id . "'");
        
        return $query->row;
    }
    
    /**
     * Get invoice by order ID
     */
    public function getInvoiceByOrderId($order_id) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "trendyol_efaturam_invoices WHERE order_id = '" . (int)$order_id . "'");
        
        return $query->row;
    }
    
    /**
     * Update invoice status
     */
    public function updateInvoiceStatus($invoice_id, $status, $error_message = '') {
        $sql = "UPDATE " . DB_PREFIX . "trendyol_efaturam_invoices SET 
                status = '" . $this->db->escape($status) . "'";
        
        if ($error_message) {
            $sql .= ", error_message = '" . $this->db->escape($error_message) . "'";
        }
        
        $sql .= " WHERE invoice_id = '" . (int)$invoice_id . "'";
        
        $this->db->query($sql);
    }
    
    /**
     * Increment retry count
     */
    public function incrementRetryCount($invoice_id) {
        $this->db->query("UPDATE " . DB_PREFIX . "trendyol_efaturam_invoices SET retry_count = retry_count + 1 WHERE invoice_id = '" . (int)$invoice_id . "'");
    }
    
    /**
     * Get logs with pagination
     */
    public function getLogs($data = array()) {
        $sql = "SELECT * FROM " . DB_PREFIX . "trendyol_efaturam_logs WHERE 1=1";
        
        if (!empty($data['filter_order_id'])) {
            $sql .= " AND order_id = '" . (int)$data['filter_order_id'] . "'";
        }
        
        if (!empty($data['filter_level'])) {
            $sql .= " AND log_level = '" . $this->db->escape($data['filter_level']) . "'";
        }
        
        if (!empty($data['filter_date_from'])) {
            $sql .= " AND DATE(date_created) >= '" . $this->db->escape($data['filter_date_from']) . "'";
        }
        
        if (!empty($data['filter_date_to'])) {
            $sql .= " AND DATE(date_created) <= '" . $this->db->escape($data['filter_date_to']) . "'";
        }
        
        $sql .= " ORDER BY date_created DESC";
        
        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }
            
            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }
            
            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }
        
        $query = $this->db->query($sql);
        
        return $query->rows;
    }
    
    /**
     * Get total logs count
     */
    public function getTotalLogs($data = array()) {
        $sql = "SELECT COUNT(*) AS total FROM " . DB_PREFIX . "trendyol_efaturam_logs WHERE 1=1";
        
        if (!empty($data['filter_order_id'])) {
            $sql .= " AND order_id = '" . (int)$data['filter_order_id'] . "'";
        }
        
        if (!empty($data['filter_level'])) {
            $sql .= " AND log_level = '" . $this->db->escape($data['filter_level']) . "'";
        }
        
        if (!empty($data['filter_date_from'])) {
            $sql .= " AND DATE(date_created) >= '" . $this->db->escape($data['filter_date_from']) . "'";
        }
        
        if (!empty($data['filter_date_to'])) {
            $sql .= " AND DATE(date_created) <= '" . $this->db->escape($data['filter_date_to']) . "'";
        }
        
        $query = $this->db->query($sql);
        
        return $query->row['total'];
    }
    
    /**
     * Add log entry
     */
    public function addLog($level, $message, $order_id = null, $invoice_id = null, $data = null) {
        $ip_address = '';
        $user_agent = '';
        
        if (isset($_SERVER['REMOTE_ADDR'])) {
            $ip_address = $_SERVER['REMOTE_ADDR'];
        }
        
        if (isset($_SERVER['HTTP_USER_AGENT'])) {
            $user_agent = $_SERVER['HTTP_USER_AGENT'];
        }
        
        $sql = "INSERT INTO " . DB_PREFIX . "trendyol_efaturam_logs SET 
                log_level = '" . $this->db->escape($level) . "',
                message = '" . $this->db->escape($message) . "',
                ip_address = '" . $this->db->escape($ip_address) . "',
                user_agent = '" . $this->db->escape($user_agent) . "'";
        
        if ($order_id) {
            $sql .= ", order_id = '" . (int)$order_id . "'";
        }
        
        if ($invoice_id) {
            $sql .= ", invoice_id = '" . (int)$invoice_id . "'";
        }
        
        if ($data) {
            $sql .= ", data = '" . $this->db->escape(json_encode($data)) . "'";
        }
        
        $this->db->query($sql);
    }
    
    /**
     * Clean old logs (older than specified days)
     */
    public function cleanOldLogs($days = 30) {
        $this->db->query("DELETE FROM " . DB_PREFIX . "trendyol_efaturam_logs WHERE date_created < DATE_SUB(NOW(), INTERVAL " . (int)$days . " DAY)");
        
        return $this->db->countAffected();
    }
    
    /**
     * Get statistics
     */
    public function getStatistics() {
        $stats = array();
        
        // Total invoices
        $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "trendyol_efaturam_invoices");
        $stats['total_invoices'] = $query->row['total'];
        
        // Successful invoices
        $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "trendyol_efaturam_invoices WHERE status = 'sent'");
        $stats['successful_invoices'] = $query->row['total'];
        
        // Failed invoices
        $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "trendyol_efaturam_invoices WHERE status = 'failed'");
        $stats['failed_invoices'] = $query->row['total'];
        
        // Pending invoices
        $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "trendyol_efaturam_invoices WHERE status = 'pending'");
        $stats['pending_invoices'] = $query->row['total'];
        
        // E-Invoice count
        $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "trendyol_efaturam_invoices WHERE invoice_type = 'e-invoice'");
        $stats['e_invoice_count'] = $query->row['total'];
        
        // E-Archive count
        $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "trendyol_efaturam_invoices WHERE invoice_type = 'e-archive'");
        $stats['e_archive_count'] = $query->row['total'];
        
        return $stats;
    }
}
