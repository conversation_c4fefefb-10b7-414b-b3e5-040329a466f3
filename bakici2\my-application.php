<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Giriş kontrolü
if (!isLoggedIn()) {
    header('Location: auth/login.php');
    exit;
}

$application_id = intval($_GET['id'] ?? 0);
if (!$application_id) {
    header('Location: dashboard.php?error=invalid_application');
    exit;
}

// Başvuru bilgilerini getir
try {
    $sql = "SELECT ja.*, 
                   jl.title as job_title, jl.description as job_description, jl.location, jl.budget_min, jl.budget_max, jl.budget_type,
                   employer.full_name as employer_name, employer.email as employer_email, employer.phone as employer_phone
            FROM job_applications ja
            JOIN job_listings jl ON ja.job_id = jl.id
            JOIN users employer ON jl.user_id = employer.id
            WHERE ja.id = ? AND ja.caregiver_id = ?";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([$application_id, $_SESSION['user_id']]);
    $application = $stmt->fetch();
    
    if (!$application) {
        header('Location: dashboard.php?error=application_not_found');
        exit;
    }
    
} catch (PDOException $e) {
    header('Location: dashboard.php?error=database_error');
    exit;
}

// Durum renkleri ve isimleri
$status_colors = [
    'pending' => 'warning',
    'viewed' => 'info',
    'shortlisted' => 'primary',
    'interview' => 'secondary',
    'accepted' => 'success',
    'rejected' => 'danger',
    'withdrawn' => 'dark',
    'hired' => 'success'
];

$status_names = [
    'pending' => 'Bekliyor',
    'viewed' => 'Görüldü',
    'shortlisted' => 'Kısa Liste',
    'interview' => 'Görüşme',
    'accepted' => 'Kabul Edildi',
    'rejected' => 'Reddedildi',
    'withdrawn' => 'Geri Çekildi',
    'hired' => 'İşe Alındı'
];

$page_title = 'Başvuru Detayı';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bakıcı Platform</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c5aa0;
        }
        
        .status-timeline {
            position: relative;
            padding-left: 2rem;
        }
        
        .status-timeline::before {
            content: '';
            position: absolute;
            left: 0.5rem;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #dee2e6;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 1rem;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -0.75rem;
            top: 0.25rem;
            width: 1rem;
            height: 1rem;
            border-radius: 50%;
            background: #dee2e6;
        }
        
        .timeline-item.active::before {
            background: var(--primary-color);
        }
        
        .timeline-item.completed::before {
            background: #198754;
        }
        
        .timeline-item.rejected::before {
            background: #dc3545;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="index.php">
                <i class="bi bi-heart-fill me-2"></i>Bakıcı Platform
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.php">Dashboard</a>
                <a class="nav-link" href="auth/logout.php">Çıkış</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-lg-8">
                <!-- Başvuru Bilgileri -->
                <div class="card mb-4">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Başvuru Detayı</h5>
                            <span class="badge bg-<?php echo $status_colors[$application['status']] ?? 'secondary'; ?> fs-6">
                                <?php echo $status_names[$application['status']] ?? $application['status']; ?>
                            </span>
                        </div>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0">
                                <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                                <li class="breadcrumb-item active">Başvuru Detayı</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h4 class="text-primary mb-3"><?php echo htmlspecialchars($application['job_title']); ?></h4>
                                
                                <div class="mb-3">
                                    <h6>İş Tanımı</h6>
                                    <p><?php echo nl2br(htmlspecialchars($application['job_description'])); ?></p>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>Lokasyon:</strong></p>
                                        <p><i class="bi bi-geo-alt text-muted me-1"></i><?php echo htmlspecialchars($application['location']); ?></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>Bütçe:</strong></p>
                                        <p>
                                            <?php if ($application['budget_min'] || $application['budget_max']): ?>
                                                <?php if ($application['budget_min'] && $application['budget_max']): ?>
                                                    <?php echo formatMoney($application['budget_min']); ?> - <?php echo formatMoney($application['budget_max']); ?>
                                                <?php elseif ($application['budget_min']): ?>
                                                    <?php echo formatMoney($application['budget_min']); ?>+
                                                <?php else: ?>
                                                    <?php echo formatMoney($application['budget_max']); ?>'e kadar
                                                <?php endif; ?>
                                                <small class="text-muted">(<?php echo $application['budget_type']; ?>)</small>
                                            <?php else: ?>
                                                <span class="text-muted">Görüşülür</span>
                                            <?php endif; ?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="bg-light p-3 rounded">
                                    <h6>İşveren Bilgileri</h6>
                                    <p class="mb-2"><strong><?php echo htmlspecialchars($application['employer_name']); ?></strong></p>
                                    <p class="mb-2"><small><?php echo htmlspecialchars($application['employer_email']); ?></small></p>
                                    <?php if ($application['employer_phone']): ?>
                                        <p class="mb-0"><small><?php echo htmlspecialchars($application['employer_phone']); ?></small></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Başvuru Mesajım -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Başvuru Mesajım</h6>
                    </div>
                    <div class="card-body">
                        <div class="bg-light p-3 rounded">
                            <?php echo nl2br(htmlspecialchars($application['message'])); ?>
                        </div>
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                <strong>Başvuru Tarihi:</strong> <?php echo date('d.m.Y H:i', strtotime($application['applied_at'])); ?>
                            </small>
                            <?php if ($application['updated_at'] && $application['updated_at'] != $application['created_at']): ?>
                                <br>
                                <small class="text-muted">
                                    <strong>Son Güncelleme:</strong> <?php echo date('d.m.Y H:i', strtotime($application['updated_at'])); ?>
                                </small>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <!-- Başvuru Durumu Timeline -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">Başvuru Süreci</h6>
                    </div>
                    <div class="card-body">
                        <div class="status-timeline">
                            <?php
                            $statuses = [
                                'pending' => 'Başvuru Gönderildi',
                                'viewed' => 'İşveren Tarafından Görüldü',
                                'shortlisted' => 'Kısa Listeye Alındı',
                                'interview' => 'Görüşmeye Çağrıldı',
                                'accepted' => 'Başvuru Kabul Edildi',
                                'hired' => 'İşe Alındı'
                            ];
                            
                            $current_status = $application['status'];
                            $status_order = ['pending', 'viewed', 'shortlisted', 'interview', 'accepted', 'hired'];
                            $current_index = array_search($current_status, $status_order);
                            
                            foreach ($statuses as $status => $label):
                                $status_index = array_search($status, $status_order);
                                $class = '';
                                
                                if ($current_status === 'rejected' || $current_status === 'withdrawn') {
                                    if ($status === 'pending') {
                                        $class = 'completed';
                                    } elseif ($status === $current_status) {
                                        $class = 'rejected';
                                    }
                                } else {
                                    if ($status_index < $current_index) {
                                        $class = 'completed';
                                    } elseif ($status === $current_status) {
                                        $class = 'active';
                                    }
                                }
                            ?>
                                <div class="timeline-item <?php echo $class; ?>">
                                    <small class="<?php echo $class ? 'fw-bold' : 'text-muted'; ?>">
                                        <?php echo $label; ?>
                                    </small>
                                </div>
                            <?php endforeach; ?>
                            
                            <?php if ($current_status === 'rejected'): ?>
                                <div class="timeline-item rejected">
                                    <small class="fw-bold text-danger">Başvuru Reddedildi</small>
                                </div>
                            <?php elseif ($current_status === 'withdrawn'): ?>
                                <div class="timeline-item rejected">
                                    <small class="fw-bold text-dark">Başvuru Geri Çekildi</small>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- İşlemler -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">İşlemler</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <?php if (in_array($application['status'], ['pending', 'viewed'])): ?>
                                <a href="edit-my-application.php?id=<?php echo $application['id']; ?>" class="btn btn-outline-primary">
                                    <i class="bi bi-pencil me-2"></i>Başvurumu Düzenle
                                </a>
                                <button class="btn btn-outline-warning" onclick="withdrawApplication(<?php echo $application['id']; ?>)">
                                    <i class="bi bi-arrow-counterclockwise me-2"></i>Başvuruyu Geri Çek
                                </button>
                            <?php endif; ?>
                            
                            <a href="send-message.php?to=<?php echo $application['employer_id']; ?>" class="btn btn-outline-success">
                                <i class="bi bi-chat-dots me-2"></i>İşverene Mesaj Gönder
                            </a>
                            
                            <a href="job-details.php?id=<?php echo $application['job_id']; ?>" class="btn btn-outline-info">
                                <i class="bi bi-eye me-2"></i>İş İlanını Görüntüle
                            </a>
                            
                            <a href="dashboard.php" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-2"></i>Dashboard'a Dön
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function withdrawApplication(applicationId) {
            if (confirm('Başvurunuzu geri çekmek istediğinizden emin misiniz? Bu işlem geri alınamaz!')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = 'update-application-status.php';
                form.innerHTML = `
                    <input type="hidden" name="application_id" value="${applicationId}">
                    <input type="hidden" name="status" value="withdrawn">
                    <input type="hidden" name="redirect" value="dashboard.php">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
