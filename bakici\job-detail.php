<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$job_slug = safeArray($_GET, 'slug', '');

if (!$job_slug) {
    redirect('jobs.php');
}

try {
    // İş ilanı bilgilerini getir
    $sql = "SELECT jl.*, u.full_name as employer_name, u.city as employer_city, 
                   c.name as category_name, c.icon as category_icon
            FROM job_listings jl 
            LEFT JOIN users u ON jl.user_id = u.id 
            LEFT JOIN categories c ON jl.category_id = c.id
            WHERE jl.slug = ? AND jl.status = 'active'";
    $stmt = $db->prepare($sql);
    $stmt->execute([$job_slug]);
    $job = $stmt->fetch();
    
    if (!$job) {
        redirect('jobs.php');
    }
} catch (PDOException $e) {
    redirect('jobs.php');
}

$page_title = escape($job['title']);
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c5aa0;
        }
        
        .job-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #1e3d72 100%);
            color: white;
            padding: 60px 0;
        }
        
        .job-card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-top: -50px;
            background: white;
            position: relative;
            z-index: 10;
        }
        
        .urgent-badge {
            background: #dc3545;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .navbar {
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="index.php">
                <i class="bi bi-heart-fill me-2"></i><?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="caregivers.php">Bakıcı Ara</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="jobs.php">İş İlanları</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">Hakkımızda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">İletişim</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle me-1"></i><?php echo escape(safeArray($_SESSION, 'full_name', 'Kullanıcı')); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="dashboard.php"><i class="bi bi-speedometer2 me-2"></i>Dashboard</a></li>
                                <li><a class="dropdown-item" href="profile.php"><i class="bi bi-person me-2"></i>Profil</a></li>
                                <li><a class="dropdown-item" href="messages.php"><i class="bi bi-chat-dots me-2"></i>Mesajlar</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="auth/logout.php"><i class="bi bi-box-arrow-right me-2"></i>Çıkış</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="auth/login.php">Giriş Yap</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-primary ms-2" href="auth/register.php">Üye Ol</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Job Header -->
    <section class="job-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb text-white-50">
                            <li class="breadcrumb-item"><a href="index.php" class="text-white-50">Ana Sayfa</a></li>
                            <li class="breadcrumb-item"><a href="jobs.php" class="text-white-50">İş İlanları</a></li>
                            <li class="breadcrumb-item active text-white"><?php echo escape($job['title']); ?></li>
                        </ol>
                    </nav>
                    <div class="d-flex align-items-center mb-3">
                        <?php if ($job['is_urgent']): ?>
                            <span class="urgent-badge me-3">ACİL</span>
                        <?php endif; ?>
                        <?php if ($job['category_icon']): ?>
                            <i class="<?php echo $job['category_icon']; ?> me-3" style="font-size: 2rem;"></i>
                        <?php endif; ?>
                    </div>
                    <h1 class="display-5 fw-bold"><?php echo escape($job['title']); ?></h1>
                    <p class="lead">
                        <i class="bi bi-person me-2"></i><?php echo escape($job['employer_name']); ?> • 
                        <i class="bi bi-geo-alt me-2"></i><?php echo escape($job['location_city']); ?>
                        <?php if ($job['location_district']): ?>
                            , <?php echo escape($job['location_district']); ?>
                        <?php endif; ?>
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <?php if (isLoggedIn()): ?>
                        <?php if ($_SESSION['user_type'] === 'caregiver'): ?>
                            <a href="apply-job.php?job_id=<?php echo $job['id']; ?>" class="btn btn-light btn-lg">
                                <i class="bi bi-send me-2"></i>Başvur
                            </a>
                        <?php elseif ($_SESSION['user_type'] === 'family' && $_SESSION['user_id'] != $job['user_id']): ?>
                            <a href="send-message.php?to=<?php echo $job['user_id']; ?>&job_id=<?php echo $job['id']; ?>" class="btn btn-outline-primary btn-lg">
                                <i class="bi bi-chat-dots me-2"></i>İlan Sahibine Mesaj
                            </a>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>

    <div class="container">
        <!-- Job Details Card -->
        <div class="job-card p-5">
            <div class="row">
                <div class="col-lg-8">
                    <h4 class="fw-bold mb-4">İş Açıklaması</h4>
                    <div class="mb-4">
                        <?php echo nl2br(escape($job['description'])); ?>
                    </div>
                    
                    <?php if ($job['requirements']): ?>
                    <h5 class="fw-bold mb-3">Aranan Özellikler</h5>
                    <div class="mb-4">
                        <?php echo nl2br(escape($job['requirements'])); ?>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($job['benefits']): ?>
                    <h5 class="fw-bold mb-3">Sunulan İmkanlar</h5>
                    <div class="mb-4">
                        <?php echo nl2br(escape($job['benefits'])); ?>
                    </div>
                    <?php endif; ?>
                </div>
                
                <div class="col-lg-4">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h5 class="card-title fw-bold mb-4">İş Detayları</h5>
                            
                            <div class="mb-3">
                                <h6 class="fw-bold text-muted">İş Tipi</h6>
                                <p><?php echo getJobTypes()[$job['job_type']] ?? $job['job_type']; ?></p>
                            </div>
                            
                            <div class="mb-3">
                                <h6 class="fw-bold text-muted">Bakım Tipi</h6>
                                <p><?php echo getCareTypes()[$job['care_type']] ?? $job['care_type']; ?></p>
                            </div>
                            
                            <?php if ($job['budget_min'] || $job['budget_max']): ?>
                            <div class="mb-3">
                                <h6 class="fw-bold text-muted">Bütçe</h6>
                                <p class="text-primary fw-bold">
                                    <?php if ($job['budget_min'] && $job['budget_max']): ?>
                                        <?php echo formatMoney($job['budget_min']); ?> - <?php echo formatMoney($job['budget_max']); ?>
                                    <?php elseif ($job['budget_min']): ?>
                                        <?php echo formatMoney($job['budget_min']); ?>+
                                    <?php else: ?>
                                        <?php echo formatMoney($job['budget_max']); ?>'e kadar
                                    <?php endif; ?>
                                    <?php if ($job['budget_type']): ?>
                                        /<?php echo $job['budget_type'] === 'hourly' ? 'saat' : ($job['budget_type'] === 'daily' ? 'gün' : ($job['budget_type'] === 'weekly' ? 'hafta' : 'ay')); ?>
                                    <?php endif; ?>
                                </p>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($job['start_date']): ?>
                            <div class="mb-3">
                                <h6 class="fw-bold text-muted">Başlangıç Tarihi</h6>
                                <p><?php echo formatDate($job['start_date']); ?></p>
                            </div>
                            <?php endif; ?>
                            
                            <div class="mb-3">
                                <h6 class="fw-bold text-muted">İlan Tarihi</h6>
                                <p><?php echo timeAgo($job['created_at']); ?></p>
                            </div>
                            
                            <?php if ($job['expires_at']): ?>
                            <div class="mb-3">
                                <h6 class="fw-bold text-muted">Son Başvuru</h6>
                                <p><?php echo formatDate($job['expires_at']); ?></p>
                            </div>
                            <?php endif; ?>
                            
                            <hr>
                            
                            <?php if (isLoggedIn()): ?>
                                <?php if ($_SESSION['user_type'] === 'caregiver'): ?>
                                    <div class="d-grid gap-2">
                                        <a href="apply-job.php?job_id=<?php echo $job['id']; ?>" class="btn btn-primary btn-lg">
                                            <i class="bi bi-send me-2"></i>Başvur
                                        </a>
                                        <a href="send-message.php?to=<?php echo $job['user_id']; ?>&job_id=<?php echo $job['id']; ?>" class="btn btn-outline-primary">
                                            <i class="bi bi-chat-dots me-2"></i>İlan Sahibine Mesaj
                                        </a>
                                    </div>
                                <?php elseif ($_SESSION['user_type'] === 'family'): ?>
                                    <?php if ($_SESSION['user_id'] == $job['user_id']): ?>
                                        <div class="d-grid gap-2">
                                            <a href="jobs/edit.php?id=<?php echo $job['id']; ?>" class="btn btn-primary btn-lg">
                                                <i class="bi bi-pencil me-2"></i>İlanı Düzenle
                                            </a>
                                            <a href="applications.php?job_id=<?php echo $job['id']; ?>" class="btn btn-outline-success">
                                                <i class="bi bi-people me-2"></i>Başvuruları Gör
                                            </a>
                                        </div>
                                    <?php else: ?>
                                        <div class="d-grid">
                                            <a href="send-message.php?to=<?php echo $job['user_id']; ?>&job_id=<?php echo $job['id']; ?>" class="btn btn-primary btn-lg">
                                                <i class="bi bi-chat-dots me-2"></i>İlan Sahibine Mesaj
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                <?php endif; ?>
                            <?php else: ?>
                                <div class="d-grid gap-2">
                                    <a href="auth/register.php?type=caregiver" class="btn btn-primary btn-lg">
                                        <i class="bi bi-person-plus me-2"></i>Bakıcı Olarak Üye Ol
                                    </a>
                                    <a href="auth/register.php?type=family" class="btn btn-outline-primary">
                                        <i class="bi bi-house me-2"></i>Aile Olarak Üye Ol
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Benzer İlanlar -->
        <div class="mt-5 mb-5">
            <h4 class="fw-bold mb-4">Benzer İş İlanları</h4>
            <div class="row g-4">
                <?php
                try {
                    $similar_sql = "SELECT * FROM job_listings 
                                   WHERE id != ? AND location_city = ? AND status = 'active' 
                                   ORDER BY created_at DESC LIMIT 3";
                    $similar_stmt = $db->prepare($similar_sql);
                    $similar_stmt->execute([$job['id'], $job['location_city']]);
                    $similar_jobs = $similar_stmt->fetchAll();
                    
                    foreach ($similar_jobs as $similar_job):
                ?>
                <div class="col-md-4">
                    <div class="card h-100">
                        <div class="card-body">
                            <h6 class="card-title"><?php echo escape($similar_job['title']); ?></h6>
                            <p class="text-muted small">
                                <i class="bi bi-geo-alt me-1"></i><?php echo escape($similar_job['location_city']); ?>
                            </p>
                            <p class="text-muted small"><?php echo escape(substr($similar_job['description'], 0, 100)); ?>...</p>
                            <a href="job-detail.php?slug=<?php echo $similar_job['slug']; ?>" class="btn btn-outline-primary btn-sm">
                                Detayları Gör
                            </a>
                        </div>
                    </div>
                </div>
                <?php 
                    endforeach;
                } catch (PDOException $e) {
                    // Hata durumunda sessizce devam et
                }
                ?>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
