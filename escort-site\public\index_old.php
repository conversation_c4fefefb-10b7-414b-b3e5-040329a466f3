<?php
// EscortNews Style - Escort İlan Sitesi
// PHP 7.1 Uyumlu Versiyon

session_start();

// Veritabanı bağlantısı
function getDbConnection() {
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=escort_site;charset=utf8mb4", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch (PDOException $e) {
        die("Veritabanı bağlantı hatası: " . $e->getMessage());
    }
}

// Güvenlik fonksiyonu
function sanitize($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

// URL slug oluştur
function createSlug($text) {
    $text = strtolower($text);
    $text = preg_replace('/[^a-z0-9\s-]/', '', $text);
    $text = preg_replace('/[\s-]+/', '-', $text);
    return trim($text, '-');
}

// Site ayarlarını al
function getSettings() {
    $pdo = getDbConnection();
    $stmt = $pdo->query("SELECT `key`, `value` FROM settings");
    $settings = array();
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $settings[$row['key']] = $row['value'];
    }
    return $settings;
}

// Kategorileri al
function getCategories() {
    $pdo = getDbConnection();
    $stmt = $pdo->query("SELECT * FROM categories WHERE status = 1 ORDER BY sort_order ASC");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Şehirleri al
function getCities() {
    $pdo = getDbConnection();
    $stmt = $pdo->query("SELECT * FROM cities WHERE status = 1 ORDER BY sort_order ASC");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Son ilanları al
function getLatestAds($limit = 12) {
    $pdo = getDbConnection();
    $limit = (int)$limit; // Integer'a çevir
    $stmt = $pdo->query("
        SELECT a.*, c.name as category_name, c.color as category_color,
               ci.name as city_name, u.username,
               (SELECT filename FROM ad_photos WHERE ad_id = a.id AND is_primary = 1 LIMIT 1) as primary_photo
        FROM ads a
        LEFT JOIN categories c ON a.category_id = c.id
        LEFT JOIN cities ci ON a.city_id = ci.id
        LEFT JOIN users u ON a.user_id = u.id
        WHERE a.status = 'active' AND (a.expires_at IS NULL OR a.expires_at > NOW())
        ORDER BY a.featured DESC, a.created_at DESC
        LIMIT $limit
    ");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Öne çıkan ilanları al
function getFeaturedAds($limit = 8) {
    $pdo = getDbConnection();
    $limit = (int)$limit; // Integer'a çevir
    $stmt = $pdo->query("
        SELECT a.*, c.name as category_name, c.color as category_color,
               ci.name as city_name, u.username,
               (SELECT filename FROM ad_photos WHERE ad_id = a.id AND is_primary = 1 LIMIT 1) as primary_photo
        FROM ads a
        LEFT JOIN categories c ON a.category_id = c.id
        LEFT JOIN cities ci ON a.city_id = ci.id
        LEFT JOIN users u ON a.user_id = u.id
        WHERE a.status = 'active' AND a.featured = 1 AND (a.expires_at IS NULL OR a.expires_at > NOW())
        ORDER BY a.priority DESC, a.created_at DESC
        LIMIT $limit
    ");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// İstatistikleri al
function getStats() {
    $pdo = getDbConnection();

    $stmt = $pdo->query("SELECT COUNT(*) as count FROM categories WHERE status = 1");
    $categoryCount = $stmt->fetch()['count'];

    $stmt = $pdo->query("SELECT COUNT(*) as count FROM cities WHERE status = 1");
    $cityCount = $stmt->fetch()['count'];

    $stmt = $pdo->query("SELECT COUNT(*) as count FROM ads WHERE status = 'active' AND (expires_at IS NULL OR expires_at > NOW())");
    $adsCount = $stmt->fetch()['count'];

    $stmt = $pdo->query("SELECT COUNT(*) as count FROM ads WHERE status = 'active' AND DATE(created_at) = CURDATE()");
    $todayCount = $stmt->fetch()['count'];

    return array(
        'total_categories' => $categoryCount,
        'total_cities' => $cityCount,
        'total_ads' => $adsCount,
        'today_ads' => $todayCount
    );
}

// Verileri al
$settings = getSettings();
$categories = getCategories();
$cities = getCities();
$stats = getStats();
$latestAds = getLatestAds(12);
$featuredAds = getFeaturedAds(6);

$siteName = isset($settings['site_name']) ? $settings['site_name'] : 'EscortNews - Escort İlan Sitesi';
$siteDescription = isset($settings['site_description']) ? $settings['site_description'] : 'Türkiye\'nin en güvenilir escort ilan ve haber sitesi';

// Arama işlemi
$searchResults = array();
if (isset($_GET['search']) && !empty($_GET['search'])) {
    $searchTerm = sanitize($_GET['search']);
    $pdo = getDbConnection();
    $stmt = $pdo->prepare("
        SELECT a.*, c.name as category_name, ci.name as city_name,
               (SELECT filename FROM ad_photos WHERE ad_id = a.id AND is_primary = 1 LIMIT 1) as primary_photo
        FROM ads a
        LEFT JOIN categories c ON a.category_id = c.id
        LEFT JOIN cities ci ON a.city_id = ci.id
        WHERE a.status = 'active' AND (a.title LIKE ? OR a.description LIKE ?)
        ORDER BY a.featured DESC, a.created_at DESC
        LIMIT 20
    ");
    $searchTerm = '%' . $searchTerm . '%';
    $stmt->execute(array($searchTerm, $searchTerm));
    $searchResults = $stmt->fetchAll(PDO::FETCH_ASSOC);
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($siteName); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($siteDescription); ?>">
    <meta name="keywords" content="escort, ilan, haber, türkiye, güvenilir, premium">
    <meta name="robots" content="index, follow">

    <!-- Open Graph -->
    <meta property="og:title" content="<?php echo htmlspecialchars($siteName); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars($siteDescription); ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo 'http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?>">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #DC2626;
            --secondary-color: #7C3AED;
            --accent-color: #F59E0B;
            --dark-color: #111827;
            --light-color: #F9FAFB;
            --gray-100: #F3F4F6;
            --gray-200: #E5E7EB;
            --gray-300: #D1D5DB;
            --gray-600: #4B5563;
            --gray-800: #1F2937;
            --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            --gradient-accent: linear-gradient(135deg, var(--accent-color), var(--primary-color));
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: var(--gray-800);
            background-color: var(--light-color);
            font-size: 16px;
        }

        .font-display {
            font-family: 'Playfair Display', serif;
        }
        
        .navbar {
            background: white !important;
            box-shadow: var(--shadow-md);
            padding: 1rem 0;
            border-bottom: 1px solid var(--gray-200);
        }

        .navbar-brand {
            font-weight: 800;
            font-size: 1.75rem;
            color: var(--primary-color) !important;
            font-family: 'Playfair Display', serif;
        }

        .navbar-nav .nav-link {
            color: var(--gray-600) !important;
            font-weight: 500;
            margin: 0 0.75rem;
            padding: 0.5rem 1rem !important;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            color: var(--primary-color) !important;
            background-color: var(--gray-100);
            transform: translateY(-1px);
        }

        .navbar-nav .nav-link.active {
            color: var(--primary-color) !important;
            background-color: rgba(220, 38, 38, 0.1);
            font-weight: 600;
        }
        
        .hero-section {
            background: var(--gradient-primary);
            color: white;
            padding: 5rem 0;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="0,0 1000,0 1000,100 0,80"/></svg>');
            background-size: cover;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 1.5rem;
            font-family: 'Playfair Display', serif;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 1.25rem;
            margin-bottom: 3rem;
            opacity: 0.95;
            font-weight: 400;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .search-box {
            background: white;
            border-radius: 15px;
            padding: 1rem;
            box-shadow: var(--shadow-xl);
            max-width: 600px;
            margin: 0 auto;
        }

        .search-input {
            border: none;
            outline: none;
            font-size: 1.1rem;
            padding: 0.75rem 1rem;
        }

        .search-btn {
            background: var(--gradient-primary);
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }
        
        .btn-primary {
            background: var(--gradient-primary);
            border: none;
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            color: white;
        }

        .btn-outline-primary {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
            background: transparent;
        }

        .btn-outline-primary:hover {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .card {
            border: none;
            border-radius: 16px;
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
            overflow: hidden;
            background: white;
        }

        .card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-xl);
        }

        .card-img-top {
            height: 200px;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .card:hover .card-img-top {
            transform: scale(1.05);
        }

        .ad-card {
            position: relative;
            margin-bottom: 2rem;
        }

        .ad-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: var(--gradient-accent);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            z-index: 3;
        }

        .featured-badge {
            background: var(--gradient-primary);
        }
        
        .stats-section {
            background: white;
            padding: 3rem 0;
        }
        
        .stat-card {
            text-align: center;
            padding: 2rem;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .stat-label {
            color: #666;
            font-weight: 500;
        }
        
        .footer {
            background: var(--dark-color);
            color: white;
            padding: 3rem 0 1rem;
            margin-top: 4rem;
        }
        
        .footer h5 {
            color: var(--primary-color);
            margin-bottom: 1rem;
        }
        
        .footer a {
            color: rgba(255,255,255,0.7);
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .footer a:hover {
            color: var(--primary-color);
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
                line-height: 1.1;
            }

            .hero-subtitle {
                font-size: 1rem;
                margin-bottom: 2rem;
            }

            .search-box {
                padding: 0.75rem;
            }

            .search-input {
                font-size: 1rem;
                padding: 0.5rem 0.75rem;
            }

            .search-btn {
                padding: 0.5rem 1rem;
                font-size: 0.9rem;
            }

            .navbar-brand {
                font-size: 1.5rem;
            }

            .stat-number {
                font-size: 2rem;
            }

            .card-title {
                font-size: 1.1rem;
            }

            .ad-card {
                margin-bottom: 1.5rem;
            }
        }

        @media (max-width: 576px) {
            .hero-title {
                font-size: 2rem;
            }

            .hero-section {
                padding: 3rem 0;
            }

            .search-box .d-flex {
                flex-direction: column;
            }

            .search-btn {
                margin-top: 0.5rem !important;
                margin-left: 0 !important;
            }

            .stat-card {
                padding: 1rem;
            }

            .stat-number {
                font-size: 1.75rem;
            }
        }

        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(220, 38, 38, 0.3);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Utility Classes */
        .text-gradient {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .bg-gradient-primary {
            background: var(--gradient-primary);
        }

        .bg-gradient-accent {
            background: var(--gradient-accent);
        }

        /* Hover Effects */
        .hover-lift {
            transition: transform 0.3s ease;
        }

        .hover-lift:hover {
            transform: translateY(-5px);
        }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--gray-100);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-color);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-newspaper me-2"></i>
                EscortNews
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">
                            <i class="fas fa-home me-1"></i> Ana Sayfa
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#ads">
                            <i class="fas fa-list me-1"></i> İlanlar
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#categories">
                            <i class="fas fa-tags me-1"></i> Kategoriler
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#news">
                            <i class="fas fa-newspaper me-1"></i> Haberler
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#premium">
                            <i class="fas fa-crown me-1"></i> Premium
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="#login">
                            <i class="fas fa-sign-in-alt me-1"></i> Giriş
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#register">
                            <i class="fas fa-user-plus me-1"></i> Kayıt
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="btn btn-primary ms-2" href="#post-ad">
                            <i class="fas fa-plus me-1"></i> İlan Ver
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="hero-content text-center">
                <h1 class="hero-title">
                    Türkiye'nin En Güvenilir<br>
                    <span style="color: var(--accent-color);">Escort Haber</span> Platformu
                </h1>
                <p class="hero-subtitle">
                    Güncel haberler, güvenilir ilanlar ve premium hizmetler tek platformda.
                    Kaliteli içerik ve güvenli deneyim için doğru adrestesiniz.
                </p>

                <div class="search-box">
                    <form method="GET" action="index.php" class="d-flex">
                        <input type="text" name="search" class="form-control search-input flex-grow-1"
                               placeholder="Şehir, kategori veya anahtar kelime ara..."
                               value="<?php echo isset($_GET['search']) ? htmlspecialchars($_GET['search']) : ''; ?>">
                        <button type="submit" class="btn search-btn ms-2">
                            <i class="fas fa-search me-1"></i> Ara
                        </button>
                    </form>
                </div>

                <?php if (!empty($searchResults)): ?>
                <div class="mt-4">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        "<?php echo htmlspecialchars($_GET['search']); ?>" için <?php echo count($searchResults); ?> sonuç bulundu.
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Search Results Section -->
    <?php if (!empty($searchResults)): ?>
    <section class="py-5 bg-light">
        <div class="container">
            <h2 class="mb-4">
                <i class="fas fa-search me-2 text-primary"></i>
                Arama Sonuçları
            </h2>
            <div class="row">
                <?php foreach ($searchResults as $ad): ?>
                <div class="col-md-4 mb-4">
                    <div class="card ad-card h-100">
                        <?php if ($ad['featured']): ?>
                        <div class="ad-badge featured-badge">
                            <i class="fas fa-star me-1"></i> Öne Çıkan
                        </div>
                        <?php endif; ?>

                        <?php if ($ad['primary_photo']): ?>
                        <img src="uploads/ads/<?php echo htmlspecialchars($ad['primary_photo']); ?>"
                             class="card-img-top" alt="<?php echo htmlspecialchars($ad['title']); ?>">
                        <?php else: ?>
                        <div class="card-img-top bg-gradient-primary d-flex align-items-center justify-content-center">
                            <i class="fas fa-image fa-3x text-white opacity-50"></i>
                        </div>
                        <?php endif; ?>

                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <span class="badge" style="background-color: <?php echo $ad['category_color'] ?? '#DC2626'; ?>;">
                                    <?php echo htmlspecialchars($ad['category_name']); ?>
                                </span>
                                <small class="text-muted">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    <?php echo htmlspecialchars($ad['city_name']); ?>
                                </small>
                            </div>
                            <h5 class="card-title"><?php echo htmlspecialchars($ad['title']); ?></h5>
                            <p class="card-text text-muted">
                                <?php echo htmlspecialchars(substr($ad['description'], 0, 100)) . '...'; ?>
                            </p>
                            <div class="d-flex justify-content-between align-items-center">
                                <?php if ($ad['price_per_hour']): ?>
                                <span class="fw-bold text-primary">
                                    ₺<?php echo number_format($ad['price_per_hour'], 0, ',', '.'); ?>/saat
                                </span>
                                <?php endif; ?>
                                <a href="#" class="btn btn-outline-primary btn-sm">
                                    Detay Gör
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Stats Section -->
    <section class="stats-section">
        <div class="container">
            <div class="row">
                <div class="col-md-3 col-6">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['total_ads']; ?></div>
                        <div class="stat-label">Aktif İlan</div>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['total_cities']; ?></div>
                        <div class="stat-label">Şehir</div>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['total_categories']; ?></div>
                        <div class="stat-label">Kategori</div>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['today_ads']; ?></div>
                        <div class="stat-label">Bugünkü İlan</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Ads Section -->
    <?php if (!empty($featuredAds)): ?>
    <section class="py-5" id="featured">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center mb-5">
                <h2 class="font-display">
                    <i class="fas fa-star me-2 text-warning"></i>
                    Öne Çıkan İlanlar
                </h2>
                <a href="#all-ads" class="btn btn-outline-primary">
                    Tümünü Gör <i class="fas fa-arrow-right ms-1"></i>
                </a>
            </div>
            <div class="row">
                <?php foreach ($featuredAds as $ad): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card ad-card h-100">
                        <div class="ad-badge featured-badge">
                            <i class="fas fa-crown me-1"></i> Premium
                        </div>

                        <?php if ($ad['primary_photo']): ?>
                        <img src="uploads/ads/<?php echo htmlspecialchars($ad['primary_photo']); ?>"
                             class="card-img-top" alt="<?php echo htmlspecialchars($ad['title']); ?>">
                        <?php else: ?>
                        <div class="card-img-top bg-gradient-primary d-flex align-items-center justify-content-center">
                            <i class="fas fa-image fa-3x text-white opacity-50"></i>
                        </div>
                        <?php endif; ?>

                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <span class="badge" style="background-color: <?php echo $ad['category_color'] ?? '#DC2626'; ?>;">
                                    <?php echo htmlspecialchars($ad['category_name']); ?>
                                </span>
                                <small class="text-muted">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    <?php echo htmlspecialchars($ad['city_name']); ?>
                                </small>
                            </div>
                            <h5 class="card-title"><?php echo htmlspecialchars($ad['title']); ?></h5>
                            <p class="card-text text-muted">
                                <?php echo htmlspecialchars(substr($ad['description'], 0, 100)) . '...'; ?>
                            </p>
                            <div class="d-flex justify-content-between align-items-center">
                                <?php if ($ad['price_per_hour']): ?>
                                <span class="fw-bold text-primary">
                                    ₺<?php echo number_format($ad['price_per_hour'], 0, ',', '.'); ?>/saat
                                </span>
                                <?php endif; ?>
                                <a href="#" class="btn btn-primary btn-sm">
                                    <i class="fas fa-eye me-1"></i> Detay
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Categories Section -->
    <section class="py-5 bg-light" id="categories">
        <div class="container">
            <h2 class="text-center mb-5 font-display">
                <i class="fas fa-tags me-2" style="color: var(--primary-color);"></i>
                Kategoriler
            </h2>
            <div class="row">
                <?php if (!empty($categories)): ?>
                    <?php foreach ($categories as $category): ?>
                        <div class="col-md-3 col-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="<?php echo htmlspecialchars($category['icon']); ?> fa-3x mb-3" style="color: <?php echo htmlspecialchars($category['color']); ?>;"></i>
                                    <h5 class="card-title"><?php echo htmlspecialchars($category['name']); ?></h5>
                                    <p class="card-text text-muted"><?php echo htmlspecialchars($category['description']); ?></p>
                                    <a href="#" class="btn btn-primary btn-sm">
                                        İlanları Gör
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="col-12 text-center">
                        <p class="text-muted">Henüz kategori bulunmuyor.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Latest Ads Section -->
    <?php if (!empty($latestAds)): ?>
    <section class="py-5" id="latest">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center mb-5">
                <h2 class="font-display">
                    <i class="fas fa-clock me-2 text-info"></i>
                    Son Eklenen İlanlar
                </h2>
                <a href="#all-ads" class="btn btn-outline-primary">
                    Tümünü Gör <i class="fas fa-arrow-right ms-1"></i>
                </a>
            </div>
            <div class="row">
                <?php foreach (array_slice($latestAds, 0, 6) as $ad): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card ad-card h-100">
                        <?php if ($ad['featured']): ?>
                        <div class="ad-badge">
                            <i class="fas fa-star me-1"></i> Öne Çıkan
                        </div>
                        <?php endif; ?>

                        <?php if ($ad['primary_photo']): ?>
                        <img src="uploads/ads/<?php echo htmlspecialchars($ad['primary_photo']); ?>"
                             class="card-img-top" alt="<?php echo htmlspecialchars($ad['title']); ?>">
                        <?php else: ?>
                        <div class="card-img-top bg-gradient-primary d-flex align-items-center justify-content-center">
                            <i class="fas fa-image fa-3x text-white opacity-50"></i>
                        </div>
                        <?php endif; ?>

                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <span class="badge" style="background-color: <?php echo $ad['category_color'] ?? '#DC2626'; ?>;">
                                    <?php echo htmlspecialchars($ad['category_name']); ?>
                                </span>
                                <small class="text-muted">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    <?php echo htmlspecialchars($ad['city_name']); ?>
                                </small>
                            </div>
                            <h5 class="card-title"><?php echo htmlspecialchars($ad['title']); ?></h5>
                            <p class="card-text text-muted">
                                <?php echo htmlspecialchars(substr($ad['description'], 0, 100)) . '...'; ?>
                            </p>
                            <div class="d-flex justify-content-between align-items-center">
                                <?php if ($ad['price_per_hour']): ?>
                                <span class="fw-bold text-primary">
                                    ₺<?php echo number_format($ad['price_per_hour'], 0, ',', '.'); ?>/saat
                                </span>
                                <?php endif; ?>
                                <a href="#" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye me-1"></i> Detay
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Cities Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <h2 class="text-center mb-5 font-display">
                <i class="fas fa-map-marker-alt me-2" style="color: var(--primary-color);"></i>
                Popüler Şehirler
            </h2>
            <div class="row">
                <?php if (!empty($cities)): ?>
                    <?php foreach (array_slice($cities, 0, 8) as $city): ?>
                        <div class="col-md-3 col-6 mb-3">
                            <a href="#" class="btn btn-outline-primary w-100">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                <?php echo htmlspecialchars($city['name']); ?>
                            </a>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="col-12 text-center">
                        <p class="text-muted">Henüz şehir bulunmuyor.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Newsletter Section -->
    <section class="py-5" style="background: var(--gradient-primary);">
        <div class="container">
            <div class="row align-items-center text-white">
                <div class="col-md-8">
                    <h3 class="font-display mb-2">Güncel Haberler ve İlanlar</h3>
                    <p class="mb-0">En son haberler ve özel ilanlar için bültenimize abone olun.</p>
                </div>
                <div class="col-md-4">
                    <div class="input-group">
                        <input type="email" class="form-control" placeholder="E-posta adresiniz">
                        <button class="btn btn-light" type="button">
                            <i class="fas fa-paper-plane me-1"></i> Abone Ol
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <h5 class="font-display">
                        <i class="fas fa-newspaper me-2"></i>EscortNews
                    </h5>
                    <p class="text-muted">
                        Türkiye'nin en güvenilir escort haber ve ilan platformu.
                        Kaliteli içerik ve güvenli deneyim için doğru adrestesiniz.
                    </p>
                    <div class="social-links">
                        <a href="#" class="me-3"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="me-3"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="me-3"><i class="fab fa-telegram"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5>Hızlı Linkler</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.php">Ana Sayfa</a></li>
                        <li><a href="#ads">İlanlar</a></li>
                        <li><a href="#categories">Kategoriler</a></li>
                        <li><a href="#news">Haberler</a></li>
                        <li><a href="#premium">Premium</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5>Kategoriler</h5>
                    <ul class="list-unstyled">
                        <?php foreach (array_slice($categories, 0, 4) as $category): ?>
                        <li><a href="#category-<?php echo $category['slug']; ?>"><?php echo htmlspecialchars($category['name']); ?></a></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5>Şehirler</h5>
                    <ul class="list-unstyled">
                        <?php foreach (array_slice($cities, 0, 4) as $city): ?>
                        <li><a href="#city-<?php echo $city['slug']; ?>"><?php echo htmlspecialchars($city['name']); ?></a></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5>Yasal</h5>
                    <ul class="list-unstyled">
                        <li><a href="#privacy">Gizlilik Politikası</a></li>
                        <li><a href="#terms">Kullanım Şartları</a></li>
                        <li><a href="#contact">İletişim</a></li>
                        <li><a href="#about">Hakkımızda</a></li>
                    </ul>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">
                        &copy; <?php echo date('Y'); ?> EscortNews. Tüm hakları saklıdır.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0 text-muted">
                        <i class="fas fa-code text-primary"></i>
                        PHP <?php echo phpversion(); ?> ile geliştirilmiştir
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <script>
        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Search form enhancement
        const searchForm = document.querySelector('form[method="GET"]');
        if (searchForm) {
            searchForm.addEventListener('submit', function(e) {
                const searchInput = this.querySelector('input[name="search"]');
                if (searchInput.value.trim() === '') {
                    e.preventDefault();
                    searchInput.focus();
                    searchInput.placeholder = 'Lütfen arama terimi girin...';
                }
            });
        }

        // Card hover effects
        document.querySelectorAll('.ad-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });

        // Newsletter subscription
        const newsletterBtn = document.querySelector('.btn:has(.fa-paper-plane)');
        if (newsletterBtn) {
            newsletterBtn.addEventListener('click', function() {
                const emailInput = this.parentElement.querySelector('input[type="email"]');
                const email = emailInput.value.trim();

                if (email === '') {
                    alert('Lütfen e-posta adresinizi girin.');
                    emailInput.focus();
                    return;
                }

                if (!isValidEmail(email)) {
                    alert('Lütfen geçerli bir e-posta adresi girin.');
                    emailInput.focus();
                    return;
                }

                // Simulate subscription
                this.innerHTML = '<i class="fas fa-check me-1"></i> Abone Olundu!';
                this.classList.remove('btn-light');
                this.classList.add('btn-success');
                emailInput.value = '';

                setTimeout(() => {
                    this.innerHTML = '<i class="fas fa-paper-plane me-1"></i> Abone Ol';
                    this.classList.remove('btn-success');
                    this.classList.add('btn-light');
                }, 3000);
            });
        }

        // Email validation
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        // Lazy loading for images
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    </script>
</body>
</html>
