<?php

/*
 * This file is part of Twig.
 *
 * (c) 2015 Fabien Po<PERSON>cier
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

/**
 * Implements a no-cache strategy.
 *
 * <AUTHOR> <<EMAIL>>
 */
class Twig_Cache_Null implements Twig_CacheInterface
{
    /**
     * {@inheritdoc}
     */
    public function generateKey($name, $className)
    {
        return '';
    }

    /**
     * {@inheritdoc}
     */
    public function write($key, $content)
    {
    }

    /**
     * {@inheritdoc}
     */
    public function load($key)
    {
    }

    /**
     * {@inheritdoc}
     */
    public function getTimestamp($key)
    {
        return 0;
    }
}
