<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$success_count = 0;

try {
    // Demo aile kullanıcıları
    $demo_families = [
        ['<EMAIL>', '<PERSON><PERSON> Yılmaz', '0532 123 45 67', 'İstanbul', 'Kadıköy'],
        ['<EMAIL>', '<PERSON><PERSON>', '0533 234 56 78', 'Ankara', 'Çankaya'],
        ['<EMAIL>', '<PERSON><PERSON><PERSON> Demir', '0534 345 67 89', 'İzmir', 'Bornova'],
        ['<EMAIL>', 'Zeynep Özkan', '0535 456 78 90', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'],
        ['<EMAIL>', '<PERSON> Çelik', '0536 567 89 01', '<PERSON><PERSON><PERSON>', '<PERSON>rat<PERSON>ş<PERSON>']
    ];

    foreach ($demo_families as $family) {
        // Kullanıcı zaten var mı kontrol et
        $check_sql = "SELECT id FROM users WHERE email = ?";
        $check_stmt = $db->prepare($check_sql);
        $check_stmt->execute([$family[0]]);
        
        if (!$check_stmt->fetch()) {
            $password = password_hash('demo123', PASSWORD_DEFAULT);
            $sql = "INSERT INTO users (user_type, email, password, full_name, phone, city, district, is_verified, is_active)
                    VALUES ('family', ?, ?, ?, ?, ?, ?, 1, 1)";
            $stmt = $db->prepare($sql);
            $stmt->execute([$family[0], $password, $family[1], $family[2], $family[3], $family[4]]);
            
            $family_id = $db->lastInsertId();
            
            // Aile profili oluştur
            $profile_sql = "INSERT INTO family_profiles (user_id, family_size, children_count, children_ages, home_type, additional_info)
                           VALUES (?, ?, ?, ?, ?, ?)";
            $profile_stmt = $db->prepare($profile_sql);
            $profile_stmt->execute([
                $family_id,
                rand(2, 5),
                rand(1, 3),
                '3,7,12',
                'apartment',
                'Güvenilir ve deneyimli bakıcı arıyoruz.'
            ]);
            
            $success_count++;
        }
    }

    // Demo bakıcı kullanıcıları
    $demo_caregivers = [
        ['<EMAIL>', 'Fatma Yılmaz', '0537 678 90 12', 'İstanbul', 'Beşiktaş', '1985-03-15', 'female', 8, 'Lise', 'Türkçe, İngilizce', 'Çocuk bakımı, Ev temizliği', 25.00, 200.00, 4500.00],
        ['<EMAIL>', 'Ayşe Kara', '0538 789 01 23', 'Ankara', 'Kızılay', '1978-07-22', 'female', 12, 'Ön Lisans', 'Türkçe', 'Yaşlı bakımı, Hasta bakımı', 30.00, 250.00, 5500.00],
        ['<EMAIL>', 'Merve Öztürk', '0539 890 12 34', 'İzmir', 'Alsancak', '1990-11-08', 'female', 5, 'Lisans', 'Türkçe, İngilizce, Almanca', 'Çocuk bakımı, Ev yardımcısı', 28.00, 220.00, 4800.00],
        ['<EMAIL>', 'Hatice Yıldız', '0540 901 23 45', 'Bursa', 'Osmangazi', '1982-05-30', 'female', 10, 'Lise', 'Türkçe', 'Yaşlı bakımı, Ev yardımcısı', 22.00, 180.00, 4000.00],
        ['<EMAIL>', 'Emine Şahin', '0541 012 34 56', 'Antalya', 'Kepez', '1987-09-12', 'female', 6, 'Lise', 'Türkçe, İngilizce', 'Çocuk bakımı, Evcil hayvan bakımı', 24.00, 190.00, 4200.00]
    ];

    foreach ($demo_caregivers as $caregiver) {
        // Kullanıcı zaten var mı kontrol et
        $check_sql = "SELECT id FROM users WHERE email = ?";
        $check_stmt = $db->prepare($check_sql);
        $check_stmt->execute([$caregiver[0]]);
        
        if (!$check_stmt->fetch()) {
            $password = password_hash('demo123', PASSWORD_DEFAULT);
            $sql = "INSERT INTO users (user_type, email, password, full_name, phone, city, district, is_verified, is_active)
                    VALUES ('caregiver', ?, ?, ?, ?, ?, ?, 1, 1)";
            $stmt = $db->prepare($sql);
            $stmt->execute([$caregiver[0], $password, $caregiver[1], $caregiver[2], $caregiver[3], $caregiver[4]]);

            $caregiver_id = $db->lastInsertId();

            // Bakıcı profili oluştur
            $profile_sql = "INSERT INTO caregiver_profiles (user_id, birth_date, gender, experience_years, education_level, languages, specializations, hourly_rate, daily_rate, monthly_rate, bio, rating, total_reviews, total_jobs)
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $profile_stmt = $db->prepare($profile_sql);
            $rating = 3.5 + (rand(0, 15) / 10); // 3.5 - 5.0 arası rating
            $reviews = rand(5, 25);
            $jobs = rand(10, 50);
            $bio = "Deneyimli ve güvenilir bakıcıyım. Çocukları ve yaşlıları çok seviyorum. Referanslarım mevcuttur.";
            
            $profile_stmt->execute([
                $caregiver_id, $caregiver[5], $caregiver[6], $caregiver[7], $caregiver[8],
                $caregiver[9], $caregiver[10], $caregiver[11], $caregiver[12], $caregiver[13],
                $bio, $rating, $reviews, $jobs
            ]);
            
            $success_count++;
        }
    }

    // Demo iş ilanları oluştur
    $family_ids = $db->query("SELECT id FROM users WHERE user_type = 'family' ORDER BY id LIMIT 5")->fetchAll(PDO::FETCH_COLUMN);
    $category_ids = $db->query("SELECT id FROM categories ORDER BY id LIMIT 6")->fetchAll(PDO::FETCH_COLUMN);

    if (!empty($family_ids) && !empty($category_ids)) {
        $demo_jobs = [
            ['Deneyimli Çocuk Bakıcısı Aranıyor', 'child_care', 'live_out', 'İstanbul', 'Kadıköy', 'Kadıköy merkezde oturan ailemiz için 3 yaşındaki kızımıza bakacak deneyimli, güvenilir ve sevecen bir bakıcı arıyoruz.', 3000, 4000, 'monthly', '2024-02-01'],
            ['Yaşlı Bakım Elemanı - Yatılı', 'elderly_care', 'live_in', 'Ankara', 'Çankaya', '85 yaşındaki annem için yatılı bakım elemanı arıyoruz. Alzheimer hastası olduğu için deneyimli birini tercih ediyoruz.', 5000, 6000, 'monthly', '2024-02-05'],
            ['Hasta Bakım Elemanı - Acil', 'patient_care', 'live_in', 'İzmir', 'Bornova', 'Ameliyat sonrası iyileşme sürecindeki babam için hasta bakım elemanı gerekiyor. Medikal bilgisi olan tercih edilir.', 4000, 5000, 'monthly', '2024-01-28'],
            ['İkiz Bebek Bakıcısı', 'child_care', 'daily', 'Bursa', 'Nilüfer', '6 aylık ikiz bebeklerimiz için günlük bakıcı arıyoruz. Bebek bakımında deneyimli olması şart.', 200, 250, 'daily', '2024-02-10'],
            ['Ev Yardımcısı ve Yaşlı Bakımı', 'house_help', 'live_out', 'Antalya', 'Muratpaşa', 'Ev temizliği ve 78 yaşındaki babama bakım için yardımcı arıyoruz. Hafta içi gündüz çalışma.', 25, 30, 'hourly', '2024-02-15']
        ];

        foreach ($demo_jobs as $index => $job) {
            $family_id = $family_ids[$index % count($family_ids)];
            $category_id = $category_ids[$index % count($category_ids)];
            
            $slug = generateUniqueSlug('job_listings', $job[0]);
            
            $sql = "INSERT INTO job_listings (user_id, category_id, title, slug, job_type, care_type, location_city, location_district, description, budget_min, budget_max, budget_type, start_date, status, expires_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', DATE_ADD(NOW(), INTERVAL 30 DAY))";
            $stmt = $db->prepare($sql);
            $stmt->execute([
                $family_id, $category_id, $job[0], $slug, $job[1], $job[2], $job[3], $job[4], 
                $job[5], $job[6], $job[7], $job[8], $job[9]
            ]);
            
            $success_count++;
        }
    }

    // Demo başvurular oluştur
    $job_ids = $db->query("SELECT id FROM job_listings ORDER BY id LIMIT 5")->fetchAll(PDO::FETCH_COLUMN);
    $caregiver_ids = $db->query("SELECT id FROM users WHERE user_type = 'caregiver' ORDER BY id LIMIT 5")->fetchAll(PDO::FETCH_COLUMN);

    if (!empty($job_ids) && !empty($caregiver_ids)) {
        foreach ($job_ids as $job_id) {
            $application_count = rand(2, 4);
            $selected_caregivers = array_rand($caregiver_ids, min($application_count, count($caregiver_ids)));
            if (!is_array($selected_caregivers)) $selected_caregivers = [$selected_caregivers];

            foreach ($selected_caregivers as $index) {
                $caregiver_id = $caregiver_ids[$index];
                $messages = [
                    'Merhaba, ilanınızı gördüm ve çok ilgimi çekti. Bu konuda deneyimliyim ve referanslarım mevcut.',
                    'İlanınız için başvuru yapmak istiyorum. Uzun yıllardır bu alanda çalışıyorum.',
                    'Merhaba, ilanınıza uygun olduğumu düşünüyorum. Detayları konuşmak isterim.',
                    'İlanınız için başvuruyorum. Deneyimim ve referanslarım hakkında bilgi verebilirim.',
                    'Merhaba, bu pozisyon için çok uygun olduğumu düşünüyorum. Görüşmek isterim.'
                ];

                $sql = "INSERT INTO job_applications (job_id, caregiver_id, cover_letter, proposed_rate, status)
                        VALUES (?, ?, ?, ?, 'pending')";
                $stmt = $db->prepare($sql);
                $proposed_rate = rand(20, 50) + (rand(0, 9) / 10);
                $stmt->execute([$job_id, $caregiver_id, $messages[array_rand($messages)], $proposed_rate]);
            }
        }
        $success_count += count($job_ids);
    }

    // Başarı mesajı göster
    if ($success_count > 0) {
        echo "<!DOCTYPE html>
<html lang='tr'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Demo Veriler Yüklendi</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css' rel='stylesheet'>
</head>
<body class='bg-light'>
    <div class='container mt-5'>
        <div class='row justify-content-center'>
            <div class='col-md-8'>
                <div class='card shadow'>
                    <div class='card-body text-center p-5'>
                        <div class='text-success mb-4'>
                            <i class='bi bi-check-circle' style='font-size: 4rem;'></i>
                        </div>
                        <h2 class='text-success mb-3'>Demo Veriler Başarıyla Yüklendi!</h2>
                        <p class='lead mb-4'>Toplam {$success_count} demo veri oluşturuldu.</p>

                        <div class='row g-3 mb-4'>
                            <div class='col-md-4'>
                                <div class='card bg-primary text-white'>
                                    <div class='card-body'>
                                        <h6>Admin Hesabı</h6>
                                        <small><EMAIL><br>admin123</small>
                                    </div>
                                </div>
                            </div>
                            <div class='col-md-4'>
                                <div class='card bg-success text-white'>
                                    <div class='card-body'>
                                        <h6>Demo Aile</h6>
                                        <small><EMAIL><br>demo123</small>
                                    </div>
                                </div>
                            </div>
                            <div class='col-md-4'>
                                <div class='card bg-info text-white'>
                                    <div class='card-body'>
                                        <h6>Demo Bakıcı</h6>
                                        <small><EMAIL><br>demo123</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class='d-grid gap-2 d-md-flex justify-content-md-center'>
                            <a href='index.php' class='btn btn-primary btn-lg'>
                                <i class='bi bi-house me-2'></i>Ana Sayfaya Git
                            </a>
                            <a href='auth/login.php' class='btn btn-success btn-lg'>
                                <i class='bi bi-box-arrow-in-right me-2'></i>Giriş Yap
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>";
    } else {
        echo "Demo veriler zaten mevcut.";
    }

} catch (PDOException $e) {
    echo "<!DOCTYPE html>
<html lang='tr'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Hata</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css' rel='stylesheet'>
</head>
<body class='bg-light'>
    <div class='container mt-5'>
        <div class='row justify-content-center'>
            <div class='col-md-6'>
                <div class='card shadow'>
                    <div class='card-body text-center p-4'>
                        <div class='text-danger mb-3'>
                            <i class='bi bi-exclamation-triangle' style='font-size: 3rem;'></i>
                        </div>
                        <h4 class='text-danger mb-3'>Veritabanı Hatası</h4>
                        <p class='mb-4'>Demo veriler yüklenirken bir hata oluştu. Lütfen veritabanı bağlantınızı kontrol edin.</p>
                        <a href='config/database.php' class='btn btn-primary'>
                            <i class='bi bi-arrow-clockwise me-2'></i>Tekrar Dene
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>";
    error_log("Demo data yükleme hatası: " . $e->getMessage());
}
?>
