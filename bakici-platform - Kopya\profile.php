<?php
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/session.php';

// Giriş kontrolü
if (!isset($_SESSION['user_id'])) {
    header('Location: auth/login.php');
    exit;
}

$user_id = $_SESSION['user_id'];
$error_message = '';
$success_message = '';

// Kullanıcı bilgilerini getir
try {
    $sql = "SELECT * FROM users WHERE id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$user_id]);
    $user = $stmt->fetch();

    if (!$user) {
        header('Location: auth/logout.php');
        exit;
    }
} catch (PDOException $e) {
    $error_message = 'Kullanıcı bilgileri alınamadı.';
}

// Form gönderildi mi?
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $full_name = trim($_POST['full_name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $current_password = $_POST['current_password'] ?? '';
    $new_password = $_POST['new_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';

    // Validasyon
    if (empty($full_name)) {
        $error_message = 'Ad soyad gereklidir.';
    } elseif (empty($email)) {
        $error_message = 'Email gereklidir.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error_message = 'Geçerli bir email adresi girin.';
    } else {
        try {
            // Email kontrolü (başka kullanıcıda var mı?)
            $email_check_sql = "SELECT id FROM users WHERE email = ? AND id != ?";
            $email_check_stmt = $db->prepare($email_check_sql);
            $email_check_stmt->execute([$email, $user_id]);

            if ($email_check_stmt->fetch()) {
                $error_message = 'Bu email adresi başka bir kullanıcı tarafından kullanılıyor.';
            } else {
                // Şifre değişikliği var mı?
                if (!empty($new_password)) {
                    if (empty($current_password)) {
                        $error_message = 'Mevcut şifrenizi girin.';
                    } elseif (!password_verify($current_password, $user['password'])) {
                        $error_message = 'Mevcut şifre yanlış.';
                    } elseif (strlen($new_password) < 6) {
                        $error_message = 'Yeni şifre en az 6 karakter olmalıdır.';
                    } elseif ($new_password !== $confirm_password) {
                        $error_message = 'Yeni şifreler eşleşmiyor.';
                    } else {
                        // Şifre ile birlikte güncelle
                        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                        $update_sql = "UPDATE users SET full_name = ?, email = ?, phone = ?, password = ?, updated_at = NOW() WHERE id = ?";
                        $update_stmt = $db->prepare($update_sql);
                        $update_stmt->execute([$full_name, $email, $phone, $hashed_password, $user_id]);

                        $success_message = 'Profil bilgileriniz ve şifreniz başarıyla güncellendi!';
                    }
                } else {
                    // Sadece profil bilgilerini güncelle
                    $update_sql = "UPDATE users SET full_name = ?, email = ?, phone = ?, updated_at = NOW() WHERE id = ?";
                    $update_stmt = $db->prepare($update_sql);
                    $update_stmt->execute([$full_name, $email, $phone, $user_id]);

                    $success_message = 'Profil bilgileriniz başarıyla güncellendi!';
                }

                if (empty($error_message)) {
                    // Session'daki bilgileri güncelle
                    $_SESSION['full_name'] = $full_name;
                    $_SESSION['email'] = $email;

                    // Güncel bilgileri tekrar getir
                    $stmt->execute([$user_id]);
                    $user = $stmt->fetch();
                }
            }
        } catch (PDOException $e) {
            $error_message = 'Profil güncellenirken bir hata oluştu.';
        }
    }
}

$page_title = 'Profil Ayarları';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bakıcı Platform</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c5aa0;
        }

        .profile-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="index.php">
                <i class="bi bi-heart-fill me-2"></i>Bakıcı Platform
            </a>

            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.php">Dashboard</a>
                <a class="nav-link" href="messages.php">Mesajlar</a>
                <a class="nav-link" href="auth/logout.php">Çıkış</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Alert Messages -->
        <?php if (isset($_GET['success'])): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="bi bi-check-circle me-2"></i>
                <?php
                $success_messages = [
                    'profile_updated' => 'Profiliniz başarıyla güncellendi.',
                    'password_changed' => 'Şifreniz başarıyla değiştirildi.',
                    'email_updated' => 'E-posta adresiniz güncellendi.',
                    'photo_uploaded' => 'Profil fotoğrafınız yüklendi.',
                    'photo_deleted' => 'Profil fotoğrafınız silindi.'
                ];
                echo $success_messages[$_GET['success']] ?? 'İşlem başarıyla tamamlandı.';
                ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_GET['error'])): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="bi bi-exclamation-triangle me-2"></i>
                <?php
                $error_messages = [
                    'invalid_data' => 'Geçersiz veri gönderildi.',
                    'email_exists' => 'Bu e-posta adresi zaten kullanılıyor.',
                    'current_password_wrong' => 'Mevcut şifreniz yanlış.',
                    'password_mismatch' => 'Şifreler eşleşmiyor.',
                    'upload_error' => 'Dosya yükleme hatası.',
                    'invalid_file_type' => 'Geçersiz dosya türü.',
                    'file_too_large' => 'Dosya boyutu çok büyük.',
                    'database_error' => 'Veritabanı hatası oluştu.'
                ];
                echo $error_messages[$_GET['error']] ?? 'Bir hata oluştu.';
                ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Sayfa Başlığı -->
        <div class="row mb-4">
            <div class="col">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                        <li class="breadcrumb-item active">Profil Ayarları</li>
                    </ol>
                </nav>
                <h2 class="fw-bold">Profil Ayarları</h2>
                <p class="text-muted">Hesap bilgilerinizi güncelleyin</p>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="profile-card card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-person-gear me-2"></i>Kişisel Bilgiler
                        </h5>
                    </div>
                    <div class="card-body p-4">
                        <?php if ($error_message): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="bi bi-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error_message); ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <?php if ($success_message): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="bi bi-check-circle me-2"></i><?php echo htmlspecialchars($success_message); ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <form method="POST">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="full_name" class="form-label">Ad Soyad *</label>
                                    <input type="text" class="form-control" id="full_name" name="full_name"
                                           value="<?php echo htmlspecialchars($user['full_name']); ?>" required>
                                </div>

                                <div class="col-md-6">
                                    <label for="email" class="form-label">Email *</label>
                                    <input type="email" class="form-control" id="email" name="email"
                                           value="<?php echo htmlspecialchars($user['email']); ?>" required>
                                </div>

                                <div class="col-md-6">
                                    <label for="phone" class="form-label">Telefon</label>
                                    <input type="tel" class="form-control" id="phone" name="phone"
                                           value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>"
                                           placeholder="+90 555 123 45 67">
                                </div>

                                <div class="col-md-6">
                                    <label class="form-label">Kullanıcı Tipi</label>
                                    <input type="text" class="form-control"
                                           value="<?php echo $user['user_type'] === 'family' ? 'Aile' : ($user['user_type'] === 'caregiver' ? 'Bakıcı' : 'Admin'); ?>"
                                           readonly>
                                </div>

                                <div class="col-12">
                                    <hr class="my-4">
                                    <h6 class="fw-bold">Şifre Değiştir</h6>
                                    <p class="text-muted small">Şifrenizi değiştirmek istemiyorsanız bu alanları boş bırakın</p>
                                </div>

                                <div class="col-md-4">
                                    <label for="current_password" class="form-label">Mevcut Şifre</label>
                                    <input type="password" class="form-control" id="current_password" name="current_password">
                                </div>

                                <div class="col-md-4">
                                    <label for="new_password" class="form-label">Yeni Şifre</label>
                                    <input type="password" class="form-control" id="new_password" name="new_password">
                                </div>

                                <div class="col-md-4">
                                    <label for="confirm_password" class="form-label">Yeni Şifre (Tekrar)</label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                                </div>

                                <div class="col-12 mt-4">
                                    <div class="d-flex gap-3">
                                        <button type="submit" class="btn btn-primary btn-lg">
                                            <i class="bi bi-check-circle me-2"></i>Güncelle
                                        </button>
                                        <a href="dashboard.php" class="btn btn-outline-secondary btn-lg">
                                            <i class="bi bi-arrow-left me-2"></i>Dashboard'a Dön
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Hesap Bilgileri -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">Hesap Bilgileri</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-2">
                                    <strong>Kayıt Tarihi:</strong><br>
                                    <small class="text-muted"><?php echo date('d.m.Y H:i', strtotime($user['created_at'])); ?></small>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <p class="mb-2">
                                    <strong>Son Güncelleme:</strong><br>
                                    <small class="text-muted"><?php echo $user['updated_at'] ? date('d.m.Y H:i', strtotime($user['updated_at'])) : 'Hiç güncellenmedi'; ?></small>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <p class="mb-2">
                                    <strong>Email Doğrulama:</strong><br>
                                    <?php if (isset($user['email_verified']) && $user['email_verified']): ?>
                                        <span class="badge bg-success">Doğrulandı</span>
                                    <?php else: ?>
                                        <span class="badge bg-warning">Bekliyor</span>
                                    <?php endif; ?>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <p class="mb-2">
                                    <strong>Hesap Durumu:</strong><br>
                                    <?php if (isset($user['status']) && $user['status'] === 'active'): ?>
                                        <span class="badge bg-success">Aktif</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Pasif</span>
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Şifre alanları için validasyon
        document.getElementById('new_password').addEventListener('input', function() {
            const currentPassword = document.getElementById('current_password');
            const confirmPassword = document.getElementById('confirm_password');

            if (this.value) {
                currentPassword.required = true;
                confirmPassword.required = true;
            } else {
                currentPassword.required = false;
                confirmPassword.required = false;
            }
        });

        // Şifre eşleşme kontrolü
        document.getElementById('confirm_password').addEventListener('input', function() {
            const newPassword = document.getElementById('new_password').value;

            if (this.value && this.value !== newPassword) {
                this.setCustomValidity('Şifreler eşleşmiyor');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
