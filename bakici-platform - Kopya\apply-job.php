<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// G<PERSON>ş kontrolü
requireLogin();

// Sadece bakıcı kullanıcıları erişebilir
if ($_SESSION['user_type'] !== 'caregiver') {
    redirect('jobs.php');
}

$job_id = safeNumber(safeArray($_GET, 'job_id', 0));
$user_id = $_SESSION['user_id'];

if (!$job_id) {
    redirect('jobs.php');
}

// İş ilanını getir
try {
    $sql = "SELECT jl.*, u.full_name as employer_name 
            FROM job_listings jl 
            JOIN users u ON jl.user_id = u.id 
            WHERE jl.id = ? AND jl.status = 'active'";
    $stmt = $db->prepare($sql);
    $stmt->execute([$job_id]);
    $job = $stmt->fetch();
    
    if (!$job) {
        redirect('jobs.php');
    }
    
    // Daha önce başvuru yapılmış mı?
    $check_sql = "SELECT id FROM job_applications WHERE job_id = ? AND caregiver_id = ?";
    $check_stmt = $db->prepare($check_sql);
    $check_stmt->execute([$job_id, $user_id]);
    $existing_application = $check_stmt->fetch();
    
    if ($existing_application) {
        setMessage('Bu iş ilanına daha önce başvuru yaptınız.', 'warning');
        redirect('job-detail.php?slug=' . $job['slug']);
    }
    
} catch (PDOException $e) {
    redirect('jobs.php');
}

$error_message = '';
$success_message = '';

// Form gönderildi mi?
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrf_token = safeArray($_POST, 'csrf_token', '');
    
    if (!validateCSRFToken($csrf_token)) {
        $error_message = 'Güvenlik hatası. Lütfen sayfayı yenileyin.';
    } else {
        $cover_letter = trim(safeArray($_POST, 'cover_letter', ''));
        $proposed_rate = safeNumber(safeArray($_POST, 'proposed_rate', 0));
        $availability = safeArray($_POST, 'availability', '');
        
        // Validasyon
        if (empty($cover_letter)) {
            $error_message = 'Başvuru mektubu gereklidir.';
        } elseif (!validateLength($cover_letter, 20, 1000)) {
            $error_message = 'Başvuru mektubu 20-1000 karakter arasında olmalıdır.';
        } elseif ($proposed_rate < 0) {
            $error_message = 'Teklif edilen ücret negatif olamaz.';
        } else {
            try {
                // Başvuruyu kaydet
                $sql = "INSERT INTO job_applications (job_id, caregiver_id, cover_letter, proposed_rate, availability, status, applied_at) 
                        VALUES (?, ?, ?, ?, ?, 'pending', NOW())";
                $stmt = $db->prepare($sql);
                $stmt->execute([$job_id, $user_id, $cover_letter, $proposed_rate ?: null, $availability]);
                
                $application_id = $db->lastInsertId();
                
                // Aktivite kaydı
                logActivity($user_id, 'job_apply', 'İş ilanına başvuru yaptı: ' . $job['title'], 'job_applications', $application_id);
                
                // İşverene bildirim gönder
                $notification_title = 'Yeni Başvuru';
                $notification_message = $_SESSION['full_name'] . ' "' . $job['title'] . '" ilanınıza başvuru yaptı.';
                sendNotification($job['user_id'], 'job_application', $notification_title, $notification_message, 'applications.php');
                
                $success_message = 'Başvurunuz başarıyla gönderildi! İşveren size en kısa sürede dönüş yapacaktır.';
                
                // 3 saniye sonra yönlendir
                echo "<script>
                    setTimeout(function() {
                        window.location.href = 'applications/my-applications.php';
                    }, 3000);
                </script>";
                
            } catch (PDOException $e) {
                $error_message = 'Başvuru gönderilirken bir hata oluştu. Lütfen tekrar deneyin.';
                error_log("Job application error: " . $e->getMessage());
            }
        }
    }
}

$page_title = 'İş Başvurusu';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c5aa0;
        }
        
        .form-card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            background: white;
        }
        
        .job-info-card {
            border: none;
            border-radius: 15px;
            background: linear-gradient(135deg, var(--primary-color), #1e3d72);
            color: white;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
        }
        
        .navbar {
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="index.php">
                <i class="bi bi-heart-fill me-2"></i><?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="caregivers.php">Bakıcı Ara</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="jobs.php">İş İlanları</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">Hakkımızda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">İletişim</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i><?php echo escape($_SESSION['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="dashboard.php"><i class="bi bi-speedometer2 me-2"></i>Dashboard</a></li>
                            <li><a class="dropdown-item" href="profile.php"><i class="bi bi-person me-2"></i>Profil</a></li>
                            <li><a class="dropdown-item" href="messages.php"><i class="bi bi-chat-dots me-2"></i>Mesajlar</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="auth/logout.php"><i class="bi bi-box-arrow-right me-2"></i>Çıkış</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4 mb-5">
        <!-- Sayfa Başlığı -->
        <div class="row mb-4">
            <div class="col">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="jobs.php">İş İlanları</a></li>
                        <li class="breadcrumb-item"><a href="job-detail.php?slug=<?php echo $job['slug']; ?>"><?php echo escape($job['title']); ?></a></li>
                        <li class="breadcrumb-item active">Başvuru Yap</li>
                    </ol>
                </nav>
                <h2 class="fw-bold">İş Başvurusu</h2>
                <p class="text-muted">Bu iş ilanına başvuru yapın</p>
            </div>
        </div>

        <div class="row">
            <!-- İş Bilgileri -->
            <div class="col-lg-4 mb-4">
                <div class="job-info-card card">
                    <div class="card-body p-4">
                        <h5 class="card-title fw-bold"><?php echo escape($job['title']); ?></h5>
                        <p class="mb-2">
                            <i class="bi bi-person me-2"></i><?php echo escape($job['employer_name']); ?>
                        </p>
                        <p class="mb-2">
                            <i class="bi bi-geo-alt me-2"></i>
                            <?php echo escape($job['location_city']); ?>
                            <?php if ($job['location_district']): ?>
                                , <?php echo escape($job['location_district']); ?>
                            <?php endif; ?>
                        </p>
                        <p class="mb-2">
                            <i class="bi bi-briefcase me-2"></i>
                            <?php echo getJobTypes()[$job['job_type']] ?? $job['job_type']; ?>
                        </p>
                        <?php if ($job['budget_min'] || $job['budget_max']): ?>
                        <p class="mb-0">
                            <i class="bi bi-currency-exchange me-2"></i>
                            <?php if ($job['budget_min'] && $job['budget_max']): ?>
                                <?php echo formatMoney($job['budget_min']); ?> - <?php echo formatMoney($job['budget_max']); ?>
                            <?php elseif ($job['budget_min']): ?>
                                <?php echo formatMoney($job['budget_min']); ?>+
                            <?php else: ?>
                                <?php echo formatMoney($job['budget_max']); ?>'e kadar
                            <?php endif; ?>
                            <?php if ($job['budget_type']): ?>
                                /<?php echo $job['budget_type'] === 'hourly' ? 'saat' : ($job['budget_type'] === 'daily' ? 'gün' : ($job['budget_type'] === 'weekly' ? 'hafta' : 'ay')); ?>
                            <?php endif; ?>
                        </p>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">Başvuru İpuçları</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled small">
                            <li class="mb-2">
                                <i class="bi bi-lightbulb text-warning me-2"></i>
                                Deneyimlerinizi ve yeteneklerinizi vurgulayın
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-lightbulb text-warning me-2"></i>
                                İş ilanındaki gereksinimlere uygunluğunuzu belirtin
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-lightbulb text-warning me-2"></i>
                                Profesyonel ve samimi bir dil kullanın
                            </li>
                            <li class="mb-0">
                                <i class="bi bi-lightbulb text-warning me-2"></i>
                                Uygun bir ücret teklifi yapın
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Başvuru Formu -->
            <div class="col-lg-8">
                <div class="form-card p-5">
                    <?php if ($error_message): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i><?php echo escape($error_message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success_message): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle me-2"></i><?php echo escape($success_message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <form id="applicationForm" method="POST">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <div class="mb-4">
                            <label for="cover_letter" class="form-label">Başvuru Mektubu *</label>
                            <textarea class="form-control" id="cover_letter" name="cover_letter" rows="8" 
                                      placeholder="Kendinizi tanıtın, bu iş için neden uygun olduğunuzu açıklayın..." required><?php echo escape(safeArray($_POST, 'cover_letter', '')); ?></textarea>
                            <div class="form-text">Minimum 20, maksimum 1000 karakter</div>
                        </div>
                        
                        <div class="row g-3 mb-4">
                            <div class="col-md-6">
                                <label for="proposed_rate" class="form-label">Teklif Ettiğiniz Ücret (TL/saat)</label>
                                <input type="number" class="form-control" id="proposed_rate" name="proposed_rate" 
                                       value="<?php echo escape(safeArray($_POST, 'proposed_rate', '')); ?>"
                                       placeholder="0" min="0" step="0.01">
                                <div class="form-text">İsteğe bağlı - Boş bırakabilirsiniz</div>
                            </div>
                            <div class="col-md-6">
                                <label for="availability" class="form-label">Müsaitlik Durumu</label>
                                <select class="form-select" id="availability" name="availability">
                                    <option value="">Seçin</option>
                                    <option value="immediately" <?php echo safeArray($_POST, 'availability', '') === 'immediately' ? 'selected' : ''; ?>>Hemen başlayabilirim</option>
                                    <option value="1_week" <?php echo safeArray($_POST, 'availability', '') === '1_week' ? 'selected' : ''; ?>>1 hafta içinde</option>
                                    <option value="2_weeks" <?php echo safeArray($_POST, 'availability', '') === '2_weeks' ? 'selected' : ''; ?>>2 hafta içinde</option>
                                    <option value="1_month" <?php echo safeArray($_POST, 'availability', '') === '1_month' ? 'selected' : ''; ?>>1 ay içinde</option>
                                    <option value="negotiable" <?php echo safeArray($_POST, 'availability', '') === 'negotiable' ? 'selected' : ''; ?>>Görüşülür</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="d-flex gap-3">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="bi bi-send me-2"></i>Başvuru Gönder
                            </button>
                            <a href="job-detail.php?slug=<?php echo $job['slug']; ?>" class="btn btn-outline-secondary btn-lg">
                                <i class="bi bi-arrow-left me-2"></i>İlana Geri Dön
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Form validasyonu
        document.getElementById('applicationForm').addEventListener('submit', function(e) {
            const coverLetter = document.getElementById('cover_letter').value.trim();
            
            if (!coverLetter) {
                e.preventDefault();
                alert('Başvuru mektubu gereklidir.');
                return;
            }
            
            if (coverLetter.length < 20) {
                e.preventDefault();
                alert('Başvuru mektubu en az 20 karakter olmalıdır.');
                return;
            }
            
            if (coverLetter.length > 1000) {
                e.preventDefault();
                alert('Başvuru mektubu en fazla 1000 karakter olmalıdır.');
                return;
            }
            
            // Loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Gönderiliyor...';
            submitBtn.disabled = true;
        });
        
        // Karakter sayacı
        const coverLetterTextarea = document.getElementById('cover_letter');
        const charCountDiv = document.createElement('div');
        charCountDiv.className = 'form-text text-end';
        coverLetterTextarea.parentNode.appendChild(charCountDiv);
        
        function updateCharCount() {
            const length = coverLetterTextarea.value.length;
            charCountDiv.textContent = `${length}/1000 karakter`;
            
            if (length < 20) {
                charCountDiv.className = 'form-text text-end text-danger';
            } else if (length > 900) {
                charCountDiv.className = 'form-text text-end text-warning';
            } else {
                charCountDiv.className = 'form-text text-end text-success';
            }
        }
        
        coverLetterTextarea.addEventListener('input', updateCharCount);
        updateCharCount(); // İlk yükleme
    </script>
</body>
</html>
