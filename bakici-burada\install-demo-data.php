<?php
// Demo veri oluşturma dosyası
// Bu dosya install.php tarafından çağrılır

if (!isset($db)) {
    die('Bu dosya doğrudan çalıştırılamaz.');
}

try {
    // Paketleri oluştur
    $packages = [
        ['Temel Aile Paketi', 'Temel özelliklerle bakıcı bulun', 'family', 29.90, 30, '<PERSON>lan verme, Mesajlaşma', 1, 10, 50, 0, 0],
        ['Premium Aile Paketi', 'Gelişmiş özelliklerle hızlı bakıcı bulun', 'family', 59.90, 30, '<PERSON><PERSON> verme, <PERSON><PERSON><PERSON>, İletişim bilgileri', 3, 50, 200, 1, 1],
        ['Temel Bakıcı Paketi', '<PERSON>ş fırsatlarına başvurun', 'caregiver', 19.90, 30, 'Profil oluşturma, Başvuru yapma', 0, 20, 30, 0, 0],
        ['Premium Bakıcı Paketi', 'Öne çıkan profil ile daha fazla iş', 'caregiver', 39.90, 30, '<PERSON>ne çıkan profil, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> başvuru', 0, 100, 100, 1, 1]
    ];
    
    foreach ($packages as $package) {
        $package_sql = "INSERT INTO packages (name, description, user_type, price, duration_days, features, max_job_listings, max_applications, max_messages, can_see_contact_info, is_featured) 
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $package_stmt = $db->prepare($package_sql);
        $package_stmt->execute($package);
    }

    // Demo aile kullanıcıları
    $demo_families = [
        ['<EMAIL>', 'Ahmet Yılmaz', '0532 123 45 67', 'İstanbul', 'Kadıköy'],
        ['<EMAIL>', 'Elif Kaya', '0533 234 56 78', 'Ankara', 'Çankaya'],
        ['<EMAIL>', 'Mehmet Demir', '0534 345 67 89', 'İzmir', 'Bornova'],
        ['<EMAIL>', 'Zeynep Özkan', '0535 456 78 90', 'Bursa', 'Nilüfer'],
        ['<EMAIL>', 'Ali Çelik', '0536 567 89 01', 'Antalya', 'Muratpaşa']
    ];
    
    foreach ($demo_families as $family) {
        $password = password_hash('demo123', PASSWORD_DEFAULT);
        $sql = "INSERT INTO users (user_type, email, password, full_name, phone, city, district, is_verified, is_active)
                VALUES ('family', ?, ?, ?, ?, ?, ?, 1, 1)";
        $stmt = $db->prepare($sql);
        $stmt->execute([$family[0], $family[1], $family[2], $family[3], $family[4]]);
    }
    
    // Demo bakıcı kullanıcıları
    $demo_caregivers = [
        ['<EMAIL>', 'Fatma Yılmaz', '0537 678 90 12', 'İstanbul', 'Beşiktaş', '1985-03-15', 'female', 8, 'Lise', 'Türkçe, İngilizce', 'Çocuk bakımı, Ev temizliği', 25.00, 200.00, 4500.00, 'Hafta içi 08:00-18:00', 'Çocuk bakımı konusunda 8 yıllık deneyimim var. Çocukları çok seviyorum.', 'Çocuk gelişimi, İlk yardım', 'Çocuk bakım sertifikası, İlk yardım sertifikası', 'Önceki işverenlerden mükemmel referanslar', 1],
        ['<EMAIL>', 'Ayşe Kara', '0538 789 01 23', 'Ankara', 'Kızılay', '1978-07-22', 'female', 12, 'Ön Lisans', 'Türkçe', 'Yaşlı bakımı, Hasta bakımı', 30.00, 250.00, 5500.00, '7/24 yatılı bakım', 'Yaşlı ve hasta bakımında uzmanım. Sabırlı ve anlayışlıyım.', 'Hasta bakımı, Yaşlı bakımı, Medikal bilgi', 'Hasta bakım sertifikası, Yaşlı bakım sertifikası', 'Hastane ve evde bakım deneyimi', 1],
        ['<EMAIL>', 'Merve Öztürk', '0539 890 12 34', 'İzmir', 'Alsancak', '1990-11-08', 'female', 5, 'Lisans', 'Türkçe, İngilizce, Almanca', 'Çocuk bakımı, Ev yardımcısı', 28.00, 220.00, 4800.00, 'Esnek çalışma saatleri', 'Üniversite mezunuyum. Çocuk gelişimi alanında eğitim aldım.', 'Çocuk gelişimi, Yabancı dil', 'Çocuk gelişimi sertifikası', 'Eğitimci ailelerden referanslar', 1],
        ['<EMAIL>', 'Hatice Yıldız', '0540 901 23 45', 'Bursa', 'Osmangazi', '1982-05-30', 'female', 10, 'Lise', 'Türkçe', 'Yaşlı bakımı, Ev yardımcısı', 22.00, 180.00, 4000.00, 'Hafta içi gündüz', 'Yaşlı bakımında deneyimliyim. Temizlik ve yemek yapma konularında iyiyim.', 'Ev işleri, Yemek yapma, Yaşlı bakımı', 'Ev yardımcısı sertifikası', 'Uzun süreli çalıştığım aileler', 1],
        ['<EMAIL>', 'Emine Şahin', '0541 012 34 56', 'Antalya', 'Kepez', '1987-09-12', 'female', 6, 'Lise', 'Türkçe, İngilizce', 'Çocuk bakımı, Evcil hayvan bakımı', 24.00, 190.00, 4200.00, 'Hafta sonu dahil', 'Çocuk ve hayvan sever bir kişiyim. Güvenilir ve sorumlu.', 'Çocuk bakımı, Evcil hayvan bakımı', 'Çocuk bakım sertifikası', 'Memnun ailelerden referanslar', 1]
    ];
    
    foreach ($demo_caregivers as $caregiver) {
        $password = password_hash('demo123', PASSWORD_DEFAULT);
        $sql = "INSERT INTO users (user_type, email, password, full_name, phone, city, district, is_verified, is_active)
                VALUES ('caregiver', ?, ?, ?, ?, ?, ?, 1, 1)";
        $stmt = $db->prepare($sql);
        $stmt->execute([$caregiver[0], $caregiver[1], $caregiver[2], $caregiver[3], $caregiver[4]]);
        
        $caregiver_id = $db->lastInsertId();
        
        // Bakıcı profili oluştur
        $profile_sql = "INSERT INTO caregiver_profiles (user_id, birth_date, gender, experience_years, education_level, languages, services, hourly_rate, daily_rate, monthly_rate, availability, bio, skills, certificates, reference_info, background_check, background_check_file, cv_file, rating, total_reviews, total_jobs)
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, NULL, ?, ?, ?)";
        $profile_stmt = $db->prepare($profile_sql);
        $rating = 3.5 + (rand(0, 15) / 10); // 3.5 - 5.0 arası rating
        $reviews = rand(5, 25);
        $jobs = rand(10, 50);
        $profile_stmt->execute([
            $caregiver_id, $caregiver[5], $caregiver[6], $caregiver[7], $caregiver[8], 
            $caregiver[9], $caregiver[10], $caregiver[11], $caregiver[12], $caregiver[13], 
            $caregiver[14], $caregiver[15], $caregiver[16], $caregiver[17], $caregiver[18], 
            $caregiver[19], $rating, $reviews, $jobs
        ]);
    }
    
    // Demo iş ilanları
    $family_ids = $db->query("SELECT id FROM users WHERE user_type = 'family' ORDER BY id LIMIT 5")->fetchAll(PDO::FETCH_COLUMN);
    
    $demo_jobs = [
        ['Deneyimli Çocuk Bakıcısı Aranıyor', 'child_care', 'live_out', 'İstanbul', 'Kadıköy', 'Kadıköy merkezde oturan ailemiz için 3 yaşındaki kızımıza bakacak deneyimli, güvenilir ve sevecen bir bakıcı arıyoruz.', 3000, 4000, 'monthly', '2024-02-01', 'En az 2 yıl deneyim, referans, temiz adli sicil', 'female', 25, 40, 2, 'Türkçe', '0532 123 45 67', '<EMAIL>', 0, 0, 0, 'active'],
        ['Yaşlı Bakım Elemanı - Yatılı', 'elderly_care', 'live_in', 'Ankara', 'Çankaya', '85 yaşındaki annem için yatılı bakım elemanı arıyoruz. Alzheimer hastası olduğu için deneyimli birini tercih ediyoruz.', 5000, 6000, 'monthly', '2024-02-05', 'Yaşlı bakım deneyimi, sabırlı, Alzheimer deneyimi', 'no_preference', 30, 55, 3, 'Türkçe', '0533 234 56 78', '<EMAIL>', 1, 0, 0, 'active'],
        ['Hasta Bakım Elemanı - Acil', 'patient_care', 'live_in', 'İzmir', 'Bornova', 'Ameliyat sonrası iyileşme sürecindeki babam için hasta bakım elemanı gerekiyor. Medikal bilgisi olan tercih edilir.', 4000, 5000, 'monthly', '2024-01-28', 'Hasta bakım sertifikası, medikal bilgi, referans', 'no_preference', 25, 50, 2, 'Türkçe', '0534 345 67 89', '<EMAIL>', 1, 0, 0, 'active'],
        ['İkiz Bebek Bakıcısı', 'child_care', 'daily', 'Bursa', 'Nilüfer', '6 aylık ikiz bebeklerimiz için günlük bakıcı arıyoruz. Bebek bakımında deneyimli olması şart.', 200, 250, 'daily', '2024-02-10', 'Bebek bakım deneyimi, referans, hijyen kurallarına uyum', 'female', 22, 45, 3, 'Türkçe', '0535 456 78 90', '<EMAIL>', 0, 1, 0, 'active'],
        ['Ev Yardımcısı ve Yaşlı Bakımı', 'house_help', 'live_out', 'Antalya', 'Muratpaşa', 'Ev temizliği ve 78 yaşındaki babama bakım için yardımcı arıyoruz. Hafta içi gündüz çalışma.', 25, 30, 'hourly', '2024-02-15', 'Ev işleri deneyimi, yaşlı bakım bilgisi', 'female', 25, 50, 1, 'Türkçe', '0536 567 89 01', '<EMAIL>', 0, 0, 0, 'active']
    ];

    foreach ($demo_jobs as $index => $job) {
        // Family ID'yi array'den al
        $family_id = $family_ids[$index % count($family_ids)];

        $sql = "INSERT INTO job_listings (user_id, title, job_type, care_type, location_city, location_district, description, budget_min, budget_max, budget_type, start_date, requirements, preferred_gender, preferred_age_min, preferred_age_max, required_experience, required_languages, contact_phone, contact_email, is_urgent, is_featured, is_premium, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $db->prepare($sql);

        // User_id'yi başa ekleyerek execute et
        $job_data = array_merge([$family_id], $job);
        $stmt->execute($job_data);

        // expires_at'ı güncelle
        $job_id = $db->lastInsertId();
        $update_sql = "UPDATE job_listings SET expires_at = DATE_ADD(NOW(), INTERVAL 30 DAY) WHERE id = ?";
        $update_stmt = $db->prepare($update_sql);
        $update_stmt->execute([$job_id]);
    }
    
    // Demo başvurular
    $job_ids = $db->query("SELECT id FROM job_listings ORDER BY id LIMIT 5")->fetchAll(PDO::FETCH_COLUMN);
    $caregiver_ids = $db->query("SELECT id FROM users WHERE user_type = 'caregiver' ORDER BY id LIMIT 5")->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($job_ids as $job_id) {
        $application_count = rand(2, 3);
        $selected_caregivers = array_rand($caregiver_ids, min($application_count, count($caregiver_ids)));
        if (!is_array($selected_caregivers)) $selected_caregivers = [$selected_caregivers];
        
        foreach ($selected_caregivers as $index) {
            $caregiver_id = $caregiver_ids[$index];
            $messages = [
                'Merhaba, ilanınızı gördüm ve çok ilgimi çekti. Bu konuda deneyimliyim ve referanslarım mevcut.',
                'İlanınız için başvuru yapmak istiyorum. Uzun yıllardır bu alanda çalışıyorum.',
                'Merhaba, ilanınıza uygun olduğumu düşünüyorum. Detayları konuşmak isterim.'
            ];
            
            $sql = "INSERT INTO job_applications (job_id, caregiver_id, message, proposed_rate, status)
                    VALUES (?, ?, ?, ?, 'pending')";
            $stmt = $db->prepare($sql);
            $proposed_rate = rand(20, 50) + (rand(0, 9) / 10);
            $stmt->execute([$job_id, $caregiver_id, $messages[array_rand($messages)], $proposed_rate]);
        }
    }
    
    // Demo değerlendirmeler
    $review_comments = [
        ['Çok Memnun Kaldık', 'Fatma hanım çok güvenilir ve çocuklarımızı çok seviyor. Kesinlikle tavsiye ederim.', 5],
        ['Harika Bir Bakıcı', 'Ayşe hanım babama çok iyi bakıyor. Çok sabırlı ve anlayışlı.', 5],
        ['Profesyonel Hizmet', 'Merve hanım çok eğitimli ve çocuk gelişimi konusunda bilgili.', 4],
        ['Güvenilir ve Titiz', 'Hatice hanım ev işlerinde çok titiz ve yaşlı anneme çok iyi bakıyor.', 4],
        ['Çocuklar Çok Seviyor', 'Emine hanım çocuklarla çok iyi anlaşıyor. Çocuklarımız onu çok seviyor.', 5]
    ];
    
    foreach ($caregiver_ids as $index => $caregiver_id) {
        if (isset($review_comments[$index]) && isset($family_ids[$index])) {
            $family_id = $family_ids[$index];
            $review = $review_comments[$index];
            
            $sql = "INSERT INTO reviews (reviewer_id, reviewed_id, rating, title, comment, is_approved)
                    VALUES (?, ?, ?, ?, ?, 1)";
            $stmt = $db->prepare($sql);
            $stmt->execute([$family_id, $caregiver_id, $review[2], $review[0], $review[1]]);
        }
    }
    
    // Demo mesajlar
    $demo_messages = [
        ['İş İlanı Hakkında', 'Merhaba, çocuk bakımı ilanınız hakkında bilgi almak istiyorum.'],
        ['Referans Bilgileri', 'Merhaba, referanslarınızı paylaşabilir misiniz?'],
        ['Çalışma Saatleri', 'Hafta sonu çalışma konusunda nasıl düşünüyorsunuz?']
    ];
    
    foreach ($demo_messages as $index => $message) {
        if ($index < count($family_ids) && $index < count($caregiver_ids)) {
            $sql = "INSERT INTO messages (sender_id, receiver_id, subject, message)
                    VALUES (?, ?, ?, ?)";
            $stmt = $db->prepare($sql);
            $stmt->execute([$family_ids[$index], $caregiver_ids[$index], $message[0], $message[1]]);
            
            // Cevap mesajı
            $reply = 'Merhaba, mesajınız için teşekkürler. Bu konuda detaylı konuşabiliriz.';
            $stmt->execute([$caregiver_ids[$index], $family_ids[$index], 'Re: ' . $message[0], $reply]);
        }
    }
    
    // Demo bildirimler
    $all_user_ids = $db->query("SELECT id FROM users WHERE user_type IN ('family', 'caregiver') ORDER BY id LIMIT 8")->fetchAll(PDO::FETCH_COLUMN);
    
    $demo_notifications = [
        ['new_application', 'Yeni Başvuru', 'İlanınıza yeni bir başvuru geldi.'],
        ['message_received', 'Yeni Mesaj', 'Size yeni bir mesaj gönderildi.'],
        ['profile_viewed', 'Profil Görüntülendi', 'Profiliniz bir işveren tarafından görüntülendi.'],
        ['job_match', 'Uygun İş İlanı', 'Size uygun yeni bir iş ilanı yayınlandı.'],
        ['review_received', 'Yeni Değerlendirme', 'Hakkınızda yeni bir değerlendirme yazıldı.']
    ];
    
    foreach ($all_user_ids as $index => $user_id) {
        if ($index < count($demo_notifications)) {
            $notification = $demo_notifications[$index];
            $sql = "INSERT INTO notifications (user_id, type, title, message)
                    VALUES (?, ?, ?, ?)";
            $stmt = $db->prepare($sql);
            $stmt->execute([$user_id, $notification[0], $notification[1], $notification[2]]);
        }
    }
    
} catch (PDOException $e) {
    throw new Exception("Demo veri oluşturma hatası: " . $e->getMessage());
}
?>
