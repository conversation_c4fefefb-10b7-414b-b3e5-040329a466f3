<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Giriş kontrolü
requireLogin();

// <PERSON><PERSON>e aile kullanıcıları erişebilir
if ($_SESSION['user_type'] !== 'family') {
    redirect('../dashboard.php');
}

$user_id = $_SESSION['user_id'];

// İlanları getir
try {
    $sql = "SELECT jl.*, COUNT(ja.id) as application_count
            FROM job_listings jl
            LEFT JOIN job_applications ja ON jl.id = ja.job_id
            WHERE jl.user_id = ?
            GROUP BY jl.id
            ORDER BY jl.created_at DESC";
    $stmt = $db->prepare($sql);
    $stmt->execute([$user_id]);
    $jobs = $stmt->fetchAll();
} catch (PDOException $e) {
    $jobs = [];
}

$page_title = 'İlanlarım';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c5aa0;
        }
        
        .job-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }
        
        .job-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }
        
        .status-active { border-left: 4px solid #28a745; }
        .status-paused { border-left: 4px solid #ffc107; }
        .status-closed { border-left: 4px solid #dc3545; }
        .status-draft { border-left: 4px solid #6c757d; }
        
        .navbar {
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="../index.php">
                <i class="bi bi-heart-fill me-2"></i><?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../caregivers.php">Bakıcı Ara</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../jobs.php">İş İlanları</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../about.php">Hakkımızda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../contact.php">İletişim</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i><?php echo escape($_SESSION['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../dashboard.php"><i class="bi bi-speedometer2 me-2"></i>Dashboard</a></li>
                            <li><a class="dropdown-item" href="../profile.php"><i class="bi bi-person me-2"></i>Profil</a></li>
                            <li><a class="dropdown-item" href="../messages.php"><i class="bi bi-chat-dots me-2"></i>Mesajlar</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../auth/logout.php"><i class="bi bi-box-arrow-right me-2"></i>Çıkış</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Sayfa Başlığı -->
        <div class="row mb-4">
            <div class="col">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="../dashboard.php">Dashboard</a></li>
                        <li class="breadcrumb-item active">İlanlarım</li>
                    </ol>
                </nav>
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="fw-bold">İş İlanlarım</h2>
                        <p class="text-muted">Verdiğiniz iş ilanlarını yönetin</p>
                    </div>
                    <a href="create.php" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>Yeni İlan Ver
                    </a>
                </div>
            </div>
        </div>

        <!-- İlanlar -->
        <div class="row">
            <?php if (!empty($jobs)): ?>
                <?php foreach ($jobs as $job): ?>
                <div class="col-12">
                    <div class="job-card status-<?php echo $job['status']; ?> card" data-job-id="<?php echo $job['id']; ?>">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <h5 class="fw-bold mb-2"><?php echo escape($job['title']); ?></h5>
                                    <p class="text-muted mb-2">
                                        <i class="bi bi-geo-alt me-1"></i>
                                        <?php echo escape($job['location_city']); ?>
                                        <?php if ($job['location_district']): ?>
                                            , <?php echo escape($job['location_district']); ?>
                                        <?php endif; ?>
                                    </p>
                                    <p class="text-muted small mb-0">
                                        <?php echo escape(substr($job['description'], 0, 150)); ?>...
                                    </p>
                                </div>
                                <div class="col-md-2">
                                    <p class="mb-1"><strong>Başvuru:</strong></p>
                                    <span class="badge bg-primary"><?php echo $job['application_count']; ?> başvuru</span>
                                </div>
                                <div class="col-md-2">
                                    <p class="mb-1"><strong>Durum:</strong></p>
                                    <span class="badge bg-<?php 
                                        echo $job['status'] === 'active' ? 'success' : 
                                            ($job['status'] === 'paused' ? 'warning' : 
                                            ($job['status'] === 'closed' ? 'danger' : 'secondary')); 
                                    ?>">
                                        <?php echo getJobStatuses()[$job['status']] ?? $job['status']; ?>
                                    </span>
                                    <br>
                                    <small class="text-muted"><?php echo timeAgo($job['created_at']); ?></small>
                                </div>
                                <div class="col-md-2 text-end">
                                    <div class="btn-group-vertical" role="group">
                                        <a href="../job-detail.php?slug=<?php echo $job['slug']; ?>" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-eye"></i> Görüntüle
                                        </a>
                                        <a href="edit.php?id=<?php echo $job['id']; ?>" class="btn btn-sm btn-outline-secondary">
                                            <i class="bi bi-pencil"></i> Düzenle
                                        </a>
                                        <?php if ($job['status'] === 'active'): ?>
                                            <button class="btn btn-sm btn-warning" onclick="pauseJob(<?php echo $job['id']; ?>)">
                                                <i class="bi bi-pause"></i> Duraklat
                                            </button>
                                        <?php elseif ($job['status'] === 'paused'): ?>
                                            <button class="btn btn-sm btn-success" onclick="activateJob(<?php echo $job['id']; ?>)">
                                                <i class="bi bi-play"></i> Aktifleştir
                                            </button>
                                        <?php endif; ?>
                                        <button class="btn btn-sm btn-danger" onclick="deleteJob(<?php echo $job['id']; ?>)">
                                            <i class="bi bi-trash"></i> Sil
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="bi bi-briefcase text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">Henüz iş ilanınız yok</h4>
                        <p class="text-muted">İlk iş ilanınızı vererek bakıcı aramaya başlayın.</p>
                        <a href="create.php" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-2"></i>İlk İlanımı Ver
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function pauseJob(jobId) {
            if (confirm('İlanı duraklatmak istediğinizden emin misiniz?')) {
                manageJob(jobId, 'pause');
            }
        }

        function activateJob(jobId) {
            if (confirm('İlanı aktifleştirmek istediğinizden emin misiniz?')) {
                manageJob(jobId, 'activate');
            }
        }

        function deleteJob(jobId) {
            if (confirm('İlanı silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.')) {
                manageJob(jobId, 'delete');
            }
        }

        function closeJob(jobId) {
            if (confirm('İlanı kapatmak istediğinizden emin misiniz?')) {
                manageJob(jobId, 'close');
            }
        }

        function manageJob(jobId, action) {
            // Loading state
            const buttons = document.querySelectorAll(`[onclick*="${jobId}"]`);
            buttons.forEach(btn => {
                btn.disabled = true;
                btn.innerHTML = '<i class="bi bi-hourglass-split"></i> İşleniyor...';
            });

            // AJAX isteği
            const formData = new FormData();
            formData.append('job_id', jobId);
            formData.append('action', action);

            fetch('manage-job.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Başarı mesajı
                    showMessage(data.message, 'success');

                    // Sayfayı yenile (silme işlemi için)
                    if (action === 'delete') {
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    } else {
                        // Durum badge'ini güncelle ve butonları yenile
                        updateJobStatus(jobId, data.new_status);
                    }
                } else {
                    showMessage(data.message, 'error');
                    // Butonları eski haline getir
                    restoreButtons(jobId);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showMessage('Bir hata oluştu. Lütfen tekrar deneyin.', 'error');
                restoreButtons(jobId);
            });
        }

        function updateJobStatus(jobId, newStatus) {
            const jobCard = document.querySelector(`[data-job-id="${jobId}"]`);
            if (!jobCard) return;

            // Status badge'ini güncelle
            const statusBadge = jobCard.querySelector('.badge');
            const statusNames = {
                'active': 'Aktif',
                'paused': 'Duraklatılmış',
                'closed': 'Kapatılmış',
                'draft': 'Taslak'
            };

            const statusClasses = {
                'active': 'bg-success',
                'paused': 'bg-warning',
                'closed': 'bg-danger',
                'draft': 'bg-secondary'
            };

            if (statusBadge) {
                statusBadge.className = `badge ${statusClasses[newStatus]}`;
                statusBadge.textContent = statusNames[newStatus];
            }

            // Butonları güncelle
            updateJobButtons(jobId, newStatus);
        }

        function updateJobButtons(jobId, status) {
            const buttonContainer = document.querySelector(`[data-job-id="${jobId}"] .btn-group-vertical`);
            if (!buttonContainer) return;

            let buttonsHtml = `
                <a href="../job-detail.php?slug=\${getJobSlug(jobId)}" class="btn btn-sm btn-outline-primary">
                    <i class="bi bi-eye"></i> Görüntüle
                </a>
                <a href="edit.php?id=${jobId}" class="btn btn-sm btn-outline-secondary">
                    <i class="bi bi-pencil"></i> Düzenle
                </a>
            `;

            if (status === 'active') {
                buttonsHtml += `
                    <button class="btn btn-sm btn-warning" onclick="pauseJob(${jobId})">
                        <i class="bi bi-pause"></i> Duraklat
                    </button>
                `;
            } else if (status === 'paused' || status === 'draft') {
                buttonsHtml += `
                    <button class="btn btn-sm btn-success" onclick="activateJob(${jobId})">
                        <i class="bi bi-play"></i> Aktifleştir
                    </button>
                `;
            }

            if (status !== 'closed') {
                buttonsHtml += `
                    <button class="btn btn-sm btn-secondary" onclick="closeJob(${jobId})">
                        <i class="bi bi-x-circle"></i> Kapat
                    </button>
                `;
            }

            buttonsHtml += `
                <button class="btn btn-sm btn-danger" onclick="deleteJob(${jobId})">
                    <i class="bi bi-trash"></i> Sil
                </button>
            `;

            buttonContainer.innerHTML = buttonsHtml;
        }

        function restoreButtons(jobId) {
            // Butonları eski haline getir
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        }

        function showMessage(message, type) {
            // Basit alert yerine toast notification
            const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show position-fixed"
                     style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                    <i class="bi bi-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', alertHtml);

            // 5 saniye sonra otomatik kapat
            setTimeout(() => {
                const alert = document.querySelector('.alert');
                if (alert) {
                    alert.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>
