<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}



// Giriş kontrolü
if (!isset($_SESSION['user_id'])) {
    header('Location: auth/login.php');
    exit;
}

$receiver_id = isset($_GET['to']) ? (int)$_GET['to'] : 0;
$job_id = isset($_GET['job_id']) ? (int)$_GET['job_id'] : 0;

if (!$receiver_id) {
    header('Location: dashboard.php');
    exit;
}

// Alıcı bilgilerini getir
try {
    $sql = "SELECT id, full_name, email, user_type FROM users WHERE id = ? AND status = 'active'";
    $stmt = $db->prepare($sql);
    $stmt->execute([$receiver_id]);
    $receiver = $stmt->fetch();
    
    if (!$receiver) {
        header('Location: dashboard.php');
        exit;
    }
    
    // İş ilanı bilgisi varsa getir
    $job = null;
    if ($job_id) {
        $job_sql = "SELECT id, title, slug FROM job_listings WHERE id = ?";
        $job_stmt = $db->prepare($job_sql);
        $job_stmt->execute([$job_id]);
        $job = $job_stmt->fetch();
    }
    
} catch (PDOException $e) {
    header('Location: dashboard.php');
    exit;
}

$error_message = '';
$success_message = '';

// Form gönderildi mi?
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $subject = trim($_POST['subject'] ?? '');
    $message = trim($_POST['message'] ?? '');

    // Basit validasyon
    if (empty($subject)) {
        $error_message = 'Konu başlığı gereklidir.';
    } elseif (empty($message)) {
        $error_message = 'Mesaj içeriği gereklidir.';
    } else {
        try {
            // Mesajı kaydet
            $sql = "INSERT INTO messages (sender_id, receiver_id, subject, message, created_at) VALUES (?, ?, ?, ?, NOW())";
            $stmt = $db->prepare($sql);
            $stmt->execute([$_SESSION['user_id'], $receiver_id, $subject, $message]);

            $success_message = 'Mesajınız başarıyla gönderildi!';

            // 2 saniye sonra yönlendir
            echo "<script>
                setTimeout(function() {
                    window.location.href = 'messages.php';
                }, 2000);
            </script>";

        } catch (PDOException $e) {
            $error_message = 'Mesaj gönderilirken bir hata oluştu.';
        }
    }
}

$page_title = 'Mesaj Gönder';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bakıcı Platform</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c5aa0;
        }
        
        .form-card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            background: white;
        }
        
        .receiver-info {
            background: linear-gradient(135deg, var(--primary-color), #1e3d72);
            color: white;
            border-radius: 15px;
            padding: 20px;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
        }
        
        .navbar {
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="index.php">
                <i class="bi bi-heart-fill me-2"></i>Bakıcı Platform
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="caregivers.php">Bakıcı Ara</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="jobs.php">İş İlanları</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">Hakkımızda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">İletişim</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i><?php echo htmlspecialchars($_SESSION['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="dashboard.php"><i class="bi bi-speedometer2 me-2"></i>Dashboard</a></li>
                            <li><a class="dropdown-item" href="profile.php"><i class="bi bi-person me-2"></i>Profil</a></li>
                            <li><a class="dropdown-item" href="messages.php"><i class="bi bi-chat-dots me-2"></i>Mesajlar</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="auth/logout.php"><i class="bi bi-box-arrow-right me-2"></i>Çıkış</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4 mb-5">
        <!-- Sayfa Başlığı -->
        <div class="row mb-4">
            <div class="col">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="messages.php">Mesajlar</a></li>
                        <li class="breadcrumb-item active">Mesaj Gönder</li>
                    </ol>
                </nav>
                <h2 class="fw-bold">Mesaj Gönder</h2>
                <p class="text-muted">Kullanıcıya mesaj gönderin</p>
            </div>
        </div>

        <div class="row">
            <!-- Alıcı Bilgileri -->
            <div class="col-lg-4 mb-4">
                <div class="receiver-info">
                    <h5 class="fw-bold mb-3">
                        <i class="bi bi-person-circle me-2"></i>Alıcı Bilgileri
                    </h5>
                    <p class="mb-2">
                        <strong>Ad Soyad:</strong><br>
                        <?php echo htmlspecialchars($receiver['full_name']); ?>
                    </p>
                    <p class="mb-2">
                        <strong>Kullanıcı Tipi:</strong><br>
                        <span class="badge bg-light text-dark">
                            <?php echo $receiver['user_type'] === 'family' ? 'Aile' : 'Bakıcı'; ?>
                        </span>
                    </p>
                    <?php if ($job): ?>
                    <div class="mt-3 pt-3 border-top border-light">
                        <p class="mb-2">
                            <strong>İlgili İş İlanı:</strong><br>
                            <a href="job-detail.php?slug=<?php echo $job['slug']; ?>" class="text-white text-decoration-underline">
                                <?php echo htmlspecialchars($job['title']); ?>
                            </a>
                        </p>
                    </div>
                    <?php endif; ?>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">Mesaj İpuçları</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled small">
                            <li class="mb-2">
                                <i class="bi bi-lightbulb text-warning me-2"></i>
                                Açık ve net bir konu başlığı yazın
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-lightbulb text-warning me-2"></i>
                                Mesajınızı kibar ve profesyonel tutun
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-lightbulb text-warning me-2"></i>
                                İletişim bilgilerinizi paylaşabilirsiniz
                            </li>
                            <li class="mb-0">
                                <i class="bi bi-lightbulb text-warning me-2"></i>
                                Yanıt süresini sabırla bekleyin
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Mesaj Formu -->
            <div class="col-lg-8">
                <div class="form-card p-5">
                    <?php if ($error_message): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error_message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success_message): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle me-2"></i><?php echo htmlspecialchars($success_message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <form id="messageForm" method="POST">
                        
                        <div class="mb-4">
                            <label for="subject" class="form-label">Konu Başlığı *</label>
                            <input type="text" class="form-control" id="subject" name="subject"
                                   value="<?php echo htmlspecialchars($_POST['subject'] ?? ($job ? 'İş İlanı Hakkında: ' . $job['title'] : '')); ?>"
                                   placeholder="Mesajınızın konusunu yazın..." required>
                        </div>
                        
                        <div class="mb-4">
                            <label for="message" class="form-label">Mesaj İçeriği *</label>
                            <textarea class="form-control" id="message" name="message" rows="8"
                                      placeholder="Mesajınızı buraya yazın..." required><?php echo htmlspecialchars($_POST['message'] ?? ''); ?></textarea>
                            <div class="form-text">Minimum 10, maksimum 2000 karakter</div>
                        </div>
                        
                        <div class="d-flex gap-3">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="bi bi-send me-2"></i>Mesajı Gönder
                            </button>
                            <a href="messages.php" class="btn btn-outline-secondary btn-lg">
                                <i class="bi bi-arrow-left me-2"></i>Mesajlara Dön
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Form validasyonu
        document.getElementById('messageForm').addEventListener('submit', function(e) {
            const subject = document.getElementById('subject').value.trim();
            const message = document.getElementById('message').value.trim();
            
            if (!subject) {
                e.preventDefault();
                alert('Konu başlığı gereklidir.');
                return;
            }
            
            if (subject.length < 3 || subject.length > 255) {
                e.preventDefault();
                alert('Konu başlığı 3-255 karakter arasında olmalıdır.');
                return;
            }
            
            if (!message) {
                e.preventDefault();
                alert('Mesaj içeriği gereklidir.');
                return;
            }
            
            if (message.length < 10 || message.length > 2000) {
                e.preventDefault();
                alert('Mesaj içeriği 10-2000 karakter arasında olmalıdır.');
                return;
            }
            
            // Loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Gönderiliyor...';
            submitBtn.disabled = true;
        });
        
        // Karakter sayacı
        const messageTextarea = document.getElementById('message');
        const charCountDiv = document.createElement('div');
        charCountDiv.className = 'form-text text-end';
        messageTextarea.parentNode.appendChild(charCountDiv);
        
        function updateCharCount() {
            const length = messageTextarea.value.length;
            charCountDiv.textContent = `${length}/2000 karakter`;
            
            if (length < 10) {
                charCountDiv.className = 'form-text text-end text-danger';
            } else if (length > 1800) {
                charCountDiv.className = 'form-text text-end text-warning';
            } else {
                charCountDiv.className = 'form-text text-end text-success';
            }
        }
        
        messageTextarea.addEventListener('input', updateCharCount);
        updateCharCount(); // İlk yükleme
    </script>
</body>
</html>
