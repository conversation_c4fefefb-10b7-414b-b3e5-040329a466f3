<?php

namespace App\Models;

use CodeIgniter\Model;

class CategoryModel extends Model
{
    protected $table = 'categories';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'name', 'slug', 'description', 'icon', 'color', 'status', 'sort_order'
    ];

    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = null;

    protected $validationRules = [
        'name' => 'required|min_length[2]|max_length[100]',
        'slug' => 'required|alpha_dash|max_length[100]|is_unique[categories.slug,id,{id}]',
        'color' => 'permit_empty|regex_match[/^#[0-9A-Fa-f]{6}$/]'
    ];

    protected $validationMessages = [
        'name' => [
            'required' => '<PERSON>gori adı gereklidir.',
            'min_length' => '<PERSON><PERSON><PERSON> adı en az 2 karakter olmalıdır.',
            'max_length' => '<PERSON><PERSON><PERSON> adı en fazla 100 karakter olabilir.'
        ],
        'slug' => [
            'required' => '<PERSON>gori slug\'ı gereklidir.',
            'alpha_dash' => 'Slug sadece harf, rakam, tire ve alt çizgi içerebilir.',
            'is_unique' => 'Bu slug zaten kullanılıyor.'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    protected $allowCallbacks = true;
    protected $beforeInsert = ['generateSlug'];
    protected $beforeUpdate = ['generateSlug'];

    // Aktif kategorileri getir
    public function getActiveCategories()
    {
        return $this->where('status', 1)
                   ->orderBy('sort_order', 'ASC')
                   ->orderBy('name', 'ASC')
                   ->findAll();
    }

    // Kategori detayını getir
    public function getCategoryBySlug($slug)
    {
        return $this->where('slug', $slug)
                   ->where('status', 1)
                   ->first();
    }

    // Kategori ile birlikte ilan sayısını getir
    public function getCategoriesWithAdCount()
    {
        return $this->select('categories.*, COUNT(ads.id) as ad_count')
                   ->join('ads', 'ads.category_id = categories.id AND ads.status = "active" AND ads.expires_at > NOW()', 'left')
                   ->where('categories.status', 1)
                   ->groupBy('categories.id')
                   ->orderBy('categories.sort_order', 'ASC')
                   ->orderBy('categories.name', 'ASC')
                   ->findAll();
    }

    // Toplam aktif kategori sayısı
    public function getTotalActiveCategories()
    {
        return $this->where('status', 1)->countAllResults();
    }

    // Slug oluştur
    protected function generateSlug(array $data)
    {
        if (isset($data['data']['name']) && empty($data['data']['slug'])) {
            $data['data']['slug'] = url_title($data['data']['name'], '-', true);
            
            // Benzersiz slug kontrolü
            $count = 1;
            $originalSlug = $data['data']['slug'];
            
            while ($this->where('slug', $data['data']['slug'])->first()) {
                $data['data']['slug'] = $originalSlug . '-' . $count;
                $count++;
            }
        }
        
        return $data;
    }
}
