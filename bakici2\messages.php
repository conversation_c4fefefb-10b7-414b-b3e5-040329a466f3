<?php
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/session.php';

// Giriş kontrolü
requireLogin();

$user_id = $_SESSION['user_id'];

// Mesajları getir (gö<PERSON>ilen ve alınan)
try {
    $sql = "SELECT m.*,
                   sender.full_name as sender_name,
                   receiver.full_name as receiver_name,
                   CASE
                       WHEN m.sender_id = ? THEN 'sent'
                       ELSE 'received'
                   END as message_type
            FROM messages m
            JOIN users sender ON m.sender_id = sender.id
            JOIN users receiver ON m.receiver_id = receiver.id
            WHERE m.sender_id = ? OR m.receiver_id = ?
            ORDER BY m.created_at DESC
            LIMIT 50";
    $stmt = $db->prepare($sql);
    $stmt->execute([$user_id, $user_id, $user_id]);
    $messages = $stmt->fetchAll();

    // Okunmamış mesaj sayısı
    $unread_sql = "SELECT COUNT(*) FROM messages WHERE receiver_id = ? AND is_read = 0";
    $unread_stmt = $db->prepare($unread_sql);
    $unread_stmt->execute([$user_id]);
    $unread_count = $unread_stmt->fetchColumn();

} catch (PDOException $e) {
    $messages = [];
    $unread_count = 0;
}

$page_title = 'Mesajlar';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c5aa0;
        }

        .message-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }

        .message-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .message-sent {
            border-left: 4px solid #28a745;
        }

        .message-received {
            border-left: 4px solid #007bff;
        }

        .message-unread {
            background-color: #f8f9ff;
            border-left: 4px solid #ffc107;
        }

        .navbar {
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="index.php">
                <i class="bi bi-heart-fill me-2"></i><?php echo escape(getSetting('site_name', 'Bakıcı Platform')); ?>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="caregivers.php">Bakıcı Ara</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="jobs.php">İş İlanları</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">Hakkımızda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">İletişim</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i><?php echo escape($_SESSION['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="dashboard.php"><i class="bi bi-speedometer2 me-2"></i>Dashboard</a></li>
                            <li><a class="dropdown-item" href="profile.php"><i class="bi bi-person me-2"></i>Profil</a></li>
                            <li><a class="dropdown-item active" href="messages.php"><i class="bi bi-chat-dots me-2"></i>Mesajlar</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="auth/logout.php"><i class="bi bi-box-arrow-right me-2"></i>Çıkış</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Alert Messages -->
        <?php if (isset($_GET['success'])): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="bi bi-check-circle me-2"></i>
                <?php
                $success_messages = [
                    'message_sent' => 'Mesajınız başarıyla gönderildi.',
                    'message_deleted' => 'Mesaj silindi.',
                    'message_marked_read' => 'Mesaj okundu olarak işaretlendi.'
                ];
                echo $success_messages[$_GET['success']] ?? 'İşlem başarıyla tamamlandı.';
                ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_GET['error'])): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="bi bi-exclamation-triangle me-2"></i>
                <?php
                $error_messages = [
                    'invalid_user' => 'Geçersiz kullanıcı.',
                    'user_not_found' => 'Kullanıcı bulunamadı.',
                    'cannot_message_self' => 'Kendinize mesaj gönderemezsiniz.',
                    'database_error' => 'Veritabanı hatası oluştu.',
                    'permission_denied' => 'Bu işlem için yetkiniz yok.',
                    'message_not_found' => 'Mesaj bulunamadı.'
                ];
                echo $error_messages[$_GET['error']] ?? 'Bir hata oluştu.';
                ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Sayfa Başlığı -->
        <div class="row mb-4">
            <div class="col">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                        <li class="breadcrumb-item active">Mesajlar</li>
                    </ol>
                </nav>
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="fw-bold">Mesajlar</h2>
                        <p class="text-muted">
                            Toplam <?php echo count($messages); ?> mesaj
                            <?php if ($unread_count > 0): ?>
                                - <span class="text-warning fw-bold"><?php echo $unread_count; ?> okunmamış</span>
                            <?php endif; ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mesajlar -->
        <div class="row">
            <div class="col-lg-8">
                <?php if (!empty($messages)): ?>
                    <?php foreach ($messages as $message): ?>
                    <div class="message-card card <?php echo $message['message_type'] === 'sent' ? 'message-sent' : 'message-received'; ?> <?php echo $message['message_type'] === 'received' && !$message['is_read'] ? 'message-unread' : ''; ?>">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div>
                                    <h6 class="fw-bold mb-1">
                                        <?php echo escape($message['subject']); ?>
                                        <?php if ($message['message_type'] === 'received' && !$message['is_read']): ?>
                                            <span class="badge bg-warning text-dark ms-2">Yeni</span>
                                        <?php endif; ?>
                                    </h6>
                                    <p class="text-muted small mb-0">
                                        <?php if ($message['message_type'] === 'sent'): ?>
                                            <i class="bi bi-arrow-right me-1"></i>
                                            <strong>Gönderildi:</strong> <?php echo escape($message['receiver_name']); ?>
                                        <?php else: ?>
                                            <i class="bi bi-arrow-left me-1"></i>
                                            <strong>Gönderen:</strong> <?php echo escape($message['sender_name']); ?>
                                        <?php endif; ?>
                                    </p>
                                </div>
                                <div class="text-end">
                                    <small class="text-muted"><?php echo timeAgo($message['created_at']); ?></small>
                                    <?php if ($message['message_type'] === 'sent'): ?>
                                        <br><span class="badge bg-success">Gönderildi</span>
                                    <?php elseif ($message['is_read']): ?>
                                        <br><span class="badge bg-secondary">Okundu</span>
                                    <?php else: ?>
                                        <br><span class="badge bg-warning text-dark">Okunmadı</span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="bg-light p-3 rounded">
                                <p class="mb-0"><?php echo nl2br(escape($message['message'])); ?></p>
                            </div>

                            <div class="mt-3">
                                <?php if ($message['message_type'] === 'received'): ?>
                                    <a href="send-message.php?to=<?php echo $message['sender_id']; ?>" class="btn btn-primary btn-sm">
                                        <i class="bi bi-reply me-1"></i>Yanıtla
                                    </a>
                                <?php else: ?>
                                    <a href="send-message.php?to=<?php echo $message['receiver_id']; ?>" class="btn btn-outline-primary btn-sm">
                                        <i class="bi bi-chat-dots me-1"></i>Yeni Mesaj Gönder
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="bi bi-chat-dots text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">Henüz mesajınız yok</h4>
                        <p class="text-muted">Bakıcılarla iletişime geçerek mesajlaşmaya başlayın.</p>
                        <a href="caregivers.php" class="btn btn-primary">Bakıcı Ara</a>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Mesaj İstatistikleri</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <h4 class="text-primary fw-bold"><?php echo count($messages); ?></h4>
                                <small class="text-muted">Toplam Mesaj</small>
                            </div>
                            <div class="col-6">
                                <h4 class="text-warning fw-bold"><?php echo $unread_count; ?></h4>
                                <small class="text-muted">Okunmamış</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">Hızlı İşlemler</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="caregivers.php" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-search me-2"></i>Bakıcı Ara
                            </a>
                            <a href="jobs.php" class="btn btn-outline-success btn-sm">
                                <i class="bi bi-briefcase me-2"></i>İş İlanları
                            </a>
                            <a href="dashboard.php" class="btn btn-outline-secondary btn-sm">
                                <i class="bi bi-speedometer2 me-2"></i>Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
