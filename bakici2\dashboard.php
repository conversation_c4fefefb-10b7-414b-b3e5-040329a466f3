<?php
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/session.php';

// Giriş kontrolü
requireLogin();

$user_id = $_SESSION['user_id'];
$user_type = $_SESSION['user_type'];
$user = getCurrentUser();

// Kullanıcı istatistikleri
$stats = [];

if ($user_type === 'family') {
    // Aile istatistikleri
    try {
        $stats['my_jobs'] = $db->prepare("SELECT COUNT(*) FROM job_listings WHERE user_id = ?");
        $stats['my_jobs']->execute([$user_id]);
        $stats['my_jobs'] = $stats['my_jobs']->fetchColumn();

        $stats['active_jobs'] = $db->prepare("SELECT COUNT(*) FROM job_listings WHERE user_id = ? AND status = 'active'");
        $stats['active_jobs']->execute([$user_id]);
        $stats['active_jobs'] = $stats['active_jobs']->fetchColumn();

        $stats['total_applications'] = $db->prepare("SELECT COUNT(*) FROM job_applications ja JOIN job_listings jl ON ja.job_id = jl.id WHERE jl.user_id = ?");
        $stats['total_applications']->execute([$user_id]);
        $stats['total_applications'] = $stats['total_applications']->fetchColumn();

        $stats['unread_messages'] = $db->prepare("SELECT COUNT(*) FROM messages WHERE receiver_id = ? AND is_read = 0");
        $stats['unread_messages']->execute([$user_id]);
        $stats['unread_messages'] = $stats['unread_messages']->fetchColumn();

        // Son başvurular
        $recent_applications_sql = "SELECT ja.*, jl.title as job_title, jl.slug as job_slug, u.full_name as caregiver_name, cp.rating, cp.experience_years
                                   FROM job_applications ja
                                   JOIN job_listings jl ON ja.job_id = jl.id
                                   JOIN users u ON ja.caregiver_id = u.id
                                   LEFT JOIN caregiver_profiles cp ON u.id = cp.user_id
                                   WHERE jl.user_id = ?
                                   ORDER BY ja.applied_at DESC LIMIT 5";
        $stmt = $db->prepare($recent_applications_sql);
        $stmt->execute([$user_id]);
        $recent_applications = $stmt->fetchAll();

        // Aktif ilanlar
        $active_jobs_sql = "SELECT jl.*, COUNT(ja.id) as application_count
                           FROM job_listings jl
                           LEFT JOIN job_applications ja ON jl.id = ja.job_id
                           WHERE jl.user_id = ? AND jl.status = 'active'
                           GROUP BY jl.id
                           ORDER BY jl.created_at DESC LIMIT 5";
        $stmt = $db->prepare($active_jobs_sql);
        $stmt->execute([$user_id]);
        $active_jobs = $stmt->fetchAll();

    } catch (PDOException $e) {
        $stats = ['my_jobs' => 0, 'active_jobs' => 0, 'total_applications' => 0, 'unread_messages' => 0];
        $recent_applications = [];
        $active_jobs = [];
    }

} elseif ($user_type === 'caregiver') {
    // Bakıcı istatistikleri
    try {
        $stats['my_applications'] = $db->prepare("SELECT COUNT(*) FROM job_applications WHERE caregiver_id = ?");
        $stats['my_applications']->execute([$user_id]);
        $stats['my_applications'] = $stats['my_applications']->fetchColumn();

        $stats['pending_applications'] = $db->prepare("SELECT COUNT(*) FROM job_applications WHERE caregiver_id = ? AND status = 'pending'");
        $stats['pending_applications']->execute([$user_id]);
        $stats['pending_applications'] = $stats['pending_applications']->fetchColumn();

        $stats['accepted_applications'] = $db->prepare("SELECT COUNT(*) FROM job_applications WHERE caregiver_id = ? AND status IN ('accepted', 'hired')");
        $stats['accepted_applications']->execute([$user_id]);
        $stats['accepted_applications'] = $stats['accepted_applications']->fetchColumn();

        $stats['unread_messages'] = $db->prepare("SELECT COUNT(*) FROM messages WHERE receiver_id = ? AND is_read = 0");
        $stats['unread_messages']->execute([$user_id]);
        $stats['unread_messages'] = $stats['unread_messages']->fetchColumn();

        // Profil tamamlanma oranı
        $profile_sql = "SELECT * FROM caregiver_profiles WHERE user_id = ?";
        $stmt = $db->prepare($profile_sql);
        $stmt->execute([$user_id]);
        $profile = $stmt->fetch();

        $profile_completion = 0;
        if ($profile) {
            $fields = ['birth_date', 'experience_years', 'education_level', 'specializations', 'bio', 'hourly_rate'];
            $completed_fields = 0;
            foreach ($fields as $field) {
                if (!empty($profile[$field])) $completed_fields++;
            }
            $profile_completion = round(($completed_fields / count($fields)) * 100);
        }
        $stats['profile_completion'] = $profile_completion;

        // Son başvurularım
        $my_applications_sql = "SELECT ja.*, jl.title as job_title, jl.slug as job_slug, jl.location_city, jl.budget_min, jl.budget_max, jl.budget_type, u.full_name as employer_name, u.id as employer_id
                               FROM job_applications ja
                               JOIN job_listings jl ON ja.job_id = jl.id
                               JOIN users u ON jl.user_id = u.id
                               WHERE ja.caregiver_id = ?
                               ORDER BY ja.applied_at DESC LIMIT 5";
        $stmt = $db->prepare($my_applications_sql);
        $stmt->execute([$user_id]);
        $my_applications = $stmt->fetchAll();

        // Uygun iş ilanları
        $suitable_jobs_sql = "SELECT jl.*, u.full_name as employer_name
                             FROM job_listings jl
                             JOIN users u ON jl.user_id = u.id
                             WHERE jl.status = 'active'
                             AND (jl.expires_at IS NULL OR jl.expires_at > NOW())
                             AND jl.id NOT IN (SELECT job_id FROM job_applications WHERE caregiver_id = ?)
                             ORDER BY jl.created_at DESC LIMIT 5";
        $stmt = $db->prepare($suitable_jobs_sql);
        $stmt->execute([$user_id]);
        $suitable_jobs = $stmt->fetchAll();

    } catch (PDOException $e) {
        $stats = ['my_applications' => 0, 'pending_applications' => 0, 'accepted_applications' => 0, 'unread_messages' => 0, 'profile_completion' => 0];
        $my_applications = [];
        $suitable_jobs = [];
    }
} elseif ($user_type === 'admin') {
    // Admin istatistikleri
    try {
        $stats['total_users'] = $db->query("SELECT COUNT(*) FROM users WHERE is_active = 1")->fetchColumn();
        $stats['total_caregivers'] = $db->query("SELECT COUNT(*) FROM users WHERE user_type = 'caregiver' AND is_active = 1")->fetchColumn();
        $stats['total_families'] = $db->query("SELECT COUNT(*) FROM users WHERE user_type = 'family' AND is_active = 1")->fetchColumn();
        $stats['total_jobs'] = $db->query("SELECT COUNT(*) FROM job_listings WHERE status = 'active'")->fetchColumn();
        $stats['pending_verifications'] = $db->query("SELECT COUNT(*) FROM users WHERE is_verified = 0 AND is_active = 1")->fetchColumn();
        $stats['total_applications'] = $db->query("SELECT COUNT(*) FROM job_applications")->fetchColumn();
        $stats['total_messages'] = $db->query("SELECT COUNT(*) FROM messages")->fetchColumn();
        $stats['total_reviews'] = $db->query("SELECT COUNT(*) FROM reviews WHERE is_approved = 1")->fetchColumn();

        // Son kayıtlar
        $recent_users_sql = "SELECT id, full_name, email, user_type, created_at FROM users ORDER BY created_at DESC LIMIT 5";
        $stmt = $db->prepare($recent_users_sql);
        $stmt->execute();
        $recent_users = $stmt->fetchAll();

    } catch (PDOException $e) {
        $stats = ['total_users' => 0, 'total_caregivers' => 0, 'total_families' => 0, 'total_jobs' => 0, 'pending_verifications' => 0, 'total_applications' => 0, 'total_messages' => 0, 'total_reviews' => 0];
        $recent_users = [];
    }
}

// Son mesajlar
try {
    $messages_sql = "SELECT m.*, u.full_name as sender_name
                     FROM messages m
                     JOIN users u ON m.sender_id = u.id
                     WHERE m.receiver_id = ? AND m.is_deleted_by_receiver = 0
                     ORDER BY m.created_at DESC LIMIT 5";
    $stmt = $db->prepare($messages_sql);
    $stmt->execute([$user_id]);
    $recent_messages = $stmt->fetchAll();
} catch (PDOException $e) {
    $recent_messages = [];
}

// Bildirimler
try {
    $notifications_sql = "SELECT * FROM notifications WHERE user_id = ? ORDER BY created_at DESC LIMIT 5";
    $stmt = $db->prepare($notifications_sql);
    $stmt->execute([$user_id]);
    $notifications = $stmt->fetchAll();
} catch (PDOException $e) {
    $notifications = [];
}

// Paket bilgisi
$active_package = hasActivePackage($user_id);

$page_title = 'Dashboard';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bakıcı Platform</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c5aa0;
            --secondary-color: #f8f9fa;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
        }

        .sidebar {
            background: linear-gradient(135deg, var(--primary-color) 0%, #1e3d72 100%);
            min-height: 100vh;
            color: white;
            position: fixed;
            top: 0;
            left: 0;
            width: 250px;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            border-radius: 8px;
            margin: 0.25rem 0;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: rgba(255,255,255,0.1);
            transform: translateX(5px);
        }

        .main-content {
            margin-left: 250px;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .stat-card {
            border: none;
            border-radius: 15px;
            padding: 1.5rem;
            height: 100%;
            transition: transform 0.3s ease;
            background: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 15px;
        }

        .progress-circle {
            width: 80px;
            height: 80px;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(44, 90, 160, 0.05);
        }

        .notification-item {
            border-left: 4px solid var(--primary-color);
            background: rgba(44, 90, 160, 0.05);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
        }

        .message-item {
            border-left: 4px solid var(--success-color);
            background: rgba(40, 167, 69, 0.05);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
        }

        .welcome-card {
            background: linear-gradient(135deg, var(--primary-color), #1e3d72);
            color: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .package-info {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body class="bg-light">
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="p-3">
            <h4 class="mb-0">
                <i class="bi bi-heart-fill me-2"></i>Bakıcı Platform
            </h4>
            <small class="text-light">Hoş geldiniz, <?php echo htmlspecialchars($_SESSION['full_name']); ?></small>
        </div>

        <nav class="nav flex-column px-3">
            <a class="nav-link active" href="dashboard.php">
                <i class="bi bi-speedometer2 me-2"></i>Dashboard
            </a>

            <?php if ($user_type === 'family'): ?>
                <a class="nav-link" href="jobs/my-jobs.php">
                    <i class="bi bi-briefcase me-2"></i>İlanlarım
                </a>
                <a class="nav-link" href="jobs/create.php">
                    <i class="bi bi-plus-circle me-2"></i>İlan Ver
                </a>
                <a class="nav-link" href="caregivers.php">
                    <i class="bi bi-search me-2"></i>Bakıcı Ara
                </a>
                <a class="nav-link" href="applications.php">
                    <i class="bi bi-person-check me-2"></i>Başvurular
                </a>
            <?php elseif ($user_type === 'caregiver'): ?>
                <a class="nav-link" href="profile/caregiver.php">
                    <i class="bi bi-person me-2"></i>Profilim
                </a>
                <a class="nav-link" href="jobs.php">
                    <i class="bi bi-search me-2"></i>İş Ara
                </a>
                <a class="nav-link" href="applications/my-applications.php">
                    <i class="bi bi-file-text me-2"></i>Başvurularım
                </a>
                <a class="nav-link" href="reviews.php">
                    <i class="bi bi-star me-2"></i>Değerlendirmelerim
                </a>
            <?php elseif ($user_type === 'admin'): ?>
                <a class="nav-link" href="admin/users.php">
                    <i class="bi bi-people me-2"></i>Kullanıcılar
                </a>
                <a class="nav-link" href="admin/jobs.php">
                    <i class="bi bi-briefcase me-2"></i>İş İlanları
                </a>
                <a class="nav-link" href="admin/applications.php">
                    <i class="bi bi-file-text me-2"></i>Başvurular
                </a>
                <a class="nav-link" href="admin/reviews.php">
                    <i class="bi bi-star me-2"></i>Değerlendirmeler
                </a>
                <a class="nav-link" href="admin/payments.php">
                    <i class="bi bi-credit-card me-2"></i>Ödemeler
                </a>
                <a class="nav-link" href="admin/contact-messages.php">
                    <i class="bi bi-envelope me-2"></i>İletişim Mesajları
                </a>
                <a class="nav-link" href="admin/settings.php">
                    <i class="bi bi-gear me-2"></i>Sistem Ayarları
                </a>
            <?php endif; ?>

            <a class="nav-link" href="messages.php">
                <i class="bi bi-chat-dots me-2"></i>Mesajlar
                <?php if (safeArray($stats, 'unread_messages', 0) > 0): ?>
                    <span class="badge bg-danger ms-auto"><?php echo safeArray($stats, 'unread_messages', 0); ?></span>
                <?php endif; ?>
            </a>
            <a class="nav-link" href="notifications.php">
                <i class="bi bi-bell me-2"></i>Bildirimler
            </a>

            <?php if ($user_type !== 'admin'): ?>
            <a class="nav-link" href="packages.php">
                <i class="bi bi-gift me-2"></i>Paketler
            </a>
            <?php endif; ?>

            <hr class="my-3">

            <a class="nav-link" href="profile.php">
                <i class="bi bi-person-gear me-2"></i>Profil Ayarları
            </a>
            <a class="nav-link" href="help.php">
                <i class="bi bi-question-circle me-2"></i>Yardım
            </a>
            <a class="nav-link" href="auth/logout.php">
                <i class="bi bi-box-arrow-right me-2"></i>Çıkış
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Mobile Menu Button -->
        <button class="btn btn-primary d-md-none mb-3" type="button" id="sidebarToggle">
            <i class="bi bi-list"></i>
        </button>

        <!-- Welcome Card -->
        <div class="welcome-card">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2 class="mb-2">
                        <?php
                        $hour = date('H');
                        if ($hour < 12) echo 'Günaydın';
                        elseif ($hour < 18) echo 'İyi günler';
                        else echo 'İyi akşamlar';
                        ?>, <?php echo htmlspecialchars($_SESSION['full_name']); ?>!
                    </h2>
                    <p class="mb-0">
                        <?php if ($user_type === 'family'): ?>
                            İlanlarınızı yönetin ve güvenilir bakıcılar bulun
                        <?php elseif ($user_type === 'caregiver'): ?>
                            İş fırsatlarını keşfedin ve başvuru yapın
                        <?php else: ?>
                            Sistem yönetim paneline hoş geldiniz
                        <?php endif; ?>
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <?php if ($user_type === 'family'): ?>
                        <a href="jobs/create.php" class="btn btn-light btn-lg">
                            <i class="bi bi-plus-circle me-2"></i>Yeni İlan
                        </a>
                    <?php elseif ($user_type === 'caregiver'): ?>
                        <a href="jobs.php" class="btn btn-light btn-lg">
                            <i class="bi bi-search me-2"></i>İş Ara
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Package Info -->
        <?php if ($active_package && $user_type !== 'admin'): ?>
        <div class="package-info">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h6 class="mb-1">
                        <i class="bi bi-gift me-2"></i>Aktif Paket: <?php echo escape($active_package['name']); ?>
                    </h6>
                    <small>
                        <?php echo formatDate($active_package['expires_at']); ?> tarihine kadar geçerli
                    </small>
                </div>
                <div class="col-md-4 text-end">
                    <a href="packages.php" class="btn btn-outline-light btn-sm">
                        Paket Yönet
                    </a>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Flash Messages -->
        <?php if (hasMessages()): ?>
            <?php foreach (getMessages() as $message): ?>
                <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show" role="alert">
                    <?php echo escape($message['message']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>

        <!-- İstatistik Kartları -->
        <div class="row g-4 mb-4">
            <?php if ($user_type === 'family'): ?>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon bg-primary">
                                <i class="bi bi-briefcase"></i>
                            </div>
                            <div class="ms-3">
                                <h3 class="mb-0"><?php echo safeArray($stats, 'my_jobs', 0); ?></h3>
                                <small class="text-muted">Toplam İlanım</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon bg-success">
                                <i class="bi bi-check-circle"></i>
                            </div>
                            <div class="ms-3">
                                <h3 class="mb-0"><?php echo safeArray($stats, 'active_jobs', 0); ?></h3>
                                <small class="text-muted">Aktif İlan</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon bg-warning">
                                <i class="bi bi-person-check"></i>
                            </div>
                            <div class="ms-3">
                                <h3 class="mb-0"><?php echo safeArray($stats, 'total_applications', 0); ?></h3>
                                <small class="text-muted">Toplam Başvuru</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon bg-info">
                                <i class="bi bi-chat-dots"></i>
                            </div>
                            <div class="ms-3">
                                <h3 class="mb-0"><?php echo safeArray($stats, 'unread_messages', 0); ?></h3>
                                <small class="text-muted">Okunmamış Mesaj</small>
                            </div>
                        </div>
                    </div>
                </div>
            <?php elseif ($user_type === 'caregiver'): ?>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon bg-primary">
                                <i class="bi bi-file-text"></i>
                            </div>
                            <div class="ms-3">
                                <h3 class="mb-0"><?php echo safeArray($stats, 'my_applications', 0); ?></h3>
                                <small class="text-muted">Başvurularım</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon bg-warning">
                                <i class="bi bi-clock"></i>
                            </div>
                            <div class="ms-3">
                                <h3 class="mb-0"><?php echo safeArray($stats, 'pending_applications', 0); ?></h3>
                                <small class="text-muted">Bekleyen</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon bg-success">
                                <i class="bi bi-check-circle"></i>
                            </div>
                            <div class="ms-3">
                                <h3 class="mb-0"><?php echo safeArray($stats, 'accepted_applications', 0); ?></h3>
                                <small class="text-muted">Kabul Edilen</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="d-flex align-items-center">
                            <div class="progress-circle">
                                <svg viewBox="0 0 36 36" class="circular-chart">
                                    <path class="circle-bg" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                                          fill="none" stroke="rgba(23, 162, 184, 0.3)" stroke-width="2"/>
                                    <path class="circle" stroke-dasharray="<?php echo safeArray($stats, 'profile_completion', 0); ?>, 100"
                                          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                                          fill="none" stroke="#17a2b8" stroke-width="2"/>
                                    <text x="18" y="20.35" class="percentage" fill="#17a2b8" font-size="6"><?php echo safeArray($stats, 'profile_completion', 0); ?>%</text>
                                </svg>
                            </div>
                            <div class="ms-3">
                                <small class="text-muted">Profil Tamamlanma</small>
                            </div>
                        </div>
                    </div>
                </div>
            <?php elseif ($user_type === 'admin'): ?>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon bg-primary">
                                <i class="bi bi-people"></i>
                            </div>
                            <div class="ms-3">
                                <h3 class="mb-0"><?php echo safeArray($stats, 'total_users', 0); ?></h3>
                                <small class="text-muted">Toplam Kullanıcı</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon bg-success">
                                <i class="bi bi-heart"></i>
                            </div>
                            <div class="ms-3">
                                <h3 class="mb-0"><?php echo safeArray($stats, 'total_caregivers', 0); ?></h3>
                                <small class="text-muted">Bakıcı</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon bg-warning">
                                <i class="bi bi-briefcase"></i>
                            </div>
                            <div class="ms-3">
                                <h3 class="mb-0"><?php echo safeArray($stats, 'total_jobs', 0); ?></h3>
                                <small class="text-muted">Aktif İlan</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon bg-danger">
                                <i class="bi bi-exclamation-triangle"></i>
                            </div>
                            <div class="ms-3">
                                <h3 class="mb-0"><?php echo safeArray($stats, 'pending_verifications', 0); ?></h3>
                                <small class="text-muted">Bekleyen Onay</small>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Alerts -->
        <?php if (isset($_GET['success'])): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="bi bi-check-circle me-2"></i><?php echo htmlspecialchars($_GET['success']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_GET['error'])): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="bi bi-exclamation-triangle me-2"></i>
                <?php
                $error_messages = [
                    'invalid_data' => 'Geçersiz veri gönderildi.',
                    'application_not_found' => 'Başvuru bulunamadı.',
                    'permission_denied' => 'Bu işlem için yetkiniz yok.',
                    'database_error' => 'Veritabanı hatası oluştu.'
                ];
                echo $error_messages[$_GET['error']] ?? 'Bir hata oluştu.';
                ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Ana İçerik -->
        <div class="row g-4">
            <!-- Sol Kolon -->
            <div class="col-lg-8">
                <?php if ($user_type === 'family' && !empty($recent_applications)): ?>
                    <!-- Son Başvurular -->
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="bi bi-person-check me-2"></i>Son Başvurular</h5>
                            <a href="applications.php" class="btn btn-sm btn-outline-primary">Tümünü Gör</a>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Bakıcı</th>
                                            <th>İlan</th>
                                            <th>Deneyim</th>
                                            <th>Puan</th>
                                            <th>Durum</th>
                                            <th>Tarih</th>
                                            <th>İşlemler</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_applications as $app): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center me-2"
                                                         style="width: 32px; height: 32px; font-size: 0.8rem;">
                                                        <i class="bi bi-person"></i>
                                                    </div>
                                                    <div>
                                                        <strong><?php echo escape($app['caregiver_name']); ?></strong>
                                                        <br><small class="text-muted">Bakıcı</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <a href="job-details.php?id=<?php echo $app['job_id']; ?>" class="text-decoration-none">
                                                    <?php echo escape($app['job_title']); ?>
                                                </a>
                                            </td>
                                            <td><?php echo $app['experience_years'] ?? 0; ?> yıl</td>
                                            <td>
                                                <?php if ($app['rating'] > 0): ?>
                                                    <?php echo getRatingStars($app['rating']); ?>
                                                    <small>(<?php echo number_format($app['rating'], 1); ?>)</small>
                                                <?php else: ?>
                                                    <span class="text-muted">Henüz yok</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php
                                                    echo $app['status'] === 'pending' ? 'warning' :
                                                        ($app['status'] === 'accepted' ? 'success' :
                                                        ($app['status'] === 'rejected' ? 'danger' :
                                                        ($app['status'] === 'viewed' ? 'info' : 'secondary')));
                                                ?>">
                                                    <?php
                                                    $statuses = [
                                                        'pending' => 'Bekliyor',
                                                        'viewed' => 'Görüldü',
                                                        'shortlisted' => 'Kısa Liste',
                                                        'interview' => 'Görüşme',
                                                        'accepted' => 'Kabul',
                                                        'rejected' => 'Red',
                                                        'withdrawn' => 'Geri Çekildi',
                                                        'hired' => 'İşe Alındı'
                                                    ];
                                                    echo $statuses[$app['status']] ?? $app['status'];
                                                    ?>
                                                </span>
                                            </td>
                                            <td><?php echo timeAgo($app['applied_at']); ?></td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" onclick="viewApplication(<?php echo $app['id']; ?>)" title="Görüntüle">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-secondary" onclick="editApplication(<?php echo $app['id']; ?>)" title="Düzenle">
                                                        <i class="bi bi-pencil"></i>
                                                    </button>
                                                    <?php if ($app['status'] === 'pending'): ?>
                                                        <button class="btn btn-outline-success" onclick="acceptApplication(<?php echo $app['id']; ?>)" title="Kabul Et">
                                                            <i class="bi bi-check"></i>
                                                        </button>
                                                        <button class="btn btn-outline-danger" onclick="rejectApplication(<?php echo $app['id']; ?>)" title="Reddet">
                                                            <i class="bi bi-x"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if ($user_type === 'caregiver' && !empty($my_applications)): ?>
                    <!-- Başvurularım -->
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="bi bi-file-text me-2"></i>Son Başvurularım</h5>
                            <a href="applications/my-applications.php" class="btn btn-sm btn-outline-primary">Tümünü Gör</a>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>İş İlanı</th>
                                            <th>İşveren</th>
                                            <th>Lokasyon</th>
                                            <th>Bütçe</th>
                                            <th>Durum</th>
                                            <th>Tarih</th>
                                            <th>İşlemler</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($my_applications as $app): ?>
                                        <tr>
                                            <td>
                                                <a href="job-details.php?id=<?php echo $app['job_id']; ?>" class="text-decoration-none">
                                                    <strong><?php echo escape($app['job_title']); ?></strong>
                                                </a>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center me-2"
                                                         style="width: 32px; height: 32px; font-size: 0.8rem;">
                                                        <i class="bi bi-building"></i>
                                                    </div>
                                                    <?php echo escape($app['employer_name']); ?>
                                                </div>
                                            </td>
                                            <td>
                                                <i class="bi bi-geo-alt text-muted me-1"></i>
                                                <?php echo escape($app['location_city']); ?>
                                            </td>
                                            <td>
                                                <?php if ($app['budget_min'] || $app['budget_max']): ?>
                                                    <?php if ($app['budget_min'] && $app['budget_max']): ?>
                                                        <?php echo formatMoney($app['budget_min']); ?> - <?php echo formatMoney($app['budget_max']); ?>
                                                    <?php elseif ($app['budget_min']): ?>
                                                        <?php echo formatMoney($app['budget_min']); ?>+
                                                    <?php else: ?>
                                                        <?php echo formatMoney($app['budget_max']); ?>'e kadar
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <span class="text-muted">Görüşülür</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php
                                                    echo $app['status'] === 'pending' ? 'warning' :
                                                        ($app['status'] === 'accepted' ? 'success' :
                                                        ($app['status'] === 'rejected' ? 'danger' :
                                                        ($app['status'] === 'viewed' ? 'info' : 'secondary')));
                                                ?>">
                                                    <?php
                                                    $statuses = [
                                                        'pending' => 'Bekliyor',
                                                        'viewed' => 'Görüldü',
                                                        'shortlisted' => 'Kısa Liste',
                                                        'interview' => 'Görüşme',
                                                        'accepted' => 'Kabul',
                                                        'rejected' => 'Red',
                                                        'withdrawn' => 'Geri Çekildi',
                                                        'hired' => 'İşe Alındı'
                                                    ];
                                                    echo $statuses[$app['status']] ?? $app['status'];
                                                    ?>
                                                </span>
                                            </td>
                                            <td><?php echo timeAgo($app['applied_at']); ?></td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" onclick="viewMyApplication(<?php echo $app['id']; ?>)" title="Görüntüle">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                    <?php if (in_array($app['status'], ['pending', 'viewed'])): ?>
                                                        <button class="btn btn-outline-secondary" onclick="editMyApplication(<?php echo $app['id']; ?>)" title="Düzenle">
                                                            <i class="bi bi-pencil"></i>
                                                        </button>
                                                        <button class="btn btn-outline-warning" onclick="withdrawApplication(<?php echo $app['id']; ?>)" title="Geri Çek">
                                                            <i class="bi bi-arrow-counterclockwise"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                    <button class="btn btn-outline-info" onclick="sendMessage(<?php echo $app['employer_id'] ?? 0; ?>)" title="Mesaj Gönder">
                                                        <i class="bi bi-chat-dots"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if ($user_type === 'admin' && !empty($recent_users)): ?>
                    <!-- Son Kayıtlar -->
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="bi bi-people me-2"></i>Son Kayıtlar</h5>
                            <a href="admin/users.php" class="btn btn-sm btn-outline-primary">Tümünü Gör</a>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Ad Soyad</th>
                                            <th>E-posta</th>
                                            <th>Tip</th>
                                            <th>Kayıt Tarihi</th>
                                            <th>İşlem</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_users as $user): ?>
                                        <tr>
                                            <td><?php echo escape($user['full_name']); ?></td>
                                            <td><?php echo escape($user['email']); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $user['user_type'] === 'family' ? 'primary' : ($user['user_type'] === 'caregiver' ? 'success' : 'danger'); ?>">
                                                    <?php echo ucfirst($user['user_type']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo timeAgo($user['created_at']); ?></td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewUserDetails(<?php echo $user['id']; ?>)">
                                                    <i class="bi bi-eye me-1"></i>Görüntüle
                                                </button>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Sağ Kolon -->
            <div class="col-lg-4">
                <!-- Son Mesajlar -->
                <?php if (!empty($recent_messages)): ?>
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0"><i class="bi bi-chat-dots me-2"></i>Son Mesajlar</h6>
                        <a href="messages.php" class="btn btn-sm btn-outline-primary">Tümü</a>
                    </div>
                    <div class="card-body">
                        <?php foreach ($recent_messages as $message): ?>
                        <div class="message-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <strong class="text-success"><?php echo escape($message['sender_name']); ?></strong>
                                    <p class="mb-1 small"><?php echo escape(substr($message['message'], 0, 100)); ?>...</p>
                                    <small class="text-muted"><?php echo timeAgo($message['created_at']); ?></small>
                                </div>
                                <?php if (!$message['is_read']): ?>
                                    <span class="badge bg-danger">Yeni</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Bildirimler -->
                <?php if (!empty($notifications)): ?>
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0"><i class="bi bi-bell me-2"></i>Son Bildirimler</h6>
                        <a href="notifications.php" class="btn btn-sm btn-outline-primary">Tümü</a>
                    </div>
                    <div class="card-body">
                        <?php foreach ($notifications as $notification): ?>
                        <div class="notification-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <strong class="text-primary"><?php echo escape($notification['title']); ?></strong>
                                    <p class="mb-1 small"><?php echo escape($notification['message']); ?></p>
                                    <small class="text-muted"><?php echo timeAgo($notification['created_at']); ?></small>
                                </div>
                                <?php if (!$notification['is_read']): ?>
                                    <span class="badge bg-primary">Yeni</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Hızlı Linkler -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-lightning me-2"></i>Hızlı İşlemler</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <?php if ($user_type === 'family'): ?>
                                <a href="jobs/create.php" class="btn btn-primary">
                                    <i class="bi bi-plus-circle me-2"></i>Yeni İlan Ver
                                </a>
                                <a href="caregivers.php" class="btn btn-outline-primary">
                                    <i class="bi bi-search me-2"></i>Bakıcı Ara
                                </a>
                                <a href="packages.php" class="btn btn-outline-success">
                                    <i class="bi bi-gift me-2"></i>Paket Satın Al
                                </a>
                            <?php elseif ($user_type === 'caregiver'): ?>
                                <a href="jobs.php" class="btn btn-primary">
                                    <i class="bi bi-search me-2"></i>İş Ara
                                </a>
                                <a href="profile/caregiver.php" class="btn btn-outline-primary">
                                    <i class="bi bi-person me-2"></i>Profili Düzenle
                                </a>
                                <a href="packages.php" class="btn btn-outline-success">
                                    <i class="bi bi-gift me-2"></i>Paket Satın Al
                                </a>
                            <?php elseif ($user_type === 'admin'): ?>
                                <a href="admin/users.php" class="btn btn-primary">
                                    <i class="bi bi-people me-2"></i>Kullanıcı Yönetimi
                                </a>
                                <a href="admin/jobs.php" class="btn btn-outline-primary">
                                    <i class="bi bi-briefcase me-2"></i>İlan Yönetimi
                                </a>
                                <a href="admin/settings.php" class="btn btn-outline-secondary">
                                    <i class="bi bi-gear me-2"></i>Sistem Ayarları
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Mobile sidebar toggle
        document.getElementById('sidebarToggle')?.addEventListener('click', function() {
            document.querySelector('.sidebar').classList.toggle('show');
        });

        // Auto-hide alerts
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                if (alert.querySelector('.btn-close')) {
                    alert.querySelector('.btn-close').click();
                }
            });
        }, 5000);

        // Smooth animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe stat cards for animation
        document.querySelectorAll('.stat-card').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(20px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });

        // Real-time clock
        function updateClock() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('tr-TR');
            const clockElement = document.getElementById('clock');
            if (clockElement) {
                clockElement.textContent = timeString;
            }
        }

        setInterval(updateClock, 1000);
        updateClock();

        // Application Management Functions
        function viewApplication(applicationId) {
            // Başvuru detaylarını modal'da göster
            fetch(`application-details.php?id=${applicationId}`)
                .then(response => response.text())
                .then(data => {
                    const modal = new bootstrap.Modal(document.getElementById('applicationModal') || createApplicationModal());
                    document.getElementById('applicationModalContent').innerHTML = data;
                    modal.show();
                })
                .catch(error => {
                    alert('Başvuru bilgileri yüklenirken hata oluştu.');
                });
        }

        function editApplication(applicationId) {
            // Başvuru düzenleme sayfasına yönlendir
            window.location.href = `edit-application.php?id=${applicationId}`;
        }

        function acceptApplication(applicationId) {
            if (confirm('Bu başvuruyu kabul etmek istediğinizden emin misiniz?')) {
                updateApplicationStatus(applicationId, 'accepted');
            }
        }

        function rejectApplication(applicationId) {
            if (confirm('Bu başvuruyu reddetmek istediğinizden emin misiniz?')) {
                updateApplicationStatus(applicationId, 'rejected');
            }
        }

        function viewMyApplication(applicationId) {
            // Kendi başvurumu görüntüle
            window.location.href = `my-application.php?id=${applicationId}`;
        }

        function editMyApplication(applicationId) {
            // Kendi başvurumu düzenle
            window.location.href = `edit-my-application.php?id=${applicationId}`;
        }

        function withdrawApplication(applicationId) {
            if (confirm('Başvurunuzu geri çekmek istediğinizden emin misiniz?')) {
                updateApplicationStatus(applicationId, 'withdrawn');
            }
        }

        function sendMessage(userId) {
            // Mesaj gönderme sayfasına yönlendir
            window.location.href = `send-message.php?to=${userId}`;
        }

        function updateApplicationStatus(applicationId, status) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = 'update-application-status.php';
            form.innerHTML = `
                <input type="hidden" name="application_id" value="${applicationId}">
                <input type="hidden" name="status" value="${status}">
                <input type="hidden" name="redirect" value="dashboard.php">
            `;
            document.body.appendChild(form);
            form.submit();
        }

        function createApplicationModal() {
            const modalHtml = `
                <div class="modal fade" id="applicationModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Başvuru Detayları</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body" id="applicationModalContent">
                                <div class="text-center">
                                    <div class="spinner-border" role="status">
                                        <span class="visually-hidden">Yükleniyor...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            return document.getElementById('applicationModal');
        }

        // User Details Modal Function
        function viewUserDetails(userId) {
            // Kullanıcı detaylarını modal'da göster
            fetch(`admin/user-details.php?id=${userId}`)
                .then(response => response.text())
                .then(data => {
                    const modal = new bootstrap.Modal(document.getElementById('userModal') || createUserModal());
                    document.getElementById('userModalContent').innerHTML = data;
                    modal.show();
                })
                .catch(error => {
                    alert('Kullanıcı bilgileri yüklenirken hata oluştu.');
                });
        }

        function createUserModal() {
            const modalHtml = `
                <div class="modal fade" id="userModal" tabindex="-1">
                    <div class="modal-dialog modal-xl">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Kullanıcı Detayları</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body" id="userModalContent">
                                <div class="text-center">
                                    <div class="spinner-border" role="status">
                                        <span class="visually-hidden">Yükleniyor...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            return document.getElementById('userModal');
        }
    </script>

    <!-- Session Timeout Script -->
    <?php echo getSessionTimeoutScript(); ?>
</body>
</html>
