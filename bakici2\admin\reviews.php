<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Giriş kontrolü
if (!isset($_SESSION['user_id'])) {
    header('Location: ../auth/login.php');
    exit;
}

// Sadece admin kullanıcıları erişebilir
if ($_SESSION['user_type'] !== 'admin') {
    header('Location: ../dashboard.php');
    exit;
}

// CRUD İşlemleri
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $review_id = intval($_POST['review_id'] ?? 0);

    try {
        if ($action === 'delete_review') {
            // Değerlendirmeyi sil (soft delete)
            $sql = "UPDATE reviews SET is_active = 0, updated_at = NOW() WHERE id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$review_id]);
            $success = 'Değerlendirme silindi.';

        } elseif ($action === 'toggle_status') {
            // Değerlendirme durumunu değiştir
            $sql = "UPDATE reviews SET is_active = CASE WHEN is_active = 1 THEN 0 ELSE 1 END, updated_at = NOW() WHERE id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$review_id]);
            $success = 'Değerlendirme durumu güncellendi.';
        }
    } catch (PDOException $e) {
        $error = 'İşlem sırasında hata oluştu: ' . $e->getMessage();
    }
}

// Değerlendirmeleri getir
try {
    $sql = "SELECT r.*,
                   reviewer.full_name as reviewer_name, reviewer.email as reviewer_email,
                   reviewed.full_name as reviewed_name, reviewed.email as reviewed_email
            FROM reviews r
            LEFT JOIN users reviewer ON r.reviewer_id = reviewer.id
            LEFT JOIN users reviewed ON r.reviewed_id = reviewed.id
            WHERE r.is_active = 1
            ORDER BY r.created_at DESC
            LIMIT 100";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $reviews = $stmt->fetchAll();

    // İstatistikler
    $stats = [
        'total' => $db->query("SELECT COUNT(*) FROM reviews")->fetchColumn(),
        'excellent' => $db->query("SELECT COUNT(*) FROM reviews WHERE rating >= 4")->fetchColumn(),
        'good' => $db->query("SELECT COUNT(*) FROM reviews WHERE rating = 3")->fetchColumn(),
        'poor' => $db->query("SELECT COUNT(*) FROM reviews WHERE rating <= 2")->fetchColumn()
    ];

    // Ortalama puan
    $avg_rating = $db->query("SELECT AVG(rating) FROM reviews")->fetchColumn();
    $stats['average'] = $avg_rating ? round($avg_rating, 1) : 0;

} catch (PDOException $e) {
    $reviews = [];
    $stats = ['total' => 0, 'excellent' => 0, 'good' => 0, 'poor' => 0, 'average' => 0];
    error_log("Admin reviews query error: " . $e->getMessage());
}

$page_title = 'Değerlendirme Yönetimi';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Admin Panel</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="../index.php">
                <i class="bi bi-heart-fill me-2"></i>Bakıcı Platform - Admin
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../dashboard.php">Dashboard</a>
                <a class="nav-link" href="settings.php">Ayarlar</a>
                <a class="nav-link" href="../auth/logout.php">Çıkış</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="bi bi-star me-2"></i>Değerlendirme Yönetimi</h4>
                    </div>
                    <div class="card-body">
                        <!-- Alerts -->
                        <?php if (isset($success)): ?>
                            <div class="alert alert-success alert-dismissible fade show">
                                <i class="bi bi-check-circle me-2"></i><?php echo $success; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($error)): ?>
                            <div class="alert alert-danger alert-dismissible fade show">
                                <i class="bi bi-exclamation-triangle me-2"></i><?php echo $error; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <!-- İstatistikler -->
                        <div class="row g-3 mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h3><?php echo $stats['total']; ?></h3>
                                        <small>Toplam Değerlendirme</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h3><?php echo $stats['excellent']; ?></h3>
                                        <small>Mükemmel (4-5★)</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <h3><?php echo $stats['good']; ?></h3>
                                        <small>İyi (3★)</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h3><?php echo $stats['average']; ?>★</h3>
                                        <small>Ortalama Puan</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Değerlendirme Listesi -->
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Değerlendiren</th>
                                        <th>Değerlendirilen</th>
                                        <th>Hizmet Türü</th>
                                        <th>Puan</th>
                                        <th>Yorum</th>
                                        <th>Tarih</th>
                                        <th>İşlemler</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($reviews as $review): ?>
                                    <tr>
                                        <td><?php echo $review['id']; ?></td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($review['reviewer_name']); ?></strong>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($review['reviewer_email']); ?></small>
                                        </td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($review['reviewed_name']); ?></strong>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($review['reviewed_email']); ?></small>
                                        </td>
                                        <td>
                                            <?php if ($review['service_type']): ?>
                                                <?php
                                                $service_types = [
                                                    'child_care' => 'Çocuk Bakımı',
                                                    'elderly_care' => 'Yaşlı Bakımı',
                                                    'patient_care' => 'Hasta Bakımı',
                                                    'house_cleaning' => 'Ev Temizliği',
                                                    'companion' => 'Refakatçi',
                                                    'other' => 'Diğer'
                                                ];
                                                echo $service_types[$review['service_type']] ?? $review['service_type'];
                                                ?>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span class="me-2"><?php echo $review['rating']; ?></span>
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <?php if ($i <= $review['rating']): ?>
                                                        <i class="bi bi-star-fill text-warning"></i>
                                                    <?php else: ?>
                                                        <i class="bi bi-star text-muted"></i>
                                                    <?php endif; ?>
                                                <?php endfor; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if ($review['comment']): ?>
                                                <span title="<?php echo htmlspecialchars($review['comment']); ?>">
                                                    <?php echo htmlspecialchars(substr($review['comment'], 0, 50)); ?>
                                                    <?php if (strlen($review['comment']) > 50): ?>...<?php endif; ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="text-muted">Yorum yok</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small><?php echo date('d.m.Y H:i', strtotime($review['created_at'])); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="viewReview(<?php echo $review['id']; ?>)">
                                                    <i class="bi bi-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="deleteReview(<?php echo $review['id']; ?>)">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <?php if (empty($reviews)): ?>
                            <div class="text-center py-4">
                                <i class="bi bi-star text-muted" style="font-size: 3rem;"></i>
                                <h5 class="text-muted mt-3">Henüz değerlendirme yok</h5>
                                <p class="text-muted">Kullanıcılar değerlendirme yapmaya başladığında burada görünecek.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function viewReview(reviewId) {
            // Modal ile değerlendirme detaylarını göster
            fetch(`review-details.php?id=${reviewId}`)
                .then(response => response.text())
                .then(data => {
                    const modal = new bootstrap.Modal(document.getElementById('reviewModal') || createReviewModal());
                    document.getElementById('reviewModalContent').innerHTML = data;
                    modal.show();
                })
                .catch(error => {
                    alert('Değerlendirme bilgileri yüklenirken hata oluştu.');
                });
        }

        function createReviewModal() {
            const modalHtml = `
                <div class="modal fade" id="reviewModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Değerlendirme Detayları</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body" id="reviewModalContent">
                                <div class="text-center">
                                    <div class="spinner-border" role="status">
                                        <span class="visually-hidden">Yükleniyor...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            return document.getElementById('reviewModal');
        }

        function deleteReview(reviewId) {
            if (confirm('Bu değerlendirmeyi silmek istediğinizden emin misiniz?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_review">
                    <input type="hidden" name="review_id" value="${reviewId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function toggleReviewStatus(reviewId) {
            if (confirm('Bu değerlendirmenin durumunu değiştirmek istediğinizden emin misiniz?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="toggle_status">
                    <input type="hidden" name="review_id" value="${reviewId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
