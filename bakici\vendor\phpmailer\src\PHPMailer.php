<?php
/**
 * PHPMailer - PHP email creation and transport class.
 * Basitleştirilmiş versiyon - Gerçek projede tam PHPMailer kullanın
 */

namespace PHPMailer\PHPMailer;

use Exception;

class PHPMailer
{
    const ENCRYPTION_STARTTLS = 'tls';
    const ENCRYPTION_SMTPS = 'ssl';
    
    public $Host = '';
    public $Port = 587;
    public $SMTPAuth = true;
    public $Username = '';
    public $Password = '';
    public $SMTPSecure = 'tls';
    public $From = '';
    public $FromName = '';
    public $Subject = '';
    public $Body = '';
    public $isHTML = true;
    public $CharSet = 'UTF-8';
    
    private $to = [];
    private $mailer = 'smtp';
    
    public function isSMTP()
    {
        $this->mailer = 'smtp';
    }
    
    public function addAddress($address, $name = '')
    {
        $this->to[] = ['address' => $address, 'name' => $name];
    }
    
    public function setFrom($address, $name = '')
    {
        $this->From = $address;
        $this->FromName = $name;
    }

    public function isHTML($isHtml = true)
    {
        $this->isHTML = $isHtml;
    }
    
    public function send()
    {
        try {
            $this->logEmailAttempt();

            // Geliştirme ortamı kontrolü
            $is_development = (strpos($_SERVER['HTTP_HOST'] ?? '', 'localhost') !== false);

            if ($is_development) {
                // Geliştirme ortamında sadece log'a yaz
                foreach ($this->to as $recipient) {
                    $this->sendToRecipient($recipient);
                }
                return true;
            } else {
                // Canlı ortamda gerçek SMTP gönderimi
                return $this->sendViaSMTP();
            }

        } catch (Exception $e) {
            error_log("PHPMailer Error: " . $e->getMessage());
            return false;
        }
    }

    private function sendViaSMTP()
    {
        // Gerçek SMTP gönderimi için PHP'nin mail() fonksiyonunu kullan
        // Daha gelişmiş projede socket bağlantısı yapılabilir

        $headers = [
            'MIME-Version: 1.0',
            'Content-Type: text/html; charset=' . $this->CharSet,
            'From: ' . $this->FromName . ' <' . $this->From . '>',
            'Reply-To: ' . $this->From,
            'X-Mailer: Custom PHPMailer',
            'X-SMTP-Server: ' . $this->Host
        ];

        foreach ($this->to as $recipient) {
            $result = mail(
                $recipient['address'],
                $this->Subject,
                $this->Body,
                implode("\r\n", $headers)
            );

            if (!$result) {
                error_log("Failed to send email to: " . $recipient['address']);
                return false;
            }

            error_log("Email sent successfully to: " . $recipient['address']);
        }

        return true;
    }
    
    private function logEmailAttempt()
    {
        $log = "\n=== PHPMailer SMTP Email ===\n";
        $log .= "Date: " . date('Y-m-d H:i:s') . "\n";
        $log .= "SMTP Host: " . $this->Host . ":" . $this->Port . "\n";
        $log .= "SMTP User: " . $this->Username . "\n";
        $log .= "SMTP Security: " . $this->SMTPSecure . "\n";
        $log .= "From: " . $this->FromName . " <" . $this->From . ">\n";
        $log .= "Subject: " . $this->Subject . "\n";
        $log .= "Recipients: " . count($this->to) . "\n";
        $log .= "===========================\n";
        
        error_log($log);
    }
    
    private function sendToRecipient($recipient)
    {
        $log = "Sending to: " . $recipient['name'] . " <" . $recipient['address'] . ">\n";
        $log .= "Body length: " . strlen($this->Body) . " characters\n";
        
        error_log($log);
        
        // Email dosyasını kaydet
        $this->saveEmailToFile($recipient);
        
        return true;
    }
    
    private function saveEmailToFile($recipient)
    {
        $emailDir = dirname(__DIR__, 3) . '/logs/emails';
        if (!is_dir($emailDir)) {
            mkdir($emailDir, 0755, true);
        }
        
        $filename = 'email_' . date('Y-m-d_H-i-s') . '_' . md5($recipient['address']) . '.html';
        $filepath = $emailDir . '/' . $filename;
        
        $emailContent = $this->generateEmailFile($recipient);
        file_put_contents($filepath, $emailContent);
        
        error_log("Email saved to: " . $filepath);
    }
    
    private function generateEmailFile($recipient)
    {
        $html = "<!DOCTYPE html>\n";
        $html .= "<html>\n<head>\n";
        $html .= "<meta charset='UTF-8'>\n";
        $html .= "<title>Email Log - " . htmlspecialchars($this->Subject) . "</title>\n";
        $html .= "<style>\n";
        $html .= "body { font-family: Arial, sans-serif; margin: 20px; }\n";
        $html .= ".email-header { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }\n";
        $html .= ".email-body { border: 1px solid #ddd; padding: 20px; border-radius: 5px; }\n";
        $html .= "</style>\n";
        $html .= "</head>\n<body>\n";
        
        $html .= "<div class='email-header'>\n";
        $html .= "<h2>Email Log - PHPMailer</h2>\n";
        $html .= "<p><strong>Date:</strong> " . date('Y-m-d H:i:s') . "</p>\n";
        $html .= "<p><strong>To:</strong> " . htmlspecialchars($recipient['name']) . " &lt;" . htmlspecialchars($recipient['address']) . "&gt;</p>\n";
        $html .= "<p><strong>From:</strong> " . htmlspecialchars($this->FromName) . " &lt;" . htmlspecialchars($this->From) . "&gt;</p>\n";
        $html .= "<p><strong>Subject:</strong> " . htmlspecialchars($this->Subject) . "</p>\n";
        $html .= "<p><strong>SMTP:</strong> " . htmlspecialchars($this->Host) . ":" . $this->Port . " (" . $this->SMTPSecure . ")</p>\n";
        $html .= "</div>\n";
        
        $html .= "<div class='email-body'>\n";
        $html .= $this->Body;
        $html .= "</div>\n";
        
        $html .= "</body>\n</html>";
        
        return $html;
    }
    
    public function clearAddresses()
    {
        $this->to = [];
    }
}
?>
