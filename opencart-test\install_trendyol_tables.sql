-- Trendyol E-Faturam Modülü Veritabanı Tabloları
-- OpenCart 3.0.3.2 için

USE opencart_test;

-- <PERSON><PERSON> kayıtları tablosu
CREATE TABLE IF NOT EXISTS `oc_trendyol_efaturam_invoices` (
    `invoice_id` int(11) NOT NULL AUTO_INCREMENT,
    `order_id` int(11) NOT NULL,
    `invoice_number` varchar(50) NOT NULL,
    `invoice_type` enum('e-invoice','e-archive') NOT NULL,
    `status` varchar(20) NOT NULL DEFAULT 'pending',
    `api_response` text,
    `error_message` text,
    `retry_count` int(3) NOT NULL DEFAULT 0,
    `date_created` datetime NOT NULL,
    `date_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`invoice_id`),
    KEY `order_id` (`order_id`),
    KEY `invoice_number` (`invoice_number`),
    KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Log kayıtları tablosu
CREATE TABLE IF NOT EXISTS `oc_trendyol_efaturam_logs` (
    `log_id` int(11) NOT NULL AUTO_INCREMENT,
    `order_id` int(11) DEFAULT NULL,
    `invoice_id` int(11) DEFAULT NULL,
    `log_level` enum('info','warning','error','debug') NOT NULL DEFAULT 'info',
    `message` text NOT NULL,
    `data` longtext,
    `ip_address` varchar(45),
    `user_agent` varchar(255),
    `date_created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`log_id`),
    KEY `order_id` (`order_id`),
    KEY `invoice_id` (`invoice_id`),
    KEY `log_level` (`log_level`),
    KEY `date_created` (`date_created`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Modül ayarları tablosu
CREATE TABLE IF NOT EXISTS `oc_trendyol_efaturam_settings` (
    `setting_id` int(11) NOT NULL AUTO_INCREMENT,
    `key` varchar(100) NOT NULL,
    `value` text,
    `serialized` tinyint(1) NOT NULL DEFAULT 0,
    `date_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`setting_id`),
    UNIQUE KEY `key` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Başarı mesajı
SELECT 'Trendyol E-Faturam modül tabloları başarıyla oluşturuldu!' as message;
