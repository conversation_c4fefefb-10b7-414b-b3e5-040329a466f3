<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Giriş kontrolü
if (!isLoggedIn()) {
    http_response_code(403);
    exit('Unauthorized');
}

// Sadece admin kullanıcıları erişebilir
if ($_SESSION['user_type'] !== 'admin') {
    http_response_code(403);
    exit('Forbidden');
}

$message_id = intval($_GET['id'] ?? 0);
if (!$message_id) {
    exit('Geçersiz mesaj ID');
}

// Mesaj bilgilerini getir
try {
    $sql = "SELECT * FROM contact_messages WHERE id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$message_id]);
    $message = $stmt->fetch();

    if (!$message) {
        exit('Mesaj bulunamadı');
    }

    // Mesajı okundu olarak işaretle
    if ($message['status'] === 'new') {
        $update_sql = "UPDATE contact_messages SET status = 'read', updated_at = NOW() WHERE id = ?";
        $update_stmt = $db->prepare($update_sql);
        $update_stmt->execute([$message_id]);
        $message['status'] = 'read';
    }

} catch (PDOException $e) {
    exit('Veritabanı hatası: ' . $e->getMessage());
}

// Admin notu ekleme
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['admin_note'])) {
    $admin_note = trim($_POST['admin_note']);
    try {
        $sql = "UPDATE contact_messages SET admin_notes = ?, updated_at = NOW() WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$admin_note, $message_id]);
        $note_success = 'Admin notu başarıyla eklendi.';

        // Mesaj verilerini yeniden yükle
        $sql = "SELECT * FROM contact_messages WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$message_id]);
        $message = $stmt->fetch();

    } catch (PDOException $e) {
        $note_error = 'Not eklenirken hata oluştu: ' . $e->getMessage();
    }
}

$status_colors = [
    'new' => 'danger',
    'read' => 'warning',
    'replied' => 'success',
    'closed' => 'secondary'
];

$status_names = [
    'new' => 'Yeni',
    'read' => 'Okundu',
    'replied' => 'Yanıtlandı',
    'closed' => 'Kapatıldı'
];
?>

<!-- Alert Messages -->
<?php if (isset($note_success)): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="bi bi-check-circle me-2"></i><?php echo $note_success; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($note_error)): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="bi bi-exclamation-triangle me-2"></i><?php echo $note_error; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-md-8">
        <!-- Mesaj İçeriği -->
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="bi bi-envelope me-2"></i>
                        <?php echo htmlspecialchars($message['subject']); ?>
                    </h6>
                    <span class="badge bg-<?php echo $status_colors[$message['status']]; ?>">
                        <?php echo $status_names[$message['status']]; ?>
                    </span>
                </div>
            </div>
            <div class="card-body">
                <div class="message-content">
                    <?php echo nl2br(htmlspecialchars($message['message'])); ?>
                </div>
                
                <hr>
                
                <div class="message-meta">
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">
                                <i class="bi bi-calendar me-1"></i>
                                <strong>Gönderilme:</strong> <?php echo date('d.m.Y H:i', strtotime($message['created_at'])); ?>
                            </small>
                        </div>
                        <div class="col-md-6">
                            <?php if ($message['updated_at'] && $message['updated_at'] !== $message['created_at']): ?>
                                <small class="text-muted">
                                    <i class="bi bi-clock me-1"></i>
                                    <strong>Güncellenme:</strong> <?php echo date('d.m.Y H:i', strtotime($message['updated_at'])); ?>
                                </small>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Admin Notu -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-sticky me-2"></i>Admin Notu
                </h6>
            </div>
            <div class="card-body">
                <?php if ($message['admin_notes']): ?>
                    <div class="alert alert-info">
                        <h6><i class="bi bi-info-circle me-2"></i>Mevcut Not</h6>
                        <p class="mb-0"><?php echo nl2br(htmlspecialchars($message['admin_notes'])); ?></p>
                    </div>
                <?php endif; ?>
                
                <form method="POST" id="adminNoteForm">
                    <div class="mb-3">
                        <label for="admin_note" class="form-label">
                            <?php echo $message['admin_notes'] ? 'Notu Güncelle' : 'Not Ekle'; ?>
                        </label>
                        <textarea class="form-control" id="admin_note" name="admin_note" rows="4"
                                  placeholder="Admin notu ekleyin..."><?php echo htmlspecialchars($message['admin_notes'] ?? ''); ?></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary" id="saveNoteBtn">
                        <i class="bi bi-save me-2"></i>
                        <?php echo $message['admin_notes'] ? 'Notu Güncelle' : 'Not Ekle'; ?>
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- Gönderen Bilgileri -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-person me-2"></i>Gönderen Bilgileri
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" 
                         style="width: 60px; height: 60px;">
                        <i class="bi bi-person"></i>
                    </div>
                    <h6 class="mt-2 mb-1"><?php echo htmlspecialchars($message['name']); ?></h6>
                </div>
                
                <div class="contact-info">
                    <div class="mb-2">
                        <i class="bi bi-envelope me-2 text-muted"></i>
                        <a href="mailto:<?php echo $message['email']; ?>" class="text-decoration-none">
                            <?php echo htmlspecialchars($message['email']); ?>
                        </a>
                    </div>
                    
                    <?php if ($message['phone']): ?>
                        <div class="mb-2">
                            <i class="bi bi-telephone me-2 text-muted"></i>
                            <a href="tel:<?php echo $message['phone']; ?>" class="text-decoration-none">
                                <?php echo htmlspecialchars($message['phone']); ?>
                            </a>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (isset($message['company']) && $message['company']): ?>
                        <div class="mb-2">
                            <i class="bi bi-building me-2 text-muted"></i>
                            <?php echo htmlspecialchars($message['company']); ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <hr>
                
                <div class="d-grid gap-2">
                    <a href="mailto:<?php echo $message['email']; ?>?subject=Re: <?php echo urlencode($message['subject']); ?>" 
                       class="btn btn-primary btn-sm">
                        <i class="bi bi-reply me-2"></i>E-posta Gönder
                    </a>
                    
                    <?php if ($message['phone']): ?>
                        <a href="tel:<?php echo $message['phone']; ?>" class="btn btn-success btn-sm">
                            <i class="bi bi-telephone me-2"></i>Ara
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Mesaj Durumu -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-flag me-2"></i>Mesaj Durumu
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <span class="badge bg-<?php echo $status_colors[$message['status']]; ?> fs-6">
                        <?php echo $status_names[$message['status']]; ?>
                    </span>
                </div>
                
                <div class="d-grid gap-2">
                    <?php if ($message['status'] === 'new' || $message['status'] === 'read'): ?>
                        <button class="btn btn-success btn-sm" onclick="markAsReplied(<?php echo $message['id']; ?>)">
                            <i class="bi bi-reply me-2"></i>Yanıtlandı İşaretle
                        </button>
                    <?php endif; ?>
                    
                    <?php if ($message['status'] !== 'closed'): ?>
                        <button class="btn btn-secondary btn-sm" onclick="closeMessage(<?php echo $message['id']; ?>)">
                            <i class="bi bi-x-circle me-2"></i>Mesajı Kapat
                        </button>
                    <?php endif; ?>
                    
                    <?php if ($message['status'] === 'closed'): ?>
                        <button class="btn btn-warning btn-sm" onclick="reopenMessage(<?php echo $message['id']; ?>)">
                            <i class="bi bi-arrow-clockwise me-2"></i>Tekrar Aç
                        </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Mesaj İstatistikleri -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-graph-up me-2"></i>Mesaj Bilgileri
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>Mesaj ID:</strong></td>
                        <td>#<?php echo $message['id']; ?></td>
                    </tr>
                    <tr>
                        <td><strong>Konu:</strong></td>
                        <td><?php echo htmlspecialchars($message['subject']); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Karakter Sayısı:</strong></td>
                        <td><?php echo strlen($message['message']); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Kelime Sayısı:</strong></td>
                        <td><?php echo str_word_count($message['message']); ?></td>
                    </tr>
                    <tr>
                        <td><strong>IP Adresi:</strong></td>
                        <td>
                            <?php if (isset($message['ip_address']) && $message['ip_address']): ?>
                                <code><?php echo htmlspecialchars($message['ip_address']); ?></code>
                            <?php else: ?>
                                <span class="text-muted">-</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="d-flex justify-content-between">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                <i class="bi bi-x me-2"></i>Kapat
            </button>
            <div>
                <a href="mailto:<?php echo $message['email']; ?>?subject=Re: <?php echo urlencode($message['subject']); ?>" 
                   class="btn btn-primary">
                    <i class="bi bi-reply me-2"></i>E-posta ile Yanıtla
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// Admin notu AJAX formu
document.getElementById('adminNoteForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const saveBtn = document.getElementById('saveNoteBtn');
    const originalText = saveBtn.innerHTML;

    // Loading state
    saveBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Kaydediliyor...';
    saveBtn.disabled = true;

    fetch(window.location.href, {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(data => {
        // Başarı mesajı göster
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-success alert-dismissible fade show';
        alertDiv.innerHTML = `
            <i class="bi bi-check-circle me-2"></i>Admin notu başarıyla kaydedildi.
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Alert'i form'un üstüne ekle
        const form = document.getElementById('adminNoteForm');
        form.parentNode.insertBefore(alertDiv, form);

        // 3 saniye sonra alert'i kaldır
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 3000);

        // Button'u eski haline getir
        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
    })
    .catch(error => {
        // Hata mesajı göster
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger alert-dismissible fade show';
        alertDiv.innerHTML = `
            <i class="bi bi-exclamation-triangle me-2"></i>Not kaydedilirken hata oluştu.
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const form = document.getElementById('adminNoteForm');
        form.parentNode.insertBefore(alertDiv, form);

        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 3000);

        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
    });
});

function markAsReplied(messageId) {
    if (confirm('Bu mesajı yanıtlandı olarak işaretlemek istediğinizden emin misiniz?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'contact-messages.php';
        form.innerHTML = `
            <input type="hidden" name="action" value="mark_replied">
            <input type="hidden" name="message_id" value="${messageId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function closeMessage(messageId) {
    if (confirm('Bu mesajı kapatmak istediğinizden emin misiniz?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'contact-messages.php';
        form.innerHTML = `
            <input type="hidden" name="action" value="close">
            <input type="hidden" name="message_id" value="${messageId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function reopenMessage(messageId) {
    if (confirm('Bu mesajı tekrar açmak istediğinizden emin misiniz?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'contact-messages.php';
        form.innerHTML = `
            <input type="hidden" name="action" value="mark_read">
            <input type="hidden" name="message_id" value="${messageId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
