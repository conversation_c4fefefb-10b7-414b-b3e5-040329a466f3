<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Giriş kontrolü
if (!isset($_SESSION['user_id'])) {
    header('Location: auth/login.php');
    exit;
}

$page_title = 'Ödeme Başarısız';
$payment_info = null;

// URL'den order_id al
$order_id = $_GET['order_id'] ?? '';

if ($order_id) {
    try {
        // Ödeme bilgilerini getir
        $sql = "SELECT p.*, u.full_name, u.email 
                FROM payments p 
                LEFT JOIN users u ON p.user_id = u.id 
                WHERE p.order_id = ? AND p.user_id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$order_id, $_SESSION['user_id']]);
        $payment_info = $stmt->fetch();
        
        // Ödeme durumunu failed olarak güncelle
        if ($payment_info) {
            $update_sql = "UPDATE payments SET status = 'failed', updated_at = NOW() WHERE id = ?";
            $update_stmt = $db->prepare($update_sql);
            $update_stmt->execute([$payment_info['id']]);
        }
        
    } catch (PDOException $e) {
        error_log("Payment fail page error: " . $e->getMessage());
    }
}
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bakıcı Platform</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c5aa0;
        }
        
        .fail-icon {
            font-size: 4rem;
            color: #dc3545;
        }
        
        .payment-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="index.php">
                <i class="bi bi-heart-fill me-2"></i>Bakıcı Platform
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.php">Dashboard</a>
                <a class="nav-link" href="packages.php">Paketler</a>
                <a class="nav-link" href="auth/logout.php">Çıkış</a>
            </div>
        </div>
    </nav>

    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="payment-card card">
                    <div class="card-body text-center p-5">
                        <!-- Hata İkonu -->
                        <div class="fail-icon mb-4">
                            <i class="bi bi-x-circle-fill"></i>
                        </div>
                        
                        <h2 class="text-danger fw-bold mb-3">Ödeme Başarısız!</h2>
                        <p class="text-muted mb-4">Paket satın alma işleminiz tamamlanamadı.</p>
                        
                        <?php if ($payment_info): ?>
                            <!-- Ödeme Detayları -->
                            <div class="row g-3 mb-4">
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6 class="card-title">Paket Bilgileri</h6>
                                            <p class="card-text">
                                                <strong><?php echo ucfirst($payment_info['package_type']); ?> Paket</strong><br>
                                                <span class="text-muted">₺<?php echo number_format($payment_info['amount'], 2); ?></span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6 class="card-title">Sipariş Bilgileri</h6>
                                            <p class="card-text">
                                                <strong>Sipariş No:</strong><br>
                                                <code><?php echo htmlspecialchars($payment_info['order_id']); ?></code>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Hata Nedenleri -->
                        <div class="alert alert-danger">
                            <h6><i class="bi bi-exclamation-triangle me-2"></i>Olası Nedenler</h6>
                            <ul class="list-unstyled mb-0 text-start">
                                <li><i class="bi bi-dot"></i> Kredi kartı bilgileri hatalı</li>
                                <li><i class="bi bi-dot"></i> Yetersiz bakiye</li>
                                <li><i class="bi bi-dot"></i> Banka tarafından işlem reddedildi</li>
                                <li><i class="bi bi-dot"></i> İnternet bağlantısı sorunu</li>
                                <li><i class="bi bi-dot"></i> Güvenlik kodu hatalı</li>
                            </ul>
                        </div>
                        
                        <!-- Çözüm Önerileri -->
                        <div class="alert alert-info">
                            <h6><i class="bi bi-lightbulb me-2"></i>Ne Yapabilirsiniz?</h6>
                            <ul class="list-unstyled mb-0 text-start">
                                <li><i class="bi bi-check2"></i> Kart bilgilerinizi kontrol edin</li>
                                <li><i class="bi bi-check2"></i> Farklı bir kart deneyin</li>
                                <li><i class="bi bi-check2"></i> Bankanızla iletişime geçin</li>
                                <li><i class="bi bi-check2"></i> Daha sonra tekrar deneyin</li>
                            </ul>
                        </div>
                        
                        <!-- Aksiyon Butonları -->
                        <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                            <a href="packages.php" class="btn btn-primary btn-lg">
                                <i class="bi bi-arrow-clockwise me-2"></i>Tekrar Dene
                            </a>
                            <a href="dashboard.php" class="btn btn-outline-primary btn-lg">
                                <i class="bi bi-speedometer2 me-2"></i>Dashboard'a Git
                            </a>
                        </div>
                        
                        <!-- Destek Bilgisi -->
                        <div class="mt-4 pt-4 border-top">
                            <p class="text-muted small">
                                <i class="bi bi-headset me-2"></i>
                                Sorun devam ederse <a href="mailto:<EMAIL>"><EMAIL></a> adresinden bize ulaşın.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
