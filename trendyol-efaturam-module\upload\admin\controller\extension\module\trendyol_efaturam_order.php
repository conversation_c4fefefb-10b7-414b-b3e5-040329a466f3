<?php
/**
 * Trendyol E-Faturam Order Integration
 * Sipariş sayfalarına fatura işlemleri entegrasyonu
 */

class ControllerExtensionModuleTrendyolEfaturamOrder extends Controller {
    
    public function createInvoice() {
        $this->load->language('extension/module/trendyol_efaturam');
        
        $json = array();
        
        if (!$this->user->hasPermission('modify', 'extension/module/trendyol_efaturam')) {
            $json['error'] = $this->language->get('error_permission');
        }
        
        if (!isset($this->request->get['order_id'])) {
            $json['error'] = 'Sipariş ID gerekli!';
        }
        
        if (!$json) {
            $order_id = (int)$this->request->get['order_id'];
            
            // Sipariş bilgilerini al
            $this->load->model('sale/order');
            $order_info = $this->model_sale_order->getOrder($order_id);
            
            if (!$order_info) {
                $json['error'] = 'Sipariş bulunamadı!';
            } else {
                // Daha önce fatura kesilmiş mi kontrol et
                $this->load->model('extension/module/trendyol_efaturam');
                $existing_invoice = $this->model_extension_module_trendyol_efaturam->getInvoiceByOrderId($order_id);
                
                if ($existing_invoice) {
                    $json['error'] = 'Bu sipariş için zaten fatura kesilmiş! Fatura No: ' . $existing_invoice['invoice_number'];
                } else {
                    // Fatura oluştur
                    $result = $this->createInvoiceForOrder($order_info);
                    
                    if ($result['success']) {
                        $json['success'] = 'Fatura başarıyla oluşturuldu! Fatura No: ' . $result['invoice_number'];
                        $json['invoice_data'] = $result['data'];
                    } else {
                        $json['error'] = 'Fatura oluşturulamadı: ' . $result['message'];
                    }
                }
            }
        }
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }
    
    public function getInvoiceStatus() {
        $this->load->language('extension/module/trendyol_efaturam');
        
        $json = array();
        
        if (!isset($this->request->get['order_id'])) {
            $json['error'] = 'Sipariş ID gerekli!';
        } else {
            $order_id = (int)$this->request->get['order_id'];
            
            $this->load->model('extension/module/trendyol_efaturam');
            $invoice = $this->model_extension_module_trendyol_efaturam->getInvoiceByOrderId($order_id);
            
            if ($invoice) {
                $json['success'] = true;
                $json['invoice'] = $invoice;
                
                // API'den güncel durumu al
                if ($invoice['invoice_uuid']) {
                    $this->load->library('trendyol_efaturam_api');
                    $api = new TrendyolEfaturamApi($this->getApiConfig());
                    $status_result = $api->getInvoiceStatus($invoice['invoice_uuid']);
                    
                    if ($status_result['success']) {
                        $json['api_status'] = $status_result['data'];
                    }
                }
            } else {
                $json['error'] = 'Bu sipariş için fatura bulunamadı!';
            }
        }
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }
    
    private function createInvoiceForOrder($order_info) {
        try {
            // API konfigürasyonunu al
            $api_config = $this->getApiConfig();
            
            if (!$api_config['username'] || !$api_config['password']) {
                return array(
                    'success' => false,
                    'message' => 'API bilgileri yapılandırılmamış!'
                );
            }
            
            // Sipariş ürünlerini al
            $this->load->model('sale/order');
            $order_products = $this->model_sale_order->getOrderProducts($order_info['order_id']);
            
            // API kütüphanesini yükle
            $this->load->library('trendyol_efaturam_api');
            $api = new TrendyolEfaturamApi($api_config);
            
            // Sipariş verilerini fatura formatına çevir
            $order_data = array_merge($order_info, array('products' => $order_products));
            $invoice_data = $api->formatOrderData($order_data);
            
            // Fatura verilerini doğrula
            $validation_errors = $api->validateInvoiceData($invoice_data);
            if (!empty($validation_errors)) {
                return array(
                    'success' => false,
                    'message' => 'Fatura verileri geçersiz: ' . implode(', ', $validation_errors)
                );
            }
            
            // Faturayı API'ye gönder
            $result = $api->createInvoice($invoice_data);
            
            // Veritabanına kaydet
            $this->load->model('extension/module/trendyol_efaturam');
            
            $invoice_record = array(
                'order_id' => $order_info['order_id'],
                'invoice_number' => $invoice_data['invoiceNumber'],
                'invoice_uuid' => $result['success'] ? $result['data']['uuid'] : null,
                'invoice_status' => $result['success'] ? 'sent' : 'failed',
                'invoice_data' => json_encode($invoice_data),
                'response_data' => json_encode($result['data']),
                'error_message' => $result['success'] ? null : $result['message']
            );
            
            $invoice_id = $this->model_extension_module_trendyol_efaturam->addInvoice($invoice_record);
            
            // Log ekle
            $this->model_extension_module_trendyol_efaturam->addLog(array(
                'order_id' => $order_info['order_id'],
                'invoice_id' => $invoice_id,
                'action' => 'create_invoice',
                'message' => $result['message'],
                'request_data' => json_encode($invoice_data),
                'response_data' => json_encode($result['data'])
            ));
            
            if ($result['success']) {
                return array(
                    'success' => true,
                    'message' => 'Fatura başarıyla oluşturuldu',
                    'invoice_number' => $invoice_data['invoiceNumber'],
                    'data' => $result['data']
                );
            } else {
                return array(
                    'success' => false,
                    'message' => $result['message']
                );
            }
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => 'Sistem hatası: ' . $e->getMessage()
            );
        }
    }
    
    private function getApiConfig() {
        return array(
            'api_url' => $this->config->get('module_trendyol_efaturam_api_url'),
            'username' => $this->config->get('module_trendyol_efaturam_username'),
            'password' => $this->config->get('module_trendyol_efaturam_password'),
            'test_mode' => $this->config->get('module_trendyol_efaturam_test_mode'),
            'timeout' => 30
        );
    }
}
