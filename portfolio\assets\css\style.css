/* 
* Samed <PERSON>n - Art Director <PERSON><PERSON><PERSON><PERSON>
* <PERSON><PERSON>
*/
/* ===== GENEL STILLER ===== */
:root {
    --primary-color: #ff4d4d;
    --secondary-color: #333333;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --gray-color: #6c757d;
    --border-color: #dee2e6;
}
    
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Montserrat', sans-serif;
    color: var(--dark-color);
    line-height: 1.6;
    overflow-x: hidden;
    background-color: #fff;
}

    h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--dark-color);
    }

p {
    margin-bottom: 1rem;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    color: #ff2a2a;
    text-decoration: none;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 0;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    display: inline-block;
    text-align: center;
    cursor: pointer;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: #fff;
}

.btn-primary:hover {
    background-color: #ff2a2a;
    border-color: #ff2a2a;
    color: #fff;
}

.btn-outline {
    border: 2px solid var(--dark-color);
    color: var(--dark-color);
    background-color: transparent;
}

.btn-outline:hover {
    background-color: var(--dark-color);
    color: #fff;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
}

.section-header h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    position: relative;
    display: inline-block;
}

.section-header h2:after {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background-color: var(--primary-color);
}

.section-header p {
    color: var(--gray-color);
    font-size: 1.1rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.container {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
}

/* ===== HEADER ===== */
.header {
    padding: 1.5rem 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background-color: rgba(255, 255, 255, 0.95);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.header.scrolled {
    padding: 1rem 0;
}

.navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--dark-color);
    letter-spacing: 2px;
}

.navbar-brand:hover {
    color: var(--primary-color);
}

.navbar-toggler {
    border: none;
    background-color: transparent;
    cursor: pointer;
    padding: 0.25rem 0.75rem;
    font-size: 1.25rem;
    line-height: 1;
    display: none;
}

.navbar-toggler-icon {
    display: inline-block;
    width: 1.5em;
    height: 1.5em;
    vertical-align: middle;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%280, 0, 0, 0.5%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
}

.navbar-nav {
    display: flex;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none;
}

.nav-item {
    margin: 0 0.5rem;
}

.nav-link {
    color: var(--dark-color);
    font-weight: 600;
    padding: 0.5rem 1rem !important;
    position: relative;
    display: block;
}

.nav-link:after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: all 0.3s ease;
}

.nav-link:hover:after,
.nav-link.active:after {
    width: 80%;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}
/* ===== HERO SECTION ===== */
.hero {
    padding: 10rem 0 5rem;
    background-color: var(--light-color);
    position: relative;
    overflow: hidden;
}

.hero-content {
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-title span {
    color: var(--primary-color);
}

.hero-subtitle {
    font-size: 1.5rem;
    color: var(--gray-color);
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.hero-text {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    max-width: 90%;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
}

.hero-image {
    position: relative;
}

.hero-image:after {
    content: "";
    position: absolute;
    top: 20px;
    right: -20px;
    width: 100%;
    height: 100%;
    border: 5px solid var(--primary-color);
    z-index: -1;
}

.hero-image img {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    width: 100%;
}

/* ===== PROJECTS SECTION ===== */
.projects {
    padding: 5rem 0;
    background-color: #fff;
    position: relative;
}

.project-filter {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 2rem;
}

.filter-btn {
    background: none;
    border: none;
    padding: 0.5rem 1rem;
    margin: 0 0.5rem 1rem;
    font-weight: 600;
    color: var(--gray-color);
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn:hover,
.filter-btn.active {
    color: var(--primary-color);
}

.filter-btn.active {
    position: relative;
}

.filter-btn.active:after {
    content: "";
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 2px;
    background-color: var(--primary-color);
}

.project-grid {
    margin-top: 2rem;
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
}

.project-item {
    margin-bottom: 2rem;
    padding-right: 15px;
    padding-left: 15px;
}

.project-card {
    background-color: #fff;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    height: 100%;
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.project-image {
    position: relative;
    overflow: hidden;
}

.project-image img {
    transition: all 0.5s ease;
    width: 100%;
    height: auto;
}

.project-card:hover .project-image img {
    transform: scale(1.1);
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 77, 77, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-link {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 1.2rem;
    transform: translateY(20px);
    opacity: 0;
    transition: all 0.3s ease 0.1s;
}

.project-card:hover .project-link {
    transform: translateY(0);
    opacity: 1;
}

.project-info {
    padding: 1.5rem;
}

.project-info h3 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

.project-info p {
    color: var(--gray-color);
    margin-bottom: 0;
}

/* ===== ABOUT SECTION ===== */
.about {
    padding: 5rem 0;
    background-color: var(--light-color);
    position: relative;
}

.about-image {
    position: relative;
}

.about-image:before {
    content: "";
    position: absolute;
    top: -20px;
    left: -20px;
    width: 100%;
    height: 100%;
    border: 5px solid var(--primary-color);
    z-index: 0;
}

.about-image img {
    position: relative;
    z-index: 1;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    width: 100%;
}

.about-content {
    padding-left: 2rem;
}

.about-content h2 {
    margin-bottom: 1.5rem;
    position: relative;
    display: inline-block;
}

.about-content h2:after {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: var(--primary-color);
}

.skills {
    margin-top: 2rem;
}

.skills h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
}

.skill-item {
    margin-bottom: 1.5rem;
}

.skill-name {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.skill-bar {
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.skill-progress {
    height: 100%;
    background-color: var(--primary-color);
    border-radius: 4px;
}

/* ===== TESTIMONIALS SECTION ===== */
.testimonials {
    padding: 5rem 0;
    background-color: #fff;
    position: relative;
}

.testimonial-slider {
    position: relative;
}

.testimonial-item {
    padding: 1rem;
}

.testimonial-content {
    background-color: var(--light-color);
    padding: 2rem;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    position: relative;
}

.testimonial-content:after {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 50px;
    width: 20px;
    height: 20px;
    background-color: var(--light-color);
    transform: rotate(45deg);
}

.testimonial-content p {
    font-style: italic;
    margin-bottom: 0;
}

.testimonial-author {
    display: flex;
    align-items: center;
    margin-top: 2rem;
    padding-left: 1rem;
}

.testimonial-author img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-right: 1rem;
    object-fit: cover;
}

.testimonial-author h4 {
    font-size: 1.1rem;
    margin-bottom: 0.2rem;
}

.testimonial-author p {
    color: var(--gray-color);
    margin-bottom: 0;
    font-size: 0.9rem;
}

/* Slick Slider Dots */
.slick-dots {
    display: flex;
    justify-content: center;
    list-style: none;
    padding: 0;
    margin: 2rem 0 0;
}

.slick-dots li {
    margin: 0 5px;
}

.slick-dots li button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #e9ecef;
    border: none;
    font-size: 0;
    padding: 0;
    cursor: pointer;
    transition: all 0.3s ease;
}