<?php

namespace App\Models;

use CodeIgniter\Model;

class CityModel extends Model
{
    protected $table = 'cities';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'name', 'slug', 'plate_code', 'status', 'sort_order'
    ];

    protected $useTimestamps = false;

    protected $validationRules = [
        'name' => 'required|min_length[2]|max_length[100]',
        'slug' => 'required|alpha_dash|max_length[100]|is_unique[cities.slug,id,{id}]',
        'plate_code' => 'permit_empty|max_length[3]|numeric'
    ];

    protected $validationMessages = [
        'name' => [
            'required' => 'Şehir adı gereklidir.',
            'min_length' => 'Şehir adı en az 2 karakter olmalıdır.',
            'max_length' => 'Şehir adı en fazla 100 karakter olabilir.'
        ],
        'slug' => [
            'required' => 'Şehir slug\'ı gereklidir.',
            'alpha_dash' => 'Slug sadece harf, rakam, tire ve alt çizgi içerebilir.',
            'is_unique' => 'Bu slug zaten kullanılıyor.'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    protected $allowCallbacks = true;
    protected $beforeInsert = ['generateSlug'];
    protected $beforeUpdate = ['generateSlug'];

    // Aktif şehirleri getir
    public function getActiveCities()
    {
        return $this->where('status', 1)
                   ->orderBy('sort_order', 'ASC')
                   ->orderBy('name', 'ASC')
                   ->findAll();
    }

    // Şehir detayını getir
    public function getCityBySlug($slug)
    {
        return $this->where('slug', $slug)
                   ->where('status', 1)
                   ->first();
    }

    // Şehir ile birlikte ilan sayısını getir
    public function getCitiesWithAdCount()
    {
        return $this->select('cities.*, COUNT(ads.id) as ad_count')
                   ->join('ads', 'ads.city_id = cities.id AND ads.status = "active" AND ads.expires_at > NOW()', 'left')
                   ->where('cities.status', 1)
                   ->groupBy('cities.id')
                   ->orderBy('cities.sort_order', 'ASC')
                   ->orderBy('cities.name', 'ASC')
                   ->findAll();
    }

    // Toplam aktif şehir sayısı
    public function getTotalActiveCities()
    {
        return $this->where('status', 1)->countAllResults();
    }

    // Slug oluştur
    protected function generateSlug(array $data)
    {
        if (isset($data['data']['name']) && empty($data['data']['slug'])) {
            $data['data']['slug'] = url_title($data['data']['name'], '-', true);
            
            // Benzersiz slug kontrolü
            $count = 1;
            $originalSlug = $data['data']['slug'];
            
            while ($this->where('slug', $data['data']['slug'])->first()) {
                $data['data']['slug'] = $originalSlug . '-' . $count;
                $count++;
            }
        }
        
        return $data;
    }
}
