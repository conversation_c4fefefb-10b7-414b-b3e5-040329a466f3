<?php
// Session yönetimi ve timeout kontrolü
require_once __DIR__ . '/functions.php';

// Session'ı başlat
startSession();

// Her sayfa yüklendiğinde session timeout kontrolü yap
checkSessionTimeout();

// Session bilgilerini güncelle
if (isLoggedIn()) {
    // Son aktivite zamanını güncelle
    $_SESSION['last_activity'] = time();
    
    // Session bilgilerini kontrol et
    if (!isset($_SESSION['session_id'])) {
        $_SESSION['session_id'] = session_id();
    }
    
    // Güvenlik için user agent kontrolü
    if (!isset($_SESSION['user_agent'])) {
        $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? '';
    } elseif ($_SESSION['user_agent'] !== ($_SERVER['HTTP_USER_AGENT'] ?? '')) {
        // User agent değ<PERSON>şmişse session'ı sonlandır (güvenlik)
        destroySession();
        header('Location: /bakici-platform/auth/login.php?security=1');
        exit;
    }
    
    // IP adresi kontrolü (opsiyonel - çok katı olabilir)
    /*
    if (!isset($_SESSION['ip_address'])) {
        $_SESSION['ip_address'] = $_SERVER['REMOTE_ADDR'] ?? '';
    } elseif ($_SESSION['ip_address'] !== ($_SERVER['REMOTE_ADDR'] ?? '')) {
        // IP adresi değişmişse session'ı sonlandır
        destroySession();
        header('Location: /bakici-platform/auth/login.php?security=1');
        exit;
    }
    */
}

// Session timeout warning için JavaScript kodu
function getSessionTimeoutScript() {
    if (!isLoggedIn()) return '';
    
    $timeout_duration = 3600; // 1 saat
    $warning_time = 300; // 5 dakika önce uyar
    $last_activity = $_SESSION['last_activity'] ?? time();
    $remaining_time = $timeout_duration - (time() - $last_activity);
    
    if ($remaining_time <= 0) return '';
    
    return "
    <script>
    // Session timeout uyarısı
    let sessionTimeout = {$remaining_time}; // Kalan süre (saniye)
    let warningShown = false;
    
    function updateSessionTimer() {
        sessionTimeout--;
        
        // 5 dakika kala uyarı göster
        if (sessionTimeout <= {$warning_time} && !warningShown) {
            warningShown = true;
            showSessionWarning();
        }
        
        // Session süresi doldu
        if (sessionTimeout <= 0) {
            handleSessionExpired();
            return;
        }
        
        // Timer'ı güncelle
        setTimeout(updateSessionTimer, 1000);
    }
    
    function showSessionWarning() {
        if (confirm('Oturumunuz 5 dakika içinde sona erecek. Devam etmek istiyor musunuz?')) {
            // Kullanıcı devam etmek istiyor, session'ı yenile
            refreshSession();
        }
    }
    
    function refreshSession() {
        fetch('/bakici-platform/includes/refresh-session.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                sessionTimeout = {$timeout_duration}; // Session yenilendi
                warningShown = false;
                console.log('Session yenilendi');
            } else {
                handleSessionExpired();
            }
        })
        .catch(error => {
            console.error('Session yenileme hatası:', error);
            handleSessionExpired();
        });
    }
    
    function handleSessionExpired() {
        alert('Oturumunuzun süresi doldu. Giriş sayfasına yönlendiriliyorsunuz.');
        window.location.href = '/bakici-platform/auth/login.php?timeout=1';
    }
    
    // Timer'ı başlat
    setTimeout(updateSessionTimer, 1000);
    
    // Sayfa aktivitesi olduğunda session'ı yenile
    let activityTimer;
    function resetActivityTimer() {
        clearTimeout(activityTimer);
        activityTimer = setTimeout(function() {
            // 30 dakika aktivite yoksa uyarı göster
            if (sessionTimeout > 1800) { // 30 dakika
                refreshSession();
            }
        }, 1800000); // 30 dakika
    }
    
    // Mouse ve keyboard aktivitelerini dinle
    document.addEventListener('mousemove', resetActivityTimer);
    document.addEventListener('keypress', resetActivityTimer);
    document.addEventListener('click', resetActivityTimer);
    document.addEventListener('scroll', resetActivityTimer);
    
    // İlk timer'ı başlat
    resetActivityTimer();
    </script>";
}

// Session bilgilerini debug için göster (sadece development)
function debugSessionInfo() {
    if (!isLoggedIn() || !defined('DEBUG') || !DEBUG) return '';
    
    $session_info = [
        'user_id' => $_SESSION['user_id'] ?? 'N/A',
        'login_time' => isset($_SESSION['login_time']) ? date('Y-m-d H:i:s', $_SESSION['login_time']) : 'N/A',
        'last_activity' => isset($_SESSION['last_activity']) ? date('Y-m-d H:i:s', $_SESSION['last_activity']) : 'N/A',
        'session_id' => session_id(),
        'remaining_time' => isLoggedIn() ? (3600 - (time() - ($_SESSION['last_activity'] ?? time()))) : 0
    ];
    
    return "
    <div style='position: fixed; top: 10px; right: 10px; background: #000; color: #fff; padding: 10px; font-size: 12px; z-index: 9999; border-radius: 5px;'>
        <strong>Session Debug:</strong><br>
        User ID: {$session_info['user_id']}<br>
        Login: {$session_info['login_time']}<br>
        Last Activity: {$session_info['last_activity']}<br>
        Remaining: {$session_info['remaining_time']}s<br>
        Session ID: " . substr($session_info['session_id'], 0, 8) . "...
    </div>";
}
?>
