/**
 * Trendyol E-Faturam JavaScript Functions
 * Sipariş sayfalarına fatura işlemleri entegrasyonu
 */

// Fatura oluştur
function createTrendyolInvoice(orderId) {
    if (!confirm('Bu sipariş için fatura oluşturmak istediğinizden emin misiniz?')) {
        return;
    }
    
    // Loading göster
    $('#trendyol-invoice-btn-' + orderId).prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Fatura Oluşturuluyor...');
    
    $.ajax({
        url: 'index.php?route=extension/module/trendyol_efaturam_order/createInvoice&user_token=' + getURLVar('user_token') + '&order_id=' + orderId,
        type: 'GET',
        dataType: 'json',
        success: function(json) {
            if (json.success) {
                alert('Başarılı: ' + json.success);
                location.reload(); // Say<PERSON><PERSON><PERSON> yenile
            } else if (json.error) {
                alert('Hata: ' + json.error);
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            alert('Sistem hatası: ' + thrownError);
        },
        complete: function() {
            $('#trendyol-invoice-btn-' + orderId).prop('disabled', false).html('<i class="fa fa-file-text"></i> Fatura Oluştur');
        }
    });
}

// Fatura durumunu kontrol et
function checkTrendyolInvoiceStatus(orderId) {
    $.ajax({
        url: 'index.php?route=extension/module/trendyol_efaturam_order/getInvoiceStatus&user_token=' + getURLVar('user_token') + '&order_id=' + orderId,
        type: 'GET',
        dataType: 'json',
        success: function(json) {
            if (json.success && json.invoice) {
                var invoice = json.invoice;
                var statusHtml = '<div class="alert alert-info">';
                statusHtml += '<strong>Fatura Bilgileri:</strong><br>';
                statusHtml += 'Fatura No: ' + invoice.invoice_number + '<br>';
                statusHtml += 'Durum: ' + getInvoiceStatusText(invoice.invoice_status) + '<br>';
                statusHtml += 'Oluşturma Tarihi: ' + invoice.date_created + '<br>';
                
                if (invoice.invoice_uuid) {
                    statusHtml += 'UUID: ' + invoice.invoice_uuid + '<br>';
                }
                
                if (json.api_status) {
                    statusHtml += 'API Durumu: ' + json.api_status.status + '<br>';
                }
                
                statusHtml += '</div>';
                
                $('#trendyol-invoice-status-' + orderId).html(statusHtml);
            } else if (json.error) {
                $('#trendyol-invoice-status-' + orderId).html('<div class="alert alert-warning">' + json.error + '</div>');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            $('#trendyol-invoice-status-' + orderId).html('<div class="alert alert-danger">Durum kontrol edilemedi: ' + thrownError + '</div>');
        }
    });
}

// Fatura durumu metni
function getInvoiceStatusText(status) {
    var statusTexts = {
        'pending': 'Beklemede',
        'sent': 'Gönderildi',
        'approved': 'Onaylandı',
        'rejected': 'Reddedildi',
        'failed': 'Başarısız',
        'cancelled': 'İptal Edildi'
    };
    
    return statusTexts[status] || status;
}

// URL parametresi al
function getURLVar(key) {
    var value = [];
    var query = String(document.location).split('?');
    
    if (query[1]) {
        var part = query[1].split('&');
        
        for (i = 0; i < part.length; i++) {
            var data = part[i].split('=');
            
            if (data[0] && data[1]) {
                value[data[0]] = data[1];
            }
        }
        
        if (value[key]) {
            return value[key];
        } else {
            return '';
        }
    }
}

// Sayfa yüklendiğinde çalışacak fonksiyonlar
$(document).ready(function() {
    // Sipariş detay sayfasında fatura butonunu ekle
    if (window.location.href.indexOf('route=sale/order/info') > -1) {
        var orderId = getURLVar('order_id');
        if (orderId) {
            addInvoiceButtonToOrderPage(orderId);
        }
    }
    
    // Sipariş listesi sayfasında fatura durumlarını göster
    if (window.location.href.indexOf('route=sale/order') > -1 && window.location.href.indexOf('route=sale/order/info') === -1) {
        addInvoiceStatusToOrderList();
    }
});

// Sipariş detay sayfasına fatura butonu ekle
function addInvoiceButtonToOrderPage(orderId) {
    var buttonHtml = '<div class="panel panel-default">';
    buttonHtml += '<div class="panel-heading"><h3 class="panel-title"><i class="fa fa-file-text"></i> Trendyol E-Faturam</h3></div>';
    buttonHtml += '<div class="panel-body">';
    buttonHtml += '<button type="button" id="trendyol-invoice-btn-' + orderId + '" class="btn btn-primary" onclick="createTrendyolInvoice(' + orderId + ')">';
    buttonHtml += '<i class="fa fa-file-text"></i> Fatura Oluştur</button> ';
    buttonHtml += '<button type="button" class="btn btn-info" onclick="checkTrendyolInvoiceStatus(' + orderId + ')">';
    buttonHtml += '<i class="fa fa-search"></i> Durum Kontrol Et</button>';
    buttonHtml += '<div id="trendyol-invoice-status-' + orderId + '" class="mt-3"></div>';
    buttonHtml += '</div></div>';
    
    // Sipariş geçmişi panelinden önce ekle
    $('.panel:contains("Order History")').before(buttonHtml);
    
    // Sayfa yüklendiğinde durumu kontrol et
    checkTrendyolInvoiceStatus(orderId);
}

// Sipariş listesine fatura durumu ekle
function addInvoiceStatusToOrderList() {
    // Tablo başlığına sütun ekle
    $('table thead tr').append('<th>E-Fatura</th>');
    
    // Her sipariş satırına durum ekle
    $('table tbody tr').each(function() {
        var $row = $(this);
        var orderLink = $row.find('td:first a').attr('href');
        
        if (orderLink) {
            var orderId = orderLink.match(/order_id=(\d+)/);
            if (orderId && orderId[1]) {
                $row.append('<td><span id="invoice-status-' + orderId[1] + '">Kontrol ediliyor...</span></td>');
                checkOrderInvoiceStatus(orderId[1]);
            }
        } else {
            $row.append('<td>-</td>');
        }
    });
}

// Sipariş listesi için fatura durumu kontrol et
function checkOrderInvoiceStatus(orderId) {
    $.ajax({
        url: 'index.php?route=extension/module/trendyol_efaturam_order/getInvoiceStatus&user_token=' + getURLVar('user_token') + '&order_id=' + orderId,
        type: 'GET',
        dataType: 'json',
        success: function(json) {
            var statusHtml = '';
            if (json.success && json.invoice) {
                var status = json.invoice.invoice_status;
                var statusClass = 'label-default';
                
                switch(status) {
                    case 'sent':
                    case 'approved':
                        statusClass = 'label-success';
                        break;
                    case 'pending':
                        statusClass = 'label-warning';
                        break;
                    case 'failed':
                    case 'rejected':
                        statusClass = 'label-danger';
                        break;
                }
                
                statusHtml = '<span class="label ' + statusClass + '">' + getInvoiceStatusText(status) + '</span>';
            } else {
                statusHtml = '<span class="label label-default">Fatura Yok</span>';
            }
            
            $('#invoice-status-' + orderId).html(statusHtml);
        },
        error: function() {
            $('#invoice-status-' + orderId).html('<span class="label label-default">Hata</span>');
        }
    });
}
