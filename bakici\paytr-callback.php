<?php
/**
 * PayTR Callback Handler
 * Bu dosya PayTR tarafından ödeme sonucu bildirimi için ç<PERSON>ğrılır
 */

require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/PayTR.php';

// Log fonksiyonu
function logPayTRCallback($message, $data = null) {
    $log_message = date('Y-m-d H:i:s') . " - " . $message;
    if ($data) {
        $log_message .= " - Data: " . json_encode($data);
    }
    error_log($log_message . "\n", 3, 'logs/paytr_callback.log');
}

try {
    // POST verilerini al
    $post_data = $_POST;
    
    logPayTRCallback("PayTR Callback received", $post_data);
    
    // Gerekli alanları kontrol et
    $required_fields = ['merchant_oid', 'status', 'total_amount', 'hash'];
    foreach ($required_fields as $field) {
        if (!isset($post_data[$field])) {
            logPayTRCallback("Missing required field: " . $field);
            echo "FAIL - Missing field: " . $field;
            exit;
        }
    }
    
    // PayTR sınıfını başlat
    $paytr = new PayTR();
    
    // Callback'i doğrula
    $verification = $paytr->verifyCallback($post_data);
    
    if (!$verification['verified']) {
        logPayTRCallback("Hash verification failed");
        echo "FAIL - Hash verification failed";
        exit;
    }
    
    $merchant_oid = $verification['merchant_oid'];
    $status = $verification['status'];
    $total_amount = $verification['total_amount'];
    
    logPayTRCallback("Callback verified", $verification);
    
    // Ödeme kaydını bul
    $sql = "SELECT * FROM payments WHERE order_id = ?";
    $stmt = $db->prepare($sql);
    $stmt->execute([$merchant_oid]);
    $payment = $stmt->fetch();
    
    if (!$payment) {
        logPayTRCallback("Payment not found for order_id: " . $merchant_oid);
        echo "FAIL - Payment not found";
        exit;
    }
    
    // Ödeme durumunu güncelle
    if ($status == 'success') {
        // Başarılı ödeme
        $update_sql = "UPDATE payments SET 
                       status = 'completed', 
                       transaction_id = ?, 
                       updated_at = NOW() 
                       WHERE id = ?";
        $update_stmt = $db->prepare($update_sql);
        $update_stmt->execute([
            $post_data['transaction_id'] ?? 'TXN_' . time(),
            $payment['id']
        ]);
        
        // Kullanıcı paketini aktif et
        activateUserPackage($payment['user_id'], $payment['package_type'], $payment['id']);
        
        logPayTRCallback("Payment completed successfully", [
            'payment_id' => $payment['id'],
            'user_id' => $payment['user_id'],
            'package_type' => $payment['package_type']
        ]);
        
        echo "OK";
        
    } else {
        // Başarısız ödeme
        $update_sql = "UPDATE payments SET 
                       status = 'failed', 
                       updated_at = NOW() 
                       WHERE id = ?";
        $update_stmt = $db->prepare($update_sql);
        $update_stmt->execute([$payment['id']]);
        
        logPayTRCallback("Payment failed", [
            'payment_id' => $payment['id'],
            'reason' => $post_data['failed_reason_msg'] ?? 'Unknown'
        ]);
        
        echo "OK";
    }
    
} catch (Exception $e) {
    logPayTRCallback("Callback error: " . $e->getMessage());
    echo "FAIL - " . $e->getMessage();
}

/**
 * Kullanıcı paketini aktif et
 */
function activateUserPackage($user_id, $package_type, $payment_id) {
    global $db;
    
    try {
        // Paket bilgilerini al
        $packages = PayTR::getPackages();
        $package = $packages[$package_type] ?? null;
        
        if (!$package) {
            throw new Exception("Invalid package type: " . $package_type);
        }
        
        // Mevcut aktif paketi pasif yap
        $deactivate_sql = "UPDATE user_packages SET status = 'expired' WHERE user_id = ? AND status = 'active'";
        $deactivate_stmt = $db->prepare($deactivate_sql);
        $deactivate_stmt->execute([$user_id]);
        
        // Yeni paketi aktif et
        $start_date = date('Y-m-d');
        $end_date = date('Y-m-d', strtotime('+' . $package['duration'] . ' days'));
        
        $insert_sql = "INSERT INTO user_packages 
                       (user_id, package_type, start_date, end_date, job_limit, jobs_used, status, payment_id) 
                       VALUES (?, ?, ?, ?, ?, 0, 'active', ?)";
        $insert_stmt = $db->prepare($insert_sql);
        $insert_stmt->execute([
            $user_id,
            $package_type,
            $start_date,
            $end_date,
            $package['job_limit'],
            $payment_id
        ]);
        
        logPayTRCallback("User package activated", [
            'user_id' => $user_id,
            'package_type' => $package_type,
            'start_date' => $start_date,
            'end_date' => $end_date,
            'job_limit' => $package['job_limit']
        ]);
        
        // Email bildirimi gönder (opsiyonel)
        sendPackageActivationEmail($user_id, $package_type, $package);
        
    } catch (Exception $e) {
        logPayTRCallback("Package activation error: " . $e->getMessage());
        throw $e;
    }
}

/**
 * Paket aktivasyon email'i gönder
 */
function sendPackageActivationEmail($user_id, $package_type, $package) {
    try {
        // Kullanıcı bilgilerini al
        global $db;
        $sql = "SELECT full_name, email FROM users WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$user_id]);
        $user = $stmt->fetch();
        
        if (!$user) return;
        
        $subject = "Paket Aktivasyonu - " . $package['name'];
        $message = "
        <h2>Paketiniz Aktif Edildi!</h2>
        <p>Merhaba {$user['full_name']},</p>
        <p><strong>{$package['name']}</strong> paketiniz başarıyla aktif edildi.</p>
        <p><strong>Paket Özellikleri:</strong></p>
        <ul>";
        
        foreach ($package['features'] as $feature) {
            $message .= "<li>" . htmlspecialchars(trim($feature)) . "</li>";
        }
        
        $message .= "
        </ul>
        <p>Paketinizin geçerlilik süresi: {$package['duration']} gün</p>
        <p>Hemen platformumuzu kullanmaya başlayabilirsiniz!</p>
        <p><a href='" . getSetting('site_url', 'http://localhost/bakici-platform') . "/dashboard.php'>Dashboard'a Git</a></p>
        ";
        
        // Email gönder (PHPMailer kullanarak)
        // sendEmail($user['email'], $subject, $message);
        
        logPayTRCallback("Package activation email sent", [
            'user_id' => $user_id,
            'email' => $user['email'],
            'package_type' => $package_type
        ]);
        
    } catch (Exception $e) {
        logPayTRCallback("Email sending error: " . $e->getMessage());
    }
}
?>
