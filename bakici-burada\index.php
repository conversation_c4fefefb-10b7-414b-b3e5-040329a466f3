<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// İstatistikleri al
$stats = [];
try {
    $stats['total_caregivers'] = $db->query("SELECT COUNT(*) FROM users WHERE user_type = 'caregiver' AND is_active = 1")->fetchColumn();
    $stats['total_families'] = $db->query("SELECT COUNT(*) FROM users WHERE user_type = 'family' AND is_active = 1")->fetchColumn();
    $stats['total_jobs'] = $db->query("SELECT COUNT(*) FROM job_listings WHERE status = 'active'")->fetchColumn();
    $stats['total_reviews'] = $db->query("SELECT COUNT(*) FROM reviews WHERE is_approved = 1")->fetchColumn();
} catch (PDOException $e) {
    $stats = ['total_caregivers' => 0, 'total_families' => 0, 'total_jobs' => 0, 'total_reviews' => 0];
}

// Son eklenen bakıcıları al
$recent_caregivers = [];
try {
    $sql = "SELECT u.id, u.full_name, u.city, u.profile_photo, 
                   cp.experience_years, cp.hourly_rate, cp.rating, cp.total_reviews, cp.services
            FROM users u 
            LEFT JOIN caregiver_profiles cp ON u.id = cp.user_id 
            WHERE u.user_type = 'caregiver' AND u.is_active = 1 AND u.is_verified = 1
            ORDER BY u.created_at DESC 
            LIMIT 8";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $recent_caregivers = $stmt->fetchAll();
} catch (PDOException $e) {
    $recent_caregivers = [];
}

// Son iş ilanlarını al
$recent_jobs = [];
try {
    $sql = "SELECT jl.id, jl.title, jl.job_type, jl.care_type, jl.location_city, jl.location_district,
                   jl.budget_min, jl.budget_max, jl.budget_type, jl.is_urgent, jl.created_at,
                   u.full_name as employer_name
            FROM job_listings jl
            JOIN users u ON jl.user_id = u.id
            WHERE jl.status = 'active' AND jl.expires_at > NOW()
            ORDER BY jl.is_urgent DESC, jl.created_at DESC
            LIMIT 6";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $recent_jobs = $stmt->fetchAll();
} catch (PDOException $e) {
    $recent_jobs = [];
}

$page_title = 'Güvenilir Bakıcı ve Yardımcı Bulma Platformu';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bakıcı Burada</title>
    <meta name="description" content="Güvenilir çocuk bakıcısı, yaşlı bakıcısı, hasta bakıcısı ve ev yardımcısı bulun. Binlerce doğrulanmış bakıcı profili ile güvenli platform.">
    <meta name="keywords" content="bakıcı, çocuk bakıcısı, yaşlı bakıcısı, hasta bakıcısı, ev yardımcısı, güvenilir bakıcı">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #2c5aa0;
            --secondary-color: #f8f9fa;
            --accent-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }
        
        .hero-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, #1e3d72 100%);
            color: white;
            padding: 100px 0;
        }
        
        .search-box {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .feature-card {
            border: none;
            border-radius: 15px;
            padding: 30px;
            height: 100%;
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-section {
            background: var(--secondary-color);
            padding: 60px 0;
        }
        
        .caregiver-card {
            border: none;
            border-radius: 15px;
            overflow: hidden;
            transition: transform 0.3s ease;
            height: 100%;
        }
        
        .caregiver-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .job-card {
            border-left: 4px solid var(--primary-color);
            transition: all 0.3s ease;
        }
        
        .job-card:hover {
            border-left-color: var(--accent-color);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .urgent-job {
            border-left-color: var(--danger-color) !important;
        }
        
        .rating-stars {
            color: #ffc107;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold text-primary" href="index.php">
                <i class="bi bi-heart-fill me-2"></i>Bakıcı Burada
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="caregivers.php">Bakıcı Ara</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="jobs.php">İş İlanları</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">Hakkımızda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">İletişim</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <?php if (isset($_SESSION['user_id'])): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle me-1"></i><?php echo escape(safeArray($_SESSION, 'full_name', 'Kullanıcı')); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="dashboard.php">Dashboard</a></li>
                                <li><a class="dropdown-item" href="profile.php">Profil</a></li>
                                <li><a class="dropdown-item" href="messages.php">Mesajlar</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="auth/logout.php">Çıkış</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="auth/login.php">Giriş Yap</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-primary ms-2" href="auth/register.php">Üye Ol</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="display-4 fw-bold mb-4">Güvenilir Bakıcı Bulmanın En Kolay Yolu</h1>
                    <p class="lead mb-4">Çocuk, yaşlı ve hasta bakımı için doğrulanmış, deneyimli bakıcılarla tanışın. Güvenli platform, kolay iletişim.</p>
                    <div class="d-flex gap-3">
                        <a href="caregivers.php" class="btn btn-light btn-lg">
                            <i class="bi bi-search me-2"></i>Bakıcı Ara
                        </a>
                        <a href="jobs/create.php" class="btn btn-outline-light btn-lg">
                            <i class="bi bi-plus-circle me-2"></i>İlan Ver
                        </a>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="search-box">
                        <h4 class="text-dark mb-4">Hızlı Arama</h4>
                        <form action="caregivers.php" method="GET">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <select name="job_type" class="form-select">
                                        <option value="">Bakım Türü Seçin</option>
                                        <?php foreach (getJobTypes() as $key => $value): ?>
                                            <option value="<?php echo $key; ?>"><?php echo $value; ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <select name="city" class="form-select">
                                        <option value="">Şehir Seçin</option>
                                        <?php foreach (getCities() as $city): ?>
                                            <option value="<?php echo $city; ?>"><?php echo $city; ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary w-100 btn-lg">
                                        <i class="bi bi-search me-2"></i>Bakıcı Ara
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats-section">
        <div class="container">
            <div class="row text-center">
                <div class="col-md-3 mb-4">
                    <div class="h2 text-primary fw-bold"><?php echo number_format($stats['total_caregivers']); ?>+</div>
                    <p class="text-muted">Doğrulanmış Bakıcı</p>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="h2 text-success fw-bold"><?php echo number_format($stats['total_families']); ?>+</div>
                    <p class="text-muted">Mutlu Aile</p>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="h2 text-warning fw-bold"><?php echo number_format($stats['total_jobs']); ?>+</div>
                    <p class="text-muted">Aktif İş İlanı</p>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="h2 text-info fw-bold"><?php echo number_format($stats['total_reviews']); ?>+</div>
                    <p class="text-muted">Memnuniyet Yorumu</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-5">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-lg-8 mx-auto">
                    <h2 class="display-5 fw-bold">Neden Bakıcı Burada?</h2>
                    <p class="lead text-muted">Güvenli, hızlı ve kolay bakıcı bulma deneyimi</p>
                </div>
            </div>
            
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="card feature-card text-center">
                        <div class="card-body">
                            <div class="text-primary mb-3">
                                <i class="bi bi-shield-check" style="font-size: 3rem;"></i>
                            </div>
                            <h5 class="card-title">Güvenilir Bakıcılar</h5>
                            <p class="card-text">Tüm bakıcılarımız kimlik ve adli sicil kontrolünden geçer. Referansları doğrulanır.</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card feature-card text-center">
                        <div class="card-body">
                            <div class="text-success mb-3">
                                <i class="bi bi-chat-dots" style="font-size: 3rem;"></i>
                            </div>
                            <h5 class="card-title">Kolay İletişim</h5>
                            <p class="card-text">Platform üzerinden güvenli mesajlaşma. İletişim bilgilerine kontrollü erişim.</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card feature-card text-center">
                        <div class="card-body">
                            <div class="text-warning mb-3">
                                <i class="bi bi-star-fill" style="font-size: 3rem;"></i>
                            </div>
                            <h5 class="card-title">Değerlendirme Sistemi</h5>
                            <p class="card-text">Gerçek kullanıcı yorumları ve puanları ile doğru seçim yapın.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Recent Caregivers -->
    <?php if (!empty($recent_caregivers)): ?>
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row mb-4">
                <div class="col-lg-8">
                    <h3 class="fw-bold">Öne Çıkan Bakıcılar</h3>
                    <p class="text-muted">Deneyimli ve güvenilir bakıcılarımızla tanışın</p>
                </div>
                <div class="col-lg-4 text-end">
                    <a href="caregivers.php" class="btn btn-outline-primary">
                        Tümünü Gör <i class="bi bi-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
            
            <div class="row g-4">
                <?php foreach (array_slice($recent_caregivers, 0, 4) as $caregiver): ?>
                <div class="col-md-6 col-lg-3">
                    <div class="card caregiver-card">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <?php if ($caregiver['profile_photo']): ?>
                                    <img src="uploads/profiles/<?php echo $caregiver['profile_photo']; ?>" 
                                         class="rounded-circle" width="80" height="80" style="object-fit: cover;">
                                <?php else: ?>
                                    <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center" 
                                         style="width: 80px; height: 80px;">
                                        <i class="bi bi-person-fill text-white" style="font-size: 2rem;"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <h6 class="card-title"><?php echo escape($caregiver['full_name']); ?></h6>
                            <p class="text-muted small">
                                <i class="bi bi-geo-alt me-1"></i><?php echo escape($caregiver['city']); ?>
                            </p>
                            <?php if ($caregiver['rating'] > 0): ?>
                            <div class="rating-stars mb-2">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <i class="bi bi-star<?php echo $i <= $caregiver['rating'] ? '-fill' : ''; ?>"></i>
                                <?php endfor; ?>
                                <small class="text-muted ms-1">(<?php echo $caregiver['total_reviews']; ?>)</small>
                            </div>
                            <?php endif; ?>
                            <p class="text-primary fw-bold">
                                <?php echo $caregiver['hourly_rate'] ? formatMoney($caregiver['hourly_rate']) . '/saat' : 'Fiyat Görüşülür'; ?>
                            </p>
                            <a href="caregiver.php?id=<?php echo $caregiver['id']; ?>" class="btn btn-outline-primary btn-sm">
                                Profili Gör
                            </a>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Recent Jobs -->
    <?php if (!empty($recent_jobs)): ?>
    <section class="py-5">
        <div class="container">
            <div class="row mb-4">
                <div class="col-lg-8">
                    <h3 class="fw-bold">Son İş İlanları</h3>
                    <p class="text-muted">Yeni iş fırsatlarını kaçırmayın</p>
                </div>
                <div class="col-lg-4 text-end">
                    <a href="jobs.php" class="btn btn-outline-primary">
                        Tümünü Gör <i class="bi bi-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>
            
            <div class="row g-4">
                <?php foreach (array_slice($recent_jobs, 0, 3) as $job): ?>
                <div class="col-md-4">
                    <div class="card job-card <?php echo $job['is_urgent'] ? 'urgent-job' : ''; ?>">
                        <div class="card-body">
                            <?php if ($job['is_urgent']): ?>
                                <span class="badge bg-danger mb-2">ACİL</span>
                            <?php endif; ?>
                            <h6 class="card-title"><?php echo escape($job['title']); ?></h6>
                            <p class="text-muted small mb-2">
                                <i class="bi bi-geo-alt me-1"></i>
                                <?php echo escape($job['location_city']); ?>
                                <?php if ($job['location_district']): ?>
                                    , <?php echo escape($job['location_district']); ?>
                                <?php endif; ?>
                            </p>
                            <p class="text-muted small mb-2">
                                <i class="bi bi-briefcase me-1"></i>
                                <?php echo getJobTypes()[$job['job_type']] ?? $job['job_type']; ?> - 
                                <?php echo getCareTypes()[$job['care_type']] ?? $job['care_type']; ?>
                            </p>
                            <?php if ($job['budget_min'] || $job['budget_max']): ?>
                            <p class="text-primary fw-bold mb-2">
                                <i class="bi bi-currency-exchange me-1"></i>
                                <?php if ($job['budget_min'] && $job['budget_max']): ?>
                                    <?php echo formatMoney($job['budget_min']); ?> - <?php echo formatMoney($job['budget_max']); ?>
                                <?php elseif ($job['budget_min']): ?>
                                    <?php echo formatMoney($job['budget_min']); ?>+
                                <?php else: ?>
                                    <?php echo formatMoney($job['budget_max']); ?>'e kadar
                                <?php endif; ?>
                                <?php if ($job['budget_type']): ?>
                                    /<?php echo $job['budget_type'] === 'hourly' ? 'saat' : ($job['budget_type'] === 'daily' ? 'gün' : ($job['budget_type'] === 'weekly' ? 'hafta' : 'ay')); ?>
                                <?php endif; ?>
                            </p>
                            <?php endif; ?>
                            <small class="text-muted">
                                <i class="bi bi-clock me-1"></i><?php echo formatDateTime($job['created_at'], 'd.m.Y'); ?>
                            </small>
                            <div class="mt-3">
                                <a href="job.php?id=<?php echo $job['id']; ?>" class="btn btn-primary btn-sm">
                                    Detayları Gör
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- CTA Section -->
    <section class="py-5 bg-primary text-white">
        <div class="container text-center">
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <h3 class="fw-bold mb-4">Hemen Başlayın!</h3>
                    <p class="lead mb-4">Güvenilir bakıcı bulun veya iş fırsatlarını keşfedin</p>
                    <div class="d-flex gap-3 justify-content-center">
                        <a href="auth/register.php?type=family" class="btn btn-light btn-lg">
                            <i class="bi bi-search me-2"></i>Bakıcı Arıyorum
                        </a>
                        <a href="auth/register.php?type=caregiver" class="btn btn-outline-light btn-lg">
                            <i class="bi bi-briefcase me-2"></i>İş Arıyorum
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4 mb-4">
                    <h5 class="fw-bold">Bakıcı Burada</h5>
                    <p class="text-muted">Güvenilir bakıcı bulmanın en kolay yolu. Ailenizin güvenliği bizim önceliğimiz.</p>
                    <div class="d-flex gap-3">
                        <a href="#" class="text-white"><i class="bi bi-facebook"></i></a>
                        <a href="#" class="text-white"><i class="bi bi-twitter"></i></a>
                        <a href="#" class="text-white"><i class="bi bi-instagram"></i></a>
                        <a href="#" class="text-white"><i class="bi bi-linkedin"></i></a>
                    </div>
                </div>
                <div class="col-md-2 mb-4">
                    <h6 class="fw-bold">Platform</h6>
                    <ul class="list-unstyled">
                        <li><a href="caregivers.php" class="text-muted text-decoration-none">Bakıcı Ara</a></li>
                        <li><a href="jobs.php" class="text-muted text-decoration-none">İş İlanları</a></li>
                        <li><a href="packages.php" class="text-muted text-decoration-none">Paketler</a></li>
                    </ul>
                </div>
                <div class="col-md-2 mb-4">
                    <h6 class="fw-bold">Destek</h6>
                    <ul class="list-unstyled">
                        <li><a href="help.php" class="text-muted text-decoration-none">Yardım</a></li>
                        <li><a href="contact.php" class="text-muted text-decoration-none">İletişim</a></li>
                        <li><a href="faq.php" class="text-muted text-decoration-none">SSS</a></li>
                    </ul>
                </div>
                <div class="col-md-2 mb-4">
                    <h6 class="fw-bold">Yasal</h6>
                    <ul class="list-unstyled">
                        <li><a href="privacy.php" class="text-muted text-decoration-none">Gizlilik</a></li>
                        <li><a href="terms.php" class="text-muted text-decoration-none">Kullanım Şartları</a></li>
                        <li><a href="cookies.php" class="text-muted text-decoration-none">Çerezler</a></li>
                    </ul>
                </div>
                <div class="col-md-2 mb-4">
                    <h6 class="fw-bold">İletişim</h6>
                    <ul class="list-unstyled text-muted">
                        <li><i class="bi bi-envelope me-2"></i><EMAIL></li>
                        <li><i class="bi bi-telephone me-2"></i>0850 123 45 67</li>
                        <li><i class="bi bi-geo-alt me-2"></i>İstanbul, Türkiye</li>
                    </ul>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-muted mb-0">&copy; 2024 Bakıcı Burada. Tüm hakları saklıdır.</p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="text-muted mb-0">Güvenli ödeme altyapısı ile desteklenmektedir.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
