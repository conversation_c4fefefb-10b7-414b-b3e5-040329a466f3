# Kurulum Kılavuzu - Trendyol E-Faturam OpenCart Entegrasyonu

Bu dokümanda OpenCart 3.0.3.2 için Trendyol E-Faturam modülünün detaylı kurulum adımları anlatılmaktadır.

## Ön Hazırlık

### 1. Sistem Gereksinimleri Kontrolü

Kuruluma başlamadan önce aşağıdaki gereksinimlerin karşılandığından emin olun:

- ✅ OpenCart 3.0.3.2 veya üzeri
- ✅ PHP 7.1 veya üzeri
- ✅ MySQL 5.6 veya üzeri
- ✅ cURL extension aktif
- ✅ JSON extension aktif
- ✅ OpenSSL extension aktif
- ✅ SSL sertifikası (production için)

### 2. Yedekleme

**ÖNEMLİ**: Ku<PERSON>luma başlamadan önce mutlaka yedek alın!

```bash
# Veritabanı yedeği
mysqldump -u username -p database_name > backup_$(date +%Y%m%d).sql

# Dosya yedeği
tar -czf opencart_backup_$(date +%Y%m%d).tar.gz /path/to/opencart/
```

### 3. Trendyol E-Faturam Hesabı

- Trendyol E-Faturam hesabınızın aktif olduğundan emin olun
- API kullanıcı adı ve şifrenizi hazır bulundurun
- Test ve canlı ortam bilgilerinizi not edin

## Adım Adım Kurulum

### Adım 1: Dosyaları Yükleme

1. **Modül dosyalarını indirin**
   ```bash
   # Git ile indirme
   git clone https://github.com/yourusername/opencart-trendyol-efaturam.git
   
   # Veya ZIP dosyasını indirip açın
   unzip opencart-trendyol-efaturam.zip
   ```

2. **Upload klasörünü OpenCart dizinine kopyalayın**
   ```bash
   cp -r opencart-trendyol-efaturam/upload/* /path/to/opencart/
   ```

3. **Dosya izinlerini ayarlayın**
   ```bash
   # Linux/Unix sistemlerde
   chmod -R 755 /path/to/opencart/admin/controller/extension/module/trendyol_efaturam*
   chmod -R 755 /path/to/opencart/admin/view/template/extension/module/trendyol_efaturam*
   chmod -R 755 /path/to/opencart/admin/language/*/extension/module/trendyol_efaturam*
   chmod -R 755 /path/to/opencart/catalog/controller/extension/module/trendyol_efaturam*
   chmod -R 755 /path/to/opencart/system/library/trendyol_efaturam_api.php
   
   # Log klasörünün yazılabilir olduğundan emin olun
   chmod -R 777 /path/to/opencart/system/storage/logs/
   ```

### Adım 2: Modül Kurulumu

1. **Admin panele giriş yapın**
   - OpenCart admin paneline giriş yapın
   - URL: `https://yourdomain.com/admin/`

2. **Extensions menüsüne gidin**
   - Sol menüden **Extensions** > **Extensions** seçin
   - **Choose the extension type** dropdown'dan **Modules** seçin

3. **Modülü kurun**
   - Listede **Trendyol E-Faturam** modülünü bulun
   - **Install** butonuna tıklayın
   - Kurulum başarılı mesajını bekleyin

4. **Modül ayarlarını açın**
   - **Edit** butonuna tıklayın
   - Modül yapılandırma sayfası açılacak

### Adım 3: OCMOD Kurulumu (Opsiyonel)

OCMOD kurulumu admin menüsüne kısayollar ekler.

1. **Installer sayfasına gidin**
   - **Extensions** > **Installer** menüsüne gidin

2. **OCMOD dosyasını yükleyin**
   - **Upload** butonuna tıklayın
   - `install.ocmod.xml` dosyasını seçin
   - **Continue** butonuna tıklayın

3. **Modifications'ı yenileyin**
   - **Extensions** > **Modifications** menüsüne gidin
   - **Refresh** butonuna tıklayın

### Adım 4: Veritabanı Kontrolü

Modül kurulumu sırasında otomatik olarak veritabanı tabloları oluşturulur. Kontrol etmek için:

```sql
-- Tabloların oluşturulduğunu kontrol edin
SHOW TABLES LIKE '%trendyol_efaturam%';

-- Beklenen çıktı:
-- oc_trendyol_efaturam_invoices
-- oc_trendyol_efaturam_logs
-- oc_trendyol_efaturam_settings
```

## Yapılandırma

### Temel Ayarlar

1. **Status**: **Enabled** seçin
2. **Mode**: 
   - Test için: **Test Mode**
   - Canlı için: **Production Mode**

### API Bilgileri

1. **API URL**: 
   - Test: `https://test-api.trendyolefaturam.com`
   - Canlı: `https://api.trendyolefaturam.com`

2. **Username**: Trendyol E-Faturam kullanıcı adınız
3. **Password**: Trendyol E-Faturam şifreniz

### Şirket Bilgileri

1. **Company Name**: Şirket unvanınız
2. **Tax Number**: Vergi numaranız (11 haneli)
3. **Tax Office**: Vergi daireniz

### Otomatik Gönderim

1. **Auto Send**: **Enabled** seçin (önerilir)

## Test ve Doğrulama

### 1. Bağlantı Testi

1. Modül ayarları sayfasında **Test Connection** butonuna tıklayın
2. Başarılı bağlantı mesajını bekleyin
3. Hata alırsanız API bilgilerini kontrol edin

### 2. Sistem Testleri

1. **Extensions** > **Trendyol E-Faturam** > **Test** menüsüne gidin
2. Tüm testlerin başarılı olduğunu kontrol edin
3. Hataları düzeltin

### 3. Test Siparişi

1. Test modunda bir sipariş oluşturun
2. Sipariş durumunu **Complete** yapın
3. **Invoices** sayfasından faturanın oluşturulduğunu kontrol edin

## Sorun Giderme

### Yaygın Kurulum Sorunları

#### 1. "Class not found" Hatası

**Çözüm:**
```bash
# Dosya yollarını kontrol edin
ls -la /path/to/opencart/system/library/trendyol_efaturam_api.php
ls -la /path/to/opencart/admin/controller/extension/module/trendyol_efaturam.php
```

#### 2. "Permission denied" Hatası

**Çözüm:**
```bash
# Dosya izinlerini düzeltin
chmod -R 755 /path/to/opencart/admin/controller/extension/module/
chmod -R 777 /path/to/opencart/system/storage/logs/
```

#### 3. "Table doesn't exist" Hatası

**Çözüm:**
```sql
-- Tabloları manuel oluşturun
CREATE TABLE IF NOT EXISTS `oc_trendyol_efaturam_invoices` (
    `invoice_id` int(11) NOT NULL AUTO_INCREMENT,
    `order_id` int(11) NOT NULL,
    `invoice_number` varchar(50) NOT NULL,
    `invoice_type` enum('e-invoice','e-archive') NOT NULL,
    `status` varchar(20) NOT NULL DEFAULT 'pending',
    `api_response` text,
    `error_message` text,
    `retry_count` int(3) NOT NULL DEFAULT 0,
    `date_created` datetime NOT NULL,
    `date_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`invoice_id`),
    KEY `order_id` (`order_id`),
    KEY `invoice_number` (`invoice_number`),
    KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;
```

### Log Dosyaları

Sorun yaşadığınızda aşağıdaki log dosyalarını kontrol edin:

- `system/storage/logs/trendyol_efaturam.log`
- `system/storage/logs/error.log`

## Güvenlik Önerileri

1. **SSL Kullanın**: Production ortamında mutlaka HTTPS kullanın
2. **Güçlü Şifreler**: API şifrelerinizi güçlü tutun
3. **Düzenli Yedekleme**: Veritabanı ve dosyalarınızı düzenli yedekleyin
4. **Log Temizliği**: Log dosyalarını düzenli temizleyin
5. **Güncelleme**: Modülü düzenli güncelleyin

## Destek

Kurulum sırasında sorun yaşarsanız:

1. **Dokümantasyonu kontrol edin**: README.md dosyasını okuyun
2. **Log dosyalarını inceleyin**: Hata mesajlarını kontrol edin
3. **Test sayfasını kullanın**: Sistem testlerini çalıştırın
4. **İletişime geçin**: Destek ekibiyle iletişime geçin

## Sonraki Adımlar

Kurulum tamamlandıktan sonra:

1. ✅ Canlı ortam ayarlarını yapın
2. ✅ Müşteri formlarına vergi bilgisi alanları ekleyin
3. ✅ Fatura şablonlarını özelleştirin
4. ✅ Personeli eğitin
5. ✅ Düzenli bakım planı yapın

Kurulum tamamlandı! 🎉
