<?php
require_once '../config/database.php';
require_once '../includes/functions.php';

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Zaten giriş yapmışsa dashboard'a yönlendir
if (isset($_SESSION['user_id'])) {
    header('Location: ../dashboard.php');
    exit;
}

$error_messages = [];
$success_message = '';
$user_type = safeArray($_GET, 'type', 'family'); // Varsayılan aile

// Form gönderildi mi?
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $user_type = safeArray($_POST, 'user_type', 'family');
    $full_name = trim(safeArray($_POST, 'full_name', ''));
    $email = trim(safeArray($_POST, 'email', ''));
    $phone = trim(safeArray($_POST, 'phone', ''));
    $city = safeArray($_POST, 'city', '');
    $password = safeArray($_POST, 'password', '');
    $password_confirm = safeArray($_POST, 'password_confirm', '');
    $terms_accepted = safeArray($_POST, 'terms_accepted', false);
    
    // Validasyon
    if (empty($full_name)) {
        $error_messages[] = 'Ad Soyad gereklidir.';
    } elseif (strlen($full_name) < 2) {
        $error_messages[] = 'Ad Soyad en az 2 karakter olmalıdır.';
    }
    
    if (empty($email)) {
        $error_messages[] = 'E-posta adresi gereklidir.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error_messages[] = 'Geçerli bir e-posta adresi girin.';
    }
    
    if (empty($phone)) {
        $error_messages[] = 'Telefon numarası gereklidir.';
    } elseif (!preg_match('/^[0-9\s\-\+\(\)]{10,}$/', $phone)) {
        $error_messages[] = 'Geçerli bir telefon numarası girin.';
    }
    
    if (empty($city)) {
        $error_messages[] = 'Şehir seçimi gereklidir.';
    }
    
    if (empty($password)) {
        $error_messages[] = 'Şifre gereklidir.';
    } elseif (strlen($password) < 6) {
        $error_messages[] = 'Şifre en az 6 karakter olmalıdır.';
    }
    
    if ($password !== $password_confirm) {
        $error_messages[] = 'Şifreler eşleşmiyor.';
    }
    
    if (!$terms_accepted) {
        $error_messages[] = 'Kullanım şartlarını kabul etmelisiniz.';
    }
    
    // E-posta benzersizlik kontrolü
    if (empty($error_messages)) {
        try {
            $email_check = $db->prepare("SELECT id FROM users WHERE email = ?");
            $email_check->execute([$email]);
            if ($email_check->fetch()) {
                $error_messages[] = 'Bu e-posta adresi zaten kullanılıyor.';
            }
        } catch (PDOException $e) {
            $error_messages[] = 'Veritabanı hatası oluştu.';
        }
    }
    
    // Kayıt işlemi
    if (empty($error_messages)) {
        try {
            $db->beginTransaction();
            
            // Kullanıcı oluştur
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            $sql = "INSERT INTO users (user_type, email, password, full_name, phone, city, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, NOW())";
            $stmt = $db->prepare($sql);
            $stmt->execute([$user_type, $email, $hashed_password, $full_name, $phone, $city]);
            
            $user_id = $db->lastInsertId();
            
            // Bakıcı ise profil tablosu oluştur
            if ($user_type === 'caregiver') {
                $profile_sql = "INSERT INTO caregiver_profiles (user_id, created_at) VALUES (?, NOW())";
                $profile_stmt = $db->prepare($profile_sql);
                $profile_stmt->execute([$user_id]);
            }
            
            $db->commit();
            
            // Aktivite kaydı
            logActivity($user_id, 'register', 'Yeni kullanıcı kaydı', 'users', $user_id);
            
            // Hoş geldin bildirimi
            $welcome_title = $user_type === 'caregiver' ? 'Bakıcı Burada\'ya Hoş Geldiniz!' : 'Bakıcı Burada\'ya Hoş Geldiniz!';
            $welcome_message = $user_type === 'caregiver' 
                ? 'Profilinizi tamamlayarak iş fırsatlarına başvurmaya başlayabilirsiniz.'
                : 'İlan vererek güvenilir bakıcılar bulabilirsiniz.';
            
            sendNotification($user_id, 'welcome', $welcome_title, $welcome_message);
            
            // E-posta doğrulama (basit)
            $verification_token = bin2hex(random_bytes(32));
            $update_sql = "UPDATE users SET email_verification_token = ? WHERE id = ?";
            $update_stmt = $db->prepare($update_sql);
            $update_stmt->execute([$verification_token, $user_id]);
            
            // E-posta gönder (basit)
            $verification_link = "http://" . $_SERVER['HTTP_HOST'] . "/bakici-burada/auth/verify-email.php?token=" . $verification_token;
            $email_subject = "E-posta Adresinizi Doğrulayın - Bakıcı Burada";
            $email_body = "
                <h2>Hoş Geldiniz!</h2>
                <p>Merhaba {$full_name},</p>
                <p>Bakıcı Burada'ya kaydolduğunuz için teşekkür ederiz.</p>
                <p>E-posta adresinizi doğrulamak için aşağıdaki linke tıklayın:</p>
                <p><a href='{$verification_link}'>E-posta Adresimi Doğrula</a></p>
                <p>İyi günler dileriz!</p>
            ";
            
            sendEmail($email, $email_subject, $email_body);
            
            $success_message = 'Kayıt işlemi başarılı! E-posta adresinize doğrulama linki gönderildi.';
            
        } catch (PDOException $e) {
            $db->rollBack();
            $error_messages[] = 'Kayıt işlemi sırasında hata oluştu: ' . $e->getMessage();
        }
    }
}

$page_title = 'Üye Ol';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Bakıcı Burada</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c5aa0;
            --secondary-color: #f8f9fa;
        }
        
        body {
            background: linear-gradient(135deg, var(--primary-color) 0%, #1e3d72 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        
        .auth-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .auth-header {
            background: var(--primary-color);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .auth-body {
            padding: 2rem;
        }
        
        .user-type-selector {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .user-type-card {
            flex: 1;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .user-type-card.active {
            border-color: var(--primary-color);
            background-color: rgba(44, 90, 160, 0.1);
        }
        
        .user-type-card:hover {
            border-color: var(--primary-color);
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: #1e3d72;
            border-color: #1e3d72;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="auth-card">
                    <div class="auth-header">
                        <h3 class="mb-0">
                            <i class="bi bi-heart-fill me-2"></i>Bakıcı Burada
                        </h3>
                        <p class="mb-0 mt-2">Hesap oluşturun</p>
                    </div>
                    
                    <div class="auth-body">
                        <?php if (!empty($error_messages)): ?>
                            <div class="alert alert-danger" role="alert">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                <ul class="mb-0">
                                    <?php foreach ($error_messages as $error): ?>
                                        <li><?php echo escape($error); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($success_message): ?>
                            <div class="alert alert-success" role="alert">
                                <i class="bi bi-check-circle me-2"></i><?php echo escape($success_message); ?>
                                <div class="mt-3">
                                    <a href="login.php" class="btn btn-success">Giriş Yap</a>
                                </div>
                            </div>
                        <?php else: ?>
                        
                        <form method="POST" id="registerForm">
                            <!-- Kullanıcı Türü Seçimi -->
                            <div class="user-type-selector">
                                <div class="user-type-card <?php echo $user_type === 'family' ? 'active' : ''; ?>" 
                                     onclick="selectUserType('family')">
                                    <i class="bi bi-house-heart text-primary" style="font-size: 2rem;"></i>
                                    <h6 class="mt-2 mb-1">Aile</h6>
                                    <small class="text-muted">Bakıcı arıyorum</small>
                                    <input type="radio" name="user_type" value="family" 
                                           <?php echo $user_type === 'family' ? 'checked' : ''; ?> style="display: none;">
                                </div>
                                <div class="user-type-card <?php echo $user_type === 'caregiver' ? 'active' : ''; ?>" 
                                     onclick="selectUserType('caregiver')">
                                    <i class="bi bi-person-heart text-success" style="font-size: 2rem;"></i>
                                    <h6 class="mt-2 mb-1">Bakıcı</h6>
                                    <small class="text-muted">İş arıyorum</small>
                                    <input type="radio" name="user_type" value="caregiver" 
                                           <?php echo $user_type === 'caregiver' ? 'checked' : ''; ?> style="display: none;">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="full_name" class="form-label">Ad Soyad *</label>
                                    <input type="text" class="form-control" id="full_name" name="full_name" 
                                           value="<?php echo escape(safeArray($_POST, 'full_name', '')); ?>" 
                                           placeholder="Adınız ve soyadınız" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">E-posta Adresi *</label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?php echo escape(safeArray($_POST, 'email', '')); ?>" 
                                           placeholder="<EMAIL>" required>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">Telefon Numarası *</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" 
                                           value="<?php echo escape(safeArray($_POST, 'phone', '')); ?>" 
                                           placeholder="0555 123 45 67" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="city" class="form-label">Şehir *</label>
                                    <select class="form-select" id="city" name="city" required>
                                        <option value="">Şehir seçin</option>
                                        <?php foreach (getCities() as $city): ?>
                                            <option value="<?php echo $city; ?>" 
                                                    <?php echo safeArray($_POST, 'city', '') === $city ? 'selected' : ''; ?>>
                                                <?php echo $city; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="password" class="form-label">Şifre *</label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="password" name="password" 
                                               placeholder="En az 6 karakter" required>
                                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="password_confirm" class="form-label">Şifre Tekrar *</label>
                                    <input type="password" class="form-control" id="password_confirm" name="password_confirm" 
                                           placeholder="Şifrenizi tekrar girin" required>
                                </div>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="terms_accepted" name="terms_accepted" value="1" required>
                                <label class="form-check-label" for="terms_accepted">
                                    <a href="../terms.php" target="_blank" class="text-decoration-none">Kullanım Şartları</a> ve 
                                    <a href="../privacy.php" target="_blank" class="text-decoration-none">Gizlilik Politikası</a>'nı 
                                    okudum ve kabul ediyorum. *
                                </label>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-person-plus me-2"></i>Hesap Oluştur
                                </button>
                            </div>
                        </form>
                        
                        <?php endif; ?>
                        
                        <hr class="my-4">
                        
                        <div class="text-center">
                            <p class="mb-0">
                                Zaten hesabınız var mı? 
                                <a href="login.php" class="text-decoration-none fw-bold">
                                    Giriş yapın
                                </a>
                            </p>
                        </div>
                        
                        <div class="text-center mt-4">
                            <a href="../index.php" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-2"></i>Ana Sayfaya Dön
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Kullanıcı türü seçimi
        function selectUserType(type) {
            // Tüm kartları pasif yap
            document.querySelectorAll('.user-type-card').forEach(card => {
                card.classList.remove('active');
            });
            
            // Seçili kartı aktif yap
            event.currentTarget.classList.add('active');
            
            // Radio button'ı seç
            document.querySelector(`input[name="user_type"][value="${type}"]`).checked = true;
        }
        
        // Şifre göster/gizle
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.className = 'bi bi-eye-slash';
            } else {
                passwordInput.type = 'password';
                icon.className = 'bi bi-eye';
            }
        });
        
        // Form validasyonu
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const passwordConfirm = document.getElementById('password_confirm').value;
            const termsAccepted = document.getElementById('terms_accepted').checked;
            
            if (password !== passwordConfirm) {
                e.preventDefault();
                alert('Şifreler eşleşmiyor.');
                return;
            }
            
            if (password.length < 6) {
                e.preventDefault();
                alert('Şifre en az 6 karakter olmalıdır.');
                return;
            }
            
            if (!termsAccepted) {
                e.preventDefault();
                alert('Kullanım şartlarını kabul etmelisiniz.');
                return;
            }
        });
        
        // Telefon formatı
        document.getElementById('phone').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 0) {
                if (value.length <= 3) {
                    value = value;
                } else if (value.length <= 6) {
                    value = value.slice(0, 3) + ' ' + value.slice(3);
                } else if (value.length <= 8) {
                    value = value.slice(0, 3) + ' ' + value.slice(3, 6) + ' ' + value.slice(6);
                } else {
                    value = value.slice(0, 3) + ' ' + value.slice(3, 6) + ' ' + value.slice(6, 8) + ' ' + value.slice(8, 10);
                }
            }
            e.target.value = value;
        });
    </script>
</body>
</html>
