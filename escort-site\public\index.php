<?php
// Escort İlan Sitesi - Ana Sayfa
// PHP 7.1 Uyumlu Versiyon

// Veritabanı bağlantısı
function getDbConnection() {
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=escort_site;charset=utf8mb4", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch (PDOException $e) {
        die("Veritabanı bağlantı hatası: " . $e->getMessage());
    }
}

// Site ayarlarını al
function getSettings() {
    $pdo = getDbConnection();
    $stmt = $pdo->query("SELECT `key`, `value` FROM settings");
    $settings = array();
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $settings[$row['key']] = $row['value'];
    }
    return $settings;
}

// Kategorileri al
function getCategories() {
    $pdo = getDbConnection();
    $stmt = $pdo->query("SELECT * FROM categories WHERE status = 1 ORDER BY sort_order ASC");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Şehirleri al
function getCities() {
    $pdo = getDbConnection();
    $stmt = $pdo->query("SELECT * FROM cities WHERE status = 1 ORDER BY sort_order ASC");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// İstatistikleri al
function getStats() {
    $pdo = getDbConnection();
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM categories WHERE status = 1");
    $categoryCount = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM cities WHERE status = 1");
    $cityCount = $stmt->fetch()['count'];
    
    return array(
        'total_categories' => $categoryCount,
        'total_cities' => $cityCount,
        'total_ads' => 0,
        'today_ads' => 0
    );
}

// Verileri al
$settings = getSettings();
$categories = getCategories();
$cities = getCities();
$stats = getStats();

$siteName = isset($settings['site_name']) ? $settings['site_name'] : 'Escort İlan Sitesi';
$siteDescription = isset($settings['site_description']) ? $settings['site_description'] : 'Türkiye\'nin en güvenilir escort ilan sitesi';
?>
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($siteName); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($siteDescription); ?>">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #E91E63;
            --secondary-color: #9C27B0;
            --gradient-bg: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            --dark-color: #1A1A1A;
            --light-color: #F8F9FA;
            --accent-color: #FF4081;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: var(--light-color);
        }
        
        .navbar {
            background: var(--gradient-bg) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem 0;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: white !important;
        }
        
        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }
        
        .navbar-nav .nav-link:hover {
            color: white !important;
            transform: translateY(-2px);
        }
        
        .hero-section {
            background: var(--gradient-bg);
            color: white;
            padding: 4rem 0;
            text-align: center;
        }
        
        .hero-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        
        .hero-subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .btn-primary {
            background: var(--gradient-bg);
            border: none;
            border-radius: 25px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(233, 30, 99, 0.4);
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }
        
        .stats-section {
            background: white;
            padding: 3rem 0;
        }
        
        .stat-card {
            text-align: center;
            padding: 2rem;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .stat-label {
            color: #666;
            font-weight: 500;
        }
        
        .footer {
            background: var(--dark-color);
            color: white;
            padding: 3rem 0 1rem;
            margin-top: 4rem;
        }
        
        .footer h5 {
            color: var(--primary-color);
            margin-bottom: 1rem;
        }
        
        .footer a {
            color: rgba(255,255,255,0.7);
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .footer a:hover {
            color: var(--primary-color);
        }
        
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }
            
            .hero-subtitle {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="index_backup.php">
                <i class="fas fa-heart me-2"></i>
                <?php echo htmlspecialchars($siteName); ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index_backup.php">
                            <i class="fas fa-home me-1"></i> Ana Sayfa
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#ads">
                            <i class="fas fa-list me-1"></i> İlanlar
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#categories">
                            <i class="fas fa-tags me-1"></i> Kategoriler
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="#login">
                            <i class="fas fa-sign-in-alt me-1"></i> Giriş
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#register">
                            <i class="fas fa-user-plus me-1"></i> Kayıt
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <h1 class="hero-title">
                <i class="fas fa-heart me-3"></i>
                <?php echo htmlspecialchars($siteName); ?>
            </h1>
            <p class="hero-subtitle">
                <?php echo htmlspecialchars($siteDescription); ?>
            </p>
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="input-group input-group-lg">
                        <input type="text" class="form-control" placeholder="Şehir, kategori veya anahtar kelime ara...">
                        <button class="btn btn-light" type="button">
                            <i class="fas fa-search"></i> Ara
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats-section">
        <div class="container">
            <div class="row">
                <div class="col-md-3 col-6">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['total_ads']; ?></div>
                        <div class="stat-label">Aktif İlan</div>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['total_cities']; ?></div>
                        <div class="stat-label">Şehir</div>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['total_categories']; ?></div>
                        <div class="stat-label">Kategori</div>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['today_ads']; ?></div>
                        <div class="stat-label">Bugünkü İlan</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Categories Section -->
    <section class="py-5" id="categories">
        <div class="container">
            <h2 class="text-center mb-5">
                <i class="fas fa-tags me-2" style="color: var(--primary-color);"></i>
                Kategoriler
            </h2>
            <div class="row">
                <?php if (!empty($categories)): ?>
                    <?php foreach ($categories as $category): ?>
                        <div class="col-md-3 col-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="<?php echo htmlspecialchars($category['icon']); ?> fa-3x mb-3" style="color: <?php echo htmlspecialchars($category['color']); ?>;"></i>
                                    <h5 class="card-title"><?php echo htmlspecialchars($category['name']); ?></h5>
                                    <p class="card-text text-muted"><?php echo htmlspecialchars($category['description']); ?></p>
                                    <a href="#" class="btn btn-primary btn-sm">
                                        İlanları Gör
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="col-12 text-center">
                        <p class="text-muted">Henüz kategori bulunmuyor.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Cities Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <h2 class="text-center mb-5">
                <i class="fas fa-map-marker-alt me-2" style="color: var(--primary-color);"></i>
                Popüler Şehirler
            </h2>
            <div class="row">
                <?php if (!empty($cities)): ?>
                    <?php foreach (array_slice($cities, 0, 8) as $city): ?>
                        <div class="col-md-3 col-6 mb-3">
                            <a href="#" class="btn btn-outline-primary w-100">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                <?php echo htmlspecialchars($city['name']); ?>
                            </a>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="col-12 text-center">
                        <p class="text-muted">Henüz şehir bulunmuyor.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6 mb-4">
                    <h5><i class="fas fa-heart me-2"></i><?php echo htmlspecialchars($siteName); ?></h5>
                    <p><?php echo htmlspecialchars($siteDescription); ?></p>
                </div>
                <div class="col-md-3 mb-4">
                    <h5>Hızlı Linkler</h5>
                    <ul class="list-unstyled">
                        <li><a href="index_backup.php">Ana Sayfa</a></li>
                        <li><a href="#ads">İlanlar</a></li>
                        <li><a href="#about">Hakkımızda</a></li>
                        <li><a href="#contact">İletişim</a></li>
                    </ul>
                </div>
                <div class="col-md-3 mb-4">
                    <h5>Yasal</h5>
                    <ul class="list-unstyled">
                        <li><a href="#privacy">Gizlilik Politikası</a></li>
                        <li><a href="#terms">Kullanım Şartları</a></li>
                    </ul>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; <?php echo date('Y'); ?> <?php echo htmlspecialchars($siteName); ?>. Tüm hakları saklıdır.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <i class="fas fa-code text-primary"></i> 
                        PHP <?php echo phpversion(); ?> ile geliştirilmiştir
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <script>
        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });
    </script>
</body>
</html>
