<?php
// EscortNews - <PERSON>
require_once 'includes/config.php';

// <PERSON>fa bilgileri
$pageTitle = $siteName . ' - Ana <PERSON>';
$pageDescription = $siteDescription;
$pageKeywords = 'escort, ilan, haber, türk<PERSON>ye, g<PERSON><PERSON><PERSON>r, premium, ana sayfa';

// Verileri al
$featuredAds = getFeaturedAds(6);
$latestAds = getLatestAds(8);

// Arama işlemi
$searchResults = array();
if (isset($_GET['search']) && !empty($_GET['search'])) {
    $searchTerm = sanitize($_GET['search']);
    $searchResults = searchAds($searchTerm, 12);
}

// Header'ı dahil et
include 'includes/header.php';
?>

<!-- Hero Section -->
<section style="background: var(--gradient-primary); color: white; padding: 5rem 0; position: relative; overflow: hidden;">
    <div style="content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1000 100\" fill=\"white\" opacity=\"0.1\"><polygon points=\"0,0 1000,0 1000,100 0,80\"/></svg>'); background-size: cover;"></div>
    <div class="container" style="position: relative; z-index: 2;">
        <div class="text-center">
            <h1 style="font-size: 3.5rem; font-weight: 800; margin-bottom: 1.5rem; font-family: 'Playfair Display', serif; line-height: 1.2;">
                Türkiye'nin En Güvenilir<br>
                <span style="color: var(--accent-color);">Escort Haber</span> Platformu
            </h1>
            <p style="font-size: 1.25rem; margin-bottom: 3rem; opacity: 0.95; font-weight: 400; max-width: 600px; margin-left: auto; margin-right: auto;">
                Güncel haberler, güvenilir ilanlar ve premium hizmetler tek platformda. 
                Kaliteli içerik ve güvenli deneyim için doğru adrestesiniz.
            </p>
            
            <div style="background: white; border-radius: 15px; padding: 1rem; box-shadow: var(--shadow-xl); max-width: 600px; margin: 0 auto;">
                <form method="GET" action="search.php" class="d-flex">
                    <input type="text" name="q" class="form-control flex-grow-1" 
                           style="border: none; outline: none; font-size: 1.1rem; padding: 0.75rem 1rem;"
                           placeholder="Şehir, kategori veya anahtar kelime ara..." 
                           value="<?php echo isset($_GET['search']) ? htmlspecialchars($_GET['search']) : ''; ?>">
                    <button type="submit" class="btn ms-2" 
                            style="background: var(--gradient-primary); border: none; color: white; padding: 0.75rem 2rem; border-radius: 10px; font-weight: 600;">
                        <i class="fas fa-search me-1"></i> Ara
                    </button>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section style="background: white; padding: 3rem 0;">
    <div class="container">
        <div class="row">
            <div class="col-md-3 col-6">
                <div style="text-align: center; padding: 2rem;">
                    <div style="font-size: 2.5rem; font-weight: 700; color: var(--primary-color);"><?php echo $stats['total_ads']; ?></div>
                    <div style="color: #666; font-weight: 500;">Aktif İlan</div>
                </div>
            </div>
            <div class="col-md-3 col-6">
                <div style="text-align: center; padding: 2rem;">
                    <div style="font-size: 2.5rem; font-weight: 700; color: var(--primary-color);"><?php echo $stats['total_cities']; ?></div>
                    <div style="color: #666; font-weight: 500;">Şehir</div>
                </div>
            </div>
            <div class="col-md-3 col-6">
                <div style="text-align: center; padding: 2rem;">
                    <div style="font-size: 2.5rem; font-weight: 700; color: var(--primary-color);"><?php echo $stats['total_categories']; ?></div>
                    <div style="color: #666; font-weight: 500;">Kategori</div>
                </div>
            </div>
            <div class="col-md-3 col-6">
                <div style="text-align: center; padding: 2rem;">
                    <div style="font-size: 2.5rem; font-weight: 700; color: var(--primary-color);"><?php echo $stats['today_ads']; ?></div>
                    <div style="color: #666; font-weight: 500;">Bugünkü İlan</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Ads Section -->
<?php if (!empty($featuredAds)): ?>
<section class="py-5" id="featured">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-5">
            <h2 class="font-display">
                <i class="fas fa-star me-2 text-warning"></i>
                Öne Çıkan İlanlar
            </h2>
            <a href="ads.php?featured=1" class="btn btn-outline-primary">
                Tümünü Gör <i class="fas fa-arrow-right ms-1"></i>
            </a>
        </div>
        <div class="row">
            <?php foreach ($featuredAds as $ad): ?>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card ad-card h-100">
                    <div class="ad-badge featured-badge">
                        <i class="fas fa-crown me-1"></i> Premium
                    </div>
                    
                    <?php if ($ad['primary_photo']): ?>
                    <img src="uploads/ads/<?php echo htmlspecialchars($ad['primary_photo']); ?>" 
                         class="card-img-top" alt="<?php echo htmlspecialchars($ad['title']); ?>">
                    <?php else: ?>
                    <div class="card-img-top d-flex align-items-center justify-content-center" style="background: var(--gradient-primary); height: 200px;">
                        <i class="fas fa-image fa-3x text-white opacity-50"></i>
                    </div>
                    <?php endif; ?>
                    
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <span class="badge" style="background-color: <?php echo $ad['category_color'] ?? '#DC2626'; ?>;">
                                <?php echo htmlspecialchars($ad['category_name']); ?>
                            </span>
                            <small class="text-muted">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                <?php echo htmlspecialchars($ad['city_name']); ?>
                            </small>
                        </div>
                        <h5 class="card-title"><?php echo htmlspecialchars($ad['title']); ?></h5>
                        <p class="card-text text-muted">
                            <?php echo htmlspecialchars(substr($ad['description'], 0, 100)) . '...'; ?>
                        </p>
                        <div class="d-flex justify-content-between align-items-center">
                            <?php if ($ad['price_per_hour']): ?>
                            <span class="fw-bold text-primary">
                                ₺<?php echo number_format($ad['price_per_hour'], 0, ',', '.'); ?>/saat
                            </span>
                            <?php endif; ?>
                            <a href="ad-detail.php?slug=<?php echo $ad['slug']; ?>" class="btn btn-primary btn-sm">
                                <i class="fas fa-eye me-1"></i> Detay
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Categories Section -->
<section class="py-5 bg-light" id="categories">
    <div class="container">
        <h2 class="text-center mb-5 font-display">
            <i class="fas fa-tags me-2" style="color: var(--primary-color);"></i>
            Kategoriler
        </h2>
        <div class="row">
            <?php if (!empty($categories)): ?>
                <?php foreach ($categories as $category): ?>
                <div class="col-md-3 col-6 mb-4">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="<?php echo $category['icon'] ?? 'fas fa-heart'; ?> fa-3x mb-3" style="color: <?php echo $category['color'] ?? '#DC2626'; ?>;"></i>
                            <h5 class="card-title"><?php echo htmlspecialchars($category['name']); ?></h5>
                            <p class="card-text text-muted"><?php echo htmlspecialchars($category['description']); ?></p>
                            <a href="category.php?slug=<?php echo $category['slug']; ?>" class="btn btn-primary btn-sm">
                                İlanları Gör
                            </a>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="col-12 text-center">
                    <p class="text-muted">Henüz kategori bulunmuyor.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- Latest Ads Section -->
<?php if (!empty($latestAds)): ?>
<section class="py-5" id="latest">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-5">
            <h2 class="font-display">
                <i class="fas fa-clock me-2 text-info"></i>
                Son Eklenen İlanlar
            </h2>
            <a href="ads.php" class="btn btn-outline-primary">
                Tümünü Gör <i class="fas fa-arrow-right ms-1"></i>
            </a>
        </div>
        <div class="row">
            <?php foreach (array_slice($latestAds, 0, 6) as $ad): ?>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card ad-card h-100">
                    <?php if ($ad['featured']): ?>
                    <div class="ad-badge">
                        <i class="fas fa-star me-1"></i> Öne Çıkan
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($ad['primary_photo']): ?>
                    <img src="uploads/ads/<?php echo htmlspecialchars($ad['primary_photo']); ?>" 
                         class="card-img-top" alt="<?php echo htmlspecialchars($ad['title']); ?>">
                    <?php else: ?>
                    <div class="card-img-top d-flex align-items-center justify-content-center" style="background: var(--gradient-primary); height: 200px;">
                        <i class="fas fa-image fa-3x text-white opacity-50"></i>
                    </div>
                    <?php endif; ?>
                    
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <span class="badge" style="background-color: <?php echo $ad['category_color'] ?? '#DC2626'; ?>;">
                                <?php echo htmlspecialchars($ad['category_name']); ?>
                            </span>
                            <small class="text-muted">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                <?php echo htmlspecialchars($ad['city_name']); ?>
                            </small>
                        </div>
                        <h5 class="card-title"><?php echo htmlspecialchars($ad['title']); ?></h5>
                        <p class="card-text text-muted">
                            <?php echo htmlspecialchars(substr($ad['description'], 0, 100)) . '...'; ?>
                        </p>
                        <div class="d-flex justify-content-between align-items-center">
                            <?php if ($ad['price_per_hour']): ?>
                            <span class="fw-bold text-primary">
                                ₺<?php echo number_format($ad['price_per_hour'], 0, ',', '.'); ?>/saat
                            </span>
                            <?php endif; ?>
                            <a href="ad-detail.php?slug=<?php echo $ad['slug']; ?>" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye me-1"></i> Detay
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Cities Section -->
<section class="py-5 bg-light">
    <div class="container">
        <h2 class="text-center mb-5 font-display">
            <i class="fas fa-map-marker-alt me-2" style="color: var(--primary-color);"></i>
            Popüler Şehirler
        </h2>
        <div class="row">
            <?php if (!empty($cities)): ?>
                <?php foreach (array_slice($cities, 0, 8) as $city): ?>
                <div class="col-md-3 col-6 mb-3">
                    <a href="city.php?slug=<?php echo $city['slug']; ?>" class="btn btn-outline-primary w-100">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        <?php echo htmlspecialchars($city['name']); ?>
                    </a>
                </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="col-12 text-center">
                    <p class="text-muted">Henüz şehir bulunmuyor.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>

<?php
// Footer'ı dahil et
include 'includes/footer.php';
?>
